<?php
/**
 * Voice Engine - Local TTS integration for character voices
 * Part of Sutradhar Engine
 */

class VoiceEngine {
    private $config;
    private $voicePacks;
    private $cacheDir;
    private $tempDir;

    public function __construct() {
        $this->loadConfig();
        $this->setupDirectories();
    }

    private function loadConfig() {
        $configPath = __DIR__ . '/../config/settings.json';
        $this->config = json_decode(file_get_contents($configPath), true);
        $this->voicePacks = $this->config['voice_packs'];
        $this->cacheDir = __DIR__ . '/../data/voice_cache/';
        $this->tempDir = __DIR__ . '/../temp/';
    }

    private function setupDirectories() {
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
        if (!is_dir($this->tempDir)) {
            mkdir($this->tempDir, 0755, true);
        }
    }

    /**
     * Generate voice audio for text segments
     */
    public function generateVoice($segments, $voicePack, $jobId) {
        $audioFiles = [];
        $voiceConfig = $this->voicePacks[$voicePack] ?? $this->voicePacks['babu_rao'];

        foreach ($segments as $index => $segment) {
            $audioFile = $this->generateSegmentAudio($segment, $voiceConfig, $jobId, $index);
            $audioFiles[] = $audioFile;
        }

        return $audioFiles;
    }

    /**
     * Generate audio for a single segment
     */
    private function generateSegmentAudio($segment, $voiceConfig, $jobId, $index) {
        $text = $segment['text'];
        $voiceStyle = $segment['voice_style'] ?? 'normal';
        
        // Create cache key
        $cacheKey = md5($text . $voiceConfig['name'] . $voiceStyle);
        $cacheFile = $this->cacheDir . $cacheKey . '.wav';

        // Check cache first
        if ($this->config['voice']['cache_enabled'] && file_exists($cacheFile)) {
            $cacheAge = time() - filemtime($cacheFile);
            if ($cacheAge < $this->config['voice']['cache_duration']) {
                return [
                    'file' => $cacheFile,
                    'duration' => $this->getAudioDuration($cacheFile),
                    'cached' => true
                ];
            }
        }

        // Generate new audio
        $outputFile = $this->tempDir . $jobId . '_segment_' . $index . '.wav';
        
        // Prepare text with voice modifications
        $processedText = $this->processTextForVoice($text, $voiceStyle, $voiceConfig);
        
        // Generate audio using selected TTS engine
        $success = $this->generateTTSAudio($processedText, $voiceConfig, $outputFile);
        
        if (!$success) {
            throw new Exception("Failed to generate audio for segment {$index}");
        }

        // Copy to cache if enabled
        if ($this->config['voice']['cache_enabled']) {
            copy($outputFile, $cacheFile);
        }

        return [
            'file' => $outputFile,
            'duration' => $this->getAudioDuration($outputFile),
            'cached' => false
        ];
    }

    /**
     * Process text for voice generation
     */
    private function processTextForVoice($text, $voiceStyle, $voiceConfig) {
        // Add SSML-like markup for voice control
        $processedText = $text;

        // Apply voice style modifications
        switch ($voiceStyle) {
            case 'energetic':
                $processedText = "<prosody rate='fast' pitch='+10%'>{$text}</prosody>";
                break;
            case 'slow':
                $processedText = "<prosody rate='slow' pitch='-5%'>{$text}</prosody>";
                break;
            case 'aggressive':
                $processedText = "<prosody rate='fast' pitch='+15%' volume='loud'>{$text}</prosody>";
                break;
            case 'sarcastic':
                $processedText = "<prosody rate='medium' pitch='+5%'>{$text}</prosody>";
                break;
            case 'storytelling':
                $processedText = "<prosody rate='medium' pitch='medium'>{$text}</prosody>";
                break;
            case 'persuasive':
                $processedText = "<prosody rate='slow' pitch='+8%'>{$text}</prosody>";
                break;
        }

        // Add accent-specific modifications
        if ($voiceConfig['accent'] === 'gujarati') {
            $processedText = $this->addGujaratiAccent($processedText);
        } elseif ($voiceConfig['accent'] === 'punjabi') {
            $processedText = $this->addPunjabiAccent($processedText);
        } elseif ($voiceConfig['accent'] === 'delhi') {
            $processedText = $this->addDelhiAccent($processedText);
        }

        return $processedText;
    }

    /**
     * Generate TTS audio using configured engine
     */
    private function generateTTSAudio($text, $voiceConfig, $outputFile) {
        $engine = $this->config['voice']['engine'];
        
        switch ($engine) {
            case 'bark':
                return $this->generateBarkAudio($text, $voiceConfig, $outputFile);
            case 'coqui':
                return $this->generateCoquiAudio($text, $voiceConfig, $outputFile);
            case 'espeak':
                return $this->generateEspeakAudio($text, $voiceConfig, $outputFile);
            case 'fallback':
            default:
                return $this->generateFallbackAudio($text, $outputFile);
        }
    }

    /**
     * Generate audio using Bark TTS
     */
    private function generateBarkAudio($text, $voiceConfig, $outputFile) {
        // Bark TTS command (assuming bark-cli is installed)
        $modelPath = $this->config['paths']['assets'] . 'voices/' . $voiceConfig['model_file'];
        
        $command = sprintf(
            'python -m bark --text %s --voice %s --output %s --sample_rate %d',
            escapeshellarg($text),
            escapeshellarg($modelPath),
            escapeshellarg($outputFile),
            $this->config['voice']['sample_rate']
        );

        $result = shell_exec($command . ' 2>&1');
        
        return file_exists($outputFile) && filesize($outputFile) > 0;
    }

    /**
     * Generate audio using Coqui TTS
     */
    private function generateCoquiAudio($text, $voiceConfig, $outputFile) {
        // Coqui TTS command
        $command = sprintf(
            'tts --text %s --model_name "tts_models/en/ljspeech/tacotron2-DDC" --out_path %s',
            escapeshellarg($text),
            escapeshellarg($outputFile)
        );

        $result = shell_exec($command . ' 2>&1');
        
        return file_exists($outputFile) && filesize($outputFile) > 0;
    }

    /**
     * Generate audio using eSpeak (fallback)
     */
    private function generateEspeakAudio($text, $voiceConfig, $outputFile) {
        // eSpeak command (basic TTS)
        $voice = $this->mapVoiceToEspeak($voiceConfig);
        
        $command = sprintf(
            'espeak -v %s -s 150 -p 50 -w %s %s',
            $voice,
            escapeshellarg($outputFile),
            escapeshellarg($text)
        );

        $result = shell_exec($command . ' 2>&1');
        
        return file_exists($outputFile) && filesize($outputFile) > 0;
    }

    /**
     * Fallback audio generation (creates a simple WAV file with tone patterns)
     */
    private function generateFallbackAudio($text, $outputFile) {
        // Create a more interesting audio file with tone patterns representing speech
        $duration = max(strlen($text) * 0.08, 1.0); // Roughly 80ms per character
        $sampleRate = $this->config['voice']['sample_rate'];
        $channels = 1; // Mono for simplicity
        $bitsPerSample = 16;

        // Calculate the number of samples
        $numSamples = intval($duration * $sampleRate);
        $dataSize = $numSamples * $channels * ($bitsPerSample / 8);

        // Create WAV header
        $header = pack('V', 0x46464952); // "RIFF"
        $header .= pack('V', $dataSize + 36); // File size - 8
        $header .= pack('V', 0x45564157); // "WAVE"
        $header .= pack('V', 0x20746d66); // "fmt "
        $header .= pack('V', 16); // Subchunk1Size
        $header .= pack('v', 1); // AudioFormat (PCM)
        $header .= pack('v', $channels); // NumChannels
        $header .= pack('V', $sampleRate); // SampleRate
        $header .= pack('V', $sampleRate * $channels * ($bitsPerSample / 8)); // ByteRate
        $header .= pack('v', $channels * ($bitsPerSample / 8)); // BlockAlign
        $header .= pack('v', $bitsPerSample); // BitsPerSample
        $header .= pack('V', 0x61746164); // "data"
        $header .= pack('V', $dataSize); // Subchunk2Size

        // Create audio data with speech-like patterns
        $audioData = '';
        $words = explode(' ', $text);
        $samplesPerWord = intval($numSamples / max(count($words), 1));

        for ($wordIndex = 0; $wordIndex < count($words); $wordIndex++) {
            $word = $words[$wordIndex];
            $wordLength = strlen($word);

            // Generate tone pattern for this word
            for ($i = 0; $i < $samplesPerWord; $i++) {
                $progress = $i / $samplesPerWord;

                // Create varying frequency based on word characteristics
                $baseFreq = 150 + ($wordLength * 20); // Base frequency varies with word length
                $freq = $baseFreq + sin($progress * pi() * 2) * 50; // Add some variation

                // Create amplitude envelope (fade in/out for each word)
                $envelope = sin($progress * pi()) * 0.3;

                // Add some noise for more natural sound
                $noise = (rand(-100, 100) / 1000) * 0.1;

                $sample = intval(sin($i * 2 * pi() * $freq / $sampleRate) * $envelope * 3000 + $noise * 1000);
                $sample = max(-32767, min(32767, $sample)); // Clamp to 16-bit range

                $audioData .= pack('v', $sample);
            }

            // Add pause between words
            if ($wordIndex < count($words) - 1) {
                $pauseSamples = intval($sampleRate * 0.1); // 100ms pause
                for ($i = 0; $i < $pauseSamples && strlen($audioData) < $numSamples * 2; $i++) {
                    $audioData .= pack('v', 0);
                }
            }
        }

        // Pad or trim to exact duration
        $currentSamples = strlen($audioData) / 2;
        if ($currentSamples < $numSamples) {
            // Pad with silence
            $audioData .= str_repeat(pack('v', 0), $numSamples - $currentSamples);
        } else {
            // Trim to exact length
            $audioData = substr($audioData, 0, $numSamples * 2);
        }

        // Write the WAV file
        $wavContent = $header . $audioData;
        $result = file_put_contents($outputFile, $wavContent);

        return $result !== false && file_exists($outputFile);
    }

    /**
     * Map voice pack to eSpeak voice
     */
    private function mapVoiceToEspeak($voiceConfig) {
        $mapping = [
            'babu_rao' => 'en+m3',
            'villain' => 'en+m1',
            'dadi' => 'en+f3',
            'gym_bro' => 'en+m2',
            'news_anchor' => 'en+m4'
        ];

        return $mapping[$voiceConfig['name']] ?? 'en';
    }

    /**
     * Add Gujarati accent characteristics
     */
    private function addGujaratiAccent($text) {
        // Simple phonetic replacements for Gujarati accent
        $replacements = [
            'the' => 'dha',
            'this' => 'dhis',
            'that' => 'dhat',
            'what' => 'vhat',
            'where' => 'vhere'
        ];

        foreach ($replacements as $from => $to) {
            $text = str_ireplace($from, $to, $text);
        }

        return $text;
    }

    /**
     * Add Punjabi accent characteristics
     */
    private function addPunjabiAccent($text) {
        // Simple phonetic replacements for Punjabi accent
        $replacements = [
            'very' => 'wery',
            'village' => 'willage',
            'voice' => 'woice'
        ];

        foreach ($replacements as $from => $to) {
            $text = str_ireplace($from, $to, $text);
        }

        return $text;
    }

    /**
     * Add Delhi accent characteristics
     */
    private function addDelhiAccent($text) {
        // Add typical Delhi expressions
        $delhiPhrases = ['yaar', 'bhai', 'arre'];
        $randomPhrase = $delhiPhrases[array_rand($delhiPhrases)];
        
        return $text . ', ' . $randomPhrase;
    }

    /**
     * Get audio file duration
     */
    private function getAudioDuration($audioFile) {
        if (!file_exists($audioFile)) {
            return 0;
        }

        $command = sprintf(
            'ffprobe -v quiet -show_entries format=duration -of csv="p=0" %s',
            escapeshellarg($audioFile)
        );

        $duration = shell_exec($command);
        return floatval(trim($duration ?? '0'));
    }

    /**
     * Apply audio effects to generated voice
     */
    public function applyEffects($audioFile, $effects) {
        if (empty($effects)) {
            return $audioFile;
        }

        $outputFile = str_replace('.wav', '_fx.wav', $audioFile);
        $filters = [];

        foreach ($effects as $effect) {
            switch ($effect) {
                case 'reverb':
                    $filters[] = 'aecho=0.8:0.9:1000:0.3';
                    break;
                case 'pitch_up':
                    $filters[] = 'asetrate=48000*1.1,aresample=48000';
                    break;
                case 'pitch_down':
                    $filters[] = 'asetrate=48000*0.9,aresample=48000';
                    break;
                case 'robot':
                    $filters[] = 'afftfilt=real="hypot(re,im)*cos(0)":imag="hypot(re,im)*sin(0)"';
                    break;
            }
        }

        if (!empty($filters)) {
            $filterString = implode(',', $filters);
            $command = sprintf(
                'ffmpeg -i %s -af "%s" %s -y',
                escapeshellarg($audioFile),
                $filterString,
                escapeshellarg($outputFile)
            );

            shell_exec($command . ' 2>&1');
            
            if (file_exists($outputFile)) {
                return $outputFile;
            }
        }

        return $audioFile;
    }

    /**
     * Clean up temporary files
     */
    public function cleanup($jobId) {
        $pattern = $this->tempDir . $jobId . '_*';
        $files = glob($pattern);
        
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
    }
}
