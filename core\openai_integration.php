<?php
/**
 * Open Source AI Integration - Connects to free/open-source AI APIs
 * Part of Sutradhar Engine
 */

class OpenAIIntegration {
    private $config;
    private $endpoints;

    public function __construct() {
        $this->loadConfig();
        $this->setupEndpoints();
    }

    private function loadConfig() {
        $configPath = __DIR__ . '/../config/settings.json';
        $this->config = json_decode(file_get_contents($configPath), true);
    }

    private function setupEndpoints() {
        $this->endpoints = [
            // Hugging Face Inference API (Free tier available)
            'huggingface' => [
                'base_url' => 'https://api-inference.huggingface.co/models/',
                'text_generation' => 'microsoft/DialoGPT-medium',
                'text_to_speech' => 'facebook/fastspeech2-en-ljspeech',
                'headers' => [
                    'Authorization' => 'Bearer ' . ($this->config['api_keys']['huggingface'] ?? ''),
                    'Content-Type' => 'application/json'
                ]
            ],
            // Ollama (Local LLM server)
            'ollama' => [
                'base_url' => 'http://localhost:11434/api/',
                'generate' => 'generate',
                'models' => ['llama2', 'mistral', 'codellama']
            ],
            // OpenAI-compatible APIs (like Together AI, Groq, etc.)
            'openai_compatible' => [
                'base_url' => $this->config['api_endpoints']['openai_compatible'] ?? 'https://api.together.xyz/v1/',
                'headers' => [
                    'Authorization' => 'Bearer ' . ($this->config['api_keys']['together'] ?? ''),
                    'Content-Type' => 'application/json'
                ]
            ]
        ];
    }

    /**
     * Generate unique content using open-source models
     */
    public function generateUniqueContent($prompt, $style, $flowType, $maxTokens = 500) {
        // Try different providers in order of preference
        $providers = ['ollama', 'huggingface', 'openai_compatible'];
        
        foreach ($providers as $provider) {
            try {
                $result = $this->callProvider($provider, $prompt, $style, $flowType, $maxTokens);
                if ($result && !empty($result['content'])) {
                    return [
                        'content' => $result['content'],
                        'provider' => $provider,
                        'model' => $result['model'] ?? 'unknown',
                        'tokens_used' => $result['tokens_used'] ?? 0
                    ];
                }
            } catch (Exception $e) {
                error_log("Provider {$provider} failed: " . $e->getMessage());
                continue;
            }
        }
        
        // Fallback to local generation
        return $this->generateLocalContent($prompt, $style, $flowType);
    }

    /**
     * Call specific AI provider
     */
    private function callProvider($provider, $prompt, $style, $flowType, $maxTokens) {
        switch ($provider) {
            case 'ollama':
                return $this->callOllama($prompt, $style, $flowType, $maxTokens);
            case 'huggingface':
                return $this->callHuggingFace($prompt, $style, $flowType, $maxTokens);
            case 'openai_compatible':
                return $this->callOpenAICompatible($prompt, $style, $flowType, $maxTokens);
            default:
                throw new Exception("Unknown provider: {$provider}");
        }
    }

    /**
     * Call Ollama local LLM server
     */
    private function callOllama($prompt, $style, $flowType, $maxTokens) {
        $endpoint = $this->endpoints['ollama'];
        $url = $endpoint['base_url'] . $endpoint['generate'];
        
        // Check if Ollama is available
        if (!$this->isOllamaAvailable()) {
            throw new Exception("Ollama server not available");
        }
        
        $systemPrompt = $this->buildSystemPrompt($style, $flowType);
        $fullPrompt = $systemPrompt . "\n\nUser: " . $prompt . "\n\nAssistant:";
        
        $data = [
            'model' => $endpoint['models'][0], // Use first available model
            'prompt' => $fullPrompt,
            'stream' => false,
            'options' => [
                'num_predict' => $maxTokens,
                'temperature' => 0.8,
                'top_p' => 0.9
            ]
        ];
        
        $response = $this->makeHttpRequest($url, $data);
        
        if ($response && isset($response['response'])) {
            return [
                'content' => trim($response['response']),
                'model' => $response['model'] ?? 'ollama',
                'tokens_used' => strlen($response['response']) / 4 // Rough estimate
            ];
        }
        
        throw new Exception("Ollama API call failed");
    }

    /**
     * Call Hugging Face Inference API
     */
    private function callHuggingFace($prompt, $style, $flowType, $maxTokens) {
        $endpoint = $this->endpoints['huggingface'];
        $url = $endpoint['base_url'] . $endpoint['text_generation'];
        
        if (empty($this->config['api_keys']['huggingface'])) {
            throw new Exception("Hugging Face API key not configured");
        }
        
        $systemPrompt = $this->buildSystemPrompt($style, $flowType);
        $fullPrompt = $systemPrompt . "\n" . $prompt;
        
        $data = [
            'inputs' => $fullPrompt,
            'parameters' => [
                'max_new_tokens' => $maxTokens,
                'temperature' => 0.8,
                'top_p' => 0.9,
                'do_sample' => true
            ]
        ];
        
        $response = $this->makeHttpRequest($url, $data, $endpoint['headers']);
        
        if ($response && is_array($response) && isset($response[0]['generated_text'])) {
            $generatedText = $response[0]['generated_text'];
            // Remove the original prompt from the response
            $content = str_replace($fullPrompt, '', $generatedText);
            
            return [
                'content' => trim($content),
                'model' => 'huggingface-' . $endpoint['text_generation'],
                'tokens_used' => strlen($content) / 4
            ];
        }
        
        throw new Exception("Hugging Face API call failed");
    }

    /**
     * Call OpenAI-compatible API (Together AI, Groq, etc.)
     */
    private function callOpenAICompatible($prompt, $style, $flowType, $maxTokens) {
        $endpoint = $this->endpoints['openai_compatible'];
        $url = $endpoint['base_url'] . 'chat/completions';
        
        if (empty($this->config['api_keys']['together'])) {
            throw new Exception("OpenAI-compatible API key not configured");
        }
        
        $systemPrompt = $this->buildSystemPrompt($style, $flowType);
        
        $data = [
            'model' => 'mistralai/Mixtral-8x7B-Instruct-v0.1', // Free model on Together AI
            'messages' => [
                ['role' => 'system', 'content' => $systemPrompt],
                ['role' => 'user', 'content' => $prompt]
            ],
            'max_tokens' => $maxTokens,
            'temperature' => 0.8,
            'top_p' => 0.9
        ];
        
        $response = $this->makeHttpRequest($url, $data, $endpoint['headers']);
        
        if ($response && isset($response['choices'][0]['message']['content'])) {
            return [
                'content' => trim($response['choices'][0]['message']['content']),
                'model' => $response['model'] ?? 'openai-compatible',
                'tokens_used' => $response['usage']['total_tokens'] ?? 0
            ];
        }
        
        throw new Exception("OpenAI-compatible API call failed");
    }

    /**
     * Build system prompt based on style and flow type
     */
    private function buildSystemPrompt($style, $flowType) {
        $basePrompt = "You are a creative Indian content creator specializing in {$flowType} content with a {$style} style.";
        
        $stylePrompts = [
            'funny' => "Create humorous, sarcastic content with Indian cultural references. Use Hinglish naturally and add comedic timing.",
            'desi' => "Create culturally authentic Indian content with traditional values and relatable family situations. Mix Hindi and English naturally.",
            'emotional' => "Create heartfelt, touching content that resonates with Indian emotions and family values. Be sincere and warm.",
            'bollywood' => "Create dramatic, filmy content with Bollywood references, movie dialogues, and over-the-top expressions."
        ];
        
        $flowPrompts = [
            'reel' => "Create short, engaging content perfect for social media reels. Include a hook, main content, and call-to-action.",
            'audio_story' => "Create a complete narrative story with introduction, development, climax, and moral lesson.",
            'meme_rant' => "Create a humorous rant about everyday Indian situations with cultural observations and comedic commentary."
        ];
        
        return $basePrompt . " " . ($stylePrompts[$style] ?? '') . " " . ($flowPrompts[$flowType] ?? '') . 
               " Keep the content authentic, engaging, and culturally relevant to Indian audiences.";
    }

    /**
     * Generate content locally as fallback
     */
    private function generateLocalContent($prompt, $style, $flowType) {
        // Use the existing content generator as fallback
        require_once __DIR__ . '/content_generator.php';
        $contentGenerator = new ContentGenerator();
        
        $uniqueContent = $contentGenerator->generateUniqueContent($prompt, $flowType, $style, 'babu_rao');
        
        return [
            'content' => $uniqueContent['total_text'],
            'provider' => 'local',
            'model' => 'sutradhar-local',
            'tokens_used' => 0
        ];
    }

    /**
     * Check if Ollama is available
     */
    private function isOllamaAvailable() {
        $url = $this->endpoints['ollama']['base_url'] . 'tags';
        
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 5,
                'ignore_errors' => true
            ]
        ]);
        
        $result = @file_get_contents($url, false, $context);
        return $result !== false;
    }

    /**
     * Make HTTP request to API
     */
    private function makeHttpRequest($url, $data, $headers = []) {
        $defaultHeaders = ['Content-Type: application/json'];
        
        foreach ($headers as $key => $value) {
            $defaultHeaders[] = $key . ': ' . $value;
        }
        
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => implode("\r\n", $defaultHeaders),
                'content' => json_encode($data),
                'timeout' => 30,
                'ignore_errors' => true
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response === false) {
            throw new Exception("HTTP request failed to {$url}");
        }
        
        $decoded = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("Invalid JSON response: " . json_last_error_msg());
        }
        
        return $decoded;
    }

    /**
     * Generate voice using open-source TTS APIs
     */
    public function generateVoiceFromAPI($text, $voicePack, $outputFile) {
        // Try Hugging Face TTS API
        if (!empty($this->config['api_keys']['huggingface'])) {
            try {
                return $this->generateHuggingFaceTTS($text, $voicePack, $outputFile);
            } catch (Exception $e) {
                error_log("Hugging Face TTS failed: " . $e->getMessage());
            }
        }
        
        // Fallback to local generation
        return false;
    }

    /**
     * Generate TTS using Hugging Face
     */
    private function generateHuggingFaceTTS($text, $voicePack, $outputFile) {
        $endpoint = $this->endpoints['huggingface'];
        $url = $endpoint['base_url'] . $endpoint['text_to_speech'];
        
        $data = ['inputs' => $text];
        
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => implode("\r\n", [
                    'Authorization: ' . $endpoint['headers']['Authorization'],
                    'Content-Type: application/json'
                ]),
                'content' => json_encode($data),
                'timeout' => 60
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response !== false && strlen($response) > 1000) { // Reasonable audio file size
            return file_put_contents($outputFile, $response) !== false;
        }
        
        return false;
    }

    /**
     * Get available models for a provider
     */
    public function getAvailableModels($provider = 'ollama') {
        switch ($provider) {
            case 'ollama':
                if ($this->isOllamaAvailable()) {
                    $url = $this->endpoints['ollama']['base_url'] . 'tags';
                    try {
                        $response = $this->makeHttpRequest($url, [], []);
                        return $response['models'] ?? [];
                    } catch (Exception $e) {
                        return [];
                    }
                }
                return [];
            default:
                return $this->endpoints[$provider]['models'] ?? [];
        }
    }

    /**
     * Test API connectivity
     */
    public function testConnectivity() {
        $results = [];
        
        // Test Ollama
        $results['ollama'] = $this->isOllamaAvailable();
        
        // Test Hugging Face
        $results['huggingface'] = !empty($this->config['api_keys']['huggingface']);
        
        // Test OpenAI-compatible
        $results['openai_compatible'] = !empty($this->config['api_keys']['together']);
        
        return $results;
    }
}
