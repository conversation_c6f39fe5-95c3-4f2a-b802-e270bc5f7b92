<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Test Authentication System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0F0F23 0%, #1A1A2E 50%, #16213E 100%);
            font-family: 'Arial', sans-serif;
            color: white;
            min-height: 100vh;
        }
        .test-card {
            background: rgba(26, 26, 46, 0.8);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            transition: all 0.3s ease;
        }
        .success { border-color: #10B981; background: rgba(16, 185, 129, 0.1); }
        .error { border-color: #EF4444; background: rgba(239, 68, 68, 0.1); }
        .loading { border-color: #F59E0B; background: rgba(245, 158, 11, 0.1); }
        .cyber-button {
            background: linear-gradient(45deg, #00D4FF, #0099CC);
            border: none;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .cyber-button:hover {
            background: linear-gradient(45deg, #0099CC, #00D4FF);
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="p-8">
    <div class="max-w-4xl mx-auto">
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold mb-4">🔐 Authentication System Test</h1>
            <p class="text-gray-300">Testing the complete authentication and video generation system</p>
        </div>

        <!-- Current Status -->
        <div class="test-card p-6 rounded-lg mb-6">
            <h3 class="text-lg font-semibold mb-3">📊 Current Status</h3>
            <div id="current-status">Loading...</div>
        </div>

        <div class="grid md:grid-cols-2 gap-6 mb-8">
            <!-- Registration Test -->
            <div class="test-card p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-3">📝 Registration Test</h3>
                <div class="space-y-3">
                    <input type="text" id="reg-firstname" placeholder="First Name" class="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white">
                    <input type="text" id="reg-lastname" placeholder="Last Name" class="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white">
                    <input type="email" id="reg-email" placeholder="Email" class="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white">
                    <input type="password" id="reg-password" placeholder="Password" class="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white">
                    <button onclick="testRegistration()" class="cyber-button px-4 py-2 rounded text-sm w-full">Register</button>
                </div>
                <div class="mt-3 text-sm" id="registration-result">Ready to test registration</div>
            </div>

            <!-- Login Test -->
            <div class="test-card p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-3">🔑 Login Test</h3>
                <div class="space-y-3">
                    <input type="email" id="login-email" placeholder="Email" class="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white">
                    <input type="password" id="login-password" placeholder="Password" class="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white">
                    <button onclick="testLogin()" class="cyber-button px-4 py-2 rounded text-sm w-full">Login</button>
                </div>
                <div class="mt-3 text-sm" id="login-result">Ready to test login</div>
            </div>
        </div>

        <!-- Video Generation Test -->
        <div class="test-card p-6 rounded-lg mb-6">
            <h3 class="text-lg font-semibold mb-3">🎬 Video Generation Test</h3>
            <div class="grid md:grid-cols-3 gap-4 mb-4">
                <select id="test-mood" class="bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white">
                    <option value="euphoric">Euphoric</option>
                    <option value="serene">Serene</option>
                    <option value="dramatic">Dramatic</option>
                    <option value="mysterious">Mysterious</option>
                </select>
                <select id="test-topic" class="bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white">
                    <option value="nature_wildlife">Nature & Wildlife</option>
                    <option value="urban_city">Urban City</option>
                    <option value="abstract_art">Abstract Art</option>
                    <option value="technology">Technology</option>
                </select>
                <button onclick="testVideoGeneration()" class="cyber-button px-4 py-2 rounded text-sm">Generate Video</button>
            </div>
            <div id="video-generation-result">Ready to test video generation</div>
        </div>

        <!-- Quick Actions -->
        <div class="test-card p-6 rounded-lg mb-6">
            <h3 class="text-lg font-semibold mb-3">⚡ Quick Actions</h3>
            <div class="flex flex-wrap gap-2">
                <button onclick="checkAuthStatus()" class="cyber-button px-4 py-2 rounded text-sm">Check Auth Status</button>
                <button onclick="testLogout()" class="cyber-button px-4 py-2 rounded text-sm">Logout</button>
                <button onclick="clearSession()" class="cyber-button px-4 py-2 rounded text-sm">Clear Session</button>
                <button onclick="runFullAuthTest()" class="cyber-button px-4 py-2 rounded text-sm">Run Full Test</button>
            </div>
            <div class="mt-3 text-sm" id="quick-actions-result">Ready for quick actions</div>
        </div>

        <!-- Homepage Integration Test -->
        <div class="test-card p-6 rounded-lg">
            <h3 class="text-lg font-semibold mb-3">🏠 Homepage Integration Test</h3>
            <div class="flex gap-2 mb-4">
                <button onclick="testHomepageSignup()" class="cyber-button px-4 py-2 rounded text-sm">Test Homepage Signup</button>
                <button onclick="testHomepageLogin()" class="cyber-button px-4 py-2 rounded text-sm">Test Homepage Login</button>
                <a href="index.html" target="_blank" class="cyber-button px-4 py-2 rounded text-sm">Open Homepage</a>
            </div>
            <div id="homepage-integration-result">Ready to test homepage integration</div>
        </div>
    </div>

    <script>
        let currentUser = null;

        // Check auth status on page load
        window.addEventListener('load', () => {
            checkAuthStatus();
        });

        async function checkAuthStatus() {
            try {
                const response = await fetch('/api/auth/status');
                const data = await response.json();
                
                if (data.success) {
                    currentUser = data.data.user;
                    updateCurrentStatus(data.data);
                } else {
                    updateCurrentStatus({ authenticated: false, demo_mode: true });
                }
            } catch (error) {
                updateCurrentStatus({ error: error.message });
            }
        }

        function updateCurrentStatus(status) {
            const statusDiv = document.getElementById('current-status');
            
            if (status.error) {
                statusDiv.innerHTML = `❌ Error: ${status.error}`;
                return;
            }
            
            if (status.authenticated && status.user) {
                statusDiv.innerHTML = `
                    ✅ <strong>Authenticated User</strong><br>
                    👤 Name: ${status.user.first_name} ${status.user.last_name}<br>
                    📧 Email: ${status.user.email}<br>
                    💰 Credits: ${status.user.credits}<br>
                    📅 Member since: ${status.user.created_at}
                `;
            } else {
                statusDiv.innerHTML = `
                    🔓 <strong>Not Authenticated</strong><br>
                    🎭 Demo Mode: ${status.demo_mode ? 'Yes' : 'No'}
                `;
            }
        }

        async function testRegistration() {
            const firstName = document.getElementById('reg-firstname').value;
            const lastName = document.getElementById('reg-lastname').value;
            const email = document.getElementById('reg-email').value;
            const password = document.getElementById('reg-password').value;
            const result = document.getElementById('registration-result');
            
            if (!firstName || !lastName || !email || !password) {
                result.innerHTML = '❌ Please fill all fields';
                return;
            }
            
            result.innerHTML = '⏳ Registering...';
            
            try {
                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        first_name: firstName,
                        last_name: lastName,
                        email: email,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = `✅ Registration successful!<br>User: ${data.data.user.first_name} ${data.data.user.last_name}<br>Credits: ${data.data.user.credits}`;
                    currentUser = data.data.user;
                    checkAuthStatus();
                } else {
                    result.innerHTML = `❌ Registration failed: ${data.error}`;
                }
            } catch (error) {
                result.innerHTML = `❌ Request failed: ${error.message}`;
            }
        }

        async function testLogin() {
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;
            const result = document.getElementById('login-result');
            
            if (!email || !password) {
                result.innerHTML = '❌ Please enter email and password';
                return;
            }
            
            result.innerHTML = '⏳ Logging in...';
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = `✅ Login successful!<br>Welcome back, ${data.data.user.first_name}!<br>Credits: ${data.data.user.credits}`;
                    currentUser = data.data.user;
                    checkAuthStatus();
                } else {
                    result.innerHTML = `❌ Login failed: ${data.error}`;
                }
            } catch (error) {
                result.innerHTML = `❌ Request failed: ${error.message}`;
            }
        }

        async function testVideoGeneration() {
            const mood = document.getElementById('test-mood').value;
            const topic = document.getElementById('test-topic').value;
            const result = document.getElementById('video-generation-result');
            
            result.innerHTML = '⏳ Starting video generation...';
            
            try {
                const response = await fetch('/api/generate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        mood: mood,
                        topic: topic,
                        custom_prompt: `Create a ${mood} video about ${topic}`
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = `✅ Generation started!<br>Job ID: ${data.data.job_id}<br>Type: ${data.data.generation_type}<br>ETA: ${data.data.estimated_time}s`;
                    
                    // Track progress
                    trackVideoProgress(data.data.job_id, result);
                } else {
                    result.innerHTML = `❌ Generation failed: ${data.error}`;
                }
            } catch (error) {
                result.innerHTML = `❌ Request failed: ${error.message}`;
            }
        }

        async function trackVideoProgress(jobId, resultDiv) {
            const checkProgress = async () => {
                try {
                    const response = await fetch(`/api/generate/status/${jobId}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        const progress = data.data.progress || 0;
                        const status = data.data.status;
                        
                        resultDiv.innerHTML = `📊 Progress: ${Math.round(progress)}%<br>Status: ${status}<br>Message: ${data.data.message}`;
                        
                        if (status === 'completed') {
                            resultDiv.innerHTML += `<br>✅ <strong>Video Ready!</strong><br>📹 <a href="${data.data.video_url}" target="_blank">View Video</a>`;
                            checkAuthStatus(); // Update credits
                        } else if (progress < 100) {
                            setTimeout(checkProgress, 2000);
                        }
                    }
                } catch (error) {
                    resultDiv.innerHTML += `<br>❌ Progress check failed: ${error.message}`;
                }
            };
            
            checkProgress();
        }

        async function testLogout() {
            const result = document.getElementById('quick-actions-result');
            result.innerHTML = '⏳ Logging out...';
            
            try {
                const response = await fetch('/api/auth/logout', { method: 'POST' });
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = '✅ Logged out successfully';
                    currentUser = null;
                    checkAuthStatus();
                } else {
                    result.innerHTML = `❌ Logout failed: ${data.error}`;
                }
            } catch (error) {
                result.innerHTML = `❌ Request failed: ${error.message}`;
            }
        }

        function clearSession() {
            // Clear form fields
            document.getElementById('reg-firstname').value = '';
            document.getElementById('reg-lastname').value = '';
            document.getElementById('reg-email').value = '';
            document.getElementById('reg-password').value = '';
            document.getElementById('login-email').value = '';
            document.getElementById('login-password').value = '';
            
            // Clear results
            document.getElementById('registration-result').innerHTML = 'Ready to test registration';
            document.getElementById('login-result').innerHTML = 'Ready to test login';
            document.getElementById('video-generation-result').innerHTML = 'Ready to test video generation';
            document.getElementById('quick-actions-result').innerHTML = 'Session cleared';
            
            checkAuthStatus();
        }

        async function runFullAuthTest() {
            const result = document.getElementById('quick-actions-result');
            result.innerHTML = '🧪 Running full authentication test...';
            
            // Fill test data
            document.getElementById('reg-firstname').value = 'Test';
            document.getElementById('reg-lastname').value = 'User';
            document.getElementById('reg-email').value = `test${Date.now()}@example.com`;
            document.getElementById('reg-password').value = 'TestPass123!';
            
            // Test registration
            await testRegistration();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Test video generation
            await testVideoGeneration();
            
            result.innerHTML = '✅ Full test completed! Check individual results above.';
        }

        function testHomepageSignup() {
            const result = document.getElementById('homepage-integration-result');
            result.innerHTML = `
                📝 <strong>Homepage Signup Test:</strong><br>
                1. Open the homepage in a new tab<br>
                2. Click "Get Started" or "Sign Up"<br>
                3. Fill the registration form<br>
                4. Submit and check for success message<br>
                <br>
                ✅ The signup should now work without "server not connected" error!
            `;
        }

        function testHomepageLogin() {
            const result = document.getElementById('homepage-integration-result');
            result.innerHTML = `
                🔑 <strong>Homepage Login Test:</strong><br>
                1. Open the homepage in a new tab<br>
                2. Click "Sign In"<br>
                3. Use credentials from registration test<br>
                4. Submit and check for success message<br>
                <br>
                ✅ The login should now work without "server not connected" error!
            `;
        }
    </script>
</body>
</html>
