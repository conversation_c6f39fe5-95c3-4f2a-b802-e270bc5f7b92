<?php
/**
 * Template Engine - Manages reusable content templates and randomization
 * Part of Sutradhar Engine
 */

class TemplateEngine {
    private $config;
    private $templatesDir;
    private $templates;

    public function __construct() {
        $this->loadConfig();
        $this->loadTemplates();
    }

    private function loadConfig() {
        $configPath = __DIR__ . '/../config/settings.json';
        $this->config = json_decode(file_get_contents($configPath), true);
        $this->templatesDir = __DIR__ . '/../flows/';
    }

    private function loadTemplates() {
        $this->templates = [
            'panchtantra_wisdom' => [
                'name' => 'Panchtantra Wisdom',
                'description' => 'Moral stories with traditional wisdom',
                'flow_type' => 'reel',
                'content' => $this->getPanchtantraTemplate()
            ],
            'bollywood_startup' => [
                'name' => 'Bollywood Startup Pitch',
                'description' => 'Business advice with movie references',
                'flow_type' => 'meme_rant',
                'content' => $this->getBollywoodStartupTemplate()
            ],
            'dadi_advice' => [
                'name' => "Dadi's Life Advice",
                'description' => 'Traditional wisdom from grandmother',
                'flow_type' => 'audio_story',
                'content' => $this->getDadiAdviceTemplate()
            ]
        ];
    }

    /**
     * Get list of available templates
     */
    public function getTemplateList() {
        $list = [];
        foreach ($this->templates as $id => $template) {
            $list[] = [
                'id' => $id,
                'name' => $template['name'],
                'description' => $template['description'],
                'flow_type' => $template['flow_type']
            ];
        }
        return $list;
    }

    /**
     * Get template content by ID
     */
    public function getTemplate($templateId) {
        if (!isset($this->templates[$templateId])) {
            throw new Exception("Template not found: {$templateId}");
        }

        $template = $this->templates[$templateId];
        
        // Randomize content if it has variations
        if (isset($template['content']['variations'])) {
            $template['content'] = $this->randomizeContent($template['content']);
        }

        return $template;
    }

    /**
     * Randomize template content
     */
    private function randomizeContent($content) {
        if (isset($content['variations'])) {
            $variations = $content['variations'];
            $randomVariation = $variations[array_rand($variations)];
            
            // Merge with base content
            $content = array_merge($content, $randomVariation);
            unset($content['variations']);
        }

        // Replace placeholders with random values
        if (isset($content['text'])) {
            $content['text'] = $this->replacePlaceholders($content['text']);
        }

        return $content;
    }

    /**
     * Replace placeholders in text with random values
     */
    private function replacePlaceholders($text) {
        $placeholders = [
            '{name}' => $this->getRandomName(),
            '{city}' => $this->getRandomCity(),
            '{number}' => rand(1, 100),
            '{emotion}' => $this->getRandomEmotion(),
            '{food}' => $this->getRandomFood(),
            '{bollywood_actor}' => $this->getRandomBollywoodActor(),
            '{startup_term}' => $this->getRandomStartupTerm()
        ];

        foreach ($placeholders as $placeholder => $value) {
            $text = str_replace($placeholder, $value, $text);
        }

        return $text;
    }

    /**
     * Get Panchtantra template
     */
    private function getPanchtantraTemplate() {
        return [
            'variations' => [
                [
                    'title' => 'The Wise Crow',
                    'text' => 'Once upon a time, there was a clever crow who found a piece of cheese. A fox saw this and said, "Oh beautiful crow, your voice must be as lovely as your feathers!" The crow, feeling proud, opened his beak to sing and dropped the cheese. The fox grabbed it and said, "Thank you for the lesson - never trust flattery!" Moral: Don\'t let praise make you forget your common sense, just like how we shouldn\'t fall for sweet words in real life.'
                ],
                [
                    'title' => 'The Ant and Grasshopper',
                    'text' => 'In summer, an ant worked hard collecting food while a grasshopper just sang and danced. "Why work so hard?" asked the grasshopper. "Winter is coming," replied the ant. When winter arrived, the grasshopper had no food and begged the ant for help. The ant said, "You should have prepared when you had time." Moral: Hard work and planning today saves you from trouble tomorrow - just like saving money for emergencies!'
                ],
                [
                    'title' => 'The Tortoise and Hare',
                    'text' => 'A fast hare made fun of a slow tortoise and challenged him to a race. The hare ran quickly and got so far ahead that he decided to take a nap. Meanwhile, the tortoise kept moving slowly but steadily. When the hare woke up, he saw the tortoise crossing the finish line! Moral: Slow and steady wins the race - consistency beats talent when talent doesn\'t work hard.'
                ]
            ]
        ];
    }

    /**
     * Get Bollywood Startup template
     */
    private function getBollywoodStartupTemplate() {
        return [
            'variations' => [
                [
                    'title' => 'Startup Pitch Bollywood Style',
                    'text' => 'Arre bhai, listen to my startup idea! Just like {bollywood_actor} said in that movie - "Sapne dekhna zaroori hai!" My app will solve all problems, from ordering chai to finding your soulmate. We need just {number} crore funding, and we\'ll become the next unicorn! Our {startup_term} will disrupt the entire market. Trust me, this is going to be bigger than Jio! What say, ready to invest?'
                ],
                [
                    'title' => 'Business Advice Filmy Style',
                    'text' => 'Yaar, starting a business is like a Bollywood movie. First act - you have a dream and everyone says "pagal hai kya?" Second act - struggles, failures, and people doubting you. Third act - either you become {bollywood_actor} or you learn that real success is the friends you made along the way. But seriously, {startup_term} is the future, and if you\'re not on this train, you\'ll be left behind like those villains in old movies!'
                ]
            ]
        ];
    }

    /**
     * Get Dadi advice template
     */
    private function getDadiAdviceTemplate() {
        return [
            'variations' => [
                [
                    'title' => "Dadi's Kitchen Wisdom",
                    'text' => 'Beta, let me tell you something I learned from my mother and her mother before that. Life is like making {food} - you need patience, the right ingredients, and lots of love. You can\'t rush good things. These days, everyone wants everything instant - instant noodles, instant success, instant happiness. But the best things in life take time to develop, just like how we let the dough rest before making rotis. Remember, beta, shortcuts might save time, but they rarely give you the satisfaction of doing things the right way.'
                ],
                [
                    'title' => "Dadi's Life Lessons",
                    'text' => 'When I was your age in {city}, we didn\'t have all these gadgets and apps. But we had something more valuable - time for each other. We would sit together, share stories, and learn from our elders. Today\'s generation is always busy, always running. But beta, remember - relationships are like plants. You need to water them with time and attention, or they will wither away. Don\'t let technology replace human connection. The warmth of a hug cannot be sent through WhatsApp!'
                ]
            ]
        ];
    }

    /**
     * Random value generators
     */
    private function getRandomName() {
        $names = ['Rahul', 'Priya', 'Amit', 'Sneha', 'Vikram', 'Anjali', 'Rohan', 'Kavya'];
        return $names[array_rand($names)];
    }

    private function getRandomCity() {
        $cities = ['Mumbai', 'Delhi', 'Bangalore', 'Chennai', 'Kolkata', 'Pune', 'Hyderabad', 'Ahmedabad'];
        return $cities[array_rand($cities)];
    }

    private function getRandomEmotion() {
        $emotions = ['excited', 'nervous', 'confident', 'worried', 'hopeful', 'determined'];
        return $emotions[array_rand($emotions)];
    }

    private function getRandomFood() {
        $foods = ['biryani', 'dal chawal', 'rajma', 'chole bhature', 'dosa', 'paratha', 'khichdi'];
        return $foods[array_rand($foods)];
    }

    private function getRandomBollywoodActor() {
        $actors = ['Shah Rukh Khan', 'Amitabh Bachchan', 'Salman Khan', 'Aamir Khan', 'Akshay Kumar', 'Hrithik Roshan'];
        return $actors[array_rand($actors)];
    }

    private function getRandomStartupTerm() {
        $terms = ['AI-powered solution', 'blockchain technology', 'machine learning algorithm', 'IoT platform', 'cloud-based system', 'data analytics'];
        return $terms[array_rand($terms)];
    }

    /**
     * Create custom template from user content
     */
    public function createCustomTemplate($content, $flowType, $style) {
        $template = [
            'name' => 'Custom Template',
            'description' => 'User-generated content',
            'flow_type' => $flowType,
            'content' => [
                'title' => 'Custom Content',
                'text' => $content,
                'style' => $style
            ]
        ];

        return $template;
    }

    /**
     * Add cultural references to content
     */
    public function addCulturalReferences($text, $style) {
        $references = [
            'funny' => [
                'Hera Pheri references',
                'Munna Bhai dialogues',
                'Typical Indian family situations'
            ],
            'desi' => [
                'Traditional sayings',
                'Regional expressions',
                'Cultural practices'
            ],
            'emotional' => [
                'Family values',
                'Emotional Bollywood scenes',
                'Life lessons from elders'
            ],
            'bollywood' => [
                'Movie dialogues',
                'Actor references',
                'Dramatic expressions'
            ]
        ];

        $styleReferences = $references[$style] ?? [];
        
        if (!empty($styleReferences)) {
            $randomRef = $styleReferences[array_rand($styleReferences)];
            $text .= " (Just like in {$randomRef}!)";
        }

        return $text;
    }

    /**
     * Handle API requests for templates
     */
    public function handleRequest() {
        $action = $_GET['action'] ?? '';
        
        switch ($action) {
            case 'list':
                header('Content-Type: application/json');
                echo json_encode($this->getTemplateList());
                break;
                
            case 'get':
                $templateId = $_GET['id'] ?? '';
                if ($templateId) {
                    try {
                        $template = $this->getTemplate($templateId);
                        header('Content-Type: application/json');
                        echo json_encode($template);
                    } catch (Exception $e) {
                        http_response_code(404);
                        echo json_encode(['error' => $e->getMessage()]);
                    }
                } else {
                    http_response_code(400);
                    echo json_encode(['error' => 'Template ID required']);
                }
                break;
                
            default:
                http_response_code(400);
                echo json_encode(['error' => 'Invalid action']);
        }
    }
}

// Handle direct requests to this file
if (basename($_SERVER['PHP_SELF']) === 'template_engine.php') {
    $engine = new TemplateEngine();
    $engine->handleRequest();
}
