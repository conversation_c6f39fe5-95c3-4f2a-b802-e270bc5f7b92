<?php
/**
 * Legacy Generator Endpoint for Sutradhar 2070
 * Redirects old requests to new unified API system
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Get request data
$input = file_get_contents('php://input');
$data = json_decode($input, true) ?: $_POST;
$action = $data['action'] ?? $_GET['action'] ?? '';

// Route to appropriate new API endpoint
switch ($action) {
    case 'generate_video':
        // Redirect to new generation API
        $newData = [
            'mood' => $data['mood'] ?? 'euphoric',
            'topic' => $data['topic'] ?? 'nature_wildlife',
            'inspiration' => $data['inspiration'] ?? [],
            'video_quality' => $data['quality'] ?? 'standard',
            'video_length' => intval($data['length'] ?? 30)
        ];
        
        // Forward to unified API
        $response = forwardToAPI('generate', 'POST', $newData);
        break;
        
    case 'get_status':
        $jobId = $data['job_id'] ?? $_GET['job_id'] ?? '';
        if ($jobId) {
            $response = forwardToAPI("generate/status/$jobId", 'GET');
        } else {
            $response = ['success' => false, 'error' => 'Job ID required'];
        }
        break;
        
    case 'get_history':
        $response = forwardToAPI('generate/history', 'GET');
        break;
        
    default:
        $response = ['success' => false, 'error' => 'Invalid action'];
}

echo json_encode($response);

/**
 * Forward request to unified API
 */
function forwardToAPI($endpoint, $method = 'GET', $data = null) {
    $url = 'http://localhost' . $_SERVER['REQUEST_URI'];
    $url = str_replace('/generator.php', '/api_unified.php', $url);
    $url .= (strpos($url, '?') !== false ? '&' : '?') . 'endpoint=' . urlencode($endpoint);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    // Forward session cookies
    if (isset($_SERVER['HTTP_COOKIE'])) {
        curl_setopt($ch, CURLOPT_COOKIE, $_SERVER['HTTP_COOKIE']);
    }
    
    if ($data && ($method === 'POST' || $method === 'PUT')) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($response === false) {
        return ['success' => false, 'error' => 'API request failed'];
    }
    
    $decoded = json_decode($response, true);
    return $decoded ?: ['success' => false, 'error' => 'Invalid API response'];
}
?>
