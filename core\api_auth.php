<?php
/**
 * Authentication API for Sutradhar 2070
 * Handles user registration, login, logout, and email verification
 */

require_once 'api_base.php';

class AuthAPI extends APIBase {
    
    public function handleRequest() {
        $path = $this->requestPath;
        $method = $this->requestMethod;
        
        switch ($path) {
            case 'auth/register':
                if ($method === 'POST') $this->register();
                break;
            case 'auth/login':
                if ($method === 'POST') $this->login();
                break;
            case 'auth/logout':
                if ($method === 'POST') $this->logout();
                break;
            case 'auth/status':
                if ($method === 'GET') $this->getStatus();
                break;
            case 'auth/verify-email':
                if ($method === 'POST') $this->verifyEmail();
                break;
            case 'auth/forgot-password':
                if ($method === 'POST') $this->forgotPassword();
                break;
            case 'auth/reset-password':
                if ($method === 'POST') $this->resetPassword();
                break;
            case 'auth/change-password':
                if ($method === 'POST') $this->changePassword();
                break;
            default:
                $this->sendError('Endpoint not found', 404);
        }
    }
    
    /**
     * User registration
     */
    private function register() {
        $this->checkRateLimit('register_' . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'), 5, 3600);
        
        $this->validateRequired(['email', 'password', 'first_name', 'last_name']);
        
        $email = strtolower(trim($this->requestData['email']));
        $password = $this->requestData['password'];
        $firstName = trim($this->requestData['first_name']);
        $lastName = trim($this->requestData['last_name']);
        
        // Validate input
        $this->validateEmail($email);
        $this->validatePassword($password);
        
        if (strlen($firstName) < 2 || strlen($lastName) < 2) {
            $this->sendError('First name and last name must be at least 2 characters long', 400);
        }
        
        // Check if user already exists
        $existingUser = $this->db->selectOne('users', ['email' => $email]);
        if ($existingUser) {
            $this->sendError('An account with this email already exists', 409);
        }
        
        // Create user
        $userId = 'user_' . uniqid() . '_' . time();
        $passwordHash = $this->hashPassword($password);
        $verificationToken = $this->generateToken();
        
        $userData = [
            'user_id' => $userId,
            'email' => $email,
            'password_hash' => $passwordHash,
            'first_name' => $firstName,
            'last_name' => $lastName,
            'email_verification_token' => $verificationToken,
            'status' => 'active'
        ];
        
        try {
            $this->db->beginTransaction();
            
            // Insert user
            $this->db->insert('users', $userData);
            
            // Create user profile
            $this->db->insert('user_profiles', [
                'user_id' => $userId,
                'terms_accepted_at' => date('Y-m-d H:i:s'),
                'privacy_accepted_at' => date('Y-m-d H:i:s')
            ]);
            
            // Initialize credit balance
            $this->db->insert('user_credit_balances', [
                'user_id' => $userId,
                'total_credits' => 50, // Free tier credits
                'used_credits' => 0
            ]);
            
            // Create initial credit transaction
            $this->db->insert('credit_transactions', [
                'transaction_id' => 'txn_' . uniqid(),
                'user_id' => $userId,
                'type' => 'bonus',
                'credits_amount' => 50,
                'description' => 'Welcome bonus credits',
                'reference_type' => 'bonus'
            ]);
            
            // Create free subscription
            $this->db->insert('user_subscriptions', [
                'subscription_id' => 'sub_' . uniqid(),
                'user_id' => $userId,
                'plan_id' => 'free',
                'status' => 'active',
                'billing_cycle' => 'monthly',
                'amount' => 0.00,
                'current_period_start' => date('Y-m-d H:i:s'),
                'current_period_end' => date('Y-m-d H:i:s', strtotime('+1 month'))
            ]);
            
            $this->db->commit();
            
            // Send verification email
            $this->sendVerificationEmail($email, $firstName, $verificationToken);
            
            $this->sendSuccess([
                'user_id' => $userId,
                'message' => 'Account created successfully. Please check your email to verify your account.'
            ], 'Registration successful', 201);
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Registration failed: " . $e->getMessage());
            $this->sendError('Registration failed. Please try again.', 500);
        }
    }
    
    /**
     * User login
     */
    private function login() {
        $this->checkRateLimit('login_' . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'), 10, 900);
        
        $this->validateRequired(['email', 'password']);
        
        $email = strtolower(trim($this->requestData['email']));
        $password = $this->requestData['password'];
        
        $this->validateEmail($email);
        
        // Get user
        $user = $this->db->selectOne('users', ['email' => $email]);
        
        if (!$user) {
            $this->sendError('Invalid email or password', 401);
        }
        
        // Check account status
        if ($user['status'] !== 'active') {
            $this->sendError('Account is not active. Please contact support.', 403);
        }
        
        // Check if account is locked
        if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
            $lockTime = date('Y-m-d H:i:s', strtotime($user['locked_until']));
            $this->sendError("Account is temporarily locked until $lockTime due to multiple failed login attempts", 423);
        }
        
        // Verify password
        if (!$this->verifyPassword($password, $user['password_hash'])) {
            $this->handleFailedLogin($user['user_id']);
            $this->sendError('Invalid email or password', 401);
        }
        
        // Reset login attempts on successful login
        $this->db->update('users', [
            'login_attempts' => 0,
            'locked_until' => null,
            'last_login_at' => date('Y-m-d H:i:s')
        ], ['user_id' => $user['user_id']]);
        
        // Create session
        $sessionId = $this->createSession($user['user_id']);
        
        // Get user data with credits and subscription
        $userData = $this->getUserData($user['user_id']);
        
        $this->sendSuccess([
            'user' => $userData,
            'session_id' => $sessionId
        ], 'Login successful');
    }
    
    /**
     * Handle failed login attempt
     */
    private function handleFailedLogin($userId) {
        $user = $this->db->selectOne('users', ['user_id' => $userId]);
        $attempts = $user['login_attempts'] + 1;
        
        $updateData = ['login_attempts' => $attempts];
        
        // Lock account after 5 failed attempts
        if ($attempts >= 5) {
            $updateData['locked_until'] = date('Y-m-d H:i:s', time() + 1800); // 30 minutes
        }
        
        $this->db->update('users', $updateData, ['user_id' => $userId]);
    }
    
    /**
     * User logout
     */
    private function logout() {
        $this->clearSession();
        $this->sendSuccess(null, 'Logged out successfully');
    }
    
    /**
     * Get authentication status
     */
    private function getStatus() {
        if ($this->currentUser) {
            $userData = $this->getUserData($this->currentUser['user_id']);
            $this->sendSuccess(['user' => $userData]);
        } else {
            $this->sendSuccess(['user' => null]);
        }
    }
    
    /**
     * Verify email address
     */
    private function verifyEmail() {
        $this->validateRequired(['token']);
        
        $token = $this->requestData['token'];
        
        $user = $this->db->selectOne('users', [
            'email_verification_token' => $token,
            'email_verified' => 0
        ]);
        
        if (!$user) {
            $this->sendError('Invalid or expired verification token', 400);
        }
        
        // Update user as verified
        $this->db->update('users', [
            'email_verified' => 1,
            'email_verified_at' => date('Y-m-d H:i:s'),
            'email_verification_token' => null
        ], ['user_id' => $user['user_id']]);
        
        $this->sendSuccess(null, 'Email verified successfully');
    }
    
    /**
     * Forgot password
     */
    private function forgotPassword() {
        $this->checkRateLimit('forgot_password_' . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'), 3, 3600);
        
        $this->validateRequired(['email']);
        
        $email = strtolower(trim($this->requestData['email']));
        $this->validateEmail($email);
        
        $user = $this->db->selectOne('users', ['email' => $email, 'status' => 'active']);
        
        if ($user) {
            $resetToken = $this->generateToken();
            $expiresAt = date('Y-m-d H:i:s', time() + 3600); // 1 hour
            
            $this->db->update('users', [
                'password_reset_token' => $resetToken,
                'password_reset_expires' => $expiresAt
            ], ['user_id' => $user['user_id']]);
            
            $this->sendPasswordResetEmail($email, $user['first_name'], $resetToken);
        }
        
        // Always return success to prevent email enumeration
        $this->sendSuccess(null, 'If an account with that email exists, a password reset link has been sent.');
    }
    
    /**
     * Reset password
     */
    private function resetPassword() {
        $this->validateRequired(['token', 'password']);
        
        $token = $this->requestData['token'];
        $password = $this->requestData['password'];
        
        $this->validatePassword($password);
        
        $user = $this->db->selectOne('users', [
            'password_reset_token' => $token
        ]);
        
        if (!$user || !$user['password_reset_expires'] || 
            strtotime($user['password_reset_expires']) < time()) {
            $this->sendError('Invalid or expired reset token', 400);
        }
        
        $passwordHash = $this->hashPassword($password);
        
        $this->db->update('users', [
            'password_hash' => $passwordHash,
            'password_reset_token' => null,
            'password_reset_expires' => null,
            'login_attempts' => 0,
            'locked_until' => null
        ], ['user_id' => $user['user_id']]);
        
        $this->sendSuccess(null, 'Password reset successfully');
    }
    
    /**
     * Change password (authenticated)
     */
    private function changePassword() {
        $this->requireAuth();
        
        $this->validateRequired(['current_password', 'new_password']);
        
        $currentPassword = $this->requestData['current_password'];
        $newPassword = $this->requestData['new_password'];
        
        $this->validatePassword($newPassword);
        
        if (!$this->verifyPassword($currentPassword, $this->currentUser['password_hash'])) {
            $this->sendError('Current password is incorrect', 400);
        }
        
        $passwordHash = $this->hashPassword($newPassword);
        
        $this->db->update('users', [
            'password_hash' => $passwordHash
        ], ['user_id' => $this->currentUser['user_id']]);
        
        $this->sendSuccess(null, 'Password changed successfully');
    }
    
    /**
     * Get complete user data
     */
    private function getUserData($userId) {
        $user = $this->db->selectOne('users', ['user_id' => $userId]);
        $profile = $this->db->selectOne('user_profiles', ['user_id' => $userId]);
        $credits = $this->db->selectOne('user_credit_balances', ['user_id' => $userId]);
        $subscription = $this->db->selectOne('user_subscriptions', [
            'user_id' => $userId,
            'status' => 'active'
        ]);
        
        // Remove sensitive data
        unset($user['password_hash'], $user['email_verification_token'], 
              $user['password_reset_token'], $user['password_reset_expires']);
        
        return [
            'user_id' => $user['user_id'],
            'email' => $user['email'],
            'first_name' => $user['first_name'],
            'last_name' => $user['last_name'],
            'avatar_url' => $user['avatar_url'],
            'email_verified' => (bool)$user['email_verified'],
            'status' => $user['status'],
            'credits' => $credits['available_credits'] ?? 0,
            'total_credits' => $credits['total_credits'] ?? 0,
            'used_credits' => $credits['used_credits'] ?? 0,
            'subscription' => $subscription ? [
                'plan_id' => $subscription['plan_id'],
                'status' => $subscription['status'],
                'current_period_end' => $subscription['current_period_end']
            ] : null,
            'profile' => $profile,
            'last_login_at' => $user['last_login_at'],
            'created_at' => $user['created_at']
        ];
    }
    
    /**
     * Send verification email
     */
    private function sendVerificationEmail($email, $firstName, $token) {
        // In a real implementation, you would use an email service
        // For now, we'll just log it
        $verificationLink = "https://sutradhar2070.com/verify-email?token=$token";
        error_log("Verification email for $email: $verificationLink");
    }
    
    /**
     * Send password reset email
     */
    private function sendPasswordResetEmail($email, $firstName, $token) {
        // In a real implementation, you would use an email service
        // For now, we'll just log it
        $resetLink = "https://sutradhar2070.com/reset-password?token=$token";
        error_log("Password reset email for $email: $resetLink");
    }
}
?>
