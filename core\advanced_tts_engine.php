<?php
/**
 * Advanced TTS Engine - Superior Cross-Platform Text-to-Speech
 * Replaces Windows SAPI with natural-sounding speech, proper intonation, and emotional expression
 */

class AdvancedTTSEngine {
    private $config;
    private $tempDir;
    private $engines = ['coqui', 'custom_neural', 'festival', 'windows_sapi'];
    private $voiceProfiles;
    private $logFile;
    
    public function __construct() {
        $this->tempDir = __DIR__ . '/../temp/';
        $this->logFile = __DIR__ . '/../logs/advanced_tts.log';
        $this->loadConfig();
        $this->initializeVoiceProfiles();
        $this->ensureDirectories();
        $this->detectAvailableEngines();
    }
    
    private function loadConfig() {
        $this->config = [
            'quality' => [
                'sample_rate' => 44100,
                'bit_depth' => 16,
                'mp3_bitrate' => 320,
                'target_file_size' => [800, 1200] // KB range
            ],
            'prosody' => [
                'sentence_pause' => 500, // ms
                'comma_pause' => 200,    // ms
                'breath_interval' => 12, // words
                'emphasis_boost' => 1.2  // volume multiplier
            ],
            'engines' => [
                'coqui' => [
                    'model' => 'tts_models/en/ljspeech/tacotron2-DDC',
                    'alternative_model' => 'tts_models/en/vctk/vits',
                    'quality' => 'high'
                ],
                'custom_neural' => [
                    'formant_frequencies' => [800, 1200, 2400, 3600],
                    'base_frequency' => 220, // Hz for female voice
                    'quality' => 'medium'
                ],
                'festival' => [
                    'voice' => 'voice_kal_diphone',
                    'quality' => 'medium'
                ],
                'windows_sapi' => [
                    'voice' => 'default',
                    'quality' => 'basic'
                ]
            ]
        ];
    }
    
    private function initializeVoiceProfiles() {
        $this->voiceProfiles = [
            'female_natural' => [
                'pitch_base' => 220,
                'pitch_range' => 50,
                'speed' => 1.0,
                'emotion' => 'neutral',
                'description' => 'Warm, clear, documentary-style'
            ],
            'female_energetic' => [
                'pitch_base' => 250,
                'pitch_range' => 80,
                'speed' => 1.2,
                'emotion' => 'excited',
                'description' => 'Higher pitch, faster pace for exciting content'
            ],
            'female_calm' => [
                'pitch_base' => 200,
                'pitch_range' => 30,
                'speed' => 0.9,
                'emotion' => 'peaceful',
                'description' => 'Lower pitch, slower pace for peaceful content'
            ],
            'male_narrator' => [
                'pitch_base' => 120,
                'pitch_range' => 40,
                'speed' => 0.95,
                'emotion' => 'authoritative',
                'description' => 'Deep, authoritative voice for educational content'
            ]
        ];
    }
    
    private function ensureDirectories() {
        $dirs = [$this->tempDir, dirname($this->logFile)];
        foreach ($dirs as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }
    
    private function detectAvailableEngines() {
        $this->log("🔍 Detecting available TTS engines...");
        
        $available = [];
        
        // Check Coqui TTS
        exec('python -c "import TTS" 2>&1', $output, $returnCode);
        if ($returnCode === 0) {
            $available[] = 'coqui';
            $this->log("✅ Coqui TTS detected");
        } else {
            $this->log("❌ Coqui TTS not available - attempting installation...");
            $this->installCoquiTTS();
        }
        
        // Check Festival
        exec('festival --version 2>&1', $output, $returnCode);
        if ($returnCode === 0) {
            $available[] = 'festival';
            $this->log("✅ Festival TTS detected");
        }
        
        // Custom Neural is always available (PHP implementation)
        $available[] = 'custom_neural';
        $this->log("✅ Custom Neural TTS available");
        
        // Windows SAPI (fallback)
        if (PHP_OS_FAMILY === 'Windows') {
            $available[] = 'windows_sapi';
            $this->log("✅ Windows SAPI available (fallback)");
        }
        
        $this->engines = array_intersect($this->engines, $available);
        $this->log("🎤 Available engines: " . implode(', ', $this->engines));
    }
    
    private function installCoquiTTS() {
        $this->log("📦 Installing Coqui TTS...");
        
        // Check if pip is available
        exec('pip --version 2>&1', $output, $returnCode);
        if ($returnCode !== 0) {
            $this->log("❌ pip not available - cannot install Coqui TTS");
            return false;
        }
        
        // Install Coqui TTS
        exec('pip install coqui-tts 2>&1', $output, $returnCode);
        if ($returnCode === 0) {
            $this->log("✅ Coqui TTS installed successfully");
            
            // Verify installation
            exec('python -c "import TTS; print(TTS.__version__)" 2>&1', $output, $returnCode);
            if ($returnCode === 0) {
                $this->engines = array_merge(['coqui'], $this->engines);
                return true;
            }
        }
        
        $this->log("❌ Coqui TTS installation failed");
        return false;
    }
    
    /**
     * Main TTS generation method with fallback system
     */
    public function generateSpeech($text, $voiceProfile = 'female_natural', $outputFile = null) {
        if (!$outputFile) {
            $outputFile = $this->tempDir . 'advanced_tts_' . uniqid() . '.wav';
        }
        
        $this->log("🎤 Generating speech: " . substr($text, 0, 100) . "...");
        $this->log("🎭 Voice profile: $voiceProfile");
        
        // Analyze content for context-aware adaptation
        $contentContext = $this->analyzeContent($text);
        $adaptedProfile = $this->adaptVoiceProfile($voiceProfile, $contentContext);
        
        // Enhance text with prosody
        $enhancedText = $this->enhanceTextWithProsody($text, $contentContext);
        
        // Try each engine in priority order
        foreach ($this->engines as $engine) {
            try {
                $this->log("🔧 Trying engine: $engine");
                $result = $this->tryEngine($engine, $enhancedText, $adaptedProfile, $outputFile);
                
                if ($this->validateAudio($result)) {
                    $this->log("✅ Success with $engine: " . round(filesize($result)/1024, 1) . " KB");
                    
                    // Post-process for quality enhancement
                    $finalAudio = $this->postProcessAudio($result, $contentContext);
                    return $finalAudio;
                }
            } catch (Exception $e) {
                $this->log("❌ Engine $engine failed: " . $e->getMessage());
                continue;
            }
        }
        
        throw new Exception("All TTS engines failed to generate speech");
    }
    
    /**
     * Analyze content for context-aware voice adaptation
     */
    private function analyzeContent($text) {
        $context = [
            'theme' => 'general',
            'emotion' => 'neutral',
            'pace' => 'normal',
            'keywords' => []
        ];
        
        $text_lower = strtolower($text);
        
        // Theme detection
        if (strpos($text_lower, 'elephant') !== false || strpos($text_lower, 'jungle') !== false || 
            strpos($text_lower, 'forest') !== false || strpos($text_lower, 'nature') !== false) {
            $context['theme'] = 'nature_documentary';
            $context['emotion'] = 'calm';
            $context['pace'] = 'slow';
        } elseif (strpos($text_lower, 'exciting') !== false || strpos($text_lower, 'amazing') !== false ||
                  strpos($text_lower, 'incredible') !== false) {
            $context['emotion'] = 'excited';
            $context['pace'] = 'fast';
        } elseif (strpos($text_lower, 'peaceful') !== false || strpos($text_lower, 'calm') !== false ||
                  strpos($text_lower, 'gentle') !== false) {
            $context['emotion'] = 'peaceful';
            $context['pace'] = 'slow';
        }
        
        // Extract keywords for emphasis
        $emphasisWords = ['magnificent', 'beautiful', 'amazing', 'incredible', 'stunning', 'majestic'];
        foreach ($emphasisWords as $word) {
            if (strpos($text_lower, $word) !== false) {
                $context['keywords'][] = $word;
            }
        }
        
        return $context;
    }
    
    /**
     * Adapt voice profile based on content context
     */
    private function adaptVoiceProfile($baseProfile, $context) {
        $profile = $this->voiceProfiles[$baseProfile] ?? $this->voiceProfiles['female_natural'];
        
        // Adapt based on theme
        if ($context['theme'] === 'nature_documentary') {
            $profile['speed'] *= 0.9; // Slower for documentary style
            $profile['pitch_base'] -= 10; // Slightly lower pitch
        }
        
        // Adapt based on emotion
        switch ($context['emotion']) {
            case 'excited':
                $profile['speed'] *= 1.1;
                $profile['pitch_base'] += 20;
                $profile['pitch_range'] += 20;
                break;
            case 'peaceful':
                $profile['speed'] *= 0.85;
                $profile['pitch_base'] -= 15;
                $profile['pitch_range'] -= 10;
                break;
        }
        
        return $profile;
    }
    
    /**
     * Enhance text with prosody markup for natural speech
     */
    private function enhanceTextWithProsody($text, $context) {
        // Add pauses after sentences
        $text = preg_replace('/([.!?])\s+/', '$1<break time="500ms"/> ', $text);
        
        // Add pauses after commas
        $text = preg_replace('/,\s+/', ',<break time="200ms"/> ', $text);
        
        // Add emphasis to keywords
        foreach ($context['keywords'] as $keyword) {
            $text = preg_replace('/\b' . preg_quote($keyword, '/') . '\b/i', 
                               '<emphasis level="moderate">$0</emphasis>', $text);
        }
        
        // Add breathing pauses every 12-15 words
        $words = explode(' ', $text);
        $enhanced = [];
        $wordCount = 0;
        
        foreach ($words as $word) {
            $enhanced[] = $word;
            $wordCount++;
            
            if ($wordCount % 13 === 0 && $wordCount < count($words) - 2) {
                $enhanced[] = '<break time="300ms"/>';
            }
        }
        
        return implode(' ', $enhanced);
    }
    
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] $message\n";
        file_put_contents($this->logFile, $logMessage, FILE_APPEND | LOCK_EX);
        echo $logMessage;
    }
    
    private function validateAudio($audioFile) {
        if (!file_exists($audioFile)) {
            return false;
        }
        
        $fileSize = filesize($audioFile);
        if ($fileSize < 10000) { // Less than 10KB is probably invalid
            return false;
        }
        
        // Check if it's a valid audio file
        $handle = fopen($audioFile, 'rb');
        $header = fread($handle, 12);
        fclose($handle);
        
        return (strpos($header, 'RIFF') !== false && strpos($header, 'WAVE') !== false) ||
               (strpos($header, 'ID3') !== false) ||
               (ord($header[0]) == 0xFF && (ord($header[1]) & 0xE0) == 0xE0);
    }

    /**
     * Try specific TTS engine
     */
    private function tryEngine($engine, $text, $voiceProfile, $outputFile) {
        switch ($engine) {
            case 'coqui':
                return $this->generateWithCoqui($text, $voiceProfile, $outputFile);
            case 'custom_neural':
                return $this->generateWithCustomNeural($text, $voiceProfile, $outputFile);
            case 'festival':
                return $this->generateWithFestival($text, $voiceProfile, $outputFile);
            case 'windows_sapi':
                return $this->generateWithWindowsSAPI($text, $voiceProfile, $outputFile);
            default:
                throw new Exception("Unknown TTS engine: $engine");
        }
    }

    /**
     * Coqui TTS Implementation (Option A - Recommended)
     */
    private function generateWithCoqui($text, $voiceProfile, $outputFile) {
        $this->log("🤖 Using Coqui TTS with Tacotron2-DDC model");

        // Clean text for Coqui (remove SSML tags for now)
        $cleanText = $this->stripSSML($text);

        // Primary model
        $model = $this->config['engines']['coqui']['model'];
        $cmd = sprintf(
            'python -c "from TTS.api import TTS; tts = TTS(\'%s\'); tts.tts_to_file(text=\'%s\', file_path=\'%s\')" 2>&1',
            $model,
            addslashes($cleanText),
            $outputFile
        );

        exec($cmd, $output, $returnCode);

        if ($returnCode === 0 && file_exists($outputFile)) {
            return $outputFile;
        }

        // Try alternative model
        $altModel = $this->config['engines']['coqui']['alternative_model'];
        $this->log("🔄 Trying alternative Coqui model: $altModel");

        $cmd = sprintf(
            'python -c "from TTS.api import TTS; tts = TTS(\'%s\'); tts.tts_to_file(text=\'%s\', file_path=\'%s\')" 2>&1',
            $altModel,
            addslashes($cleanText),
            $outputFile
        );

        exec($cmd, $output, $returnCode);

        if ($returnCode === 0 && file_exists($outputFile)) {
            return $outputFile;
        }

        throw new Exception("Coqui TTS failed: " . implode("\n", $output));
    }

    /**
     * Custom Neural TTS Implementation (Option B)
     */
    private function generateWithCustomNeural($text, $voiceProfile, $outputFile) {
        $this->log("🧠 Using Custom Neural TTS with advanced prosody");

        // Parse SSML and generate prosody data
        $prosodyData = $this->parseSSMLAndGenerateProsody($text, $voiceProfile);

        // Generate audio segments with neural synthesis
        $audioSegments = $this->synthesizeWithNeuralProsody($prosodyData, $voiceProfile);

        // Combine segments into final audio
        return $this->combineAudioSegments($audioSegments, $outputFile);
    }

    /**
     * Festival TTS Implementation (Option C)
     */
    private function generateWithFestival($text, $voiceProfile, $outputFile) {
        $this->log("🎭 Using Festival TTS with custom voice training");

        // Clean text and prepare for Festival
        $cleanText = $this->stripSSML($text);
        $tempTextFile = $this->tempDir . 'festival_text_' . uniqid() . '.txt';
        file_put_contents($tempTextFile, $cleanText);

        // Generate with Festival
        $voice = $this->config['engines']['festival']['voice'];
        $cmd = sprintf(
            'echo "(%s)" | festival --tts --otype wav > "%s" 2>&1',
            addslashes($cleanText),
            $outputFile
        );

        exec($cmd, $output, $returnCode);

        // Clean up
        if (file_exists($tempTextFile)) {
            unlink($tempTextFile);
        }

        if (file_exists($outputFile) && filesize($outputFile) > 1000) {
            return $outputFile;
        }

        throw new Exception("Festival TTS failed: " . implode("\n", $output));
    }

    /**
     * Windows SAPI Implementation (Fallback)
     */
    private function generateWithWindowsSAPI($text, $voiceProfile, $outputFile) {
        $this->log("🪟 Using Windows SAPI (fallback)");

        // Use existing Windows TTS implementation
        require_once __DIR__ . '/windows_tts_engine.php';
        $windowsTTS = new WindowsTTSEngine();

        $cleanText = $this->stripSSML($text);
        $success = $windowsTTS->generateWindowsSpeech($cleanText, 'default', $outputFile);

        if ($success && file_exists($outputFile)) {
            return $outputFile;
        }

        throw new Exception("Windows SAPI TTS failed");
    }

    /**
     * Strip SSML tags for engines that don't support them
     */
    private function stripSSML($text) {
        // Remove SSML tags but keep the content
        $text = preg_replace('/<break[^>]*>/', ' ', $text);
        $text = preg_replace('/<emphasis[^>]*>(.*?)<\/emphasis>/', '$1', $text);
        $text = preg_replace('/<prosody[^>]*>(.*?)<\/prosody>/', '$1', $text);
        $text = preg_replace('/\s+/', ' ', $text);
        return trim($text);
    }

    /**
     * Parse SSML and generate prosody data for custom neural engine
     */
    private function parseSSMLAndGenerateProsody($text, $voiceProfile) {
        $prosodyData = [];
        $segments = $this->splitTextIntoSegments($text);

        foreach ($segments as $segment) {
            $prosodyData[] = [
                'text' => $segment['text'],
                'type' => $segment['type'], // 'text', 'break', 'emphasis'
                'duration' => $segment['duration'] ?? null,
                'emphasis_level' => $segment['emphasis_level'] ?? 'normal',
                'pitch_modifier' => $this->calculatePitchModifier($segment, $voiceProfile),
                'speed_modifier' => $this->calculateSpeedModifier($segment, $voiceProfile),
                'volume_modifier' => $this->calculateVolumeModifier($segment, $voiceProfile)
            ];
        }

        return $prosodyData;
    }

    /**
     * Split text into segments for prosody analysis
     */
    private function splitTextIntoSegments($text) {
        $segments = [];

        // Split by SSML tags and text
        $pattern = '/(<break[^>]*>|<emphasis[^>]*>.*?<\/emphasis>|<prosody[^>]*>.*?<\/prosody>|[^<]+)/';
        preg_match_all($pattern, $text, $matches);

        foreach ($matches[1] as $match) {
            $match = trim($match);
            if (empty($match)) continue;

            if (strpos($match, '<break') === 0) {
                // Break/pause segment
                preg_match('/time="(\d+)ms"/', $match, $timeMatch);
                $duration = isset($timeMatch[1]) ? intval($timeMatch[1]) : 500;

                $segments[] = [
                    'type' => 'break',
                    'text' => '',
                    'duration' => $duration
                ];
            } elseif (strpos($match, '<emphasis') === 0) {
                // Emphasis segment
                preg_match('/<emphasis[^>]*>(.*?)<\/emphasis>/', $match, $emphasisMatch);
                preg_match('/level="([^"]*)"/', $match, $levelMatch);

                $segments[] = [
                    'type' => 'emphasis',
                    'text' => $emphasisMatch[1] ?? $match,
                    'emphasis_level' => $levelMatch[1] ?? 'moderate'
                ];
            } else {
                // Regular text segment
                $segments[] = [
                    'type' => 'text',
                    'text' => $match
                ];
            }
        }

        return $segments;
    }

    /**
     * Calculate pitch modifier based on segment and voice profile
     */
    private function calculatePitchModifier($segment, $voiceProfile) {
        $modifier = 1.0;

        if ($segment['type'] === 'emphasis') {
            switch ($segment['emphasis_level'] ?? 'moderate') {
                case 'strong':
                    $modifier = 1.3;
                    break;
                case 'moderate':
                    $modifier = 1.15;
                    break;
                case 'reduced':
                    $modifier = 0.9;
                    break;
            }
        }

        return $modifier;
    }

    /**
     * Calculate speed modifier based on segment and voice profile
     */
    private function calculateSpeedModifier($segment, $voiceProfile) {
        $modifier = $voiceProfile['speed'] ?? 1.0;

        if ($segment['type'] === 'emphasis') {
            $modifier *= 0.95; // Slightly slower for emphasis
        }

        return $modifier;
    }

    /**
     * Calculate volume modifier based on segment and voice profile
     */
    private function calculateVolumeModifier($segment, $voiceProfile) {
        $modifier = 1.0;

        if ($segment['type'] === 'emphasis') {
            $modifier = $this->config['prosody']['emphasis_boost'];
        }

        return $modifier;
    }

    /**
     * Synthesize audio with neural prosody
     */
    private function synthesizeWithNeuralProsody($prosodyData, $voiceProfile) {
        $audioSegments = [];
        $sampleRate = $this->config['quality']['sample_rate'];
        $baseFreq = $voiceProfile['pitch_base'];

        foreach ($prosodyData as $segment) {
            if ($segment['type'] === 'break') {
                // Generate silence
                $duration = $segment['duration'] / 1000; // Convert ms to seconds
                $samples = $this->generateSilence($duration, $sampleRate);
                $audioSegments[] = $samples;
            } else {
                // Generate speech for text
                $samples = $this->generateNeuralSpeech(
                    $segment['text'],
                    $baseFreq * $segment['pitch_modifier'],
                    $segment['speed_modifier'],
                    $segment['volume_modifier'],
                    $sampleRate
                );
                $audioSegments[] = $samples;
            }
        }

        return $audioSegments;
    }

    /**
     * Generate neural speech for text segment
     */
    private function generateNeuralSpeech($text, $frequency, $speedModifier, $volumeModifier, $sampleRate) {
        if (empty($text)) {
            return [];
        }

        $words = explode(' ', $text);
        $samples = [];

        foreach ($words as $wordIndex => $word) {
            if (empty($word)) continue;

            // Calculate word duration based on length and speed
            $baseDuration = (strlen($word) * 0.08 + 0.2) / $speedModifier;
            $wordSamples = intval($baseDuration * $sampleRate);

            // Generate formant-based synthesis
            $wordAudio = $this->generateWordWithFormants(
                $word,
                $frequency,
                $wordSamples,
                $volumeModifier,
                $sampleRate
            );

            $samples = array_merge($samples, $wordAudio);

            // Add inter-word pause
            if ($wordIndex < count($words) - 1) {
                $pauseSamples = intval(0.1 * $sampleRate);
                $samples = array_merge($samples, array_fill(0, $pauseSamples, 0));
            }
        }

        return $samples;
    }

    /**
     * Generate word audio using formant synthesis
     */
    private function generateWordWithFormants($word, $baseFreq, $samples, $volume, $sampleRate) {
        $audio = [];
        $formants = $this->config['engines']['custom_neural']['formant_frequencies'];

        for ($i = 0; $i < $samples; $i++) {
            $t = $i / $sampleRate;
            $progress = $i / $samples;

            // Create envelope for natural word shape
            $envelope = $this->createWordEnvelope($progress);

            // Generate formant frequencies
            $sample = 0;
            foreach ($formants as $index => $formantFreq) {
                $amplitude = 1.0 / (($index + 1) * 2); // Decreasing amplitude for higher formants
                $frequency = $baseFreq * ($formantFreq / 800); // Scale relative to base

                // Add slight frequency modulation for naturalness
                $modulation = 1 + sin($t * 2 * pi() * 5) * 0.02;
                $sample += sin(2 * pi() * $frequency * $modulation * $t) * $amplitude;
            }

            // Apply envelope and volume
            $sample *= $envelope * $volume * 0.3;

            // Add slight noise for realism
            $noise = (rand(-1000, 1000) / 1000000);
            $sample += $noise;

            $audio[] = intval($sample * 32767);
        }

        return $audio;
    }

    /**
     * Create natural word envelope
     */
    private function createWordEnvelope($progress) {
        // Attack, sustain, release envelope
        if ($progress < 0.1) {
            return $progress / 0.1; // Attack
        } elseif ($progress < 0.8) {
            return 1.0; // Sustain
        } else {
            return (1.0 - $progress) / 0.2; // Release
        }
    }

    /**
     * Generate silence samples
     */
    private function generateSilence($duration, $sampleRate) {
        $samples = intval($duration * $sampleRate);
        return array_fill(0, $samples, 0);
    }

    /**
     * Combine audio segments into final file
     */
    private function combineAudioSegments($audioSegments, $outputFile) {
        $allSamples = [];

        foreach ($audioSegments as $segment) {
            $allSamples = array_merge($allSamples, $segment);
        }

        // Create WAV file
        return $this->createWAVFile($allSamples, $outputFile, $this->config['quality']['sample_rate']);
    }

    /**
     * Create WAV file from samples
     */
    private function createWAVFile($samples, $outputFile, $sampleRate) {
        // WAV header
        $header = pack('V', 0x46464952); // "RIFF"
        $header .= pack('V', 36 + count($samples) * 2); // File size
        $header .= pack('V', 0x45564157); // "WAVE"
        $header .= pack('V', 0x20746d66); // "fmt "
        $header .= pack('V', 16); // Subchunk1Size
        $header .= pack('v', 1); // AudioFormat (PCM)
        $header .= pack('v', 1); // NumChannels (mono)
        $header .= pack('V', $sampleRate); // SampleRate
        $header .= pack('V', $sampleRate * 2); // ByteRate
        $header .= pack('v', 2); // BlockAlign
        $header .= pack('v', 16); // BitsPerSample
        $header .= pack('V', 0x61746164); // "data"
        $header .= pack('V', count($samples) * 2); // Subchunk2Size

        // Audio data
        $audioData = '';
        foreach ($samples as $sample) {
            $audioData .= pack('v', intval($sample));
        }

        $result = file_put_contents($outputFile, $header . $audioData);
        return $result !== false ? $outputFile : false;
    }

    /**
     * Post-process audio for quality enhancement
     */
    private function postProcessAudio($rawAudioFile, $context) {
        $this->log("🎛️ Post-processing audio for quality enhancement...");

        $enhancedFile = $this->tempDir . 'enhanced_' . uniqid() . '.wav';
        $finalFile = $this->tempDir . 'final_' . uniqid() . '.mp3';

        // Step 1: Basic audio enhancement
        $enhanceCmd = sprintf(
            'ffmpeg -i "%s" -af "highpass=f=80,lowpass=f=8000,loudnorm=I=-16:TP=-1.5:LRA=11,compand=0.3|0.3:1|1:-90/-60|-60/-40|-40/-30|-20/-20:6:0:-90:0.2" "%s" -y 2>&1',
            $rawAudioFile,
            $enhancedFile
        );

        exec($enhanceCmd, $output, $returnCode);

        if ($returnCode !== 0 || !file_exists($enhancedFile)) {
            $this->log("⚠️ Audio enhancement failed, using raw audio");
            $enhancedFile = $rawAudioFile;
        }

        // Step 2: Add ambient audio for nature content
        if ($context['theme'] === 'nature_documentary') {
            $enhancedFile = $this->addNatureAmbiance($enhancedFile, $context);
        }

        // Step 3: Convert to high-quality MP3
        $mp3Cmd = sprintf(
            'ffmpeg -i "%s" -codec:a libmp3lame -b:a %dk -ar %d "%s" -y 2>&1',
            $enhancedFile,
            $this->config['quality']['mp3_bitrate'],
            $this->config['quality']['sample_rate'],
            $finalFile
        );

        exec($mp3Cmd, $output, $returnCode);

        if ($returnCode === 0 && file_exists($finalFile)) {
            $this->log("✅ Audio post-processing complete: " . round(filesize($finalFile)/1024, 1) . " KB");

            // Clean up intermediate files
            if ($enhancedFile !== $rawAudioFile && file_exists($enhancedFile)) {
                unlink($enhancedFile);
            }
            if (file_exists($rawAudioFile)) {
                unlink($rawAudioFile);
            }

            return $finalFile;
        } else {
            $this->log("❌ MP3 conversion failed, returning enhanced WAV");
            return $enhancedFile;
        }
    }

    /**
     * Add nature ambiance for jungle/elephant content
     */
    private function addNatureAmbiance($audioFile, $context) {
        $this->log("🌿 Adding nature ambiance for jungle theme...");

        // Generate simple nature sounds
        $ambianceFile = $this->generateNatureAmbiance($audioFile);

        if (!$ambianceFile) {
            return $audioFile;
        }

        $mixedFile = $this->tempDir . 'mixed_' . uniqid() . '.wav';

        // Mix speech with ambiance at 15% volume
        $mixCmd = sprintf(
            'ffmpeg -i "%s" -i "%s" -filter_complex "[1:a]volume=0.15[amb];[0:a][amb]amix=inputs=2:duration=first" "%s" -y 2>&1',
            $audioFile,
            $ambianceFile,
            $mixedFile
        );

        exec($mixCmd, $output, $returnCode);

        // Clean up
        if (file_exists($ambianceFile)) {
            unlink($ambianceFile);
        }

        if ($returnCode === 0 && file_exists($mixedFile)) {
            $this->log("✅ Nature ambiance added successfully");
            return $mixedFile;
        } else {
            $this->log("⚠️ Failed to add ambiance, using original audio");
            return $audioFile;
        }
    }

    /**
     * Generate nature ambiance sounds
     */
    private function generateNatureAmbiance($referenceFile) {
        // Get duration of reference file
        exec("ffprobe -v quiet -show_entries format=duration -of csv=p=0 \"$referenceFile\" 2>&1", $output, $returnCode);

        if ($returnCode !== 0 || empty($output[0])) {
            return false;
        }

        $duration = floatval($output[0]);
        $ambianceFile = $this->tempDir . 'ambiance_' . uniqid() . '.wav';

        // Generate nature sounds using FFmpeg's noise generators
        $ambianceCmd = sprintf(
            'ffmpeg -f lavfi -i "anoisesrc=duration=%f:color=brown:seed=42" -af "highpass=f=200,lowpass=f=2000,volume=0.3" "%s" -y 2>&1',
            $duration,
            $ambianceFile
        );

        exec($ambianceCmd, $output, $returnCode);

        if ($returnCode === 0 && file_exists($ambianceFile)) {
            return $ambianceFile;
        }

        return false;
    }

    /**
     * Get available voice profiles
     */
    public function getVoiceProfiles() {
        return $this->voiceProfiles;
    }

    /**
     * Get available engines
     */
    public function getAvailableEngines() {
        return $this->engines;
    }

    /**
     * Test TTS system
     */
    public function testTTSSystem() {
        $testText = "A magnificent elephant walks slowly through the lush green jungle. The gentle giant moves gracefully between ancient trees as birds chirp overhead.";
        $testFile = $this->tempDir . 'tts_system_test.mp3';

        try {
            $result = $this->generateSpeech($testText, 'female_natural', $testFile);

            if ($result && file_exists($result)) {
                $fileSize = filesize($result);

                return [
                    'success' => true,
                    'message' => 'Advanced TTS system test successful',
                    'file' => $result,
                    'file_size' => $fileSize,
                    'engines_available' => $this->engines,
                    'quality_metrics' => [
                        'sample_rate' => $this->config['quality']['sample_rate'],
                        'bitrate' => $this->config['quality']['mp3_bitrate'],
                        'file_size_kb' => round($fileSize / 1024, 1)
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'TTS generation failed',
                    'engines_available' => $this->engines
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'TTS system error: ' . $e->getMessage(),
                'engines_available' => $this->engines
            ];
        }
    }
}
