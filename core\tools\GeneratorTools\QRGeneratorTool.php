<?php
/**
 * QR Code Generator Tool
 * Generate QR codes with custom styling and logos
 */

require_once __DIR__ . '/../BaseTool.php';

class QRGeneratorTool extends BaseTool {
    protected $toolId = 'qr_generator';
    
    public function process($files, $options = [], $userId = null, $operationId = null) {
        // QR generator doesn't need input files, but validate options
        $data = $options['data'] ?? '';
        $dataType = $options['data_type'] ?? 'text';
        $size = (int)($options['size'] ?? 200);
        $errorCorrection = $options['error_correction'] ?? 'M';
        $foregroundColor = $options['foreground_color'] ?? '#000000';
        $backgroundColor = $options['background_color'] ?? '#FFFFFF';
        $logoFile = $options['logo_file'] ?? null;
        $format = $options['format'] ?? 'png';
        
        if (empty($data)) {
            throw new ToolException('QR code data is required');
        }
        
        $processedFiles = [];
        $tempFiles = [];
        
        try {
            // Format data based on type
            $qrData = $this->formatQRData($data, $dataType, $options);
            
            // Generate QR code
            $qrImage = $this->generateQRCode($qrData, $size, $errorCorrection, $foregroundColor, $backgroundColor);
            
            // Add logo if provided
            if ($logoFile && isset($files[0])) {
                $logoPath = $this->storeUploadedFile($files[0], $operationId, $userId);
                $tempFiles[] = $logoPath;
                $qrImage = $this->addLogoToQR($qrImage, $logoPath, $size);
            }
            
            // Generate output filename
            $filename = 'qr_code_' . time() . '.' . $format;
            $outputPath = $this->tempDir . $operationId . '_' . $filename;
            
            // Save QR code
            $this->saveImage($qrImage, $outputPath, $format, 90);
            
            // Store processed file
            $storedFile = $this->storeProcessedFile($outputPath, $filename, $operationId, $userId);
            
            $processedFiles[] = [
                'original_name' => $filename,
                'processed_name' => basename($storedFile['stored_path']),
                'download_url' => $storedFile['download_url'],
                'file_size' => $storedFile['file_size'],
                'qr_data' => $qrData,
                'data_type' => $dataType,
                'size' => $size,
                'error_correction' => $errorCorrection,
                'has_logo' => !empty($logoFile),
                'format' => $format
            ];
            
            // Clean up
            imagedestroy($qrImage);
            $tempFiles[] = $outputPath;
            
            return [
                'success' => true,
                'processed_files' => $processedFiles,
                'total_files' => count($processedFiles),
                'qr_data' => $qrData,
                'settings_used' => [
                    'size' => $size,
                    'error_correction' => $errorCorrection,
                    'format' => $format
                ]
            ];
            
        } catch (Exception $e) {
            $this->cleanup($tempFiles);
            throw new ToolException("QR code generation failed: " . $e->getMessage());
        } finally {
            // Clean up temporary files
            foreach ($tempFiles as $tempFile) {
                if (file_exists($tempFile)) {
                    unlink($tempFile);
                }
            }
        }
    }
    
    /**
     * Format QR data based on type
     */
    private function formatQRData($data, $type, $options) {
        switch ($type) {
            case 'url':
                return filter_var($data, FILTER_VALIDATE_URL) ? $data : 'http://' . $data;
                
            case 'wifi':
                $ssid = $options['wifi_ssid'] ?? '';
                $password = $options['wifi_password'] ?? '';
                $security = $options['wifi_security'] ?? 'WPA';
                $hidden = $options['wifi_hidden'] ?? false;
                
                return "WIFI:T:$security;S:$ssid;P:$password;H:" . ($hidden ? 'true' : 'false') . ";;";
                
            case 'contact':
                $name = $options['contact_name'] ?? '';
                $phone = $options['contact_phone'] ?? '';
                $email = $options['contact_email'] ?? '';
                $organization = $options['contact_organization'] ?? '';
                
                return "BEGIN:VCARD\nVERSION:3.0\nFN:$name\nORG:$organization\nTEL:$phone\nEMAIL:$email\nEND:VCARD";
                
            case 'sms':
                $phone = $options['sms_phone'] ?? '';
                $message = $options['sms_message'] ?? '';
                return "SMSTO:$phone:$message";
                
            case 'email':
                $email = $options['email_address'] ?? '';
                $subject = $options['email_subject'] ?? '';
                $body = $options['email_body'] ?? '';
                return "mailto:$email?subject=" . urlencode($subject) . "&body=" . urlencode($body);
                
            case 'text':
            default:
                return $data;
        }
    }
    
    /**
     * Generate QR code using GD (simplified implementation)
     */
    private function generateQRCode($data, $size, $errorCorrection, $fgColor, $bgColor) {
        // This is a simplified QR code generator
        // In a real implementation, you would use a proper QR code library
        
        // Create base image
        $image = imagecreatetruecolor($size, $size);
        
        // Parse colors
        $bg = $this->hexToRgb($bgColor);
        $fg = $this->hexToRgb($fgColor);
        
        $bgColorGD = imagecolorallocate($image, $bg[0], $bg[1], $bg[2]);
        $fgColorGD = imagecolorallocate($image, $fg[0], $fg[1], $fg[2]);
        
        // Fill background
        imagefill($image, 0, 0, $bgColorGD);
        
        // Generate simple pattern (this is a placeholder - real QR generation is complex)
        $this->generateSimpleQRPattern($image, $size, $fgColorGD, $data);
        
        return $image;
    }
    
    /**
     * Generate a simple QR-like pattern (placeholder implementation)
     */
    private function generateSimpleQRPattern($image, $size, $color, $data) {
        $moduleSize = max(1, $size / 25); // 25x25 grid
        $modules = 25;
        
        // Create a simple pattern based on data hash
        $hash = md5($data);
        $pattern = [];
        
        for ($i = 0; $i < $modules * $modules; $i++) {
            $pattern[] = hexdec($hash[$i % 32]) % 2;
        }
        
        // Draw finder patterns (corners)
        $this->drawFinderPattern($image, 0, 0, $moduleSize, $color);
        $this->drawFinderPattern($image, ($modules - 7) * $moduleSize, 0, $moduleSize, $color);
        $this->drawFinderPattern($image, 0, ($modules - 7) * $moduleSize, $moduleSize, $color);
        
        // Draw data pattern
        for ($y = 0; $y < $modules; $y++) {
            for ($x = 0; $x < $modules; $x++) {
                // Skip finder pattern areas
                if ($this->isFinderPatternArea($x, $y, $modules)) {
                    continue;
                }
                
                $index = $y * $modules + $x;
                if ($pattern[$index % count($pattern)]) {
                    imagefilledrectangle(
                        $image,
                        $x * $moduleSize,
                        $y * $moduleSize,
                        ($x + 1) * $moduleSize - 1,
                        ($y + 1) * $moduleSize - 1,
                        $color
                    );
                }
            }
        }
    }
    
    /**
     * Draw QR finder pattern
     */
    private function drawFinderPattern($image, $x, $y, $moduleSize, $color) {
        // Outer 7x7 square
        imagefilledrectangle($image, $x, $y, $x + 7 * $moduleSize - 1, $y + 7 * $moduleSize - 1, $color);
        
        // Inner white 5x5 square
        $bgColor = imagecolorallocate($image, 255, 255, 255);
        imagefilledrectangle(
            $image,
            $x + $moduleSize,
            $y + $moduleSize,
            $x + 6 * $moduleSize - 1,
            $y + 6 * $moduleSize - 1,
            $bgColor
        );
        
        // Center 3x3 black square
        imagefilledrectangle(
            $image,
            $x + 2 * $moduleSize,
            $y + 2 * $moduleSize,
            $x + 5 * $moduleSize - 1,
            $y + 5 * $moduleSize - 1,
            $color
        );
    }
    
    /**
     * Check if position is in finder pattern area
     */
    private function isFinderPatternArea($x, $y, $modules) {
        // Top-left finder pattern
        if ($x < 9 && $y < 9) return true;
        
        // Top-right finder pattern
        if ($x >= $modules - 8 && $y < 9) return true;
        
        // Bottom-left finder pattern
        if ($x < 9 && $y >= $modules - 8) return true;
        
        return false;
    }
    
    /**
     * Add logo to QR code
     */
    private function addLogoToQR($qrImage, $logoPath, $qrSize) {
        $logo = $this->loadImage($logoPath);
        $logoSize = min($qrSize / 5, 50); // Logo should be max 20% of QR size
        
        // Resize logo
        $resizedLogo = imagecreatetruecolor($logoSize, $logoSize);
        imagecopyresampled(
            $resizedLogo,
            $logo,
            0, 0, 0, 0,
            $logoSize, $logoSize,
            imagesx($logo), imagesy($logo)
        );
        
        // Add white background circle for logo
        $centerX = $qrSize / 2;
        $centerY = $qrSize / 2;
        $bgRadius = $logoSize / 2 + 5;
        
        $white = imagecolorallocate($qrImage, 255, 255, 255);
        imagefilledellipse($qrImage, $centerX, $centerY, $bgRadius * 2, $bgRadius * 2, $white);
        
        // Place logo in center
        imagecopyresampled(
            $qrImage,
            $resizedLogo,
            $centerX - $logoSize / 2,
            $centerY - $logoSize / 2,
            0, 0,
            $logoSize, $logoSize,
            $logoSize, $logoSize
        );
        
        imagedestroy($logo);
        imagedestroy($resizedLogo);
        
        return $qrImage;
    }
    
    /**
     * Convert hex color to RGB
     */
    private function hexToRgb($hex) {
        $hex = ltrim($hex, '#');
        
        if (strlen($hex) === 3) {
            $hex = $hex[0] . $hex[0] . $hex[1] . $hex[1] . $hex[2] . $hex[2];
        }
        
        return [
            hexdec(substr($hex, 0, 2)),
            hexdec(substr($hex, 2, 2)),
            hexdec(substr($hex, 4, 2))
        ];
    }
}
?>
