<?php
/**
 * Base API Class for Sutradhar 2070
 * Provides common functionality for all API endpoints
 */

abstract class APIBase {
    protected $db;
    protected $currentUser = null;
    protected $requestData = null;
    protected $requestMethod;
    protected $requestPath;
    protected $startTime;
    
    public function __construct() {
        $this->startTime = microtime(true);
        $this->requestMethod = $_SERVER['REQUEST_METHOD'];
        $this->requestPath = $_GET['endpoint'] ?? '';
        
        // Set security headers
        $this->setSecurityHeaders();
        
        // Initialize database with error handling
        require_once 'database_manager.php';
        try {
            $this->db = new DatabaseManager();
        } catch (Exception $e) {
            error_log("Database connection failed: " . $e->getMessage());
            $this->db = null;
        }
        
        // Parse request data
        $this->parseRequestData();
        
        // Initialize session
        $this->initializeSession();
    }
    
    /**
     * Set security headers
     */
    private function setSecurityHeaders() {
        header('Content-Type: application/json; charset=utf-8');
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: DENY');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
        
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit;
        }
    }
    
    /**
     * Parse request data based on content type
     */
    private function parseRequestData() {
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        
        if (strpos($contentType, 'application/json') !== false) {
            $input = file_get_contents('php://input');
            $this->requestData = json_decode($input, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->sendError('Invalid JSON in request body', 400);
            }
        } else {
            $this->requestData = $_POST;
        }
        
        // Merge GET parameters
        if (!empty($_GET)) {
            $this->requestData = array_merge($this->requestData ?? [], $_GET);
        }
    }
    
    /**
     * Initialize session management
     */
    private function initializeSession() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // Check for valid session
        if (isset($_SESSION['user_id']) && isset($_SESSION['session_id'])) {
            $this->validateSession($_SESSION['user_id'], $_SESSION['session_id']);
        }
    }
    
    /**
     * Validate user session
     */
    private function validateSession($userId, $sessionId) {
        if (!$this->db) {
            return; // No database connection, skip session validation
        }

        try {
            $session = $this->db->selectOne('user_sessions', [
                'session_id' => $sessionId,
                'user_id' => $userId
            ]);

            if ($session && strtotime($session['expires_at']) > time()) {
                // Load current user
                $this->currentUser = $this->db->selectOne('users', [
                    'user_id' => $userId,
                    'status' => 'active'
                ]);

                // Update last activity
                $this->db->update('user_sessions', [
                    'last_activity' => date('Y-m-d H:i:s')
                ], ['session_id' => $sessionId]);
            } else {
                // Invalid or expired session
                $this->clearSession();
            }
        } catch (Exception $e) {
            error_log("Session validation failed: " . $e->getMessage());
            $this->clearSession();
        }
    }
    
    /**
     * Clear current session
     */
    protected function clearSession() {
        if (isset($_SESSION['session_id']) && $this->db) {
            try {
                $this->db->delete('user_sessions', ['session_id' => $_SESSION['session_id']]);
            } catch (Exception $e) {
                error_log("Failed to clear session from database: " . $e->getMessage());
            }
        }

        session_destroy();
        $this->currentUser = null;
    }
    
    /**
     * Create new user session
     */
    protected function createSession($userId) {
        $sessionId = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', time() + 86400); // 24 hours

        if ($this->db) {
            $sessionData = [
                'session_id' => $sessionId,
                'user_id' => $userId,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
                'expires_at' => $expiresAt
            ];

            try {
                $this->db->insert('user_sessions', $sessionData);
            } catch (Exception $e) {
                error_log("Failed to create session in database: " . $e->getMessage());
            }
        }

        $_SESSION['user_id'] = $userId;
        $_SESSION['session_id'] = $sessionId;

        return $sessionId;
    }
    
    /**
     * Require authentication for endpoint
     */
    protected function requireAuth() {
        if (!$this->currentUser) {
            $this->sendError('Authentication required', 401);
        }
    }
    
    /**
     * Validate required fields in request
     */
    protected function validateRequired($fields) {
        $missing = [];
        
        foreach ($fields as $field) {
            if (!isset($this->requestData[$field]) || 
                (is_string($this->requestData[$field]) && trim($this->requestData[$field]) === '')) {
                $missing[] = $field;
            }
        }
        
        if (!empty($missing)) {
            $this->sendError('Missing required fields: ' . implode(', ', $missing), 400);
        }
    }
    
    /**
     * Validate email format
     */
    protected function validateEmail($email) {
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->sendError('Invalid email format', 400);
        }
    }
    
    /**
     * Validate password strength
     */
    protected function validatePassword($password) {
        if (strlen($password) < 8) {
            $this->sendError('Password must be at least 8 characters long', 400);
        }
        
        if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/', $password)) {
            $this->sendError('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character', 400);
        }
    }
    
    /**
     * Rate limiting check
     */
    protected function checkRateLimit($key, $maxAttempts = 60, $timeWindow = 3600) {
        $cacheKey = "rate_limit_{$key}";
        $attempts = $_SESSION[$cacheKey] ?? 0;
        $resetTime = $_SESSION[$cacheKey . '_reset'] ?? time();
        
        if (time() > $resetTime) {
            $_SESSION[$cacheKey] = 0;
            $_SESSION[$cacheKey . '_reset'] = time() + $timeWindow;
            $attempts = 0;
        }
        
        if ($attempts >= $maxAttempts) {
            $this->sendError('Rate limit exceeded. Please try again later.', 429);
        }
        
        $_SESSION[$cacheKey] = $attempts + 1;
    }
    
    /**
     * Log API usage
     */
    protected function logAPIUsage($responseStatus, $errorMessage = null) {
        $responseTime = round((microtime(true) - $this->startTime) * 1000);
        
        $logData = [
            'log_id' => uniqid('log_', true),
            'user_id' => $this->currentUser['user_id'] ?? null,
            'endpoint' => $this->requestPath,
            'method' => $this->requestMethod,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_data' => json_encode($this->sanitizeLogData($this->requestData)),
            'response_status' => $responseStatus,
            'response_time_ms' => $responseTime,
            'error_message' => $errorMessage
        ];
        
        if ($this->db) {
            try {
                $this->db->insert('api_usage_logs', $logData);
            } catch (Exception $e) {
                error_log("Failed to log API usage: " . $e->getMessage());
            }
        }
    }
    
    /**
     * Sanitize sensitive data from logs
     */
    private function sanitizeLogData($data) {
        if (!is_array($data)) return $data;
        
        $sensitiveFields = ['password', 'password_confirmation', 'token', 'api_key', 'secret'];
        
        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '[REDACTED]';
            }
        }
        
        return $data;
    }
    
    /**
     * Send successful response
     */
    protected function sendSuccess($data = null, $message = null, $statusCode = 200) {
        http_response_code($statusCode);
        
        $response = ['success' => true];
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        if ($message !== null) {
            $response['message'] = $message;
        }
        
        $this->logAPIUsage($statusCode);
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * Send error response
     */
    protected function sendError($message, $statusCode = 400, $details = null) {
        http_response_code($statusCode);
        
        $response = [
            'success' => false,
            'error' => $message
        ];
        
        if ($details !== null) {
            $response['details'] = $details;
        }
        
        $this->logAPIUsage($statusCode, $message);
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * Generate secure token
     */
    protected function generateToken($length = 32) {
        return bin2hex(random_bytes($length));
    }
    
    /**
     * Hash password securely
     */
    protected function hashPassword($password) {
        return password_hash($password, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536,
            'time_cost' => 4,
            'threads' => 3
        ]);
    }
    
    /**
     * Verify password
     */
    protected function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * Abstract method for handling requests
     */
    abstract public function handleRequest();
}
?>
