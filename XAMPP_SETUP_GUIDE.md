# XAMPP Setup Guide for Sutradhar Platform

## 📋 Prerequisites

1. **XAMPP Installed** - Download from [https://www.apachefriends.org/](https://www.apachefriends.org/)
2. **PHP 8.0+** with extensions:
   - GD extension
   - cURL extension
   - PDO MySQL
   - mbstring
   - OpenSSL

## 🚀 Installation Steps

### Step 1: Copy Project to XAMPP
1. Copy this entire `sutradhar` folder to your XAMPP's `htdocs` directory:
   ```
   C:\xampp\htdocs\sutradhar\
   ```

2. Your folder structure should look like:
   ```
   C:\xampp\htdocs\sutradhar\
   ├── public\          (Web accessible files)
   ├── core\           (PHP backend classes)
   ├── assets\         (CSS, JS, images)
   ├── config\         (Configuration files)
   ├── docs\           (Documentation)
   ├── database\       (SQL schema files)
   ├── temp\           (Temporary files)
   └── logs\           (Log files)
   ```

### Step 2: Start XAMPP Services
1. Open XAMPP Control Panel
2. Start **Apache** service
3. Start **MySQL** service

### Step 3: Create Database
1. Open phpMyAdmin: `http://localhost/phpmyadmin`
2. Create a new database named `sutradhar_db`
3. Import the database schema:
   - Go to Import tab
   - Select `database/tools_schema.sql`
   - Click "Go"

### Step 4: Configure Settings
1. Edit `config/settings.json`:
   ```json
   {
       "database": {
           "host": "localhost",
           "username": "root",
           "password": "",
           "database": "sutradhar_db"
       },
       "app": {
           "base_url": "http://localhost/sutradhar/public/",
           "debug": true
       }
   }
   ```

### Step 5: Set Permissions (Windows)
1. Right-click on the `sutradhar` folder
2. Properties → Security → Edit
3. Give "Full Control" to "Users" group for these folders:
   - `temp/`
   - `logs/`
   - `public/uploads/`
   - `public/videos/`

## 🌐 Access URLs

Once setup is complete, access the platform at:

- **Main Website**: `http://localhost/sutradhar/public/`
- **Tools Interface**: `http://localhost/sutradhar/public/tools/`
- **Dashboard**: `http://localhost/sutradhar/public/dashboard.html`
- **Documentation**: `http://localhost/sutradhar/docs/`
- **API Endpoint**: `http://localhost/sutradhar/public/api_unified.php`

## 🔧 Troubleshooting

### Apache Won't Start
- Check if port 80 is in use
- Run XAMPP as Administrator
- Check Windows Firewall settings

### Database Connection Issues
- Verify MySQL is running
- Check database credentials in `config/settings.json`
- Ensure database `sutradhar_db` exists

### File Upload Issues
- Check folder permissions
- Verify `upload_max_filesize` in php.ini
- Ensure `temp/` directory is writable

### Tools Not Loading
- Check browser console for JavaScript errors
- Verify all CSS/JS files are accessible
- Check Apache error logs

## 📁 Important Directories

### Public Directory (`public/`)
This is the web-accessible directory containing:
- `index.html` - Main homepage
- `tools/` - Tools interface
- `api_unified.php` - Main API endpoint
- `dashboard.html` - User dashboard

### Core Directory (`core/`)
Backend PHP classes (not web-accessible):
- `tools/` - Tools engine classes
- `api_*.php` - API handlers
- Database managers and utilities

### Assets Directory (`assets/`)
Static resources:
- `css/` - Stylesheets
- `js/` - JavaScript files
- `images/` - Static images

## 🛠️ Development Mode

For development, enable debug mode in `config/settings.json`:
```json
{
    "debug": true,
    "log_level": "debug"
}
```

This will:
- Show detailed error messages
- Enable console logging
- Disable caching for development

## 🔒 Security Notes

1. **Never expose** the `core/`, `config/`, `logs/` directories to web access
2. **Change default passwords** in production
3. **Enable HTTPS** for production deployment
4. **Regular backups** of database and uploaded files

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review logs in `logs/` directory
3. Check browser developer console
4. Verify XAMPP error logs

## 🎯 Quick Test

After setup, test the installation:
1. Visit `http://localhost/sutradhar/public/`
2. You should see the Sutradhar homepage
3. Click "🛠️ Tools" to access the tools interface
4. Try uploading an image to test the system

The platform is now ready for use on your local XAMPP server!
