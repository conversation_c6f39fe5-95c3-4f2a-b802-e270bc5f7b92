<?php
/**
 * Subscription Manager - Handles tiered pricing plans for Sutradhar 2070
 * Manages Free, Pro, Business, and Enterprise tiers with features and restrictions
 */

require_once 'database_manager.php';
require_once 'credit_system.php';
require_once 'email_service.php';

class SubscriptionManager {
    private $db;
    private $creditSystem;
    private $emailService;
    private $plans;
    private $features;
    
    public function __construct() {
        $this->db = new DatabaseManager();
        $this->creditSystem = new CreditSystem();
        $this->emailService = new EmailService();
        
        $this->initializePlans();
        $this->initializeFeatures();
        $this->initializeDatabase();
    }
    
    /**
     * Initialize subscription plans
     */
    private function initializePlans() {
        $this->plans = [
            'free' => [
                'id' => 'free',
                'name' => 'Free Tier',
                'price' => 0,
                'currency' => 'USD',
                'billing_cycle' => 'monthly',
                'monthly_credits' => 50,
                'description' => 'Perfect for getting started with AI video generation',
                'features' => [
                    'monthly_credits' => 50,
                    'video_quality' => 'standard',
                    'watermark' => true,
                    'priority_processing' => false,
                    'api_access' => false,
                    'support_level' => 'community',
                    'max_video_length' => 30,
                    'concurrent_generations' => 1,
                    'storage_days' => 7,
                    'download_formats' => ['mp4'],
                    'mood_optimization' => false,
                    'bulk_generation' => false,
                    'custom_branding' => false,
                    'analytics' => false
                ],
                'limits' => [
                    'max_generations_per_day' => 5,
                    'max_generations_per_month' => 50,
                    'queue_priority' => 'low'
                ],
                'popular' => false,
                'trial_days' => 0
            ],
            
            'pro' => [
                'id' => 'pro',
                'name' => 'Pro Tier',
                'price' => 9.99,
                'currency' => 'USD',
                'billing_cycle' => 'monthly',
                'monthly_credits' => 500,
                'description' => 'Ideal for content creators and small businesses',
                'features' => [
                    'monthly_credits' => 500,
                    'video_quality' => 'hd',
                    'watermark' => false,
                    'priority_processing' => false,
                    'api_access' => false,
                    'support_level' => 'email',
                    'max_video_length' => 60,
                    'concurrent_generations' => 3,
                    'storage_days' => 30,
                    'download_formats' => ['mp4', 'mov'],
                    'mood_optimization' => true,
                    'bulk_generation' => true,
                    'custom_branding' => false,
                    'analytics' => 'basic'
                ],
                'limits' => [
                    'max_generations_per_day' => 50,
                    'max_generations_per_month' => 500,
                    'queue_priority' => 'normal'
                ],
                'popular' => true,
                'trial_days' => 7
            ],
            
            'business' => [
                'id' => 'business',
                'name' => 'Business Tier',
                'price' => 29.99,
                'currency' => 'USD',
                'billing_cycle' => 'monthly',
                'monthly_credits' => 2000,
                'description' => 'Perfect for growing businesses and agencies',
                'features' => [
                    'monthly_credits' => 2000,
                    'video_quality' => 'hd',
                    'watermark' => false,
                    'priority_processing' => true,
                    'api_access' => true,
                    'support_level' => 'priority',
                    'max_video_length' => 120,
                    'concurrent_generations' => 10,
                    'storage_days' => 90,
                    'download_formats' => ['mp4', 'mov', 'avi'],
                    'mood_optimization' => true,
                    'bulk_generation' => true,
                    'custom_branding' => true,
                    'analytics' => 'advanced'
                ],
                'limits' => [
                    'max_generations_per_day' => 200,
                    'max_generations_per_month' => 2000,
                    'queue_priority' => 'high'
                ],
                'popular' => false,
                'trial_days' => 14
            ],
            
            'enterprise' => [
                'id' => 'enterprise',
                'name' => 'Enterprise Tier',
                'price' => 99.99,
                'currency' => 'USD',
                'billing_cycle' => 'monthly',
                'monthly_credits' => 10000,
                'description' => 'Unlimited power for large organizations',
                'features' => [
                    'monthly_credits' => 10000,
                    'video_quality' => '4k',
                    'watermark' => false,
                    'priority_processing' => true,
                    'api_access' => true,
                    'support_level' => 'dedicated',
                    'max_video_length' => 300,
                    'concurrent_generations' => 50,
                    'storage_days' => 365,
                    'download_formats' => ['mp4', 'mov', 'avi', 'webm'],
                    'mood_optimization' => true,
                    'bulk_generation' => true,
                    'custom_branding' => true,
                    'analytics' => 'enterprise'
                ],
                'limits' => [
                    'max_generations_per_day' => 1000,
                    'max_generations_per_month' => 10000,
                    'queue_priority' => 'highest'
                ],
                'popular' => false,
                'trial_days' => 30
            ]
        ];
    }
    
    /**
     * Initialize feature definitions
     */
    private function initializeFeatures() {
        $this->features = [
            'monthly_credits' => [
                'name' => 'Monthly Credits',
                'description' => 'Credits automatically added each month',
                'type' => 'numeric'
            ],
            'video_quality' => [
                'name' => 'Video Quality',
                'description' => 'Maximum video resolution and quality',
                'type' => 'enum',
                'values' => ['standard', 'hd', '4k']
            ],
            'watermark' => [
                'name' => 'Watermark',
                'description' => 'Sutradhar 2070 watermark on generated videos',
                'type' => 'boolean'
            ],
            'priority_processing' => [
                'name' => 'Priority Processing',
                'description' => 'Faster video generation with priority queue',
                'type' => 'boolean'
            ],
            'api_access' => [
                'name' => 'API Access',
                'description' => 'Programmatic access to video generation',
                'type' => 'boolean'
            ],
            'support_level' => [
                'name' => 'Support Level',
                'description' => 'Level of customer support provided',
                'type' => 'enum',
                'values' => ['community', 'email', 'priority', 'dedicated']
            ],
            'mood_optimization' => [
                'name' => 'Mood Optimization',
                'description' => 'Advanced mood-based video generation',
                'type' => 'boolean'
            ],
            'bulk_generation' => [
                'name' => 'Bulk Generation',
                'description' => 'Generate multiple videos simultaneously',
                'type' => 'boolean'
            ],
            'custom_branding' => [
                'name' => 'Custom Branding',
                'description' => 'Add your own branding to videos',
                'type' => 'boolean'
            ],
            'analytics' => [
                'name' => 'Analytics',
                'description' => 'Usage analytics and insights',
                'type' => 'enum',
                'values' => [false, 'basic', 'advanced', 'enterprise']
            ]
        ];
    }
    
    /**
     * Get user's current subscription
     */
    public function getUserSubscription($userId) {
        $subscription = $this->db->selectOne('subscriptions', [
            'user_id' => $userId,
            'status' => 'active'
        ]);
        
        if ($subscription) {
            $subscription['plan_details'] = $this->plans[$subscription['plan_id']] ?? null;
            return $subscription;
        }
        
        // Return free tier as default
        return [
            'user_id' => $userId,
            'plan_id' => 'free',
            'status' => 'active',
            'plan_details' => $this->plans['free']
        ];
    }
    
    /**
     * Check if user has access to feature
     */
    public function hasFeature($userId, $featureName) {
        $subscription = $this->getUserSubscription($userId);
        $plan = $subscription['plan_details'];
        
        return $plan['features'][$featureName] ?? false;
    }
    
    /**
     * Get feature value for user
     */
    public function getFeatureValue($userId, $featureName) {
        $subscription = $this->getUserSubscription($userId);
        $plan = $subscription['plan_details'];
        
        return $plan['features'][$featureName] ?? null;
    }
    
    /**
     * Check if user can perform action based on limits
     */
    public function canPerformAction($userId, $action) {
        $subscription = $this->getUserSubscription($userId);
        $plan = $subscription['plan_details'];
        
        switch ($action) {
            case 'generate_video':
                // Check daily limit
                $dailyCount = $this->getDailyGenerationCount($userId);
                $dailyLimit = $plan['limits']['max_generations_per_day'];
                
                if ($dailyCount >= $dailyLimit) {
                    return [
                        'allowed' => false,
                        'reason' => 'Daily generation limit reached',
                        'limit' => $dailyLimit,
                        'current' => $dailyCount
                    ];
                }
                
                // Check monthly limit
                $monthlyCount = $this->getMonthlyGenerationCount($userId);
                $monthlyLimit = $plan['limits']['max_generations_per_month'];
                
                if ($monthlyCount >= $monthlyLimit) {
                    return [
                        'allowed' => false,
                        'reason' => 'Monthly generation limit reached',
                        'limit' => $monthlyLimit,
                        'current' => $monthlyCount
                    ];
                }
                
                return ['allowed' => true];
                
            case 'api_access':
                return [
                    'allowed' => $plan['features']['api_access'],
                    'reason' => $plan['features']['api_access'] ? null : 'API access not available in current plan'
                ];
                
            case 'bulk_generation':
                return [
                    'allowed' => $plan['features']['bulk_generation'],
                    'reason' => $plan['features']['bulk_generation'] ? null : 'Bulk generation not available in current plan'
                ];
                
            default:
                return ['allowed' => true];
        }
    }
    
    /**
     * Subscribe user to plan
     */
    public function subscribeToPlan($userId, $planId, $paymentData = null) {
        try {
            if (!isset($this->plans[$planId])) {
                throw new Exception('Invalid plan ID');
            }
            
            $plan = $this->plans[$planId];
            
            $this->db->beginTransaction();
            
            // Cancel existing subscription
            $this->cancelSubscription($userId, false);
            
            // Create new subscription
            $subscriptionData = [
                'subscription_id' => uniqid('sub_'),
                'user_id' => $userId,
                'plan_id' => $planId,
                'status' => 'active',
                'started_at' => date('Y-m-d H:i:s'),
                'current_period_start' => date('Y-m-d H:i:s'),
                'current_period_end' => date('Y-m-d H:i:s', strtotime('+1 month')),
                'cancel_at_period_end' => false,
                'payment_method' => $paymentData['payment_method'] ?? null,
                'payment_transaction_id' => $paymentData['transaction_id'] ?? null
            ];
            
            $this->db->insert('subscriptions', $subscriptionData);
            
            // Update user tier
            $this->db->update('users', ['tier' => $planId], ['user_id' => $userId]);
            
            // Add monthly credits if not free tier
            if ($planId !== 'free' && $plan['monthly_credits'] > 0) {
                $this->creditSystem->addCredits(
                    $userId,
                    $plan['monthly_credits'],
                    'subscription',
                    "Monthly credits for {$plan['name']} plan"
                );
            }
            
            $this->db->commit();
            
            // Send confirmation email
            if ($paymentData) {
                $this->sendSubscriptionConfirmationEmail($userId, $plan, $paymentData);
            }
            
            return [
                'success' => true,
                'subscription_id' => $subscriptionData['subscription_id'],
                'plan' => $plan,
                'message' => "Successfully subscribed to {$plan['name']}"
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Cancel subscription
     */
    public function cancelSubscription($userId, $immediately = false) {
        try {
            $subscription = $this->db->selectOne('subscriptions', [
                'user_id' => $userId,
                'status' => 'active'
            ]);
            
            if (!$subscription) {
                return ['success' => true, 'message' => 'No active subscription found'];
            }
            
            if ($immediately) {
                // Cancel immediately
                $updateData = [
                    'status' => 'cancelled',
                    'cancelled_at' => date('Y-m-d H:i:s')
                ];
                
                // Downgrade to free tier
                $this->db->update('users', ['tier' => 'free'], ['user_id' => $userId]);
            } else {
                // Cancel at period end
                $updateData = [
                    'cancel_at_period_end' => true,
                    'cancelled_at' => date('Y-m-d H:i:s')
                ];
            }
            
            $this->db->update('subscriptions', $updateData, [
                'subscription_id' => $subscription['subscription_id']
            ]);
            
            return [
                'success' => true,
                'message' => $immediately ? 'Subscription cancelled immediately' : 'Subscription will cancel at period end'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Initialize database tables
     */
    private function initializeDatabase() {
        // Subscriptions table
        $this->db->createTable('subscriptions', [
            'id INT AUTO_INCREMENT PRIMARY KEY',
            'subscription_id VARCHAR(50) UNIQUE NOT NULL',
            'user_id VARCHAR(50) NOT NULL',
            'plan_id VARCHAR(50) NOT NULL',
            'status ENUM("active", "cancelled", "past_due", "unpaid") DEFAULT "active"',
            'started_at TIMESTAMP NOT NULL',
            'current_period_start TIMESTAMP NOT NULL',
            'current_period_end TIMESTAMP NOT NULL',
            'cancel_at_period_end BOOLEAN DEFAULT FALSE',
            'cancelled_at TIMESTAMP NULL',
            'payment_method VARCHAR(50)',
            'payment_transaction_id VARCHAR(100)',
            'created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            'FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE',
            'INDEX idx_user_status (user_id, status)',
            'INDEX idx_period_end (current_period_end)'
        ]);

        // Subscription history table
        $this->db->createTable('subscription_history', [
            'id INT AUTO_INCREMENT PRIMARY KEY',
            'user_id VARCHAR(50) NOT NULL',
            'plan_id VARCHAR(50) NOT NULL',
            'action ENUM("subscribed", "cancelled", "upgraded", "downgraded", "renewed") NOT NULL',
            'previous_plan_id VARCHAR(50)',
            'amount DECIMAL(10,2)',
            'currency VARCHAR(3) DEFAULT "USD"',
            'created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE',
            'INDEX idx_user_date (user_id, created_at)'
        ]);
    }

    /**
     * Get daily generation count
     */
    private function getDailyGenerationCount($userId) {
        $today = date('Y-m-d');
        $result = $this->db->query(
            "SELECT COUNT(*) as count FROM credit_transactions
             WHERE user_id = :user_id
             AND type = 'consumption'
             AND DATE(created_at) = :today",
            [':user_id' => $userId, ':today' => $today]
        );

        return $result ? intval($result[0]['count']) : 0;
    }

    /**
     * Get monthly generation count
     */
    private function getMonthlyGenerationCount($userId) {
        $startOfMonth = date('Y-m-01 00:00:00');
        $result = $this->db->query(
            "SELECT COUNT(*) as count FROM credit_transactions
             WHERE user_id = :user_id
             AND type = 'consumption'
             AND created_at >= :start_date",
            [':user_id' => $userId, ':start_date' => $startOfMonth]
        );

        return $result ? intval($result[0]['count']) : 0;
    }

    /**
     * Send subscription confirmation email
     */
    private function sendSubscriptionConfirmationEmail($userId, $plan, $paymentData = null) {
        $user = $this->db->selectOne('users', ['user_id' => $userId]);

        if ($user && $user['email']) {
            $subscriptionData = [
                'plan_name' => $plan['name'],
                'monthly_credits' => $plan['monthly_credits'],
                'amount' => $plan['price'],
                'currency' => $plan['currency'],
                'next_billing_date' => date('F j, Y', strtotime('+1 month'))
            ];

            $this->emailService->sendSubscriptionConfirmationEmail(
                $user['email'],
                $user['first_name'],
                $subscriptionData
            );
        }
    }

    /**
     * Get all available plans
     */
    public function getPlans() {
        return $this->plans;
    }

    /**
     * Get plan by ID
     */
    public function getPlan($planId) {
        return $this->plans[$planId] ?? null;
    }

    /**
     * Get plan comparison data
     */
    public function getPlanComparison() {
        $comparison = [];

        foreach ($this->plans as $planId => $plan) {
            $comparison[$planId] = [
                'name' => $plan['name'],
                'price' => $plan['price'],
                'monthly_credits' => $plan['monthly_credits'],
                'key_features' => [
                    'Video Quality' => $plan['features']['video_quality'],
                    'Watermark' => $plan['features']['watermark'] ? 'Yes' : 'No',
                    'Priority Processing' => $plan['features']['priority_processing'] ? 'Yes' : 'No',
                    'API Access' => $plan['features']['api_access'] ? 'Yes' : 'No',
                    'Support Level' => ucfirst($plan['features']['support_level']),
                    'Max Video Length' => $plan['features']['max_video_length'] . 's',
                    'Storage Days' => $plan['features']['storage_days'],
                    'Mood Optimization' => $plan['features']['mood_optimization'] ? 'Yes' : 'No'
                ],
                'popular' => $plan['popular']
            ];
        }

        return $comparison;
    }

    /**
     * Upgrade/downgrade subscription
     */
    public function changePlan($userId, $newPlanId, $paymentData = null) {
        try {
            $currentSubscription = $this->getUserSubscription($userId);
            $currentPlanId = $currentSubscription['plan_id'];

            if ($currentPlanId === $newPlanId) {
                return ['success' => false, 'error' => 'Already subscribed to this plan'];
            }

            $newPlan = $this->plans[$newPlanId];
            $currentPlan = $this->plans[$currentPlanId];

            // Determine if upgrade or downgrade
            $isUpgrade = $newPlan['price'] > $currentPlan['price'];
            $action = $isUpgrade ? 'upgraded' : 'downgraded';

            // Subscribe to new plan
            $result = $this->subscribeToPlan($userId, $newPlanId, $paymentData);

            if ($result['success']) {
                // Log plan change
                $historyData = [
                    'user_id' => $userId,
                    'plan_id' => $newPlanId,
                    'action' => $action,
                    'previous_plan_id' => $currentPlanId,
                    'amount' => $newPlan['price'],
                    'currency' => $newPlan['currency']
                ];

                $this->db->insert('subscription_history', $historyData);

                return [
                    'success' => true,
                    'action' => $action,
                    'message' => "Successfully {$action} to {$newPlan['name']}"
                ];
            }

            return $result;

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Process monthly billing
     */
    public function processMonthlyBilling() {
        $expiredSubscriptions = $this->db->select('subscriptions', [
            'status' => 'active',
            'current_period_end <=' => date('Y-m-d H:i:s')
        ]);

        $processed = 0;
        $failed = 0;

        foreach ($expiredSubscriptions as $subscription) {
            try {
                if ($subscription['cancel_at_period_end']) {
                    // Cancel subscription
                    $this->db->update('subscriptions',
                        ['status' => 'cancelled'],
                        ['subscription_id' => $subscription['subscription_id']]
                    );

                    // Downgrade to free tier
                    $this->db->update('users',
                        ['tier' => 'free'],
                        ['user_id' => $subscription['user_id']]
                    );
                } else {
                    // Renew subscription
                    $newPeriodEnd = date('Y-m-d H:i:s', strtotime($subscription['current_period_end'] . ' +1 month'));

                    $this->db->update('subscriptions', [
                        'current_period_start' => $subscription['current_period_end'],
                        'current_period_end' => $newPeriodEnd
                    ], ['subscription_id' => $subscription['subscription_id']]);

                    // Add monthly credits
                    $plan = $this->plans[$subscription['plan_id']];
                    if ($plan['monthly_credits'] > 0) {
                        $this->creditSystem->addCredits(
                            $subscription['user_id'],
                            $plan['monthly_credits'],
                            'subscription',
                            "Monthly credits renewal for {$plan['name']} plan"
                        );
                    }

                    // Log renewal
                    $historyData = [
                        'user_id' => $subscription['user_id'],
                        'plan_id' => $subscription['plan_id'],
                        'action' => 'renewed',
                        'amount' => $plan['price'],
                        'currency' => $plan['currency']
                    ];

                    $this->db->insert('subscription_history', $historyData);
                }

                $processed++;

            } catch (Exception $e) {
                error_log("Failed to process subscription {$subscription['subscription_id']}: " . $e->getMessage());
                $failed++;
            }
        }

        return [
            'processed' => $processed,
            'failed' => $failed,
            'total' => count($expiredSubscriptions)
        ];
    }

    /**
     * Get subscription statistics
     */
    public function getSubscriptionStats() {
        $stats = [];

        // Count by plan
        foreach (array_keys($this->plans) as $planId) {
            $count = $this->db->query(
                "SELECT COUNT(*) as count FROM users WHERE tier = :plan_id",
                [':plan_id' => $planId]
            );
            $stats['by_plan'][$planId] = $count ? intval($count[0]['count']) : 0;
        }

        // Revenue stats
        $revenueQuery = "
            SELECT
                plan_id,
                COUNT(*) as subscriptions,
                SUM(amount) as total_revenue
            FROM subscription_history
            WHERE action IN ('subscribed', 'upgraded', 'renewed')
            AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY plan_id
        ";

        $revenueStats = $this->db->query($revenueQuery);
        $stats['revenue'] = $revenueStats;

        // Churn rate
        $churnQuery = "
            SELECT COUNT(*) as churned
            FROM subscription_history
            WHERE action = 'cancelled'
            AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ";

        $churnStats = $this->db->query($churnQuery);
        $stats['churn'] = $churnStats ? intval($churnStats[0]['churned']) : 0;

        return $stats;
    }

    /**
     * Get user's subscription history
     */
    public function getSubscriptionHistory($userId) {
        return $this->db->select(
            'subscription_history',
            ['user_id' => $userId],
            '*',
            'created_at DESC',
            20
        );
    }

    /**
     * Check if user is on trial
     */
    public function isOnTrial($userId) {
        $subscription = $this->getUserSubscription($userId);

        if ($subscription['plan_id'] === 'free') {
            return false;
        }

        $plan = $this->plans[$subscription['plan_id']];
        $trialEnd = strtotime($subscription['started_at'] . ' +' . $plan['trial_days'] . ' days');

        return time() < $trialEnd;
    }

    /**
     * Get trial days remaining
     */
    public function getTrialDaysRemaining($userId) {
        if (!$this->isOnTrial($userId)) {
            return 0;
        }

        $subscription = $this->getUserSubscription($userId);
        $plan = $this->plans[$subscription['plan_id']];
        $trialEnd = strtotime($subscription['started_at'] . ' +' . $plan['trial_days'] . ' days');

        return max(0, ceil(($trialEnd - time()) / 86400));
    }
}
