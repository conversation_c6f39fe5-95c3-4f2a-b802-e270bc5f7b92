/**
 * Mood Video Generator JavaScript
 * Handles the interactive UI for mood-based video generation
 */

class MoodVideoGenerator {
    constructor() {
        this.currentStep = 1;
        this.selectedMood = null;
        this.selectedTopic = null;
        this.selectedImageType = null;
        this.inspiration = '';
        this.generatedPrompts = [];
        this.selectedPrompt = null;
        
        this.init();
    }
    
    init() {
        this.loadMoodCategories();
        this.setupEventListeners();
        this.updateStepIndicator();
    }
    
    setupEventListeners() {
        // Generate prompts button
        document.getElementById('generatePromptsBtn').addEventListener('click', () => {
            this.generatePrompts();
        });
        
        // Generate video button
        document.getElementById('generateVideoBtn').addEventListener('click', () => {
            this.generateVideo();
        });
        
        // Inspiration textarea
        document.getElementById('inspirationText').addEventListener('input', (e) => {
            this.inspiration = e.target.value;
        });
    }
    
    async loadMoodCategories() {
        try {
            const response = await fetch('mood_api.php?action=get_moods');
            const data = await response.json();
            
            if (data.success) {
                this.renderMoodCategories(data.moods);
            } else {
                console.error('Failed to load moods:', data.error);
            }
        } catch (error) {
            console.error('Error loading moods:', error);
            // Fallback to static data
            this.renderMoodCategoriesFallback();
        }
    }
    
    renderMoodCategories(moodData) {
        const container = document.getElementById('moodCategories');
        container.innerHTML = '';
        
        Object.entries(moodData).forEach(([categoryId, categoryData]) => {
            const categoryDiv = document.createElement('div');
            categoryDiv.className = 'mb-8';
            
            categoryDiv.innerHTML = `
                <div class="flex items-center mb-4">
                    <span class="text-2xl mr-3">${categoryData.category.icon}</span>
                    <h3 class="text-xl font-semibold text-cyber-blue">${categoryData.category.name}</h3>
                    <span class="ml-auto text-sm text-gray-400">${Object.keys(categoryData.moods).length} moods</span>
                </div>
                <div class="grid md:grid-cols-4 lg:grid-cols-5 gap-4" id="category-${categoryId}">
                    ${Object.entries(categoryData.moods).map(([moodId, mood]) => `
                        <div class="mood-card p-4 rounded-lg" data-mood-id="${moodId}" data-category="${categoryId}">
                            <div class="text-center">
                                <div class="category-badge category-${categoryId.replace('_', '-')} mb-2">${mood.name}</div>
                                <p class="text-xs text-gray-300">${mood.description}</p>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
            
            container.appendChild(categoryDiv);
        });
        
        // Add click listeners to mood cards
        document.querySelectorAll('.mood-card').forEach(card => {
            card.addEventListener('click', (e) => {
                this.selectMood(e.currentTarget.dataset.moodId);
            });
        });
    }
    
    renderMoodCategoriesFallback() {
        // Fallback static mood data
        const fallbackMoods = {
            positive_energy: {
                category: { name: 'Positive Energy', icon: '⚡' },
                moods: {
                    euphoric: { name: 'Euphoric', description: 'Overwhelming joy and excitement' },
                    inspiring: { name: 'Inspiring', description: 'Motivational and uplifting' },
                    playful: { name: 'Playful', description: 'Fun and whimsical' },
                    confident: { name: 'Confident', description: 'Self-assured and powerful' },
                    adventurous: { name: 'Adventurous', description: 'Exciting exploration' }
                }
            },
            calm_peaceful: {
                category: { name: 'Calm & Peaceful', icon: '🕊️' },
                moods: {
                    serene: { name: 'Serene', description: 'Peaceful and tranquil' },
                    contemplative: { name: 'Contemplative', description: 'Thoughtful and reflective' },
                    nostalgic: { name: 'Nostalgic', description: 'Wistful remembrance' },
                    meditative: { name: 'Meditative', description: 'Deep inner peace' },
                    melancholic: { name: 'Melancholic', description: 'Gentle sadness' }
                }
            },
            dramatic_intense: {
                category: { name: 'Dramatic & Intense', icon: '🎭' },
                moods: {
                    passionate: { name: 'Passionate', description: 'Intense emotion and fervor' },
                    heroic: { name: 'Heroic', description: 'Noble courage' },
                    dramatic: { name: 'Dramatic', description: 'Theatrical and charged' },
                    intense: { name: 'Intense', description: 'Raw power and emotion' },
                    rebellious: { name: 'Rebellious', description: 'Defiant spirit' }
                }
            },
            mysterious_dark: {
                category: { name: 'Mysterious & Dark', icon: '🌙' },
                moods: {
                    mysterious: { name: 'Mysterious', description: 'Enigmatic atmosphere' },
                    dark: { name: 'Dark', description: 'Brooding and shadowy' },
                    suspenseful: { name: 'Suspenseful', description: 'Building tension' },
                    gothic: { name: 'Gothic', description: 'Dark romantic' },
                    eerie: { name: 'Eerie', description: 'Unsettling and otherworldly' }
                }
            },
            romantic_emotional: {
                category: { name: 'Romantic & Emotional', icon: '💖' },
                moods: {
                    romantic: { name: 'Romantic', description: 'Love and connection' },
                    sensual: { name: 'Sensual', description: 'Passionate and alluring' },
                    heartfelt: { name: 'Heartfelt', description: 'Deep emotional sincerity' },
                    bittersweet: { name: 'Bittersweet', description: 'Mixed joy and sadness' },
                    yearning: { name: 'Yearning', description: 'Deep longing' }
                }
            },
            futuristic_tech: {
                category: { name: 'Futuristic & Tech', icon: '🚀' },
                moods: {
                    cyberpunk: { name: 'Cyberpunk', description: 'High-tech dystopian' },
                    futuristic: { name: 'Futuristic', description: 'Advanced technology' },
                    sci_fi: { name: 'Sci-Fi', description: 'Space exploration' },
                    digital: { name: 'Digital', description: 'Virtual reality' },
                    retro_futuristic: { name: 'Retro Futuristic', description: '80s future vision' }
                }
            }
        };
        
        this.renderMoodCategories(fallbackMoods);
    }
    
    selectMood(moodId) {
        // Remove previous selection
        document.querySelectorAll('.mood-card').forEach(card => {
            card.classList.remove('selected');
        });
        
        // Add selection to clicked card
        document.querySelector(`[data-mood-id="${moodId}"]`).classList.add('selected');
        
        this.selectedMood = moodId;
        
        // Move to next step
        setTimeout(() => {
            this.nextStep();
        }, 500);
    }
    
    async loadTopicCategories() {
        try {
            const response = await fetch('mood_api.php?action=get_topics');
            const data = await response.json();
            
            if (data.success) {
                this.renderTopicCategories(data.topics);
            } else {
                console.error('Failed to load topics:', data.error);
                this.renderTopicCategoriesFallback();
            }
        } catch (error) {
            console.error('Error loading topics:', error);
            this.renderTopicCategoriesFallback();
        }
    }
    
    renderTopicCategories(topics) {
        const container = document.getElementById('topicGrid');
        container.innerHTML = '';
        
        Object.entries(topics).forEach(([topicId, topic]) => {
            const topicCard = document.createElement('div');
            topicCard.className = 'mood-card p-6 rounded-lg cursor-pointer';
            topicCard.dataset.topicId = topicId;
            
            topicCard.innerHTML = `
                <div class="text-center">
                    <div class="text-3xl mb-3">🎯</div>
                    <h4 class="font-semibold text-cyber-blue mb-2">${topic.name}</h4>
                    <p class="text-sm text-gray-300 mb-3">${topic.description}</p>
                    <div class="text-xs text-gray-400">${topic.subtopics.length} subtopics</div>
                </div>
            `;
            
            topicCard.addEventListener('click', () => {
                this.selectTopic(topicId);
            });
            
            container.appendChild(topicCard);
        });
    }
    
    renderTopicCategoriesFallback() {
        const fallbackTopics = {
            nature_wildlife: {
                name: 'Nature & Wildlife',
                description: 'Natural environments and animals',
                subtopics: ['forest_animals', 'ocean_life', 'mountain_landscapes']
            },
            urban_city: {
                name: 'Urban & City Life',
                description: 'Modern city environments',
                subtopics: ['city_skylines', 'street_art', 'urban_culture']
            },
            fantasy_magic: {
                name: 'Fantasy & Magic',
                description: 'Magical worlds and creatures',
                subtopics: ['magical_forests', 'dragon_realms', 'fairy_kingdoms']
            }
        };
        
        this.renderTopicCategories(fallbackTopics);
    }
    
    selectTopic(topicId) {
        // Remove previous selection
        document.querySelectorAll('#topicGrid .mood-card').forEach(card => {
            card.classList.remove('selected');
        });
        
        // Add selection to clicked card
        document.querySelector(`[data-topic-id="${topicId}"]`).classList.add('selected');
        
        this.selectedTopic = topicId;
        
        // Move to next step
        setTimeout(() => {
            this.nextStep();
        }, 500);
    }
    
    async loadImageTypes() {
        try {
            const response = await fetch('mood_api.php?action=get_image_types');
            const data = await response.json();
            
            if (data.success) {
                this.renderImageTypes(data.imageTypes);
            } else {
                console.error('Failed to load image types:', data.error);
                this.renderImageTypesFallback();
            }
        } catch (error) {
            console.error('Error loading image types:', error);
            this.renderImageTypesFallback();
        }
    }
    
    renderImageTypes(imageTypes) {
        const container = document.getElementById('imageTypeGrid');
        container.innerHTML = '';
        
        Object.entries(imageTypes).forEach(([typeId, type]) => {
            const typeCard = document.createElement('div');
            typeCard.className = 'mood-card p-6 rounded-lg cursor-pointer';
            typeCard.dataset.typeId = typeId;
            
            typeCard.innerHTML = `
                <div class="text-center">
                    <div class="text-3xl mb-3">🎨</div>
                    <h4 class="font-semibold text-cyber-purple mb-2">${type.name}</h4>
                    <p class="text-sm text-gray-300 mb-3">${type.description}</p>
                    <div class="flex flex-wrap justify-center gap-1">
                        ${type.characteristics.slice(0, 3).map(char => 
                            `<span class="text-xs bg-cyber-purple/20 text-cyber-purple px-2 py-1 rounded">${char}</span>`
                        ).join('')}
                    </div>
                </div>
            `;
            
            typeCard.addEventListener('click', () => {
                this.selectImageType(typeId);
            });
            
            container.appendChild(typeCard);
        });
    }
    
    renderImageTypesFallback() {
        const fallbackTypes = {
            cinematic: {
                name: 'Cinematic',
                description: 'Movie-like dramatic scenes',
                characteristics: ['dramatic', 'professional', 'storytelling']
            },
            artistic: {
                name: 'Artistic',
                description: 'Creative and expressive',
                characteristics: ['creative', 'expressive', 'unique']
            },
            photorealistic: {
                name: 'Photorealistic',
                description: 'Highly detailed realistic',
                characteristics: ['realistic', 'detailed', 'natural']
            }
        };
        
        this.renderImageTypes(fallbackTypes);
    }
    
    selectImageType(typeId) {
        // Remove previous selection
        document.querySelectorAll('#imageTypeGrid .mood-card').forEach(card => {
            card.classList.remove('selected');
        });
        
        // Add selection to clicked card
        document.querySelector(`[data-type-id="${typeId}"]`).classList.add('selected');
        
        this.selectedImageType = typeId;
        
        // Move to next step
        setTimeout(() => {
            this.nextStep();
        }, 500);
    }
    
    nextStep() {
        // Hide current step
        this.hideCurrentStep();
        
        // Move to next step
        this.currentStep++;
        
        // Show next step
        this.showCurrentStep();
        
        // Update step indicator
        this.updateStepIndicator();
    }
    
    hideCurrentStep() {
        const steps = ['moodSelection', 'topicSelection', 'imageTypeSelection', 'inspirationInput', 'promptSelection'];
        steps.forEach(step => {
            document.getElementById(step).classList.add('hidden');
        });
    }
    
    showCurrentStep() {
        const stepMap = {
            1: 'moodSelection',
            2: 'topicSelection',
            3: 'imageTypeSelection',
            4: 'inspirationInput',
            5: 'promptSelection'
        };
        
        const currentStepId = stepMap[this.currentStep];
        if (currentStepId) {
            document.getElementById(currentStepId).classList.remove('hidden');
            
            // Load data for the current step
            if (this.currentStep === 2) {
                this.loadTopicCategories();
            } else if (this.currentStep === 3) {
                this.loadImageTypes();
            }
        }
    }
    
    updateStepIndicator() {
        for (let i = 1; i <= 5; i++) {
            const step = document.getElementById(`step${i}`);
            const line = document.getElementById(`line${i}`);

            if (i < this.currentStep) {
                step.classList.add('completed');
                step.classList.remove('active');
                if (line) line.classList.add('completed');
            } else if (i === this.currentStep) {
                step.classList.add('active');
                step.classList.remove('completed');
            } else {
                step.classList.remove('active', 'completed');
                if (line) line.classList.remove('completed');
            }
        }
    }

    async generatePrompts() {
        if (!this.selectedMood || !this.selectedTopic || !this.selectedImageType || !this.inspiration.trim()) {
            alert('Please complete all steps before generating prompts.');
            return;
        }

        // Show loading state
        const btn = document.getElementById('generatePromptsBtn');
        const originalText = btn.innerHTML;
        btn.innerHTML = '🧠 GENERATING...';
        btn.disabled = true;

        try {
            const response = await fetch('mood_api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'generate_prompts',
                    mood: this.selectedMood,
                    topic: this.selectedTopic,
                    imageType: this.selectedImageType,
                    inspiration: this.inspiration,
                    count: 25
                })
            });

            const data = await response.json();

            if (data.success) {
                this.generatedPrompts = data.prompts;
                this.renderPrompts();
                this.nextStep();
            } else {
                throw new Error(data.error || 'Failed to generate prompts');
            }
        } catch (error) {
            console.error('Error generating prompts:', error);
            alert('Failed to generate prompts. Please try again.');
        } finally {
            btn.innerHTML = originalText;
            btn.disabled = false;
        }
    }

    renderPrompts() {
        const container = document.getElementById('promptGrid');
        const countElement = document.getElementById('promptCount');
        const progressElement = document.getElementById('promptProgress');

        container.innerHTML = '';
        countElement.textContent = `${this.generatedPrompts.length} prompts`;

        // Animate progress bar
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += 4;
            progressElement.style.width = `${progress}%`;
            if (progress >= 100) {
                clearInterval(progressInterval);
            }
        }, 50);

        this.generatedPrompts.forEach((prompt, index) => {
            const promptCard = document.createElement('div');
            promptCard.className = 'prompt-card p-4 rounded-lg';
            promptCard.dataset.promptIndex = index;

            // Get strategy color
            const strategyColors = {
                mood_focused: 'cyber-blue',
                topic_focused: 'cyber-green',
                image_focused: 'cyber-purple',
                inspiration_focused: 'cyber-pink'
            };

            const strategyColor = strategyColors[prompt.strategy] || 'cyber-blue';

            promptCard.innerHTML = `
                <div class="flex justify-between items-start mb-3">
                    <span class="text-xs bg-${strategyColor}/20 text-${strategyColor} px-2 py-1 rounded">
                        ${prompt.strategy.replace('_', ' ').toUpperCase()}
                    </span>
                    <span class="text-xs text-gray-400">#${index + 1}</span>
                </div>
                <p class="text-sm text-gray-200 leading-relaxed">${prompt.content}</p>
                <div class="mt-3 flex flex-wrap gap-1">
                    <span class="text-xs bg-gray-700 text-gray-300 px-2 py-1 rounded">${prompt.mood}</span>
                    <span class="text-xs bg-gray-700 text-gray-300 px-2 py-1 rounded">${prompt.topic}</span>
                    <span class="text-xs bg-gray-700 text-gray-300 px-2 py-1 rounded">${prompt.image_type}</span>
                </div>
            `;

            promptCard.addEventListener('click', () => {
                this.selectPrompt(index);
            });

            container.appendChild(promptCard);

            // Animate card appearance
            setTimeout(() => {
                promptCard.style.opacity = '0';
                promptCard.style.transform = 'translateY(20px)';
                promptCard.style.transition = 'all 0.3s ease';

                setTimeout(() => {
                    promptCard.style.opacity = '1';
                    promptCard.style.transform = 'translateY(0)';
                }, index * 50);
            }, 100);
        });
    }

    selectPrompt(index) {
        // Remove previous selection
        document.querySelectorAll('.prompt-card').forEach(card => {
            card.classList.remove('selected');
        });

        // Add selection to clicked card
        document.querySelector(`[data-prompt-index="${index}"]`).classList.add('selected');

        this.selectedPrompt = this.generatedPrompts[index];

        // Show generate video button
        document.getElementById('generateVideoBtn').classList.remove('hidden');

        // Animate button appearance
        const btn = document.getElementById('generateVideoBtn');
        btn.style.opacity = '0';
        btn.style.transform = 'translateY(20px)';

        setTimeout(() => {
            btn.style.transition = 'all 0.3s ease';
            btn.style.opacity = '1';
            btn.style.transform = 'translateY(0)';
        }, 100);
    }

    async generateVideo() {
        if (!this.selectedPrompt) {
            alert('Please select a prompt first.');
            return;
        }

        // Hide prompt selection and show progress
        document.getElementById('promptSelection').classList.add('hidden');
        document.getElementById('progressSection').classList.remove('hidden');

        // Start video generation using new unified API
        try {
            console.log('Starting video generation with:', {
                mood: this.selectedMood,
                topic: this.selectedTopic,
                inspiration: [this.selectedImageType, this.inspiration],
                custom_prompt: this.selectedPrompt.content
            });

            const response = await fetch('api_unified.php?endpoint=generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    mood: this.selectedMood,
                    topic: this.selectedTopic,
                    inspiration: [this.selectedImageType, this.inspiration],
                    custom_prompt: this.selectedPrompt.content,
                    video_quality: 'standard',
                    video_length: 30
                })
            });

            console.log('API Response status:', response.status);

            let data;
            const responseText = await response.text();
            console.log('Raw API response:', responseText);

            try {
                data = JSON.parse(responseText);
            } catch (parseError) {
                console.error('JSON parse error:', parseError);
                console.error('Response was:', responseText);
                throw new Error('Invalid response from server. Please check console for details.');
            }

            if (data.success) {
                this.trackVideoProgress(data.data.job_id);
            } else {
                // Handle authentication error specifically
                if (response.status === 401) {
                    throw new Error('Please log in first to generate videos. Redirecting to demo mode...');
                }
                throw new Error(data.error || 'Failed to start video generation');
            }
        } catch (error) {
            console.error('Error generating video:', error);

            // For demo purposes, if authentication fails, try demo mode
            if (error.message.includes('log in first') || error.message.includes('Authentication required') ||
                error.message.includes('Internal server error') || response.status === 401) {
                console.log('Authentication failed, trying demo mode...');
                this.tryDemoMode();
            } else {
                alert('Failed to start video generation: ' + error.message);
                // Show prompt selection again
                document.getElementById('promptSelection').classList.remove('hidden');
                document.getElementById('progressSection').classList.add('hidden');
            }
        }
    }

    // Try demo mode with API
    async tryDemoMode() {
        try {
            console.log('Trying demo mode with API...');

            const response = await fetch('api_unified.php?endpoint=generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    mood: this.selectedMood,
                    topic: this.selectedTopic,
                    inspiration: [this.selectedImageType, this.inspiration],
                    custom_prompt: this.selectedPrompt.content,
                    video_quality: 'standard',
                    video_length: 30,
                    demo_mode: true
                })
            });

            const responseText = await response.text();
            console.log('Demo mode API response:', responseText);

            let data;
            try {
                data = JSON.parse(responseText);
            } catch (parseError) {
                console.error('Demo mode JSON parse error:', parseError);
                throw new Error('Demo mode API error');
            }

            if (data.success && data.data && data.data.job_id) {
                console.log('Demo mode successful, tracking job:', data.data.job_id);
                this.trackVideoProgress(data.data.job_id);
            } else {
                throw new Error('Demo mode failed');
            }
        } catch (error) {
            console.error('Demo mode failed, trying simple API:', error);
            this.trySimpleAPI();
        }
    }

    // Try simple API as fallback
    async trySimpleAPI() {
        try {
            console.log('Trying simple API fallback...');

            const response = await fetch('test_api_simple.php?endpoint=generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    mood: this.selectedMood,
                    topic: this.selectedTopic,
                    inspiration: [this.selectedImageType, this.inspiration],
                    custom_prompt: this.selectedPrompt.content
                })
            });

            const responseText = await response.text();
            console.log('Simple API response:', responseText);

            let data;
            try {
                data = JSON.parse(responseText);
            } catch (parseError) {
                console.error('Simple API JSON parse error:', parseError);
                throw new Error('Simple API error');
            }

            if (data.success && data.data && data.data.job_id) {
                console.log('Simple API successful, tracking job:', data.data.job_id);
                this.trackSimpleAPIProgress(data.data.job_id);
            } else {
                throw new Error('Simple API failed');
            }
        } catch (error) {
            console.error('Simple API failed, trying working API:', error);
            this.tryWorkingAPI();
        }
    }

    // Try working API as final fallback
    async tryWorkingAPI() {
        try {
            console.log('Trying working API fallback...');

            const response = await fetch('working_api.php?endpoint=generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    mood: this.selectedMood,
                    topic: this.selectedTopic,
                    inspiration: [this.selectedImageType, this.inspiration],
                    custom_prompt: this.selectedPrompt.content
                })
            });

            const responseText = await response.text();
            console.log('Working API response:', responseText);

            let data;
            try {
                data = JSON.parse(responseText);
            } catch (parseError) {
                console.error('Working API JSON parse error:', parseError);
                throw new Error('Working API error');
            }

            if (data.success && data.data && data.data.job_id) {
                console.log('Working API successful, tracking job:', data.data.job_id);
                this.trackWorkingAPIProgress(data.data.job_id);
            } else {
                throw new Error('Working API failed');
            }
        } catch (error) {
            console.error('Working API failed, falling back to local demo:', error);
            alert('Demo Mode: Showing sample video generation process...');
            this.startDemoGeneration();
        }
    }

    // Track progress using working API
    trackWorkingAPIProgress(jobId) {
        const progressBar = document.getElementById('videoProgress');
        const statusText = document.getElementById('statusText');

        const checkProgress = async () => {
            try {
                const response = await fetch(`working_api.php?endpoint=generate/status/${jobId}`);
                const responseText = await response.text();

                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('Working API progress JSON parse error:', parseError);
                    throw new Error('Invalid response from working API');
                }

                if (data.success) {
                    const progress = data.data.progress || 0;
                    progressBar.style.width = `${progress}%`;
                    statusText.textContent = data.data.message || 'Processing...';

                    if (data.data.status === 'completed') {
                        this.showVideoResult(data.data);
                    } else if (data.data.status === 'failed') {
                        throw new Error(data.data.error_message || 'Video generation failed');
                    } else {
                        // Continue checking
                        setTimeout(checkProgress, 2000);
                    }
                } else {
                    throw new Error(data.error || 'Failed to check progress');
                }
            } catch (error) {
                console.error('Error checking working API progress:', error);
                statusText.textContent = 'Error occurred, falling back to demo...';

                // Fall back to local demo
                setTimeout(() => {
                    this.showDemoResult();
                }, 2000);
            }
        };

        checkProgress();
    }

    // Track progress using simple API
    trackSimpleAPIProgress(jobId) {
        const progressBar = document.getElementById('videoProgress');
        const statusText = document.getElementById('statusText');

        const checkProgress = async () => {
            try {
                const response = await fetch(`test_api_simple.php?endpoint=generate/status/${jobId}`);
                const responseText = await response.text();

                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('Simple API progress JSON parse error:', parseError);
                    throw new Error('Invalid response from simple API');
                }

                if (data.success) {
                    const progress = data.data.progress || 0;
                    progressBar.style.width = `${progress}%`;
                    statusText.textContent = data.data.message || 'Processing...';

                    if (data.data.status === 'completed') {
                        this.showVideoResult(data.data);
                    } else if (data.data.status === 'failed') {
                        throw new Error(data.data.error_message || 'Video generation failed');
                    } else {
                        // Continue checking
                        setTimeout(checkProgress, 2000);
                    }
                } else {
                    throw new Error(data.error || 'Failed to check progress');
                }
            } catch (error) {
                console.error('Error checking simple API progress:', error);
                statusText.textContent = 'Error occurred, falling back to demo...';

                // Fall back to local demo
                setTimeout(() => {
                    this.showDemoResult();
                }, 2000);
            }
        };

        checkProgress();
    }

    // Demo generation for when authentication is not available
    startDemoGeneration() {
        console.log('Starting demo generation...');

        // Simulate a job ID
        const demoJobId = 'demo_' + Date.now();

        // Start demo progress tracking
        this.trackDemoProgress(demoJobId);
    }

    trackDemoProgress(jobId) {
        const progressBar = document.getElementById('videoProgress');
        const statusText = document.getElementById('statusText');

        let progress = 0;
        const stages = [
            { progress: 10, message: 'Analyzing mood and topic...', delay: 1000 },
            { progress: 30, message: 'Generating AI prompts...', delay: 2000 },
            { progress: 50, message: 'Creating video content...', delay: 2000 },
            { progress: 70, message: 'Adding visual effects...', delay: 1500 },
            { progress: 90, message: 'Finalizing video...', delay: 1000 },
            { progress: 100, message: 'Video generation complete!', delay: 500 }
        ];

        let currentStage = 0;

        const updateProgress = () => {
            if (currentStage < stages.length) {
                const stage = stages[currentStage];
                progress = stage.progress;
                progressBar.style.width = `${progress}%`;
                statusText.textContent = stage.message;

                if (progress === 100) {
                    // Show demo result
                    setTimeout(() => {
                        this.showDemoResult();
                    }, 1000);
                } else {
                    setTimeout(() => {
                        currentStage++;
                        updateProgress();
                    }, stage.delay);
                }
            }
        };

        updateProgress();
    }

    showDemoResult() {
        // Demo video URLs (use existing videos if available)
        const demoVideos = [
            'elephant_jungle_video.mp4',
            'real_elephant_video.mp4',
            'final_test_video.mp4'
        ];

        const randomVideo = demoVideos[Math.floor(Math.random() * demoVideos.length)];

        const result = {
            video_url: randomVideo,
            thumbnail_url: randomVideo.replace('.mp4', '_thumbnail.jpg'),
            generation_time: '2 minutes 30 seconds',
            status: 'completed'
        };

        this.showVideoResult(result);
    }

    async trackVideoProgress(jobId) {
        const progressBar = document.getElementById('videoProgress');
        const statusText = document.getElementById('statusText');

        const checkProgress = async () => {
            try {
                const response = await fetch(`api_unified.php?endpoint=generate/status/${jobId}`);

                let data;
                const responseText = await response.text();

                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('JSON parse error in progress check:', parseError);
                    console.error('Response was:', responseText);
                    throw new Error('Invalid response from server during progress check');
                }

                if (data.success) {
                    const progress = data.data.progress || 0;
                    progressBar.style.width = `${progress}%`;
                    statusText.textContent = data.data.message || 'Processing...';

                    if (data.data.status === 'completed') {
                        this.showVideoResult(data.data);
                    } else if (data.data.status === 'failed') {
                        throw new Error(data.data.error_message || 'Video generation failed');
                    } else {
                        // Continue checking
                        setTimeout(checkProgress, 2000);
                    }
                } else {
                    throw new Error(data.error || 'Failed to check progress');
                }
            } catch (error) {
                console.error('Error checking progress:', error);
                statusText.textContent = 'Error occurred during generation: ' + error.message;

                // If it's an authentication error, fall back to demo
                if (error.message.includes('Authentication') || error.message.includes('Invalid response')) {
                    console.log('Falling back to demo mode due to error');
                    setTimeout(() => {
                        this.showDemoResult();
                    }, 2000);
                }
            }
        };

        checkProgress();
    }

    showVideoResult(result) {
        const progressSection = document.getElementById('progressSection');
        progressSection.innerHTML = `
            <h3 class="text-xl font-semibold mb-4 text-center text-cyber-green">
                ✅ VIDEO GENERATION COMPLETE
            </h3>
            <div class="text-center space-y-4">
                <div class="bg-cyber-green/10 border border-cyber-green/30 rounded-lg p-6">
                    <h4 class="text-lg font-semibold text-cyber-green mb-2">Your Video is Ready!</h4>
                    <p class="text-gray-300 mb-4">Generated using: ${this.selectedPrompt.strategy.replace('_', ' ')} strategy</p>
                    <div class="space-y-2">
                        ${result.video_url ? `
                            <a href="${result.video_url}" target="_blank"
                               class="cyber-button inline-block px-6 py-3 rounded-lg font-bold mr-2">
                                🎬 VIEW VIDEO
                            </a>
                        ` : ''}
                        ${result.thumbnail_url ? `
                            <a href="${result.thumbnail_url}" target="_blank"
                               class="cyber-button inline-block px-6 py-3 rounded-lg font-bold mr-2">
                                🖼️ VIEW THUMBNAIL
                            </a>
                        ` : ''}
                        <button onclick="location.reload()"
                                class="cyber-button px-6 py-3 rounded-lg font-bold">
                            🔄 CREATE ANOTHER
                        </button>
                    </div>
                </div>
                <div class="text-sm text-gray-400">
                    <p>Mood: ${this.selectedMood} | Topic: ${this.selectedTopic} | Style: ${this.selectedImageType}</p>
                    <p>Generation time: ${result.generation_time || 'N/A'}</p>
                </div>
            </div>
        `;
    }

    mapMoodToBackground(mood) {
        const moodBackgroundMap = {
            euphoric: 'cyberpunk',
            inspiring: 'nature',
            playful: 'studio',
            confident: 'cyberpunk',
            adventurous: 'nature',
            serene: 'nature',
            contemplative: 'studio',
            nostalgic: 'home',
            meditative: 'nature',
            melancholic: 'home',
            passionate: 'studio',
            heroic: 'cyberpunk',
            dramatic: 'studio',
            intense: 'cyberpunk',
            rebellious: 'cyberpunk',
            mysterious: 'space',
            dark: 'space',
            suspenseful: 'space',
            gothic: 'space',
            eerie: 'space',
            romantic: 'home',
            sensual: 'home',
            heartfelt: 'home',
            bittersweet: 'home',
            yearning: 'nature',
            cyberpunk: 'cyberpunk',
            futuristic: 'space',
            sci_fi: 'space',
            digital: 'cyberpunk',
            retro_futuristic: 'cyberpunk'
        };

        return moodBackgroundMap[mood] || 'studio';
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new MoodVideoGenerator();
});
