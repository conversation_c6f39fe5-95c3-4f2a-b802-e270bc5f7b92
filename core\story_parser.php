<?php
/**
 * Story Parser - Converts structured content into character dialogues
 * Part of Sutradhar Engine
 */

class StoryParser {
    private $config;
    private $styles;
    private $promptTokens;

    public function __construct() {
        $this->loadConfig();
    }

    private function loadConfig() {
        $configPath = __DIR__ . '/../config/settings.json';
        $this->config = json_decode(file_get_contents($configPath), true);
        $this->styles = $this->config['styles'];
    }

    /**
     * Parse content based on flow type and style
     */
    public function parseContent($content, $flowType, $style, $voicePack) {
        $parsedContent = [
            'flow_type' => $flowType,
            'style' => $style,
            'voice_pack' => $voicePack,
            'segments' => [],
            'metadata' => []
        ];

        // Load flow template
        $flowTemplate = $this->loadFlowTemplate($flowType);
        
        // Apply style transformations
        $styledContent = $this->applyStyleTransformations($content, $style);
        
        // Parse into segments based on flow type
        switch ($flowType) {
            case 'reel':
                $parsedContent['segments'] = $this->parseReel($styledContent, $flowTemplate);
                break;
            case 'audio_story':
                $parsedContent['segments'] = $this->parseAudioStory($styledContent, $flowTemplate);
                break;
            case 'meme_rant':
                $parsedContent['segments'] = $this->parseMemeRant($styledContent, $flowTemplate);
                break;
        }

        // Add metadata
        $parsedContent['metadata'] = [
            'total_segments' => count($parsedContent['segments']),
            'estimated_duration' => $this->estimateDuration($parsedContent['segments']),
            'prompt_tokens' => $this->getPromptTokens($style),
            'character_count' => strlen($content)
        ];

        return $parsedContent;
    }

    /**
     * Load flow template from JSON file
     */
    private function loadFlowTemplate($flowType) {
        $templatePath = __DIR__ . "/../flows/{$flowType}.flow.json";
        
        if (!file_exists($templatePath)) {
            throw new Exception("Flow template not found: {$flowType}");
        }

        return json_decode(file_get_contents($templatePath), true);
    }

    /**
     * Apply style-specific transformations to content
     */
    private function applyStyleTransformations($content, $style) {
        $transformations = [
            'funny' => [
                'add_sarcasm' => true,
                'include_humor' => true,
                'tone' => 'comedic'
            ],
            'desi' => [
                'use_hinglish' => true,
                'cultural_references' => true,
                'tone' => 'relatable'
            ],
            'emotional' => [
                'heartfelt_language' => true,
                'emotional_depth' => true,
                'tone' => 'touching'
            ],
            'bollywood' => [
                'dramatic_flair' => true,
                'movie_references' => true,
                'tone' => 'over_the_top'
            ]
        ];

        $styleConfig = $transformations[$style] ?? [];
        
        // Apply transformations based on style
        if ($styleConfig['use_hinglish'] ?? false) {
            $content = $this->addHinglishElements($content);
        }
        
        if ($styleConfig['add_sarcasm'] ?? false) {
            $content = $this->addSarcasm($content);
        }
        
        if ($styleConfig['cultural_references'] ?? false) {
            $content = $this->addCulturalReferences($content);
        }

        return $content;
    }

    /**
     * Parse content for reel format
     */
    private function parseReel($content, $template) {
        $segments = [];
        $sentences = $this->splitIntoSentences($content);
        
        // Reel format: Hook, Content, Call-to-action
        $hookSentences = array_slice($sentences, 0, 2);
        $contentSentences = array_slice($sentences, 2, -1);
        $ctaSentences = array_slice($sentences, -1);

        // Hook segment
        $segments[] = [
            'type' => 'hook',
            'text' => implode(' ', $hookSentences),
            'voice_style' => 'energetic',
            'duration_hint' => 'fast',
            'background_music' => 'upbeat'
        ];

        // Content segments (split for pacing)
        foreach (array_chunk($contentSentences, 2) as $chunk) {
            $segments[] = [
                'type' => 'content',
                'text' => implode(' ', $chunk),
                'voice_style' => 'normal',
                'duration_hint' => 'medium',
                'background_music' => 'background'
            ];
        }

        // Call-to-action
        if (!empty($ctaSentences)) {
            $segments[] = [
                'type' => 'cta',
                'text' => implode(' ', $ctaSentences),
                'voice_style' => 'persuasive',
                'duration_hint' => 'slow',
                'background_music' => 'outro'
            ];
        }

        return $segments;
    }

    /**
     * Parse content for audio story format
     */
    private function parseAudioStory($content, $template) {
        $segments = [];
        $paragraphs = explode("\n\n", $content);

        foreach ($paragraphs as $index => $paragraph) {
            if (empty(trim($paragraph))) continue;

            $segments[] = [
                'type' => $index === 0 ? 'introduction' : ($index === count($paragraphs) - 1 ? 'conclusion' : 'narrative'),
                'text' => trim($paragraph),
                'voice_style' => 'storytelling',
                'duration_hint' => 'natural',
                'background_music' => 'ambient',
                'pause_after' => 1.0
            ];
        }

        return $segments;
    }

    /**
     * Parse content for meme rant format
     */
    private function parseMemeRant($content, $template) {
        $segments = [];
        $sentences = $this->splitIntoSentences($content);

        foreach ($sentences as $index => $sentence) {
            $segments[] = [
                'type' => 'rant',
                'text' => $sentence,
                'voice_style' => $index % 2 === 0 ? 'aggressive' : 'sarcastic',
                'duration_hint' => 'varied',
                'background_music' => 'minimal',
                'emphasis' => $this->detectEmphasis($sentence)
            ];
        }

        return $segments;
    }

    /**
     * Split text into sentences
     */
    private function splitIntoSentences($text) {
        // Handle both English and Hinglish punctuation
        $sentences = preg_split('/[.!?।]+/', $text, -1, PREG_SPLIT_NO_EMPTY);
        return array_map('trim', $sentences);
    }

    /**
     * Add Hinglish elements to text
     */
    private function addHinglishElements($text) {
        $hinglishReplacements = [
            'very good' => 'bahut accha',
            'what is this' => 'ye kya hai',
            'come on' => 'arre yaar',
            'really' => 'sach mein',
            'okay' => 'theek hai',
            'brother' => 'bhai',
            'sister' => 'didi'
        ];

        foreach ($hinglishReplacements as $english => $hinglish) {
            $text = str_ireplace($english, $hinglish, $text);
        }

        return $text;
    }

    /**
     * Add sarcastic elements
     */
    private function addSarcasm($text) {
        // Add sarcastic interjections
        $sarcasticPhrases = ['obviously', 'sure sure', 'wow', 'great job'];
        $randomPhrase = $sarcasticPhrases[array_rand($sarcasticPhrases)];
        
        return $randomPhrase . ', ' . $text;
    }

    /**
     * Add cultural references
     */
    private function addCulturalReferences($text) {
        $culturalPhrases = [
            'like Amitabh Bachchan says',
            'as our dadi used to say',
            'just like in Bollywood movies',
            'typical Indian family style'
        ];
        
        $randomPhrase = $culturalPhrases[array_rand($culturalPhrases)];
        return $text . ' - ' . $randomPhrase . '.';
    }

    /**
     * Detect emphasis in text
     */
    private function detectEmphasis($text) {
        $emphasis = [];
        
        if (strpos($text, '!') !== false) {
            $emphasis[] = 'exclamation';
        }
        
        if (strpos($text, '?') !== false) {
            $emphasis[] = 'question';
        }
        
        if (preg_match('/[A-Z]{2,}/', $text)) {
            $emphasis[] = 'caps';
        }

        return $emphasis;
    }

    /**
     * Estimate total duration of content
     */
    private function estimateDuration($segments) {
        $totalDuration = 0;
        
        foreach ($segments as $segment) {
            $wordCount = str_word_count($segment['text']);
            $wordsPerMinute = 150; // Average speaking rate
            $segmentDuration = ($wordCount / $wordsPerMinute) * 60;
            
            // Apply duration hints
            switch ($segment['duration_hint'] ?? 'medium') {
                case 'fast':
                    $segmentDuration *= 0.8;
                    break;
                case 'slow':
                    $segmentDuration *= 1.3;
                    break;
                case 'varied':
                    $segmentDuration *= 1.1;
                    break;
            }
            
            $totalDuration += $segmentDuration;
            $totalDuration += $segment['pause_after'] ?? 0.5;
        }

        return round($totalDuration, 1);
    }

    /**
     * Get prompt tokens for style
     */
    private function getPromptTokens($style) {
        return $this->styles[$style]['prompt_tokens'] ?? [];
    }

    /**
     * Process uploaded file content
     */
    public function processFile($filePath) {
        if (!file_exists($filePath)) {
            throw new Exception("File not found: {$filePath}");
        }

        $extension = pathinfo($filePath, PATHINFO_EXTENSION);
        
        switch (strtolower($extension)) {
            case 'txt':
                return file_get_contents($filePath);
            case 'json':
                $data = json_decode(file_get_contents($filePath), true);
                return $data['content'] ?? $data['text'] ?? json_encode($data);
            case 'md':
                return $this->parseMarkdown(file_get_contents($filePath));
            default:
                throw new Exception("Unsupported file type: {$extension}");
        }
    }

    /**
     * Parse markdown content
     */
    private function parseMarkdown($markdown) {
        // Simple markdown to text conversion
        $text = preg_replace('/^#+\s+/m', '', $markdown); // Remove headers
        $text = preg_replace('/\*\*(.*?)\*\*/', '$1', $text); // Remove bold
        $text = preg_replace('/\*(.*?)\*/', '$1', $text); // Remove italic
        $text = preg_replace('/\[([^\]]+)\]\([^\)]+\)/', '$1', $text); // Remove links
        
        return trim($text);
    }
}
