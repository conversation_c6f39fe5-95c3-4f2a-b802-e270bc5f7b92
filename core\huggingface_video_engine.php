<?php
/**
 * Advanced Hugging Face Video Engine
 * Generates 30-second videos using Hugging Face models and FFmpeg
 */

class HuggingFaceVideoEngine {
    private $config;
    private $apiKey;
    private $tempDir;
    private $outputDir;
    private $models;

    public function __construct() {
        $this->loadConfig();
        $this->tempDir = __DIR__ . '/../temp/';
        $this->outputDir = __DIR__ . '/../data/output_history/';
        
        if (!is_dir($this->tempDir)) {
            mkdir($this->tempDir, 0755, true);
        }
    }

    private function loadConfig() {
        $configPath = __DIR__ . '/../config/settings.json';

        if (!file_exists($configPath)) {
            echo "❌ Config file not found: $configPath\n";
            $this->config = [];
            $this->apiKey = '';
            $this->models = [];
            return;
        }

        $configContent = file_get_contents($configPath);
        if ($configContent === false) {
            echo "❌ Could not read config file: $configPath\n";
            $this->config = [];
            $this->apiKey = '';
            $this->models = [];
            return;
        }

        $this->config = json_decode($configContent, true);
        if ($this->config === null) {
            echo "❌ Invalid JSON in config file: $configPath\n";
            $this->config = [];
            $this->apiKey = '';
            $this->models = [];
            return;
        }

        $this->apiKey = $this->config['api_keys']['huggingface'] ?? '';
        $this->models = $this->config['video_generation']['models'] ?? [];

        echo "✅ Config loaded successfully\n";
    }

    /**
     * Generate 30-second video with Hugging Face models
     */
    public function generateAdvancedVideo($segments, $background, $style, $jobId) {
        echo "🎬 Generating 30-second video with Hugging Face models...\n";
        
        $outputFile = $this->tempDir . $jobId . '_hf_video.mp4';
        
        try {
            // Step 1: Generate scene descriptions for each segment
            $sceneDescriptions = $this->generateSceneDescriptions($segments, $style, $background);
            
            // Step 2: Generate images for each scene using Stable Diffusion
            $sceneImages = $this->generateSceneImages($sceneDescriptions, $jobId);
            
            // Step 3: Generate video frames with animations
            $videoFrames = $this->generateVideoFrames($sceneImages, $segments, $style, $jobId);
            
            // Step 4: Create video with FFmpeg
            $success = $this->createVideoWithFFmpeg($videoFrames, $outputFile, 30);
            
            if ($success && file_exists($outputFile)) {
                echo "✅ Advanced video generated: " . round(filesize($outputFile)/1024, 1) . " KB\n";
                return $outputFile;
            }
            
        } catch (Exception $e) {
            echo "❌ Hugging Face video generation failed: " . $e->getMessage() . "\n";
        }
        
        // Fallback to enhanced local generation
        return $this->generateEnhancedLocalVideo($segments, $background, $style, $outputFile);
    }

    /**
     * Generate scene descriptions for video
     */
    private function generateSceneDescriptions($segments, $style, $background) {
        $descriptions = [];
        
        $stylePrompts = [
            'funny' => 'colorful, cartoon-style, humorous, bright colors, comic elements',
            'desi' => 'traditional Indian, warm colors, cultural elements, family setting',
            'emotional' => 'soft lighting, warm tones, emotional atmosphere, gentle colors',
            'bollywood' => 'dramatic, vibrant colors, cinematic, Bollywood movie style'
        ];
        
        $backgroundPrompts = [
            'home' => 'cozy Indian home interior, living room, family atmosphere',
            'office' => 'modern office space, professional setting, clean design',
            'nature' => 'beautiful natural landscape, outdoor setting, greenery',
            'city' => 'urban cityscape, modern buildings, bustling environment',
            'traditional' => 'traditional Indian architecture, cultural setting'
        ];
        
        $styleDesc = $stylePrompts[$style] ?? 'modern, clean, professional';
        $bgDesc = $backgroundPrompts[$background] ?? 'neutral background';
        
        foreach ($segments as $index => $segment) {
            $text = $segment['text'] ?? '';
            
            // Create detailed scene description
            $sceneDesc = "A {$styleDesc} scene showing {$bgDesc}. ";
            
            // Add text-based visual elements
            if (strpos($text, 'happy') !== false || strpos($text, 'joy') !== false) {
                $sceneDesc .= "Happy, joyful atmosphere with bright lighting. ";
            } elseif (strpos($text, 'sad') !== false || strpos($text, 'cry') !== false) {
                $sceneDesc .= "Emotional, touching scene with soft lighting. ";
            } elseif (strpos($text, 'angry') !== false || strpos($text, 'mad') !== false) {
                $sceneDesc .= "Intense, dramatic scene with strong contrasts. ";
            } else {
                $sceneDesc .= "Balanced, engaging scene with good composition. ";
            }
            
            $sceneDesc .= "High quality, detailed, professional photography, 4K resolution.";
            
            $descriptions[] = [
                'text' => $text,
                'description' => $sceneDesc,
                'index' => $index
            ];
        }
        
        return $descriptions;
    }

    /**
     * Generate images using Hugging Face Stable Diffusion
     */
    private function generateSceneImages($sceneDescriptions, $jobId) {
        $images = [];
        
        if (empty($this->apiKey)) {
            echo "⚠️  Hugging Face API key not configured, using local generation\n";
            return $this->generateLocalSceneImages($sceneDescriptions, $jobId);
        }
        
        foreach ($sceneDescriptions as $scene) {
            try {
                echo "  Generating image for scene {$scene['index']}...\n";
                
                $imageFile = $this->generateImageWithHuggingFace(
                    $scene['description'],
                    $jobId . '_scene_' . $scene['index']
                );
                
                if ($imageFile && file_exists($imageFile)) {
                    $images[] = [
                        'file' => $imageFile,
                        'text' => $scene['text'],
                        'index' => $scene['index']
                    ];
                    echo "    ✅ Generated: " . basename($imageFile) . "\n";
                } else {
                    echo "    ❌ Failed to generate image for scene {$scene['index']}\n";
                    // Generate local fallback
                    $localImage = $this->generateLocalImage($scene, $jobId);
                    if ($localImage) {
                        $images[] = [
                            'file' => $localImage,
                            'text' => $scene['text'],
                            'index' => $scene['index']
                        ];
                    }
                }
                
            } catch (Exception $e) {
                echo "    ❌ Error generating scene {$scene['index']}: " . $e->getMessage() . "\n";
                // Generate local fallback
                $localImage = $this->generateLocalImage($scene, $jobId);
                if ($localImage) {
                    $images[] = [
                        'file' => $localImage,
                        'text' => $scene['text'],
                        'index' => $scene['index']
                    ];
                }
            }
        }
        
        return $images;
    }

    /**
     * Generate image using Hugging Face API with fallback models
     */
    private function generateImageWithHuggingFace($prompt, $filename) {
        if (empty($this->apiKey)) {
            echo "    ⚠️ API key not available\n";
            return false;
        }

        // Try multiple models for better success rate
        $models = [
            $this->models['text_to_image'] ?? 'stabilityai/stable-diffusion-2-1',
            $this->models['text_to_image_backup'] ?? 'runwayml/stable-diffusion-v1-5',
            'CompVis/stable-diffusion-v1-4', // Additional fallback
            'stabilityai/stable-diffusion-xl-base-1.0' // Another option
        ];

        foreach ($models as $modelIndex => $model) {
            echo "    🔄 Trying model: $model\n";

            $result = $this->tryGenerateWithModel($model, $prompt, $filename);
            if ($result) {
                echo "    ✅ Success with model: $model\n";
                return $result;
            }

            // Don't try all models if we're running out of time
            if ($modelIndex >= 1) {
                echo "    ⏭️ Skipping remaining models to save time\n";
                break;
            }
        }

        echo "    ❌ All Hugging Face models failed\n";
        return false;
    }

    /**
     * Try to generate image with a specific model
     */
    private function tryGenerateWithModel($model, $prompt, $filename) {
        $url = "https://api-inference.huggingface.co/models/$model";

        // Optimize prompt for better results
        $optimizedPrompt = $this->optimizePrompt($prompt);

        $data = json_encode([
            'inputs' => $optimizedPrompt,
            'parameters' => [
                'num_inference_steps' => 10, // Further reduced for speed
                'guidance_scale' => 7.0,
                'width' => 720,
                'height' => 1280
            ]
        ]);

        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => [
                    'Authorization: Bearer ' . $this->apiKey,
                    'Content-Type: application/json',
                    'Content-Length: ' . strlen($data),
                    'User-Agent: SutradharEngine/1.0'
                ],
                'content' => $data,
                'timeout' => 30 // Shorter timeout per model
            ]
        ]);

        try {
            $response = file_get_contents($url, false, $context);

            if ($response === false) {
                return false;
            }

            // Check for API errors
            if (strlen($response) < 1000) {
                $errorData = json_decode($response, true);
                if (isset($errorData['error'])) {
                    echo "      ⚠️ Model error: " . substr($errorData['error'], 0, 50) . "...\n";
                    return false;
                }
            }

            // Validate image response
            if (strlen($response) > 1000) {
                $header = substr($response, 0, 10);
                // Check for various image formats
                if (strpos($header, 'PNG') !== false ||
                    strpos($header, 'JFIF') !== false ||
                    ord($header[0]) == 0xFF ||
                    ord($header[0]) == 0x89) {

                    $imageFile = $this->tempDir . $filename . '.png';
                    $result = file_put_contents($imageFile, $response);

                    if ($result && file_exists($imageFile) && filesize($imageFile) > 1000) {
                        return $imageFile;
                    }
                }
            }

            return false;

        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Optimize prompt for better image generation
     */
    private function optimizePrompt($prompt) {
        // Add quality enhancers
        $qualityTerms = [
            'high quality',
            'detailed',
            'professional',
            '4k resolution',
            'sharp focus'
        ];

        // Remove any existing quality terms to avoid duplication
        $cleanPrompt = $prompt;
        foreach ($qualityTerms as $term) {
            $cleanPrompt = str_ireplace($term, '', $cleanPrompt);
        }

        // Add optimized quality terms
        $optimized = trim($cleanPrompt) . ', high quality, detailed, professional photography, sharp focus';

        // Limit prompt length
        if (strlen($optimized) > 200) {
            $optimized = substr($optimized, 0, 197) . '...';
        }

        return $optimized;
    }

    /**
     * Generate local scene images as fallback
     */
    private function generateLocalSceneImages($sceneDescriptions, $jobId) {
        $images = [];
        
        foreach ($sceneDescriptions as $scene) {
            $imageFile = $this->generateLocalImage($scene, $jobId);
            if ($imageFile) {
                $images[] = [
                    'file' => $imageFile,
                    'text' => $scene['text'],
                    'index' => $scene['index']
                ];
            }
        }
        
        return $images;
    }

    /**
     * Generate enhanced local image using GD
     */
    private function generateLocalImage($scene, $jobId) {
        if (!function_exists('imagecreatetruecolor')) {
            echo "    ❌ GD extension not available\n";
            return false;
        }

        $width = 720;
        $height = 1280;
        $image = imagecreatetruecolor($width, $height);

        if (!$image) {
            echo "    ❌ Failed to create image resource\n";
            return false;
        }

        // Enable anti-aliasing for better quality
        imageantialias($image, true);

        // Create enhanced gradient background
        $this->createEnhancedGradientBackground($image, $width, $height, $scene['index'], $scene['text']);

        // Add enhanced text overlay
        $this->addEnhancedTextOverlay($image, $scene['text'], $width, $height);

        // Add themed decorative elements
        $this->addThemedDecorativeElements($image, $width, $height, $scene['index'], $scene['text']);

        // Add subtle texture
        $this->addSubtleTexture($image, $width, $height);

        $imageFile = $this->tempDir . $jobId . '_local_scene_' . $scene['index'] . '.png';
        $result = imagepng($image, $imageFile, 6); // Compression level 6 for good quality/size balance
        imagedestroy($image);

        if ($result && file_exists($imageFile)) {
            echo "    ✅ Enhanced local image generated (" . round(filesize($imageFile)/1024, 1) . " KB)\n";
            return $imageFile;
        } else {
            echo "    ❌ Failed to save local image\n";
            return false;
        }
    }

    /**
     * Create enhanced gradient background based on content
     */
    private function createEnhancedGradientBackground($image, $width, $height, $sceneIndex, $text) {
        // Choose colors based on text content
        $gradients = $this->getContentBasedGradients($text);
        $gradient = $gradients[$sceneIndex % count($gradients)];

        // Create smooth gradient
        for ($y = 0; $y < $height; $y++) {
            $ratio = $y / $height;

            // Add some curve to the gradient
            $curvedRatio = sin($ratio * pi() / 2);

            $r = intval($gradient[0][0] * (1 - $curvedRatio) + $gradient[1][0] * $curvedRatio);
            $g = intval($gradient[0][1] * (1 - $curvedRatio) + $gradient[1][1] * $curvedRatio);
            $b = intval($gradient[0][2] * (1 - $curvedRatio) + $gradient[1][2] * $curvedRatio);

            $color = imagecolorallocate($image, $r, $g, $b);
            imageline($image, 0, $y, $width, $y, $color);
        }
    }

    /**
     * Get gradients based on text content
     */
    private function getContentBasedGradients($text) {
        $text = strtolower($text);

        // Nature themes
        if (strpos($text, 'nature') !== false || strpos($text, 'tree') !== false || strpos($text, 'forest') !== false) {
            return [
                [[34, 139, 34], [144, 238, 144]],   // Forest green
                [[85, 107, 47], [154, 205, 50]],    // Olive green
                [[46, 125, 50], [129, 199, 132]]    // Nature green
            ];
        }

        // Sky/sunset themes
        if (strpos($text, 'sky') !== false || strpos($text, 'sunset') !== false || strpos($text, 'bird') !== false) {
            return [
                [[255, 94, 77], [255, 154, 0]],     // Sunset orange
                [[135, 206, 250], [25, 25, 112]],   // Sky blue
                [[255, 182, 193], [255, 105, 180]]  // Pink sunset
            ];
        }

        // Mountain themes
        if (strpos($text, 'mountain') !== false || strpos($text, 'peak') !== false) {
            return [
                [[105, 105, 105], [169, 169, 169]], // Gray mountain
                [[139, 69, 19], [160, 82, 45]],     // Brown mountain
                [[70, 130, 180], [176, 196, 222]]   // Blue mountain
            ];
        }

        // Default gradients
        return [
            [[255, 182, 193], [255, 105, 180]], // Pink gradient
            [[135, 206, 250], [70, 130, 180]],  // Blue gradient
            [[255, 218, 185], [255, 160, 122]], // Orange gradient
            [[221, 160, 221], [186, 85, 211]],  // Purple gradient
            [[152, 251, 152], [50, 205, 50]]    // Green gradient
        ];
    }

    /**
     * Legacy gradient background method
     */
    private function createGradientBackground($image, $width, $height, $sceneIndex) {
        $this->createEnhancedGradientBackground($image, $width, $height, $sceneIndex, '');
    }

    /**
     * Add enhanced text overlay with better styling
     */
    private function addEnhancedTextOverlay($image, $text, $width, $height) {
        $words = explode(' ', $text);
        $lines = [];
        $currentLine = '';
        $maxCharsPerLine = 30; // Slightly longer lines

        foreach ($words as $word) {
            $testLine = $currentLine . ($currentLine ? ' ' : '') . $word;
            if (strlen($testLine) <= $maxCharsPerLine) {
                $currentLine = $testLine;
            } else {
                if ($currentLine) {
                    $lines[] = $currentLine;
                }
                $currentLine = $word;
            }
        }

        if ($currentLine) {
            $lines[] = $currentLine;
        }

        // Enhanced text styling
        $textBgColor = imagecolorallocatealpha($image, 0, 0, 0, 70); // More transparent
        $textColor = imagecolorallocate($image, 255, 255, 255);
        $shadowColor = imagecolorallocatealpha($image, 0, 0, 0, 30);

        $lineHeight = 45;
        $totalTextHeight = count($lines) * $lineHeight;
        $startY = ($height - $totalTextHeight) / 2;

        // Draw rounded background rectangle
        $padding = 25;
        $bgX1 = $padding;
        $bgY1 = $startY - $padding;
        $bgX2 = $width - $padding;
        $bgY2 = $startY + $totalTextHeight + $padding;

        // Create rounded rectangle effect
        imagefilledrectangle($image, $bgX1 + 5, $bgY1, $bgX2 - 5, $bgY2, $textBgColor);
        imagefilledrectangle($image, $bgX1, $bgY1 + 5, $bgX2, $bgY2 - 5, $textBgColor);

        // Draw text with shadow
        foreach ($lines as $index => $line) {
            $lineY = $startY + ($index * $lineHeight);
            $textX = ($width - strlen($line) * 12) / 2; // Center text

            // Draw shadow
            imagestring($image, 5, $textX + 2, $lineY + 2, $line, $shadowColor);
            // Draw main text
            imagestring($image, 5, $textX, $lineY, $line, $textColor);
        }
    }

    /**
     * Legacy text overlay method
     */
    private function addAdvancedTextOverlay($image, $text, $width, $height) {
        $this->addEnhancedTextOverlay($image, $text, $width, $height);
    }

    /**
     * Add themed decorative elements based on content
     */
    private function addThemedDecorativeElements($image, $width, $height, $sceneIndex, $text) {
        $text = strtolower($text);
        $decorColor = imagecolorallocatealpha($image, 255, 255, 255, 60);
        $accentColor = imagecolorallocatealpha($image, 255, 215, 0, 40); // Gold accent

        // Nature-themed elements
        if (strpos($text, 'nature') !== false || strpos($text, 'tree') !== false) {
            $this->addNatureElements($image, $width, $height, $decorColor);
        }
        // Sky/bird themed elements
        elseif (strpos($text, 'sky') !== false || strpos($text, 'bird') !== false) {
            $this->addSkyElements($image, $width, $height, $decorColor);
        }
        // Mountain themed elements
        elseif (strpos($text, 'mountain') !== false) {
            $this->addMountainElements($image, $width, $height, $decorColor);
        }
        // Default geometric elements
        else {
            $this->addGeometricElements($image, $width, $height, $sceneIndex, $decorColor, $accentColor);
        }
    }

    /**
     * Add nature-themed elements
     */
    private function addNatureElements($image, $width, $height, $color) {
        // Add tree-like shapes
        for ($i = 0; $i < 3; $i++) {
            $x = rand($width * 0.1, $width * 0.9);
            $y = $height * 0.8;
            $treeHeight = rand(80, 120);

            // Tree trunk
            imageline($image, $x, $y, $x, $y - $treeHeight, $color);

            // Tree crown (circle)
            $crownRadius = rand(25, 40);
            imageellipse($image, $x, $y - $treeHeight, $crownRadius * 2, $crownRadius * 2, $color);
        }
    }

    /**
     * Add sky-themed elements
     */
    private function addSkyElements($image, $width, $height, $color) {
        // Add cloud-like shapes
        for ($i = 0; $i < 4; $i++) {
            $x = rand($width * 0.1, $width * 0.9);
            $y = rand($height * 0.1, $height * 0.4);

            // Cloud (multiple overlapping circles)
            for ($j = 0; $j < 3; $j++) {
                $cloudX = $x + ($j * 15);
                $cloudY = $y + rand(-5, 5);
                $radius = rand(15, 25);
                imageellipse($image, $cloudX, $cloudY, $radius * 2, $radius * 2, $color);
            }
        }

        // Add bird-like shapes
        for ($i = 0; $i < 2; $i++) {
            $x = rand($width * 0.2, $width * 0.8);
            $y = rand($height * 0.2, $height * 0.5);

            // Simple bird shape (V)
            imageline($image, $x - 10, $y, $x, $y - 5, $color);
            imageline($image, $x, $y - 5, $x + 10, $y, $color);
        }
    }

    /**
     * Add mountain-themed elements
     */
    private function addMountainElements($image, $width, $height, $color) {
        // Add mountain silhouettes
        $points = [];
        $numPoints = 8;

        for ($i = 0; $i <= $numPoints; $i++) {
            $x = ($width / $numPoints) * $i;
            $y = $height * 0.7 + rand(-50, 20);
            $points[] = $x;
            $points[] = $y;
        }

        // Draw mountain line
        for ($i = 0; $i < count($points) - 2; $i += 2) {
            imageline($image, $points[$i], $points[$i + 1], $points[$i + 2], $points[$i + 3], $color);
        }
    }

    /**
     * Add geometric elements (default)
     */
    private function addGeometricElements($image, $width, $height, $sceneIndex, $decorColor, $accentColor) {
        switch ($sceneIndex % 3) {
            case 0:
                // Enhanced circles
                for ($i = 0; $i < 4; $i++) {
                    $x = rand(50, $width - 50);
                    $y = rand(50, $height - 50);
                    $radius = rand(20, 40);
                    imageellipse($image, $x, $y, $radius * 2, $radius * 2, $decorColor);
                    if ($i < 2) {
                        imageellipse($image, $x, $y, ($radius - 5) * 2, ($radius - 5) * 2, $accentColor);
                    }
                }
                break;
            case 1:
                // Artistic lines
                for ($i = 0; $i < 6; $i++) {
                    $x1 = rand(0, $width);
                    $y1 = rand(0, $height);
                    $x2 = $x1 + rand(-100, 100);
                    $y2 = $y1 + rand(-100, 100);
                    imageline($image, $x1, $y1, $x2, $y2, $decorColor);
                }
                break;
            case 2:
                // Modern rectangles
                for ($i = 0; $i < 3; $i++) {
                    $x1 = rand(50, $width - 150);
                    $y1 = rand(50, $height - 150);
                    $x2 = $x1 + rand(60, 120);
                    $y2 = $y1 + rand(60, 120);
                    imagerectangle($image, $x1, $y1, $x2, $y2, $decorColor);
                    if ($i < 2) {
                        imagefilledrectangle($image, $x1 + 5, $y1 + 5, $x1 + 15, $y1 + 15, $accentColor);
                    }
                }
                break;
        }
    }

    /**
     * Add subtle texture to the image
     */
    private function addSubtleTexture($image, $width, $height) {
        $textureColor = imagecolorallocatealpha($image, 255, 255, 255, 120);

        // Add random dots for texture
        for ($i = 0; $i < 100; $i++) {
            $x = rand(0, $width);
            $y = rand(0, $height);
            imagesetpixel($image, $x, $y, $textureColor);
        }
    }

    /**
     * Legacy decorative elements method
     */
    private function addDecorativeElements($image, $width, $height, $sceneIndex) {
        $this->addThemedDecorativeElements($image, $width, $height, $sceneIndex, '');
    }

    /**
     * Generate video frames with animations (optimized)
     */
    private function generateVideoFrames($sceneImages, $segments, $style, $jobId) {
        $frames = [];
        $fps = 15; // Reduced FPS for faster generation
        $totalDuration = 30; // 30 seconds
        $segmentDuration = $totalDuration / count($segments);
        $framesPerSegment = max(1, intval($segmentDuration * $fps));

        echo "  Generating video frames: {$framesPerSegment} frames per segment (FPS: $fps)\n";

        foreach ($sceneImages as $sceneIndex => $scene) {
            echo "    Processing scene {$sceneIndex}...\n";

            // Generate fewer frames but with better transitions
            $frameStep = max(1, intval($framesPerSegment / 10)); // Generate every nth frame

            for ($frame = 0; $frame < $framesPerSegment; $frame += $frameStep) {
                $frameFile = $this->generateOptimizedFrame(
                    $scene['file'],
                    $scene['text'],
                    $frame,
                    $framesPerSegment,
                    $sceneIndex,
                    $jobId
                );

                if ($frameFile) {
                    // Duplicate frame for smooth playback
                    for ($i = 0; $i < $frameStep && count($frames) < $framesPerSegment * count($sceneImages); $i++) {
                        $frames[] = $frameFile;
                    }
                }
            }
        }

        echo "  Generated " . count($frames) . " total frames\n";
        return $frames;
    }

    /**
     * Generate optimized frame (faster generation)
     */
    private function generateOptimizedFrame($baseImage, $text, $frame, $totalFrames, $sceneIndex, $jobId) {
        if (!function_exists('imagecreatetruecolor') || !file_exists($baseImage)) {
            return false;
        }

        // Load base image
        $base = imagecreatefrompng($baseImage);
        if (!$base) {
            return false;
        }

        $width = imagesx($base);
        $height = imagesy($base);

        // Create new frame
        $frame_image = imagecreatetruecolor($width, $height);
        imagecopy($frame_image, $base, 0, 0, 0, 0, $width, $height);

        // Add simple animations (reduced complexity)
        $progress = $frame / $totalFrames;
        $this->addSimpleAnimations($frame_image, $progress, $width, $height);

        // Save frame
        $frameFile = $this->tempDir . $jobId . "_frame_{$sceneIndex}_{$frame}.png";
        $result = imagepng($frame_image, $frameFile);

        imagedestroy($frame_image);
        imagedestroy($base);

        return $result ? $frameFile : false;
    }

    /**
     * Generate animated frame (legacy method)
     */
    private function generateAnimatedFrame($baseImage, $text, $frame, $totalFrames, $sceneIndex, $jobId) {
        // Use optimized version
        return $this->generateOptimizedFrame($baseImage, $text, $frame, $totalFrames, $sceneIndex, $jobId);
    }

    /**
     * Add simple animations (optimized for speed)
     */
    private function addSimpleAnimations($image, $progress, $width, $height) {
        // Simple fade effect
        $alpha = intval(50 + $progress * 50);
        $animColor = imagecolorallocatealpha($image, 255, 255, 255, $alpha);

        // Simple moving element
        $moveX = sin($progress * 2 * pi()) * 30;
        $centerX = $width / 2 + $moveX;
        $centerY = $height / 4;

        // Add simple circle
        imageellipse($image, $centerX, $centerY, 40, 40, $animColor);

        // Simple progress bar
        $progressWidth = $width * 0.6;
        $progressX = ($width - $progressWidth) / 2;
        $progressY = $height - 30;
        $progressFill = $progressWidth * $progress;

        $progressBg = imagecolorallocate($image, 80, 80, 80);
        $progressFg = imagecolorallocate($image, 0, 200, 0);

        imagefilledrectangle($image, $progressX, $progressY, $progressX + $progressWidth, $progressY + 6, $progressBg);
        if ($progressFill > 0) {
            imagefilledrectangle($image, $progressX, $progressY, $progressX + $progressFill, $progressY + 6, $progressFg);
        }
    }

    /**
     * Add animations to frame (legacy method)
     */
    private function addFrameAnimations($image, $text, $progress, $width, $height) {
        // Use simple animations for better performance
        $this->addSimpleAnimations($image, $progress, $width, $height);
    }

    /**
     * Create video with FFmpeg (enhanced)
     */
    private function createVideoWithFFmpeg($frames, $outputFile, $duration) {
        if (empty($frames)) {
            echo "  ❌ No frames to create video\n";
            return false;
        }

        // Check if FFmpeg is available
        exec('ffmpeg -version 2>&1', $output, $returnCode);
        if ($returnCode !== 0) {
            echo "  ❌ FFmpeg not available\n";
            return false;
        }

        echo "  Creating video with FFmpeg (" . count($frames) . " frames)...\n";

        // Create frame list with proper timing
        $listFile = $this->tempDir . 'frame_list_' . time() . '.txt';
        $listContent = '';

        $frameDuration = $duration / count($frames);
        $validFrames = 0;

        foreach ($frames as $frameFile) {
            if (file_exists($frameFile) && filesize($frameFile) > 0) {
                $realPath = realpath($frameFile);
                if ($realPath) {
                    $listContent .= "file '" . str_replace('\\', '/', $realPath) . "'\n";
                    $listContent .= "duration $frameDuration\n";
                    $validFrames++;
                }
            }
        }

        if ($validFrames === 0) {
            echo "  ❌ No valid frames found\n";
            return false;
        }

        file_put_contents($listFile, $listContent);

        // Enhanced FFmpeg command for better quality
        $cmd = sprintf(
            'ffmpeg -f concat -safe 0 -i "%s" -vf "scale=720:1280:force_original_aspect_ratio=decrease,pad=720:1280:(ow-iw)/2:(oh-ih)/2" -c:v libx264 -preset medium -crf 23 -pix_fmt yuv420p -r 30 -t %d "%s" -y 2>&1',
            $listFile,
            $duration,
            $outputFile
        );

        exec($cmd, $ffmpegOutput, $returnCode);

        // Enhanced cleanup with job ID
        $jobId = basename($outputFile, '.mp4');
        $this->cleanupVideoGeneration($listFile, $frames, $jobId);

        if ($returnCode === 0 && file_exists($outputFile) && filesize($outputFile) > 1000) {
            echo "  ✅ Video created successfully (" . round(filesize($outputFile)/1024, 1) . " KB)\n";
            return true;
        } else {
            echo "  ❌ FFmpeg failed (return code: $returnCode)\n";
            if (!empty($ffmpegOutput)) {
                echo "  Error details: " . implode(" | ", array_slice($ffmpegOutput, -3)) . "\n";
            }
            return false;
        }
    }

    /**
     * Enhanced cleanup for video generation
     */
    private function cleanupVideoGeneration($listFile, $frames, $jobId = null) {
        echo "  🧹 Cleaning up temporary files...\n";

        // Remove frame list file
        if (file_exists($listFile)) {
            unlink($listFile);
            echo "    ✅ Removed frame list file\n";
        }

        // Remove frame files
        $frameCount = 0;
        foreach ($frames as $frameFile) {
            if (file_exists($frameFile)) {
                unlink($frameFile);
                $frameCount++;
            }
        }
        echo "    ✅ Removed $frameCount frame files\n";

        // Clean up scene images for this job
        if ($jobId) {
            $scenePattern = $this->tempDir . $jobId . '_local_scene_*.png';
            $sceneCount = 0;
            foreach (glob($scenePattern) as $sceneFile) {
                if (file_exists($sceneFile)) {
                    unlink($sceneFile);
                    $sceneCount++;
                }
            }
            if ($sceneCount > 0) {
                echo "    ✅ Removed $sceneCount scene files\n";
            }
        }

        // Clean up any orphaned files older than 5 minutes
        $patterns = [
            $this->tempDir . '*_frame_*.png',
            $this->tempDir . '*_local_scene_*.png',
            $this->tempDir . 'frame_list_*.txt'
        ];

        $orphanCount = 0;
        foreach ($patterns as $pattern) {
            foreach (glob($pattern) as $orphanFile) {
                if (file_exists($orphanFile) && (time() - filemtime($orphanFile)) > 300) { // 5 minutes old
                    unlink($orphanFile);
                    $orphanCount++;
                }
            }
        }

        if ($orphanCount > 0) {
            echo "    ✅ Removed $orphanCount orphaned files\n";
        }

        echo "  ✅ Cleanup completed\n";
    }

    /**
     * Generate enhanced local video as fallback
     */
    private function generateEnhancedLocalVideo($segments, $background, $style, $outputFile) {
        echo "🔧 Generating enhanced local video...\n";
        
        // Generate local scene images
        $sceneDescriptions = $this->generateSceneDescriptions($segments, $style, $background);
        $sceneImages = $this->generateLocalSceneImages($sceneDescriptions, 'local_' . time());
        
        if (empty($sceneImages)) {
            return false;
        }
        
        // Generate frames
        $frames = $this->generateVideoFrames($sceneImages, $segments, $style, 'local_' . time());
        
        if (empty($frames)) {
            return false;
        }
        
        // Create video
        $success = $this->createVideoWithFFmpeg($frames, $outputFile, 30);
        
        return $success ? $outputFile : false;
    }

    /**
     * Test Hugging Face connectivity
     */
    public function testHuggingFaceConnection() {
        if (empty($this->apiKey)) {
            return ['status' => 'error', 'message' => 'API key not configured'];
        }

        try {
            // Test with a simple prompt
            $testPrompt = "A simple test image";
            echo "Testing API with prompt: $testPrompt\n";

            $result = $this->generateImageWithHuggingFace($testPrompt, 'test_connection_' . time());

            if ($result && file_exists($result)) {
                $fileSize = filesize($result);
                unlink($result); // Clean up test file

                return [
                    'status' => 'success',
                    'message' => "Hugging Face API working (test image: {$fileSize} bytes)"
                ];
            } else {
                return ['status' => 'error', 'message' => 'API call failed - no valid image returned'];
            }
        } catch (Exception $e) {
            return ['status' => 'error', 'message' => 'Exception: ' . $e->getMessage()];
        }
    }

    /**
     * Get API status and model information
     */
    public function getAPIStatus() {
        $status = [
            'api_key_configured' => !empty($this->apiKey),
            'models' => $this->models,
            'temp_dir_writable' => is_writable($this->tempDir),
            'gd_available' => function_exists('imagecreatetruecolor'),
            'ffmpeg_available' => $this->checkFFmpegAvailability()
        ];

        return $status;
    }

    /**
     * Check if FFmpeg is available
     */
    private function checkFFmpegAvailability() {
        exec('ffmpeg -version 2>&1', $output, $returnCode);
        return $returnCode === 0;
    }
}
