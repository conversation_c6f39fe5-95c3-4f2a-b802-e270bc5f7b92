# User Management Documentation

## 👥 Overview

The Sutradhar platform implements a comprehensive user management system with credit-based billing, subscription tiers, and advanced analytics. This document covers all aspects of user management, from registration to enterprise features.

## 🔐 Authentication System

### User Registration
```php
// Registration process
class UserManager {
    public function registerUser($email, $password, $confirmPassword) {
        // Validate input
        $this->validateEmail($email);
        $this->validatePassword($password, $confirmPassword);
        
        // Check if user exists
        if ($this->userExists($email)) {
            throw new Exception('User already exists');
        }
        
        // Create user with default credits
        $userId = $this->createUser([
            'email' => $email,
            'password_hash' => password_hash($password, PASSWORD_DEFAULT),
            'credits_available' => 50, // Free tier starting credits
            'subscription_type' => 'free',
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        // Send welcome email
        $this->sendWelcomeEmail($email);
        
        return $userId;
    }
}
```

### Login Process
```php
public function authenticateUser($email, $password) {
    $user = $this->getUserByEmail($email);
    
    if (!$user || !password_verify($password, $user['password_hash'])) {
        $this->recordFailedLogin($email);
        throw new Exception('Invalid credentials');
    }
    
    // Check account status
    if ($user['status'] === 'suspended') {
        throw new Exception('Account suspended');
    }
    
    // Create session
    $sessionToken = $this->createSession($user['id']);
    
    // Update last login
    $this->updateLastLogin($user['id']);
    
    return [
        'user' => $user,
        'session_token' => $sessionToken
    ];
}
```

## 💳 Credit System

### Credit Mechanics

The Sutradhar platform uses a credit-based system where users consume credits for various operations:

| Operation | Credit Cost | Description |
|-----------|-------------|-------------|
| Video Generation (Low) | 5 credits | 480p, 15fps, basic quality |
| Video Generation (Medium) | 10 credits | 720p, 24fps, standard quality |
| Video Generation (High) | 15 credits | 720p, 30fps, high quality |
| Video Generation (Ultra) | 25 credits | 1080p, 30fps, premium quality |
| Image Format Conversion | 1 credit | Basic format conversion |
| Background Removal | 2 credits | AI-powered background removal |
| Advanced Image Editing | 3 credits | Filters, effects, enhancements |
| Batch Operations | 5 credits | Processing multiple files |
| QR/Barcode Generation | 1 credit | Code generation tools |
| Text-to-Image | 2 credits | AI text-to-image conversion |

### Credit Management
```php
class CreditManager {
    public function deductCredits($userId, $amount, $operation) {
        $user = $this->getUser($userId);
        
        if ($user['credits_available'] < $amount) {
            throw new InsufficientCreditsException(
                "Insufficient credits. Required: $amount, Available: {$user['credits_available']}"
            );
        }
        
        // Deduct credits
        $this->updateCredits($userId, -$amount);
        
        // Log transaction
        $this->logCreditTransaction($userId, -$amount, $operation);
        
        return $this->getUser($userId)['credits_available'];
    }
    
    public function addCredits($userId, $amount, $source = 'purchase') {
        $this->updateCredits($userId, $amount);
        $this->logCreditTransaction($userId, $amount, $source);
        
        return $this->getUser($userId)['credits_available'];
    }
}
```

## 📊 Subscription Tiers

### Tier Comparison

| Feature | Free | Pro | Business | Enterprise |
|---------|------|-----|----------|------------|
| **Monthly Credits** | 50 | 500 | 2000 | Unlimited |
| **Price** | $0 | $9.99 | $29.99 | Custom |
| **Video Quality** | Low/Medium | All | All | All |
| **Tools Access** | Basic | All | All | All |
| **Rate Limits** | 10/hour | 100/hour | 1000/hour | Unlimited |
| **File Size Limit** | 10MB | 50MB | 100MB | 500MB |
| **Batch Processing** | ❌ | ✅ | ✅ | ✅ |
| **Priority Processing** | ❌ | ❌ | ✅ | ✅ |
| **API Access** | ❌ | ❌ | ✅ | ✅ |
| **Custom Branding** | ❌ | ❌ | ❌ | ✅ |
| **Dedicated Support** | ❌ | ❌ | ❌ | ✅ |

### Subscription Management
```php
class SubscriptionManager {
    public function upgradeSubscription($userId, $planType, $paymentToken) {
        $plans = [
            'pro' => ['price' => 9.99, 'credits' => 500],
            'business' => ['price' => 29.99, 'credits' => 2000],
            'enterprise' => ['price' => 99.99, 'credits' => -1] // Unlimited
        ];
        
        if (!isset($plans[$planType])) {
            throw new Exception('Invalid plan type');
        }
        
        $plan = $plans[$planType];
        
        // Process payment
        $paymentResult = $this->processPayment($paymentToken, $plan['price']);
        
        if ($paymentResult['status'] === 'success') {
            // Update subscription
            $this->updateUserSubscription($userId, $planType);
            
            // Add credits
            if ($plan['credits'] > 0) {
                $this->creditManager->addCredits($userId, $plan['credits'], 'subscription');
            }
            
            // Set expiration (monthly)
            $expiresAt = date('Y-m-d H:i:s', strtotime('+1 month'));
            $this->setSubscriptionExpiry($userId, $expiresAt);
            
            return true;
        }
        
        throw new Exception('Payment failed: ' . $paymentResult['error']);
    }
}
```

## 📈 User Analytics

### Usage Tracking
```php
class UserAnalytics {
    public function trackToolUsage($userId, $toolName, $processingTime, $creditsUsed) {
        $this->db->insert('tool_usage', [
            'user_id' => $userId,
            'tool_name' => $toolName,
            'processing_time' => $processingTime,
            'credits_used' => $creditsUsed,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
    
    public function getUserStats($userId, $period = '30 days') {
        $stats = [
            'videos_generated' => $this->getVideoCount($userId, $period),
            'tools_used' => $this->getToolUsageCount($userId, $period),
            'credits_consumed' => $this->getCreditsUsed($userId, $period),
            'favorite_tools' => $this->getFavoriteTools($userId, $period),
            'usage_by_day' => $this->getDailyUsage($userId, $period)
        ];
        
        return $stats;
    }
    
    public function getDashboardData($userId) {
        return [
            'current_credits' => $this->getCurrentCredits($userId),
            'subscription_info' => $this->getSubscriptionInfo($userId),
            'recent_videos' => $this->getRecentVideos($userId, 5),
            'recent_tools' => $this->getRecentToolUsage($userId, 10),
            'usage_this_month' => $this->getMonthlyUsage($userId)
        ];
    }
}
```

### Analytics Dashboard Data
```json
{
    "user_stats": {
        "total_users": 15420,
        "active_users_30d": 8934,
        "new_users_today": 45,
        "subscription_breakdown": {
            "free": 12456,
            "pro": 2341,
            "business": 567,
            "enterprise": 56
        }
    },
    "usage_metrics": {
        "videos_generated_today": 1234,
        "tools_used_today": 5678,
        "credits_consumed_today": 45678,
        "popular_tools": [
            {"name": "Format Converter", "usage": 1234},
            {"name": "Background Remover", "usage": 987},
            {"name": "QR Generator", "usage": 765}
        ]
    },
    "revenue_metrics": {
        "mrr": 45678.90,
        "arr": 548146.80,
        "churn_rate": 2.3,
        "ltv": 156.78
    }
}
```

## 🔒 User Roles and Permissions

### Role-Based Access Control
```php
class RoleManager {
    const ROLES = [
        'user' => [
            'permissions' => ['generate_video', 'use_tools', 'view_profile']
        ],
        'premium' => [
            'permissions' => ['generate_video', 'use_tools', 'view_profile', 'batch_processing', 'priority_queue']
        ],
        'admin' => [
            'permissions' => ['*'] // All permissions
        ],
        'moderator' => [
            'permissions' => ['view_users', 'suspend_users', 'view_analytics']
        ]
    ];
    
    public function hasPermission($userId, $permission) {
        $user = $this->getUser($userId);
        $role = $user['role'] ?? 'user';
        
        $permissions = self::ROLES[$role]['permissions'] ?? [];
        
        return in_array('*', $permissions) || in_array($permission, $permissions);
    }
}
```

## 💰 Payment Integration

### Stripe Integration
```php
class StripePaymentProcessor {
    public function processSubscription($userId, $planType, $paymentMethodId) {
        $stripe = new \Stripe\StripeClient($this->config['stripe']['secret_key']);
        
        try {
            // Create customer if not exists
            $customer = $this->getOrCreateStripeCustomer($userId);
            
            // Attach payment method
            $stripe->paymentMethods->attach($paymentMethodId, [
                'customer' => $customer->id
            ]);
            
            // Create subscription
            $subscription = $stripe->subscriptions->create([
                'customer' => $customer->id,
                'items' => [['price' => $this->getPriceId($planType)]],
                'default_payment_method' => $paymentMethodId,
                'expand' => ['latest_invoice.payment_intent']
            ]);
            
            // Update user subscription
            $this->updateUserSubscription($userId, $planType, $subscription->id);
            
            return [
                'status' => 'success',
                'subscription_id' => $subscription->id,
                'client_secret' => $subscription->latest_invoice->payment_intent->client_secret
            ];
            
        } catch (\Stripe\Exception\CardException $e) {
            return ['status' => 'error', 'message' => $e->getError()->message];
        }
    }
}
```

### PayPal Integration
```php
class PayPalPaymentProcessor {
    public function createSubscription($userId, $planType) {
        $paypal = new PayPalApiContext();
        
        $plan = new Plan();
        $plan->setName("Sutradhar {$planType} Plan")
             ->setDescription("Monthly subscription to Sutradhar {$planType}")
             ->setType('INFINITE');
        
        // Set billing cycles
        $paymentDefinition = new PaymentDefinition();
        $paymentDefinition->setName('Regular Payments')
                         ->setType('REGULAR')
                         ->setFrequency('Month')
                         ->setFrequencyInterval('1')
                         ->setCycles('0')
                         ->setAmount(new Currency([
                             'value' => $this->getPlanPrice($planType),
                             'currency' => 'USD'
                         ]));
        
        $plan->setPaymentDefinitions([$paymentDefinition]);
        
        try {
            $createdPlan = $plan->create($paypal);
            return $createdPlan;
        } catch (Exception $e) {
            throw new PaymentException('PayPal subscription creation failed: ' . $e->getMessage());
        }
    }
}
```

## 📧 User Communication

### Email Templates
```php
class EmailManager {
    public function sendWelcomeEmail($userId) {
        $user = $this->getUser($userId);
        
        $template = $this->loadTemplate('welcome', [
            'user_name' => $user['email'],
            'credits_available' => $user['credits_available'],
            'getting_started_url' => $this->config['app_url'] . '/getting-started'
        ]);
        
        $this->sendEmail($user['email'], 'Welcome to Sutradhar!', $template);
    }
    
    public function sendCreditLowWarning($userId) {
        $user = $this->getUser($userId);
        
        if ($user['credits_available'] <= 5) {
            $template = $this->loadTemplate('credit_warning', [
                'credits_remaining' => $user['credits_available'],
                'upgrade_url' => $this->config['app_url'] . '/pricing'
            ]);
            
            $this->sendEmail($user['email'], 'Credits Running Low', $template);
        }
    }
    
    public function sendSubscriptionExpiry($userId) {
        $user = $this->getUser($userId);
        $subscription = $this->getSubscription($userId);
        
        $template = $this->loadTemplate('subscription_expiry', [
            'plan_name' => ucfirst($subscription['plan_type']),
            'expiry_date' => $subscription['expires_at'],
            'renewal_url' => $this->config['app_url'] . '/billing'
        ]);
        
        $this->sendEmail($user['email'], 'Subscription Expiring Soon', $template);
    }
}
```

## 🛡️ Security Features

### Account Security
- **Password Requirements**: Minimum 8 characters, special characters, numbers
- **Two-Factor Authentication**: Optional TOTP-based 2FA
- **Session Management**: Secure session tokens with expiration
- **Login Monitoring**: Failed login attempt tracking and lockout
- **Account Recovery**: Secure password reset via email

### Data Protection
- **GDPR Compliance**: User data export and deletion
- **Data Encryption**: Sensitive data encrypted at rest
- **Audit Logging**: Complete audit trail of user actions
- **Privacy Controls**: Granular privacy settings

## 📊 Admin Dashboard

### User Management Interface
```php
class AdminDashboard {
    public function getUserOverview() {
        return [
            'total_users' => $this->getTotalUsers(),
            'active_users' => $this->getActiveUsers(),
            'new_registrations' => $this->getNewRegistrations(),
            'subscription_revenue' => $this->getSubscriptionRevenue(),
            'top_users_by_usage' => $this->getTopUsersByUsage(),
            'support_tickets' => $this->getOpenSupportTickets()
        ];
    }
    
    public function getUserDetails($userId) {
        $user = $this->getUser($userId);
        
        return [
            'profile' => $user,
            'subscription' => $this->getSubscription($userId),
            'usage_stats' => $this->getUserUsageStats($userId),
            'payment_history' => $this->getPaymentHistory($userId),
            'support_history' => $this->getSupportHistory($userId),
            'activity_log' => $this->getActivityLog($userId)
        ];
    }
}
```

This comprehensive user management system ensures secure, scalable, and user-friendly account management with flexible billing and detailed analytics.
