<?php
/**
 * Comprehensive Hugging Face Video Engine Test
 */

echo "🎬 Comprehensive Hugging Face Video Engine Test\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// Test the Hugging Face engine comprehensively
try {
    echo "🤗 Loading Hugging Face Video Engine...\n";
    require_once 'core/huggingface_video_engine.php';

    $hfEngine = new HuggingFaceVideoEngine();
    echo "✅ Engine loaded successfully\n";

    // Test 1: System Status Check
    echo "\n📊 Checking system status...\n";
    $status = $hfEngine->getAPIStatus();

    echo "API Key Configured: " . ($status['api_key_configured'] ? "✅ Yes" : "❌ No") . "\n";
    echo "Temp Directory Writable: " . ($status['temp_dir_writable'] ? "✅ Yes" : "❌ No") . "\n";
    echo "GD Extension Available: " . ($status['gd_available'] ? "✅ Yes" : "❌ No") . "\n";
    echo "FFmpeg Available: " . ($status['ffmpeg_available'] ? "✅ Yes" : "❌ No") . "\n";
    echo "Models Configured: " . json_encode($status['models']) . "\n";

    // Test 2: API Connection
    echo "\n🔗 Testing API connection...\n";
    $connectionTest = $hfEngine->testHuggingFaceConnection();
    echo "Connection status: " . $connectionTest['status'] . "\n";
    echo "Message: " . $connectionTest['message'] . "\n";

    // Test 3: Local Image Generation (fallback test)
    echo "\n🖼️ Testing local image generation...\n";
    $testScene = [
        'text' => 'Test scene for local generation',
        'description' => 'A simple test scene with colorful background',
        'index' => 0
    ];

    $localImageMethod = new ReflectionMethod($hfEngine, 'generateLocalImage');
    $localImageMethod->setAccessible(true);
    $localImage = $localImageMethod->invoke($hfEngine, $testScene, 'test_local_' . time());

    if ($localImage && file_exists($localImage)) {
        echo "✅ Local image generation successful\n";
        echo "📁 File: " . basename($localImage) . " (" . filesize($localImage) . " bytes)\n";
        unlink($localImage); // Clean up
    } else {
        echo "❌ Local image generation failed\n";
    }

    // Test 4: Video Generation
    echo "\n🎥 Testing video generation...\n";

    // Create test segments
    $testSegments = [
        [
            'text' => 'A beautiful sunset over mountains',
            'duration' => 10,
            'index' => 0
        ],
        [
            'text' => 'Birds flying in the golden sky',
            'duration' => 10,
            'index' => 1
        ],
        [
            'text' => 'Peaceful nature scene with trees',
            'duration' => 10,
            'index' => 2
        ]
    ];

    $jobId = 'comprehensive_test_' . time();
    $background = 'nature';
    $style = 'cinematic';

    echo "Job ID: $jobId\n";
    echo "Segments: " . count($testSegments) . "\n";
    echo "Background: $background\n";
    echo "Style: $style\n\n";

    $startTime = microtime(true);
    $videoFile = $hfEngine->generateAdvancedVideo($testSegments, $background, $style, $jobId);
    $generationTime = microtime(true) - $startTime;

    if ($videoFile && file_exists($videoFile)) {
        $fileSize = filesize($videoFile);
        echo "\n✅ Video generation successful!\n";
        echo "📁 File: $videoFile\n";
        echo "📊 Size: " . round($fileSize / 1024 / 1024, 2) . " MB\n";
        echo "⏱️ Generation time: " . round($generationTime, 2) . " seconds\n";

        // Validate video file
        if ($fileSize > 50000) { // At least 50KB
            echo "✅ File size looks reasonable\n";
        } else {
            echo "⚠️ File size seems small\n";
        }

        // Try to get video info with FFmpeg
        if ($status['ffmpeg_available']) {
            exec("ffmpeg -i \"$videoFile\" 2>&1", $videoInfo);
            foreach ($videoInfo as $line) {
                if (strpos($line, 'Duration:') !== false) {
                    echo "⏱️ " . trim($line) . "\n";
                    break;
                }
            }

            // Check video properties
            foreach ($videoInfo as $line) {
                if (strpos($line, 'Video:') !== false) {
                    echo "📹 " . trim($line) . "\n";
                    break;
                }
            }
        }

        // Test 5: Cleanup verification
        echo "\n🧹 Testing cleanup...\n";
        $tempFiles = glob('temp/' . $jobId . '_*');
        echo "Temporary files found: " . count($tempFiles) . "\n";

        if (count($tempFiles) > 1) { // Video file should remain
            echo "⚠️ Some temporary files not cleaned up\n";
            foreach ($tempFiles as $file) {
                if (basename($file) !== basename($videoFile)) {
                    echo "  - " . basename($file) . "\n";
                }
            }
        } else {
            echo "✅ Cleanup looks good\n";
        }

    } else {
        echo "\n❌ Video generation failed\n";
        if ($videoFile) {
            echo "Expected file: $videoFile\n";
        }
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n🎉 Comprehensive test completed!\n";
echo "\n📋 Summary:\n";
echo "- System status checked\n";
echo "- API connectivity tested\n";
echo "- Local fallback tested\n";
echo "- Video generation tested\n";
echo "- Cleanup verified\n";
?>
