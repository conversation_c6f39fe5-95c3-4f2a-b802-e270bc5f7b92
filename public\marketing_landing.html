<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sutradhar 2070 - AI-Powered Mood-Based Video Generation</title>
    <meta name="description" content="Create stunning videos with emotional intelligence using Sutradhar 2070's revolutionary AI-powered mood-based video generation platform.">
    <meta name="keywords" content="AI video generation, mood-based videos, artificial intelligence, video creation, content creation, automated video">

    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'cyber-blue': '#00D4FF',
                        'cyber-purple': '#8B5CF6',
                        'cyber-pink': '#F472B6',
                        'cyber-green': '#10B981',
                        'cyber-orange': '#F59E0B',
                        'dark-bg': '#0F0F23',
                        'dark-card': '#1A1A2E'
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #0F0F23 0%, #1A1A2E 50%, #16213E 100%);
            font-family: 'Exo 2', sans-serif;
            color: white;
        }

        .hologram-text {
            background: linear-gradient(45deg, #00D4FF, #8B5CF6, #F472B6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: hologram 3s ease-in-out infinite;
        }

        @keyframes hologram {
            0%, 100% { opacity: 0.8; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
        }

        .cyber-button {
            background: linear-gradient(45deg, #00D4FF, #8B5CF6);
            border: 1px solid #00D4FF;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .cyber-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.8);
        }

        .cyber-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .cyber-button:hover::before {
            left: 100%;
        }

        .feature-card {
            background: rgba(26, 26, 46, 0.8);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            transition: all 0.4s ease;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            border-color: #00D4FF;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 30px rgba(0, 212, 255, 0.5);
        }

        .floating-element {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .stats-counter {
            font-size: 3rem;
            font-weight: bold;
            background: linear-gradient(45deg, #00D4FF, #8B5CF6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .video-preview {
            background: linear-gradient(45deg, rgba(0, 212, 255, 0.1), rgba(139, 92, 246, 0.1));
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 20px;
            overflow: hidden;
            position: relative;
        }

        .video-preview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(0, 212, 255, 0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .mood-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }

        .mood-item {
            background: rgba(26, 26, 46, 0.6);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .mood-item:hover {
            border-color: #00D4FF;
            background: rgba(26, 26, 46, 0.8);
            transform: scale(1.05);
        }

        .testimonial-slider {
            overflow: hidden;
            position: relative;
        }

        .testimonial-track {
            display: flex;
            transition: transform 0.5s ease;
        }

        .testimonial-slide {
            min-width: 100%;
            padding: 0 2rem;
        }

        .cta-section {
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(139, 92, 246, 0.1));
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
        }

        .cta-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(0, 212, 255, 0.1), transparent);
            animation: rotate 10s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            align-items: center;
        }

        .before-after {
            position: relative;
            border-radius: 15px;
            overflow: hidden;
        }

        .before-after img {
            width: 100%;
            height: auto;
            transition: all 0.3s ease;
        }

        .before-after:hover img {
            transform: scale(1.05);
        }

        .roi-calculator {
            background: rgba(26, 26, 46, 0.8);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            padding: 2rem;
        }

        .social-proof {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 2rem;
            flex-wrap: wrap;
            opacity: 0.7;
            filter: grayscale(100%);
            transition: all 0.3s ease;
        }

        .social-proof:hover {
            opacity: 1;
            filter: grayscale(0%);
        }

        .demo-video-container {
            position: relative;
            background: linear-gradient(45deg, rgba(0, 212, 255, 0.1), rgba(139, 92, 246, 0.1));
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
        }

        .play-button {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #00D4FF, #8B5CF6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 2rem auto;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
        }

        .play-button:hover {
            transform: scale(1.1);
            box-shadow: 0 0 50px rgba(0, 212, 255, 0.8);
        }

        .play-button::after {
            content: '▶';
            color: white;
            font-size: 24px;
            margin-left: 4px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="fixed top-0 w-full z-50 bg-dark-bg/90 backdrop-blur-lg border-b border-cyber-blue/20">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-cyber-blue to-cyber-purple rounded-full flex items-center justify-center floating-element">
                        <span class="text-white font-bold text-lg">🎭</span>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold hologram-text">SUTRADHAR 2070</h1>
                        <p class="text-cyber-blue text-xs">AI Video Generation</p>
                    </div>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#features" class="text-gray-300 hover:text-white transition-colors">Features</a>
                    <a href="#pricing" class="text-gray-300 hover:text-white transition-colors">Pricing</a>
                    <a href="#demo" class="text-gray-300 hover:text-white transition-colors">Demo</a>
                    <a href="#testimonials" class="text-gray-300 hover:text-white transition-colors">Reviews</a>
                    <button class="cyber-button px-6 py-2 rounded-lg font-bold" onclick="window.location.href='mood_video_generator.html'">
                        Try Free
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="pt-32 pb-20 text-center relative overflow-hidden">
        <div class="container mx-auto px-6 relative z-10">
            <div class="max-w-5xl mx-auto">
                <h1 class="text-6xl md:text-8xl font-bold hologram-text mb-8 leading-tight">
                    Create Videos with
                    <span class="block">Emotional Intelligence</span>
                </h1>
                <p class="text-xl md:text-2xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed">
                    Sutradhar 2070 revolutionizes video creation with AI-powered mood-based generation.
                    Transform your ideas into stunning 30-second videos that capture the perfect emotional tone.
                </p>

                <div class="flex flex-col md:flex-row justify-center items-center space-y-4 md:space-y-0 md:space-x-6 mb-16">
                    <button class="cyber-button px-8 py-4 rounded-lg font-bold text-lg" onclick="window.location.href='mood_video_generator.html'">
                        🚀 Start Creating Free
                    </button>
                    <button class="bg-transparent border-2 border-cyber-purple text-cyber-purple px-8 py-4 rounded-lg font-bold text-lg hover:bg-cyber-purple hover:text-white transition-all">
                        📺 Watch Demo
                    </button>
                </div>

                <!-- Stats -->
                <div class="grid md:grid-cols-4 gap-8 mb-16">
                    <div class="text-center">
                        <div class="stats-counter" data-target="50000">0</div>
                        <p class="text-gray-400">Videos Generated</p>
                    </div>
                    <div class="text-center">
                        <div class="stats-counter" data-target="30">0</div>
                        <p class="text-gray-400">Emotional Moods</p>
                    </div>
                    <div class="text-center">
                        <div class="stats-counter" data-target="95">0</div>
                        <p class="text-gray-400">Satisfaction Rate</p>
                    </div>
                    <div class="text-center">
                        <div class="stats-counter" data-target="2">0</div>
                        <p class="text-gray-400">Minutes Avg Generation</p>
                    </div>
                </div>

                <!-- Video Preview -->
                <div class="video-preview max-w-4xl mx-auto">
                    <div class="demo-video-container">
                        <h3 class="text-2xl font-bold mb-4">See Sutradhar 2070 in Action</h3>
                        <p class="text-gray-300 mb-6">Watch how our AI transforms a simple mood selection into a professional video</p>
                        <div class="play-button" onclick="playDemo()"></div>
                        <p class="text-sm text-gray-400">2 minute demo • No signup required</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Background Elements -->
        <div class="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
            <div class="absolute top-20 left-10 w-32 h-32 bg-cyber-blue/10 rounded-full blur-xl floating-element"></div>
            <div class="absolute top-40 right-20 w-48 h-48 bg-cyber-purple/10 rounded-full blur-xl floating-element" style="animation-delay: -2s;"></div>
            <div class="absolute bottom-20 left-1/4 w-24 h-24 bg-cyber-pink/10 rounded-full blur-xl floating-element" style="animation-delay: -4s;"></div>
        </div>
    </section>

    <!-- Social Proof -->
    <section class="py-12 border-y border-gray-800">
        <div class="container mx-auto px-6">
            <p class="text-center text-gray-400 mb-8">Trusted by leading creators and businesses worldwide</p>
            <div class="social-proof">
                <div class="text-2xl font-bold text-gray-500">TechCrunch</div>
                <div class="text-2xl font-bold text-gray-500">Forbes</div>
                <div class="text-2xl font-bold text-gray-500">Wired</div>
                <div class="text-2xl font-bold text-gray-500">VentureBeat</div>
                <div class="text-2xl font-bold text-gray-500">The Verge</div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-5xl font-bold hologram-text mb-6">
                    Revolutionary Features
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Experience the future of video creation with our cutting-edge AI technology
                    that understands emotions and creates content that resonates.
                </p>
            </div>

            <div class="grid md:grid-cols-3 gap-8 mb-16">
                <div class="feature-card p-8 rounded-2xl text-center">
                    <div class="w-16 h-16 bg-gradient-to-r from-cyber-blue to-cyber-purple rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-2xl">🎭</span>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">30 Emotional Moods</h3>
                    <p class="text-gray-300 mb-6">
                        From euphoric to melancholic, our AI understands and generates videos
                        that perfectly capture the emotional tone you want to convey.
                    </p>
                    <div class="mood-showcase">
                        <div class="mood-item">😊 Euphoric</div>
                        <div class="mood-item">😌 Serene</div>
                        <div class="mood-item">⚡ Energetic</div>
                        <div class="mood-item">🔮 Mysterious</div>
                    </div>
                </div>

                <div class="feature-card p-8 rounded-2xl text-center">
                    <div class="w-16 h-16 bg-gradient-to-r from-cyber-green to-cyber-blue rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-2xl">⚡</span>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">Lightning Fast</h3>
                    <p class="text-gray-300 mb-6">
                        Generate professional-quality 30-second videos in just 2-3 minutes.
                        Our optimized AI pipeline ensures rapid processing without compromising quality.
                    </p>
                    <div class="bg-gray-800 rounded-lg p-4 mt-4">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm">Generation Progress</span>
                            <span class="text-sm text-cyber-green">98% Complete</span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div class="bg-gradient-to-r from-cyber-green to-cyber-blue h-2 rounded-full" style="width: 98%"></div>
                        </div>
                    </div>
                </div>

                <div class="feature-card p-8 rounded-2xl text-center">
                    <div class="w-16 h-16 bg-gradient-to-r from-cyber-purple to-cyber-pink rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-2xl">🎬</span>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">Professional Quality</h3>
                    <p class="text-gray-300 mb-6">
                        HD and 4K output with advanced effects, perfect audio synchronization,
                        and cinema-grade color grading. Ready for any platform.
                    </p>
                    <div class="grid grid-cols-2 gap-2 mt-4">
                        <div class="bg-gray-800 rounded p-2 text-sm">HD 1080p</div>
                        <div class="bg-gray-800 rounded p-2 text-sm">4K Ultra</div>
                        <div class="bg-gray-800 rounded p-2 text-sm">60 FPS</div>
                        <div class="bg-gray-800 rounded p-2 text-sm">HDR</div>
                    </div>
                </div>
            </div>

            <!-- Advanced Features Grid -->
            <div class="grid md:grid-cols-2 gap-12 items-center">
                <div>
                    <h3 class="text-3xl font-bold hologram-text mb-6">
                        Advanced AI Technology
                    </h3>
                    <div class="space-y-6">
                        <div class="flex items-start space-x-4">
                            <div class="w-8 h-8 bg-cyber-blue rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                <span class="text-white text-sm">✓</span>
                            </div>
                            <div>
                                <h4 class="font-bold text-lg mb-2">Smart Prompt Generation</h4>
                                <p class="text-gray-300">AI generates 20-30 unique prompts based on your mood and topic selection, ensuring diverse and creative content.</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-4">
                            <div class="w-8 h-8 bg-cyber-purple rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                <span class="text-white text-sm">✓</span>
                            </div>
                            <div>
                                <h4 class="font-bold text-lg mb-2">Intelligent Caching</h4>
                                <p class="text-gray-300">Smart caching system reduces generation time by 70% for similar content, making iterations lightning fast.</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-4">
                            <div class="w-8 h-8 bg-cyber-green rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                <span class="text-white text-sm">✓</span>
                            </div>
                            <div>
                                <h4 class="font-bold text-lg mb-2">Quality Assurance</h4>
                                <p class="text-gray-300">Automated quality checks ensure every video meets professional standards before delivery.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="feature-card p-8 rounded-2xl">
                    <h4 class="text-xl font-bold mb-4 text-center">Generation Pipeline</h4>
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-cyber-blue rounded-full flex items-center justify-center text-sm font-bold">1</div>
                            <span>Mood Analysis & Selection</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-cyber-purple rounded-full flex items-center justify-center text-sm font-bold">2</div>
                            <span>AI Prompt Generation</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-cyber-green rounded-full flex items-center justify-center text-sm font-bold">3</div>
                            <span>Scene Creation & Rendering</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-cyber-orange rounded-full flex items-center justify-center text-sm font-bold">4</div>
                            <span>Audio Synchronization</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-cyber-pink rounded-full flex items-center justify-center text-sm font-bold">5</div>
                            <span>Quality Assurance</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-gradient-to-r from-cyber-blue to-cyber-purple rounded-full flex items-center justify-center text-sm font-bold">✓</div>
                            <span class="font-bold text-cyber-green">Professional Video Ready</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Before/After Comparison -->
    <section class="py-20 bg-gradient-to-r from-dark-bg to-dark-card">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-5xl font-bold hologram-text mb-6">
                    See the Difference
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Compare traditional video creation methods with Sutradhar 2070's AI-powered approach
                </p>
            </div>

            <div class="comparison-grid max-w-6xl mx-auto">
                <div class="text-center">
                    <h3 class="text-2xl font-bold mb-6 text-red-400">Traditional Method</h3>
                    <div class="feature-card p-6 rounded-xl mb-6">
                        <div class="space-y-4 text-left">
                            <div class="flex items-center space-x-3">
                                <span class="text-red-400">❌</span>
                                <span>Hours of planning and scripting</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <span class="text-red-400">❌</span>
                                <span>Expensive equipment and software</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <span class="text-red-400">❌</span>
                                <span>Technical expertise required</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <span class="text-red-400">❌</span>
                                <span>Days of editing and post-production</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <span class="text-red-400">❌</span>
                                <span>Inconsistent emotional tone</span>
                            </div>
                        </div>
                    </div>
                    <div class="text-3xl font-bold text-red-400">2-5 Days</div>
                    <p class="text-gray-400">Average completion time</p>
                </div>

                <div class="text-center">
                    <h3 class="text-2xl font-bold mb-6 text-cyber-green">Sutradhar 2070</h3>
                    <div class="feature-card p-6 rounded-xl mb-6 border-cyber-green">
                        <div class="space-y-4 text-left">
                            <div class="flex items-center space-x-3">
                                <span class="text-cyber-green">✅</span>
                                <span>AI-powered mood selection</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <span class="text-cyber-green">✅</span>
                                <span>No equipment needed</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <span class="text-cyber-green">✅</span>
                                <span>Zero technical knowledge required</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <span class="text-cyber-green">✅</span>
                                <span>Automatic professional editing</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <span class="text-cyber-green">✅</span>
                                <span>Perfect emotional consistency</span>
                            </div>
                        </div>
                    </div>
                    <div class="text-3xl font-bold text-cyber-green">2-3 Minutes</div>
                    <p class="text-gray-400">Average completion time</p>
                </div>
            </div>
        </div>
    </section>

    <!-- ROI Calculator -->
    <section class="py-20">
        <div class="container mx-auto px-6">
            <div class="max-w-4xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="text-4xl font-bold hologram-text mb-6">
                        Calculate Your ROI
                    </h2>
                    <p class="text-xl text-gray-300">
                        See how much time and money Sutradhar 2070 can save your business
                    </p>
                </div>

                <div class="roi-calculator">
                    <div class="grid md:grid-cols-2 gap-8">
                        <div>
                            <h3 class="text-xl font-bold mb-4">Your Current Process</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium mb-2">Videos per month</label>
                                    <input type="number" id="videos-per-month" class="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-2" value="10">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium mb-2">Hours per video</label>
                                    <input type="number" id="hours-per-video" class="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-2" value="8">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium mb-2">Hourly rate ($)</label>
                                    <input type="number" id="hourly-rate" class="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-2" value="50">
                                </div>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold mb-4">Savings with Sutradhar 2070</h3>
                            <div class="space-y-4">
                                <div class="bg-gray-800 rounded-lg p-4">
                                    <div class="flex justify-between items-center mb-2">
                                        <span>Time saved per video:</span>
                                        <span class="font-bold text-cyber-green" id="time-saved">7.95 hours</span>
                                    </div>
                                    <div class="flex justify-between items-center mb-2">
                                        <span>Monthly time savings:</span>
                                        <span class="font-bold text-cyber-green" id="monthly-time-saved">79.5 hours</span>
                                    </div>
                                    <div class="flex justify-between items-center mb-2">
                                        <span>Monthly cost savings:</span>
                                        <span class="font-bold text-cyber-green" id="monthly-savings">$3,975</span>
                                    </div>
                                    <div class="flex justify-between items-center text-lg font-bold border-t border-gray-600 pt-2">
                                        <span>Annual savings:</span>
                                        <span class="text-cyber-green" id="annual-savings">$47,700</span>
                                    </div>
                                </div>
                                <button class="w-full cyber-button py-3 rounded-lg font-bold">
                                    Start Saving Today
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <section id="testimonials" class="py-20 bg-gradient-to-r from-dark-card to-dark-bg">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-5xl font-bold hologram-text mb-6">
                    What Our Users Say
                </h2>
                <p class="text-xl text-gray-300">
                    Join thousands of satisfied creators and businesses
                </p>
            </div>

            <div class="testimonial-slider max-w-4xl mx-auto">
                <div class="testimonial-track" id="testimonial-track">
                    <div class="testimonial-slide">
                        <div class="feature-card p-8 rounded-2xl text-center">
                            <div class="flex justify-center mb-6">
                                <div class="w-16 h-16 bg-gradient-to-r from-cyber-blue to-cyber-purple rounded-full flex items-center justify-center">
                                    <span class="text-white font-bold text-xl">SM</span>
                                </div>
                            </div>
                            <blockquote class="text-xl text-gray-300 mb-6 italic">
                                "Sutradhar 2070 has completely transformed our content strategy. We're now producing
                                10x more videos with perfect emotional consistency. Our engagement rates have skyrocketed!"
                            </blockquote>
                            <div class="text-cyber-orange mb-2">★★★★★</div>
                            <h4 class="font-bold text-lg">Sarah Martinez</h4>
                            <p class="text-gray-400">Content Director, TechFlow Media</p>
                        </div>
                    </div>

                    <div class="testimonial-slide">
                        <div class="feature-card p-8 rounded-2xl text-center">
                            <div class="flex justify-center mb-6">
                                <div class="w-16 h-16 bg-gradient-to-r from-cyber-green to-cyber-blue rounded-full flex items-center justify-center">
                                    <span class="text-white font-bold text-xl">DJ</span>
                                </div>
                            </div>
                            <blockquote class="text-xl text-gray-300 mb-6 italic">
                                "The ROI is incredible. We've cut our video production costs by 85% while improving quality.
                                The mood-based generation creates content that truly resonates with our audience."
                            </blockquote>
                            <div class="text-cyber-orange mb-2">★★★★★</div>
                            <h4 class="font-bold text-lg">David Johnson</h4>
                            <p class="text-gray-400">Marketing Director, InnovateCorp</p>
                        </div>
                    </div>

                    <div class="testimonial-slide">
                        <div class="feature-card p-8 rounded-2xl text-center">
                            <div class="flex justify-center mb-6">
                                <div class="w-16 h-16 bg-gradient-to-r from-cyber-purple to-cyber-pink rounded-full flex items-center justify-center">
                                    <span class="text-white font-bold text-xl">AL</span>
                                </div>
                            </div>
                            <blockquote class="text-xl text-gray-300 mb-6 italic">
                                "As a solo creator, I was spending 80% of my time on video production. Now I focus on
                                strategy and creativity while Sutradhar 2070 handles the technical execution perfectly."
                            </blockquote>
                            <div class="text-cyber-orange mb-2">★★★★★</div>
                            <h4 class="font-bold text-lg">Alex Liu</h4>
                            <p class="text-gray-400">Independent Content Creator</p>
                        </div>
                    </div>
                </div>

                <div class="flex justify-center mt-8 space-x-2">
                    <button class="w-3 h-3 rounded-full bg-cyber-blue" onclick="showTestimonial(0)"></button>
                    <button class="w-3 h-3 rounded-full bg-gray-600" onclick="showTestimonial(1)"></button>
                    <button class="w-3 h-3 rounded-full bg-gray-600" onclick="showTestimonial(2)"></button>
                </div>
            </div>
        </div>
    </section>

    <!-- Final CTA -->
    <section class="py-20">
        <div class="container mx-auto px-6">
            <div class="cta-section max-w-4xl mx-auto p-12 text-center relative z-10">
                <h2 class="text-5xl font-bold hologram-text mb-6">
                    Ready to Transform Your Video Creation?
                </h2>
                <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                    Join the AI revolution and start creating professional videos with emotional intelligence.
                    No credit card required to get started.
                </p>

                <div class="flex flex-col md:flex-row justify-center items-center space-y-4 md:space-y-0 md:space-x-6 mb-8">
                    <button class="cyber-button px-8 py-4 rounded-lg font-bold text-lg" onclick="window.location.href='mood_video_generator.html'">
                        🚀 Start Creating Free
                    </button>
                    <button class="bg-transparent border-2 border-cyber-purple text-cyber-purple px-8 py-4 rounded-lg font-bold text-lg hover:bg-cyber-purple hover:text-white transition-all">
                        📞 Schedule Demo
                    </button>
                </div>

                <div class="grid md:grid-cols-3 gap-6 text-sm text-gray-400">
                    <div class="flex items-center justify-center space-x-2">
                        <span class="text-cyber-green">✓</span>
                        <span>Free 50 credits to start</span>
                    </div>
                    <div class="flex items-center justify-center space-x-2">
                        <span class="text-cyber-green">✓</span>
                        <span>No setup or installation</span>
                    </div>
                    <div class="flex items-center justify-center space-x-2">
                        <span class="text-cyber-green">✓</span>
                        <span>Cancel anytime</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-12 border-t border-gray-800">
        <div class="container mx-auto px-6">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-8 h-8 bg-gradient-to-r from-cyber-blue to-cyber-purple rounded-full flex items-center justify-center">
                            <span class="text-white font-bold">🎭</span>
                        </div>
                        <span class="text-xl font-bold hologram-text">SUTRADHAR 2070</span>
                    </div>
                    <p class="text-gray-400 mb-4">
                        Revolutionizing video creation with AI-powered emotional intelligence.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-cyber-blue transition-colors">Twitter</a>
                        <a href="#" class="text-gray-400 hover:text-cyber-blue transition-colors">LinkedIn</a>
                        <a href="#" class="text-gray-400 hover:text-cyber-blue transition-colors">YouTube</a>
                    </div>
                </div>

                <div>
                    <h3 class="font-bold mb-4">Product</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition-colors">Features</a></li>
                        <li><a href="pricing.html" class="hover:text-white transition-colors">Pricing</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">API</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Integrations</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-bold mb-4">Company</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition-colors">About</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Blog</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Careers</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Contact</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-bold mb-4">Support</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition-colors">Help Center</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Documentation</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Status</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Privacy</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 Sutradhar 2070. All rights reserved. Built with AI for the future of video creation.</p>
            </div>
        </div>
    </footer>

    <script>
        // Stats counter animation
        function animateCounter(element, target) {
            let current = 0;
            const increment = target / 100;
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current).toLocaleString();
            }, 20);
        }

        // Initialize counters when page loads
        document.addEventListener('DOMContentLoaded', function() {
            const counters = document.querySelectorAll('.stats-counter');
            counters.forEach(counter => {
                const target = parseInt(counter.dataset.target);
                animateCounter(counter, target);
            });
        });

        // ROI Calculator
        function updateROI() {
            const videosPerMonth = parseInt(document.getElementById('videos-per-month').value) || 0;
            const hoursPerVideo = parseInt(document.getElementById('hours-per-video').value) || 0;
            const hourlyRate = parseInt(document.getElementById('hourly-rate').value) || 0;

            const timeSavedPerVideo = Math.max(0, hoursPerVideo - 0.05); // 3 minutes = 0.05 hours
            const monthlyTimeSaved = videosPerMonth * timeSavedPerVideo;
            const monthlySavings = monthlyTimeSaved * hourlyRate;
            const annualSavings = monthlySavings * 12;

            document.getElementById('time-saved').textContent = timeSavedPerVideo.toFixed(2) + ' hours';
            document.getElementById('monthly-time-saved').textContent = monthlyTimeSaved.toFixed(1) + ' hours';
            document.getElementById('monthly-savings').textContent = '$' + monthlySavings.toLocaleString();
            document.getElementById('annual-savings').textContent = '$' + annualSavings.toLocaleString();
        }

        // Add event listeners for ROI calculator
        document.getElementById('videos-per-month').addEventListener('input', updateROI);
        document.getElementById('hours-per-video').addEventListener('input', updateROI);
        document.getElementById('hourly-rate').addEventListener('input', updateROI);

        // Initialize ROI calculator
        updateROI();

        // Testimonial slider
        let currentTestimonial = 0;
        const testimonials = document.querySelectorAll('.testimonial-slide');
        const dots = document.querySelectorAll('button[onclick^="showTestimonial"]');

        function showTestimonial(index) {
            currentTestimonial = index;
            const track = document.getElementById('testimonial-track');
            track.style.transform = `translateX(-${index * 100}%)`;

            dots.forEach((dot, i) => {
                dot.className = i === index ? 'w-3 h-3 rounded-full bg-cyber-blue' : 'w-3 h-3 rounded-full bg-gray-600';
            });
        }

        // Auto-rotate testimonials
        setInterval(() => {
            currentTestimonial = (currentTestimonial + 1) % testimonials.length;
            showTestimonial(currentTestimonial);
        }, 5000);

        // Demo video function
        function playDemo() {
            alert('Demo video would play here. In production, this would open a video modal or redirect to a demo page.');
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // GSAP Animations
        gsap.registerPlugin(ScrollTrigger);

        // Animate feature cards on scroll
        gsap.from('.feature-card', {
            duration: 0.8,
            y: 50,
            opacity: 0,
            stagger: 0.2,
            ease: 'power2.out',
            scrollTrigger: {
                trigger: '.feature-card',
                start: 'top 80%'
            }
        });

        // Animate stats on scroll
        gsap.from('.stats-counter', {
            duration: 1,
            scale: 0.5,
            opacity: 0,
            stagger: 0.1,
            ease: 'back.out(1.7)',
            scrollTrigger: {
                trigger: '.stats-counter',
                start: 'top 80%'
            }
        });
    </script>
</body>
</html>