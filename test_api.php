<?php
/**
 * Test API endpoints to ensure clean JSON responses
 */

echo "🔧 Testing Sutradhar Engine API\n";
echo "===============================\n\n";

// Test 1: List Templates
echo "📋 Testing list_templates endpoint...\n";
$response = file_get_contents('http://localhost:8000/api.php?action=list_templates');
echo "Response: " . substr($response, 0, 100) . "...\n";

$data = json_decode($response, true);
if (json_last_error() === JSON_ERROR_NONE) {
    echo "✅ Valid JSON response\n";
    echo "   Templates found: " . count($data) . "\n";
} else {
    echo "❌ Invalid JSON: " . json_last_error_msg() . "\n";
    echo "   Raw response: " . $response . "\n";
}

echo "\n";

// Test 2: Generation
echo "🎬 Testing generation endpoint...\n";

$postData = [
    'flow_type' => 'reel',
    'style' => 'funny',
    'voice_pack' => 'babu_rao',
    'background' => 'home',
    'content_source' => 'text',
    'content_text' => 'Short test story for API testing.'
];

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/x-www-form-urlencoded',
        'content' => http_build_query($postData)
    ]
]);

$response = file_get_contents('http://localhost:8000/api.php?action=generate', false, $context);
echo "Response: " . substr($response, 0, 100) . "...\n";

$data = json_decode($response, true);
if (json_last_error() === JSON_ERROR_NONE) {
    echo "✅ Valid JSON response\n";
    if (isset($data['success']) && $data['success']) {
        echo "   Generation started successfully\n";
        echo "   Job ID: " . $data['job_id'] . "\n";
        
        // Wait a moment and check status
        sleep(2);
        
        echo "\n📊 Checking job status...\n";
        $statusResponse = file_get_contents('http://localhost:8000/api.php?action=status&job_id=' . $data['job_id']);
        
        $statusData = json_decode($statusResponse, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "✅ Status response is valid JSON\n";
            echo "   Status: " . ($statusData['status'] ?? 'Unknown') . "\n";
            echo "   Progress: " . ($statusData['progress'] ?? 0) . "%\n";
            
            if (isset($statusData['complete']) && $statusData['complete']) {
                echo "   🎉 Generation completed!\n";
                if (isset($statusData['output'])) {
                    echo "   Output files:\n";
                    foreach ($statusData['output'] as $type => $fileInfo) {
                        echo "     - $type: " . $fileInfo['size'] . "\n";
                    }
                }
            }
        } else {
            echo "❌ Status response invalid JSON: " . json_last_error_msg() . "\n";
            echo "   Raw response: " . substr($statusResponse, 0, 200) . "...\n";
        }
        
    } else {
        echo "❌ Generation failed: " . ($data['error'] ?? 'Unknown error') . "\n";
    }
} else {
    echo "❌ Invalid JSON: " . json_last_error_msg() . "\n";
    echo "   Raw response: " . $response . "\n";
}

echo "\n🏁 API test completed!\n";
?>
