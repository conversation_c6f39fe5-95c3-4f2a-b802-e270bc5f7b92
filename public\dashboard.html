<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Sutradhar 2070</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'cyber-blue': '#00D4FF',
                        'cyber-purple': '#8B5CF6',
                        'cyber-pink': '#F472B6',
                        'cyber-green': '#10B981',
                        'cyber-orange': '#F59E0B',
                        'dark-bg': '#0F0F23',
                        'dark-card': '#1A1A2E'
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #0F0F23 0%, #1A1A2E 50%, #16213E 100%);
            font-family: 'Exo 2', sans-serif;
            color: white;
        }

        .glass-morphism {
            background: rgba(26, 26, 46, 0.7);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 212, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .cyber-button {
            background: linear-gradient(45deg, #00D4FF, #8B5CF6);
            border: 1px solid #00D4FF;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .cyber-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.8);
        }

        .dashboard-card {
            background: rgba(26, 26, 46, 0.8);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            transition: all 0.4s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            border-color: #00D4FF;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 212, 255, 0.5);
        }

        .hologram-text {
            background: linear-gradient(45deg, #00D4FF, #8B5CF6, #F472B6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: hologram 3s ease-in-out infinite;
        }

        @keyframes hologram {
            0%, 100% { opacity: 0.8; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
        }

        .floating-element {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .progress-ring {
            transform: rotate(-90deg);
        }

        .progress-ring-circle {
            stroke-dasharray: 251.2;
            stroke-dashoffset: 251.2;
            transition: stroke-dashoffset 0.5s ease-in-out;
        }

        .sidebar {
            background: rgba(15, 15, 35, 0.9);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(0, 212, 255, 0.2);
        }

        .nav-item {
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .nav-item:hover, .nav-item.active {
            background: rgba(0, 212, 255, 0.1);
            border-left-color: #00D4FF;
        }

        .stat-card {
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(139, 92, 246, 0.1));
            border: 1px solid rgba(0, 212, 255, 0.3);
        }

        .credit-meter {
            background: linear-gradient(90deg, #10B981, #00D4FF, #8B5CF6);
            height: 8px;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }

        .credit-meter::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .generation-item {
            background: rgba(26, 26, 46, 0.6);
            border: 1px solid rgba(0, 212, 255, 0.2);
            transition: all 0.3s ease;
        }

        .generation-item:hover {
            border-color: #00D4FF;
            background: rgba(26, 26, 46, 0.8);
        }

        .tier-badge {
            background: linear-gradient(45deg, #00D4FF, #8B5CF6);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .tier-badge.free { background: linear-gradient(45deg, #6B7280, #9CA3AF); }
        .tier-badge.pro { background: linear-gradient(45deg, #10B981, #059669); }
        .tier-badge.business { background: linear-gradient(45deg, #F59E0B, #D97706); }
        .tier-badge.enterprise { background: linear-gradient(45deg, #8B5CF6, #7C3AED); }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="fixed left-0 top-0 h-full w-64 sidebar z-50">
        <div class="p-6">
            <div class="flex items-center space-x-3 mb-8">
                <div class="w-10 h-10 bg-gradient-to-r from-cyber-blue to-cyber-purple rounded-full flex items-center justify-center floating-element">
                    <span class="text-white font-bold text-lg">🎭</span>
                </div>
                <div>
                    <h1 class="text-xl font-bold hologram-text">SUTRADHAR</h1>
                    <p class="text-cyber-blue text-xs">2070 Dashboard</p>
                </div>
            </div>

            <nav class="space-y-2">
                <a href="#overview" class="nav-item active flex items-center space-x-3 px-4 py-3 rounded-lg">
                    <span class="text-cyber-blue">📊</span>
                    <span>Overview</span>
                </a>
                <a href="#generate" class="nav-item flex items-center space-x-3 px-4 py-3 rounded-lg">
                    <span class="text-cyber-purple">🎬</span>
                    <span>Generate Video</span>
                </a>
                <a href="#history" class="nav-item flex items-center space-x-3 px-4 py-3 rounded-lg">
                    <span class="text-cyber-green">📝</span>
                    <span>Generation History</span>
                </a>
                <a href="#billing" class="nav-item flex items-center space-x-3 px-4 py-3 rounded-lg">
                    <span class="text-cyber-orange">💳</span>
                    <span>Billing & Credits</span>
                </a>
                <a href="#analytics" class="nav-item flex items-center space-x-3 px-4 py-3 rounded-lg">
                    <span class="text-cyber-pink">📈</span>
                    <span>Analytics</span>
                </a>
                <a href="#settings" class="nav-item flex items-center space-x-3 px-4 py-3 rounded-lg">
                    <span class="text-gray-400">⚙️</span>
                    <span>Settings</span>
                </a>
            </nav>
        </div>

        <div class="absolute bottom-6 left-6 right-6">
            <div class="glass-morphism p-4 rounded-lg">
                <div class="flex items-center space-x-3 mb-2">
                    <div class="w-8 h-8 bg-gradient-to-r from-cyber-blue to-cyber-purple rounded-full"></div>
                    <div>
                        <p class="font-semibold text-sm" id="user-name">John Doe</p>
                        <span class="tier-badge pro" id="user-tier">Pro</span>
                    </div>
                </div>
                <button class="w-full text-left text-sm text-gray-400 hover:text-white transition-colors" onclick="logout()">
                    🚪 Sign Out
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="ml-64 min-h-screen">
        <!-- Header -->
        <header class="glass-morphism border-b border-cyber-blue/30 sticky top-0 z-40">
            <div class="px-8 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-2xl font-bold hologram-text">Dashboard Overview</h2>
                        <p class="text-gray-400">Welcome back! Ready to create amazing videos?</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="text-right">
                            <p class="text-sm text-gray-400">Credits Remaining</p>
                            <p class="text-2xl font-bold text-cyber-green" id="credit-balance">450</p>
                        </div>
                        <button class="cyber-button px-6 py-2 rounded-lg font-bold" onclick="window.location.href='mood_video_generator.html'">
                            🚀 Generate Video
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <main class="p-8">
            <!-- Overview Section -->
            <section id="overview-section" class="space-y-8">
                <!-- Stats Cards -->
                <div class="grid md:grid-cols-4 gap-6">
                    <div class="stat-card p-6 rounded-lg">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-cyber-blue/20 rounded-lg flex items-center justify-center">
                                <span class="text-cyber-blue text-xl">🎬</span>
                            </div>
                            <span class="text-2xl font-bold text-cyber-blue" id="total-videos">24</span>
                        </div>
                        <h3 class="font-semibold mb-1">Videos Generated</h3>
                        <p class="text-sm text-gray-400">This month</p>
                    </div>

                    <div class="stat-card p-6 rounded-lg">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-cyber-purple/20 rounded-lg flex items-center justify-center">
                                <span class="text-cyber-purple text-xl">⚡</span>
                            </div>
                            <span class="text-2xl font-bold text-cyber-purple" id="credits-used">350</span>
                        </div>
                        <h3 class="font-semibold mb-1">Credits Used</h3>
                        <p class="text-sm text-gray-400">This month</p>
                    </div>

                    <div class="stat-card p-6 rounded-lg">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-cyber-green/20 rounded-lg flex items-center justify-center">
                                <span class="text-cyber-green text-xl">📊</span>
                            </div>
                            <span class="text-2xl font-bold text-cyber-green" id="avg-quality">94%</span>
                        </div>
                        <h3 class="font-semibold mb-1">Avg Quality</h3>
                        <p class="text-sm text-gray-400">Quality score</p>
                    </div>

                    <div class="stat-card p-6 rounded-lg">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-cyber-orange/20 rounded-lg flex items-center justify-center">
                                <span class="text-cyber-orange text-xl">⏱️</span>
                            </div>
                            <span class="text-2xl font-bold text-cyber-orange" id="avg-time">2.3m</span>
                        </div>
                        <h3 class="font-semibold mb-1">Avg Generation</h3>
                        <p class="text-sm text-gray-400">Time per video</p>
                    </div>
                </div>

                <!-- Credit Usage & Quick Actions -->
                <div class="grid md:grid-cols-2 gap-8">
                    <!-- Credit Usage -->
                    <div class="dashboard-card p-6 rounded-lg">
                        <h3 class="text-xl font-semibold mb-4 flex items-center">
                            <span class="text-cyber-green mr-2">💎</span>
                            Credit Usage
                        </h3>
                        
                        <div class="mb-6">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm text-gray-400">Monthly Allowance</span>
                                <span class="text-sm font-semibold">450 / 500</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="credit-meter w-4/5 h-2 rounded-full"></div>
                            </div>
                        </div>

                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-sm">Mood-Optimized (15 credits)</span>
                                <span class="text-sm font-semibold">12 videos</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm">Premium Quality (25 credits)</span>
                                <span class="text-sm font-semibold">8 videos</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm">Basic Generation (10 credits)</span>
                                <span class="text-sm font-semibold">4 videos</span>
                            </div>
                        </div>

                        <button class="w-full mt-4 bg-cyber-green/20 border border-cyber-green text-cyber-green py-2 rounded-lg hover:bg-cyber-green hover:text-white transition-all">
                            Purchase More Credits
                        </button>
                    </div>

                    <!-- Quick Actions -->
                    <div class="dashboard-card p-6 rounded-lg">
                        <h3 class="text-xl font-semibold mb-4 flex items-center">
                            <span class="text-cyber-blue mr-2">⚡</span>
                            Quick Actions
                        </h3>
                        
                        <div class="space-y-4">
                            <button class="w-full cyber-button p-4 rounded-lg text-left" onclick="window.location.href='mood_video_generator.html'">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="font-semibold">🎭 Mood-Based Generation</h4>
                                        <p class="text-sm opacity-80">Create videos with emotional intelligence</p>
                                    </div>
                                    <span class="text-2xl">→</span>
                                </div>
                            </button>

                            <button class="w-full bg-cyber-purple/20 border border-cyber-purple text-cyber-purple p-4 rounded-lg text-left hover:bg-cyber-purple hover:text-white transition-all">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="font-semibold">📊 Bulk Generation</h4>
                                        <p class="text-sm opacity-80">Generate multiple videos at once</p>
                                    </div>
                                    <span class="text-2xl">→</span>
                                </div>
                            </button>

                            <button class="w-full bg-cyber-pink/20 border border-cyber-pink text-cyber-pink p-4 rounded-lg text-left hover:bg-cyber-pink hover:text-white transition-all">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="font-semibold">🔧 API Access</h4>
                                        <p class="text-sm opacity-80">Integrate with your applications</p>
                                    </div>
                                    <span class="text-2xl">→</span>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Recent Generations -->
                <div class="dashboard-card p-6 rounded-lg">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-semibold flex items-center">
                            <span class="text-cyber-orange mr-2">📝</span>
                            Recent Generations
                        </h3>
                        <button class="text-cyber-blue hover:text-white transition-colors">View All</button>
                    </div>

                    <div class="space-y-4" id="recent-generations">
                        <!-- Generation items will be loaded here -->
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script src="dashboard.js"></script>
</body>
</html>
