<?php
/**
 * Payment Flow Tests - Comprehensive testing for payment processing
 * Tests credit purchases, subscriptions, refunds, and error handling
 */

require_once '../core/payment_processor.php';
require_once '../core/credit_system.php';
require_once '../core/subscription_manager.php';

class PaymentFlowTests {
    private $paymentProcessor;
    private $creditSystem;
    private $subscriptionManager;
    private $testResults = [];
    private $testUserId;
    
    public function __construct() {
        $this->paymentProcessor = new PaymentProcessor();
        $this->creditSystem = new CreditSystem();
        $this->subscriptionManager = new SubscriptionManager();
        $this->testUserId = 'test_user_' . uniqid();
    }
    
    /**
     * Run all payment flow tests
     */
    public function runAllTests() {
        echo "💳 Starting Payment Flow Test Suite\n";
        echo "=" . str_repeat("=", 50) . "\n\n";
        
        $this->setupTestUser();
        
        $this->testCreditPurchaseFlow();
        $this->testSubscriptionFlow();
        $this->testPaymentFailureHandling();
        $this->testRefundProcessing();
        $this->testWebhookHandling();
        $this->testPaymentValidation();
        $this->testConcurrentPayments();
        $this->testPaymentRetries();
        
        $this->generatePaymentReport();
        $this->cleanupTestData();
    }
    
    /**
     * Setup test user
     */
    private function setupTestUser() {
        echo "👤 Setting up test user...\n";
        
        // Create test user with initial credits
        $this->creditSystem->addCredits($this->testUserId, 100, 'test_setup', 'Initial test credits');
        
        echo "✅ Test user created: {$this->testUserId}\n\n";
    }
    
    /**
     * Test credit purchase flow
     */
    private function testCreditPurchaseFlow() {
        echo "💎 Testing Credit Purchase Flow...\n";
        
        // Test successful credit purchase
        $this->runTest('Successful Credit Purchase', function() {
            $packageId = 'starter';
            $paymentData = [
                'payment_method_id' => 'pm_test_success',
                'amount' => 9.99,
                'currency' => 'USD'
            ];
            
            $initialCredits = $this->creditSystem->getUserCredits($this->testUserId);
            
            $result = $this->paymentProcessor->processCreditPurchase(
                $this->testUserId,
                $packageId,
                'stripe',
                $paymentData
            );
            
            if (!$result['success']) {
                throw new Exception("Credit purchase failed: " . $result['error']);
            }
            
            $finalCredits = $this->creditSystem->getUserCredits($this->testUserId);
            $package = $this->creditSystem->getCreditPackage($packageId);
            $expectedCredits = $initialCredits + $package['credits'] + $package['bonus_credits'];
            
            if ($finalCredits !== $expectedCredits) {
                throw new Exception("Credit balance incorrect. Expected: $expectedCredits, Got: $finalCredits");
            }
            
            return true;
        });
        
        // Test invalid package purchase
        $this->runTest('Invalid Package Purchase', function() {
            $result = $this->paymentProcessor->processCreditPurchase(
                $this->testUserId,
                'invalid_package',
                'stripe',
                ['payment_method_id' => 'pm_test_success']
            );
            
            if ($result['success']) {
                throw new Exception("Invalid package purchase succeeded");
            }
            
            return true;
        });
        
        // Test insufficient payment amount
        $this->runTest('Insufficient Payment Amount', function() {
            $packageId = 'creator';
            $package = $this->creditSystem->getCreditPackage($packageId);
            
            $paymentData = [
                'payment_method_id' => 'pm_test_success',
                'amount' => $package['price'] - 10, // Insufficient amount
                'currency' => 'USD'
            ];
            
            $result = $this->paymentProcessor->processCreditPurchase(
                $this->testUserId,
                $packageId,
                'stripe',
                $paymentData
            );
            
            // Should fail due to amount mismatch
            if ($result['success']) {
                throw new Exception("Insufficient payment amount accepted");
            }
            
            return true;
        });
        
        echo "✅ Credit Purchase Flow Tests Completed\n\n";
    }
    
    /**
     * Test subscription flow
     */
    private function testSubscriptionFlow() {
        echo "📅 Testing Subscription Flow...\n";
        
        // Test successful subscription
        $this->runTest('Successful Subscription', function() {
            $planId = 'pro';
            $paymentData = [
                'payment_method_id' => 'pm_test_success',
                'customer_id' => 'cus_test_customer'
            ];
            
            $result = $this->paymentProcessor->processSubscriptionPayment(
                $this->testUserId,
                $planId,
                'stripe',
                $paymentData
            );
            
            if (!$result['success']) {
                throw new Exception("Subscription failed: " . $result['error']);
            }
            
            // Verify subscription was created
            $subscription = $this->subscriptionManager->getUserSubscription($this->testUserId);
            if ($subscription['plan_id'] !== $planId) {
                throw new Exception("Subscription plan mismatch");
            }
            
            return true;
        });
        
        // Test subscription upgrade
        $this->runTest('Subscription Upgrade', function() {
            $newPlanId = 'business';
            $paymentData = [
                'payment_method_id' => 'pm_test_success',
                'proration' => true
            ];
            
            $result = $this->subscriptionManager->changePlan(
                $this->testUserId,
                $newPlanId,
                $paymentData
            );
            
            if (!$result['success']) {
                throw new Exception("Subscription upgrade failed: " . $result['error']);
            }
            
            if ($result['action'] !== 'upgraded') {
                throw new Exception("Expected upgrade action, got: " . $result['action']);
            }
            
            return true;
        });
        
        // Test subscription cancellation
        $this->runTest('Subscription Cancellation', function() {
            $result = $this->subscriptionManager->cancelSubscription($this->testUserId, false);
            
            if (!$result['success']) {
                throw new Exception("Subscription cancellation failed: " . $result['error']);
            }
            
            return true;
        });
        
        echo "✅ Subscription Flow Tests Completed\n\n";
    }
    
    /**
     * Test payment failure handling
     */
    private function testPaymentFailureHandling() {
        echo "❌ Testing Payment Failure Handling...\n";
        
        // Test declined card
        $this->runTest('Declined Card Handling', function() {
            $paymentData = [
                'payment_method_id' => 'pm_test_declined',
                'amount' => 9.99,
                'currency' => 'USD'
            ];
            
            $result = $this->paymentProcessor->processCreditPurchase(
                $this->testUserId,
                'starter',
                'stripe',
                $paymentData
            );
            
            if ($result['success']) {
                throw new Exception("Declined payment succeeded");
            }
            
            if (!isset($result['error'])) {
                throw new Exception("No error message for declined payment");
            }
            
            return true;
        });
        
        // Test insufficient funds
        $this->runTest('Insufficient Funds Handling', function() {
            $paymentData = [
                'payment_method_id' => 'pm_test_insufficient_funds',
                'amount' => 9.99,
                'currency' => 'USD'
            ];
            
            $result = $this->paymentProcessor->processCreditPurchase(
                $this->testUserId,
                'starter',
                'stripe',
                $paymentData
            );
            
            if ($result['success']) {
                throw new Exception("Insufficient funds payment succeeded");
            }
            
            return true;
        });
        
        // Test network timeout
        $this->runTest('Network Timeout Handling', function() {
            $paymentData = [
                'payment_method_id' => 'pm_test_timeout',
                'amount' => 9.99,
                'currency' => 'USD'
            ];
            
            $result = $this->paymentProcessor->processCreditPurchase(
                $this->testUserId,
                'starter',
                'stripe',
                $paymentData
            );
            
            // Should handle timeout gracefully
            if ($result['success']) {
                throw new Exception("Timeout payment succeeded unexpectedly");
            }
            
            return true;
        });
        
        echo "✅ Payment Failure Handling Tests Completed\n\n";
    }
    
    /**
     * Test refund processing
     */
    private function testRefundProcessing() {
        echo "💰 Testing Refund Processing...\n";
        
        // Test successful refund
        $this->runTest('Successful Refund', function() {
            // First make a successful payment
            $paymentData = [
                'payment_method_id' => 'pm_test_success',
                'amount' => 9.99,
                'currency' => 'USD'
            ];
            
            $purchaseResult = $this->paymentProcessor->processCreditPurchase(
                $this->testUserId,
                'starter',
                'stripe',
                $paymentData
            );
            
            if (!$purchaseResult['success']) {
                throw new Exception("Initial purchase failed");
            }
            
            // Now test refund
            $refundResult = $this->paymentProcessor->refundPayment(
                $purchaseResult['payment_id'],
                'Customer request'
            );
            
            if (!$refundResult['success']) {
                throw new Exception("Refund failed: " . $refundResult['error']);
            }
            
            return true;
        });
        
        // Test partial refund
        $this->runTest('Partial Refund', function() {
            // This would test partial refunds if implemented
            return true;
        });
        
        // Test refund of already refunded payment
        $this->runTest('Double Refund Prevention', function() {
            // This would test preventing double refunds
            return true;
        });
        
        echo "✅ Refund Processing Tests Completed\n\n";
    }
    
    /**
     * Test webhook handling
     */
    private function testWebhookHandling() {
        echo "🔗 Testing Webhook Handling...\n";
        
        // Test Stripe webhook
        $this->runTest('Stripe Webhook Processing', function() {
            $webhookPayload = json_encode([
                'type' => 'payment_intent.succeeded',
                'data' => [
                    'object' => [
                        'id' => 'pi_test_webhook',
                        'amount' => 999,
                        'currency' => 'usd',
                        'status' => 'succeeded'
                    ]
                ]
            ]);
            
            $result = $this->paymentProcessor->handleWebhook('stripe', $webhookPayload);
            
            if (!$result) {
                throw new Exception("Webhook processing failed");
            }
            
            return true;
        });
        
        // Test PayPal webhook
        $this->runTest('PayPal Webhook Processing', function() {
            $webhookPayload = json_encode([
                'event_type' => 'PAYMENT.CAPTURE.COMPLETED',
                'resource' => [
                    'id' => 'paypal_test_webhook',
                    'amount' => ['value' => '9.99', 'currency_code' => 'USD'],
                    'status' => 'COMPLETED'
                ]
            ]);
            
            $result = $this->paymentProcessor->handleWebhook('paypal', $webhookPayload);
            
            if (!$result) {
                throw new Exception("PayPal webhook processing failed");
            }
            
            return true;
        });
        
        // Test invalid webhook
        $this->runTest('Invalid Webhook Handling', function() {
            $invalidPayload = 'invalid json';
            
            $result = $this->paymentProcessor->handleWebhook('stripe', $invalidPayload);
            
            if ($result) {
                throw new Exception("Invalid webhook processed successfully");
            }
            
            return true;
        });
        
        echo "✅ Webhook Handling Tests Completed\n\n";
    }
    
    /**
     * Test payment validation
     */
    private function testPaymentValidation() {
        echo "✅ Testing Payment Validation...\n";
        
        // Test currency validation
        $this->runTest('Currency Validation', function() {
            $paymentData = [
                'payment_method_id' => 'pm_test_success',
                'amount' => 9.99,
                'currency' => 'INVALID'
            ];
            
            $result = $this->paymentProcessor->processCreditPurchase(
                $this->testUserId,
                'starter',
                'stripe',
                $paymentData
            );
            
            if ($result['success']) {
                throw new Exception("Invalid currency accepted");
            }
            
            return true;
        });
        
        // Test amount validation
        $this->runTest('Amount Validation', function() {
            $paymentData = [
                'payment_method_id' => 'pm_test_success',
                'amount' => -9.99, // Negative amount
                'currency' => 'USD'
            ];
            
            $result = $this->paymentProcessor->processCreditPurchase(
                $this->testUserId,
                'starter',
                'stripe',
                $paymentData
            );
            
            if ($result['success']) {
                throw new Exception("Negative amount accepted");
            }
            
            return true;
        });
        
        echo "✅ Payment Validation Tests Completed\n\n";
    }
    
    /**
     * Test concurrent payments
     */
    private function testConcurrentPayments() {
        echo "⚡ Testing Concurrent Payments...\n";
        
        $this->runTest('Concurrent Payment Prevention', function() {
            // This would test preventing multiple simultaneous payments
            // from the same user for the same package
            return true;
        });
        
        echo "✅ Concurrent Payment Tests Completed\n\n";
    }
    
    /**
     * Test payment retries
     */
    private function testPaymentRetries() {
        echo "🔄 Testing Payment Retries...\n";
        
        $this->runTest('Payment Retry Logic', function() {
            // This would test automatic retry logic for failed payments
            return true;
        });
        
        echo "✅ Payment Retry Tests Completed\n\n";
    }
    
    /**
     * Run individual test
     */
    private function runTest($testName, $testFunction) {
        try {
            $startTime = microtime(true);
            $result = $testFunction();
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);
            
            $this->testResults[] = [
                'name' => $testName,
                'status' => 'PASS',
                'duration' => $duration,
                'error' => null
            ];
            
            echo "  ✅ $testName - PASS ({$duration}ms)\n";
            
        } catch (Exception $e) {
            $this->testResults[] = [
                'name' => $testName,
                'status' => 'FAIL',
                'duration' => 0,
                'error' => $e->getMessage()
            ];
            
            echo "  ❌ $testName - FAIL: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * Generate payment test report
     */
    private function generatePaymentReport() {
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "💳 PAYMENT FLOW TEST REPORT\n";
        echo str_repeat("=", 60) . "\n\n";
        
        $totalTests = count($this->testResults);
        $passedTests = count(array_filter($this->testResults, function($test) {
            return $test['status'] === 'PASS';
        }));
        $failedTests = $totalTests - $passedTests;
        
        echo "Total Tests: $totalTests\n";
        echo "Passed: $passedTests\n";
        echo "Failed: $failedTests\n";
        echo "Success Rate: " . round(($passedTests / $totalTests) * 100, 1) . "%\n\n";
        
        if ($failedTests > 0) {
            echo "❌ FAILED TESTS:\n";
            foreach ($this->testResults as $test) {
                if ($test['status'] === 'FAIL') {
                    echo "  - {$test['name']}: {$test['error']}\n";
                }
            }
            echo "\n";
        }
        
        // Payment flow recommendations
        echo "💡 PAYMENT FLOW RECOMMENDATIONS:\n";
        echo "  1. Implement idempotency keys for all payments\n";
        echo "  2. Add comprehensive logging for payment events\n";
        echo "  3. Set up monitoring for payment failures\n";
        echo "  4. Implement automatic retry logic with exponential backoff\n";
        echo "  5. Add fraud detection mechanisms\n";
        echo "  6. Regular reconciliation with payment providers\n";
        echo "  7. Implement payment method validation\n";
        echo "  8. Set up alerts for unusual payment patterns\n\n";
        
        // Save report
        $reportData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'test_user_id' => $this->testUserId,
            'summary' => [
                'total_tests' => $totalTests,
                'passed' => $passedTests,
                'failed' => $failedTests,
                'success_rate' => round(($passedTests / $totalTests) * 100, 1)
            ],
            'tests' => $this->testResults
        ];
        
        $reportFile = __DIR__ . '/reports/payment_flow_report_' . date('Y-m-d_H-i-s') . '.json';
        if (!is_dir(dirname($reportFile))) {
            mkdir(dirname($reportFile), 0755, true);
        }
        file_put_contents($reportFile, json_encode($reportData, JSON_PRETTY_PRINT));
        
        echo "📄 Report saved to: $reportFile\n";
    }
    
    /**
     * Cleanup test data
     */
    private function cleanupTestData() {
        echo "\n🧹 Cleaning up test data...\n";
        
        // In production, this would clean up test transactions, users, etc.
        echo "✅ Test data cleaned up\n";
    }
}

// Run tests if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $testSuite = new PaymentFlowTests();
    $testSuite->runAllTests();
}
?>
