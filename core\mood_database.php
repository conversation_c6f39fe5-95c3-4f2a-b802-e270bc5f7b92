<?php
/**
 * Mood Database System - Comprehensive mood management for video generation
 * Provides 30 distinct moods with associated characteristics for content creation
 */

class MoodDatabase {
    private $moods;
    private $moodCategories;
    
    public function __construct() {
        $this->initializeMoods();
        $this->initializeCategories();
    }
    
    /**
     * Initialize comprehensive mood database with 30 distinct moods
     */
    private function initializeMoods() {
        $this->moods = [
            // Positive Energy Moods
            'euphoric' => [
                'name' => 'Euphoric',
                'category' => 'positive_energy',
                'description' => 'Overwhelming joy and excitement',
                'visual_style' => [
                    'colors' => ['bright_yellow', 'electric_blue', 'vibrant_orange'],
                    'lighting' => 'high_contrast_bright',
                    'movement' => 'dynamic_fast',
                    'composition' => 'energetic_angles'
                ],
                'audio_characteristics' => [
                    'tempo' => 'fast',
                    'energy' => 'high',
                    'voice_tone' => 'excited_enthusiastic',
                    'background_music' => 'upbeat_electronic'
                ],
                'content_style' => [
                    'language' => 'exclamatory_positive',
                    'pacing' => 'rapid_energetic',
                    'emotional_intensity' => 'very_high'
                ]
            ],
            
            'inspiring' => [
                'name' => 'Inspiring',
                'category' => 'positive_energy',
                'description' => 'Motivational and uplifting energy',
                'visual_style' => [
                    'colors' => ['golden_yellow', 'sky_blue', 'warm_white'],
                    'lighting' => 'soft_upward',
                    'movement' => 'ascending_smooth',
                    'composition' => 'heroic_perspective'
                ],
                'audio_characteristics' => [
                    'tempo' => 'moderate_building',
                    'energy' => 'building_crescendo',
                    'voice_tone' => 'confident_warm',
                    'background_music' => 'orchestral_inspiring'
                ],
                'content_style' => [
                    'language' => 'empowering_positive',
                    'pacing' => 'measured_building',
                    'emotional_intensity' => 'high'
                ]
            ],
            
            'playful' => [
                'name' => 'Playful',
                'category' => 'positive_energy',
                'description' => 'Fun, lighthearted, and whimsical',
                'visual_style' => [
                    'colors' => ['bright_pink', 'lime_green', 'purple'],
                    'lighting' => 'bouncy_colorful',
                    'movement' => 'bouncy_irregular',
                    'composition' => 'asymmetrical_fun'
                ],
                'audio_characteristics' => [
                    'tempo' => 'variable_bouncy',
                    'energy' => 'playful_moderate',
                    'voice_tone' => 'cheerful_animated',
                    'background_music' => 'quirky_upbeat'
                ],
                'content_style' => [
                    'language' => 'casual_fun',
                    'pacing' => 'varied_playful',
                    'emotional_intensity' => 'moderate'
                ]
            ],
            
            'confident' => [
                'name' => 'Confident',
                'category' => 'positive_energy',
                'description' => 'Self-assured and powerful',
                'visual_style' => [
                    'colors' => ['deep_blue', 'gold', 'black'],
                    'lighting' => 'strong_directional',
                    'movement' => 'steady_purposeful',
                    'composition' => 'centered_strong'
                ],
                'audio_characteristics' => [
                    'tempo' => 'steady_strong',
                    'energy' => 'controlled_high',
                    'voice_tone' => 'authoritative_clear',
                    'background_music' => 'powerful_rhythmic'
                ],
                'content_style' => [
                    'language' => 'assertive_clear',
                    'pacing' => 'deliberate_strong',
                    'emotional_intensity' => 'high'
                ]
            ],
            
            'adventurous' => [
                'name' => 'Adventurous',
                'category' => 'positive_energy',
                'description' => 'Exciting exploration and discovery',
                'visual_style' => [
                    'colors' => ['forest_green', 'earth_brown', 'sunset_orange'],
                    'lighting' => 'natural_dynamic',
                    'movement' => 'exploring_varied',
                    'composition' => 'wide_expansive'
                ],
                'audio_characteristics' => [
                    'tempo' => 'varied_exciting',
                    'energy' => 'adventurous_building',
                    'voice_tone' => 'excited_curious',
                    'background_music' => 'adventure_orchestral'
                ],
                'content_style' => [
                    'language' => 'descriptive_exciting',
                    'pacing' => 'varied_engaging',
                    'emotional_intensity' => 'high'
                ]
            ],
            
            // Calm & Peaceful Moods
            'serene' => [
                'name' => 'Serene',
                'category' => 'calm_peaceful',
                'description' => 'Peaceful and tranquil state',
                'visual_style' => [
                    'colors' => ['soft_blue', 'pale_green', 'cream_white'],
                    'lighting' => 'soft_diffused',
                    'movement' => 'slow_flowing',
                    'composition' => 'balanced_harmonious'
                ],
                'audio_characteristics' => [
                    'tempo' => 'slow_peaceful',
                    'energy' => 'low_calming',
                    'voice_tone' => 'gentle_soothing',
                    'background_music' => 'ambient_peaceful'
                ],
                'content_style' => [
                    'language' => 'gentle_flowing',
                    'pacing' => 'slow_meditative',
                    'emotional_intensity' => 'low'
                ]
            ],
            
            'contemplative' => [
                'name' => 'Contemplative',
                'category' => 'calm_peaceful',
                'description' => 'Thoughtful and reflective',
                'visual_style' => [
                    'colors' => ['muted_purple', 'soft_gray', 'warm_beige'],
                    'lighting' => 'gentle_focused',
                    'movement' => 'minimal_thoughtful',
                    'composition' => 'centered_reflective'
                ],
                'audio_characteristics' => [
                    'tempo' => 'slow_thoughtful',
                    'energy' => 'low_focused',
                    'voice_tone' => 'reflective_calm',
                    'background_music' => 'minimal_contemplative'
                ],
                'content_style' => [
                    'language' => 'thoughtful_deep',
                    'pacing' => 'deliberate_reflective',
                    'emotional_intensity' => 'moderate'
                ]
            ],
            
            'nostalgic' => [
                'name' => 'Nostalgic',
                'category' => 'calm_peaceful',
                'description' => 'Wistful remembrance of the past',
                'visual_style' => [
                    'colors' => ['sepia_brown', 'faded_yellow', 'soft_pink'],
                    'lighting' => 'warm_vintage',
                    'movement' => 'gentle_drifting',
                    'composition' => 'soft_dreamy'
                ],
                'audio_characteristics' => [
                    'tempo' => 'moderate_gentle',
                    'energy' => 'moderate_warm',
                    'voice_tone' => 'warm_wistful',
                    'background_music' => 'vintage_emotional'
                ],
                'content_style' => [
                    'language' => 'warm_reminiscent',
                    'pacing' => 'gentle_flowing',
                    'emotional_intensity' => 'moderate'
                ]
            ],

            'meditative' => [
                'name' => 'Meditative',
                'category' => 'calm_peaceful',
                'description' => 'Deep inner peace and mindfulness',
                'visual_style' => [
                    'colors' => ['deep_purple', 'soft_white', 'gentle_gold'],
                    'lighting' => 'soft_centered',
                    'movement' => 'minimal_flowing',
                    'composition' => 'symmetrical_balanced'
                ],
                'audio_characteristics' => [
                    'tempo' => 'very_slow',
                    'energy' => 'very_low_peaceful',
                    'voice_tone' => 'calm_centered',
                    'background_music' => 'meditation_ambient'
                ],
                'content_style' => [
                    'language' => 'peaceful_mindful',
                    'pacing' => 'very_slow_deliberate',
                    'emotional_intensity' => 'low'
                ]
            ],

            'melancholic' => [
                'name' => 'Melancholic',
                'category' => 'calm_peaceful',
                'description' => 'Gentle sadness and introspection',
                'visual_style' => [
                    'colors' => ['muted_blue', 'gray', 'pale_violet'],
                    'lighting' => 'soft_dim',
                    'movement' => 'slow_downward',
                    'composition' => 'isolated_contemplative'
                ],
                'audio_characteristics' => [
                    'tempo' => 'slow_gentle',
                    'energy' => 'low_emotional',
                    'voice_tone' => 'soft_melancholic',
                    'background_music' => 'minor_key_gentle'
                ],
                'content_style' => [
                    'language' => 'gentle_sad',
                    'pacing' => 'slow_reflective',
                    'emotional_intensity' => 'moderate'
                ]
            ],

            // Dramatic & Intense Moods
            'passionate' => [
                'name' => 'Passionate',
                'category' => 'dramatic_intense',
                'description' => 'Intense emotion and fervor',
                'visual_style' => [
                    'colors' => ['deep_red', 'burning_orange', 'intense_purple'],
                    'lighting' => 'dramatic_contrasts',
                    'movement' => 'intense_dynamic',
                    'composition' => 'close_intimate'
                ],
                'audio_characteristics' => [
                    'tempo' => 'variable_intense',
                    'energy' => 'very_high_emotional',
                    'voice_tone' => 'passionate_intense',
                    'background_music' => 'dramatic_orchestral'
                ],
                'content_style' => [
                    'language' => 'intense_emotional',
                    'pacing' => 'varied_passionate',
                    'emotional_intensity' => 'very_high'
                ]
            ],

            'heroic' => [
                'name' => 'Heroic',
                'category' => 'dramatic_intense',
                'description' => 'Noble courage and determination',
                'visual_style' => [
                    'colors' => ['royal_blue', 'gold', 'silver'],
                    'lighting' => 'epic_dramatic',
                    'movement' => 'powerful_upward',
                    'composition' => 'heroic_wide'
                ],
                'audio_characteristics' => [
                    'tempo' => 'building_powerful',
                    'energy' => 'high_noble',
                    'voice_tone' => 'strong_heroic',
                    'background_music' => 'epic_orchestral'
                ],
                'content_style' => [
                    'language' => 'noble_inspiring',
                    'pacing' => 'building_epic',
                    'emotional_intensity' => 'very_high'
                ]
            ],

            'dramatic' => [
                'name' => 'Dramatic',
                'category' => 'dramatic_intense',
                'description' => 'Theatrical and emotionally charged',
                'visual_style' => [
                    'colors' => ['black', 'white', 'crimson_red'],
                    'lighting' => 'high_contrast_theatrical',
                    'movement' => 'sweeping_dramatic',
                    'composition' => 'theatrical_angles'
                ],
                'audio_characteristics' => [
                    'tempo' => 'varied_dramatic',
                    'energy' => 'high_theatrical',
                    'voice_tone' => 'dramatic_expressive',
                    'background_music' => 'theatrical_dramatic'
                ],
                'content_style' => [
                    'language' => 'expressive_theatrical',
                    'pacing' => 'varied_dramatic',
                    'emotional_intensity' => 'very_high'
                ]
            ],

            'intense' => [
                'name' => 'Intense',
                'category' => 'dramatic_intense',
                'description' => 'Raw power and overwhelming emotion',
                'visual_style' => [
                    'colors' => ['electric_red', 'stark_white', 'deep_black'],
                    'lighting' => 'harsh_contrasting',
                    'movement' => 'aggressive_fast',
                    'composition' => 'tight_focused'
                ],
                'audio_characteristics' => [
                    'tempo' => 'fast_aggressive',
                    'energy' => 'maximum_intense',
                    'voice_tone' => 'intense_powerful',
                    'background_music' => 'intense_electronic'
                ],
                'content_style' => [
                    'language' => 'powerful_direct',
                    'pacing' => 'rapid_intense',
                    'emotional_intensity' => 'maximum'
                ]
            ],

            'rebellious' => [
                'name' => 'Rebellious',
                'category' => 'dramatic_intense',
                'description' => 'Defiant and revolutionary spirit',
                'visual_style' => [
                    'colors' => ['punk_pink', 'electric_green', 'black'],
                    'lighting' => 'edgy_contrasting',
                    'movement' => 'chaotic_energetic',
                    'composition' => 'unconventional_bold'
                ],
                'audio_characteristics' => [
                    'tempo' => 'fast_rebellious',
                    'energy' => 'high_defiant',
                    'voice_tone' => 'rebellious_strong',
                    'background_music' => 'rock_electronic'
                ],
                'content_style' => [
                    'language' => 'bold_defiant',
                    'pacing' => 'aggressive_confident',
                    'emotional_intensity' => 'high'
                ]
            ],

            // Mysterious & Dark Moods
            'mysterious' => [
                'name' => 'Mysterious',
                'category' => 'mysterious_dark',
                'description' => 'Enigmatic and intriguing atmosphere',
                'visual_style' => [
                    'colors' => ['deep_purple', 'midnight_blue', 'silver'],
                    'lighting' => 'shadowy_mysterious',
                    'movement' => 'subtle_hidden',
                    'composition' => 'partially_obscured'
                ],
                'audio_characteristics' => [
                    'tempo' => 'slow_mysterious',
                    'energy' => 'moderate_intriguing',
                    'voice_tone' => 'mysterious_whispered',
                    'background_music' => 'ambient_mysterious'
                ],
                'content_style' => [
                    'language' => 'enigmatic_intriguing',
                    'pacing' => 'deliberate_mysterious',
                    'emotional_intensity' => 'moderate'
                ]
            ],

            'dark' => [
                'name' => 'Dark',
                'category' => 'mysterious_dark',
                'description' => 'Brooding and shadowy atmosphere',
                'visual_style' => [
                    'colors' => ['black', 'dark_gray', 'blood_red'],
                    'lighting' => 'minimal_harsh',
                    'movement' => 'slow_ominous',
                    'composition' => 'shadowy_dramatic'
                ],
                'audio_characteristics' => [
                    'tempo' => 'slow_ominous',
                    'energy' => 'low_dark',
                    'voice_tone' => 'deep_brooding',
                    'background_music' => 'dark_ambient'
                ],
                'content_style' => [
                    'language' => 'dark_brooding',
                    'pacing' => 'slow_ominous',
                    'emotional_intensity' => 'high'
                ]
            ],

            'suspenseful' => [
                'name' => 'Suspenseful',
                'category' => 'mysterious_dark',
                'description' => 'Tension and anticipation building',
                'visual_style' => [
                    'colors' => ['dark_blue', 'gray', 'white_highlights'],
                    'lighting' => 'dramatic_shadows',
                    'movement' => 'tense_building',
                    'composition' => 'tension_creating'
                ],
                'audio_characteristics' => [
                    'tempo' => 'building_tension',
                    'energy' => 'building_suspense',
                    'voice_tone' => 'tense_anticipatory',
                    'background_music' => 'suspense_building'
                ],
                'content_style' => [
                    'language' => 'tense_anticipatory',
                    'pacing' => 'building_suspense',
                    'emotional_intensity' => 'high'
                ]
            ],

            'gothic' => [
                'name' => 'Gothic',
                'category' => 'mysterious_dark',
                'description' => 'Dark romantic and dramatic atmosphere',
                'visual_style' => [
                    'colors' => ['deep_purple', 'black', 'silver'],
                    'lighting' => 'dramatic_gothic',
                    'movement' => 'flowing_dramatic',
                    'composition' => 'gothic_architectural'
                ],
                'audio_characteristics' => [
                    'tempo' => 'moderate_dramatic',
                    'energy' => 'moderate_dark',
                    'voice_tone' => 'dramatic_gothic',
                    'background_music' => 'gothic_orchestral'
                ],
                'content_style' => [
                    'language' => 'dramatic_poetic',
                    'pacing' => 'flowing_dramatic',
                    'emotional_intensity' => 'high'
                ]
            ],

            'eerie' => [
                'name' => 'Eerie',
                'category' => 'mysterious_dark',
                'description' => 'Unsettling and otherworldly',
                'visual_style' => [
                    'colors' => ['pale_green', 'gray', 'white'],
                    'lighting' => 'unnatural_eerie',
                    'movement' => 'unnatural_floating',
                    'composition' => 'unsettling_angles'
                ],
                'audio_characteristics' => [
                    'tempo' => 'irregular_eerie',
                    'energy' => 'low_unsettling',
                    'voice_tone' => 'eerie_whispered',
                    'background_music' => 'eerie_ambient'
                ],
                'content_style' => [
                    'language' => 'unsettling_mysterious',
                    'pacing' => 'irregular_eerie',
                    'emotional_intensity' => 'moderate'
                ]
            ],

            // Romantic & Emotional Moods
            'romantic' => [
                'name' => 'Romantic',
                'category' => 'romantic_emotional',
                'description' => 'Love and intimate connection',
                'visual_style' => [
                    'colors' => ['soft_pink', 'warm_red', 'gold'],
                    'lighting' => 'warm_intimate',
                    'movement' => 'gentle_flowing',
                    'composition' => 'intimate_close'
                ],
                'audio_characteristics' => [
                    'tempo' => 'moderate_gentle',
                    'energy' => 'moderate_warm',
                    'voice_tone' => 'warm_intimate',
                    'background_music' => 'romantic_soft'
                ],
                'content_style' => [
                    'language' => 'tender_loving',
                    'pacing' => 'gentle_intimate',
                    'emotional_intensity' => 'high'
                ]
            ],

            'sensual' => [
                'name' => 'Sensual',
                'category' => 'romantic_emotional',
                'description' => 'Passionate and alluring',
                'visual_style' => [
                    'colors' => ['deep_red', 'purple', 'black'],
                    'lighting' => 'warm_seductive',
                    'movement' => 'slow_sensual',
                    'composition' => 'intimate_alluring'
                ],
                'audio_characteristics' => [
                    'tempo' => 'slow_sensual',
                    'energy' => 'moderate_passionate',
                    'voice_tone' => 'sultry_seductive',
                    'background_music' => 'sensual_jazz'
                ],
                'content_style' => [
                    'language' => 'passionate_alluring',
                    'pacing' => 'slow_seductive',
                    'emotional_intensity' => 'high'
                ]
            ],

            'heartfelt' => [
                'name' => 'Heartfelt',
                'category' => 'romantic_emotional',
                'description' => 'Deep emotional sincerity',
                'visual_style' => [
                    'colors' => ['warm_pink', 'soft_gold', 'cream'],
                    'lighting' => 'warm_gentle',
                    'movement' => 'gentle_sincere',
                    'composition' => 'open_honest'
                ],
                'audio_characteristics' => [
                    'tempo' => 'moderate_sincere',
                    'energy' => 'moderate_emotional',
                    'voice_tone' => 'sincere_heartfelt',
                    'background_music' => 'emotional_piano'
                ],
                'content_style' => [
                    'language' => 'sincere_emotional',
                    'pacing' => 'gentle_heartfelt',
                    'emotional_intensity' => 'high'
                ]
            ],

            'bittersweet' => [
                'name' => 'Bittersweet',
                'category' => 'romantic_emotional',
                'description' => 'Mixed joy and sadness',
                'visual_style' => [
                    'colors' => ['muted_pink', 'soft_blue', 'gray'],
                    'lighting' => 'soft_mixed',
                    'movement' => 'gentle_conflicted',
                    'composition' => 'balanced_emotional'
                ],
                'audio_characteristics' => [
                    'tempo' => 'moderate_mixed',
                    'energy' => 'moderate_complex',
                    'voice_tone' => 'bittersweet_complex',
                    'background_music' => 'bittersweet_strings'
                ],
                'content_style' => [
                    'language' => 'complex_emotional',
                    'pacing' => 'varied_emotional',
                    'emotional_intensity' => 'high'
                ]
            ],

            'yearning' => [
                'name' => 'Yearning',
                'category' => 'romantic_emotional',
                'description' => 'Deep longing and desire',
                'visual_style' => [
                    'colors' => ['deep_blue', 'purple', 'silver'],
                    'lighting' => 'soft_longing',
                    'movement' => 'reaching_distant',
                    'composition' => 'distant_longing'
                ],
                'audio_characteristics' => [
                    'tempo' => 'slow_longing',
                    'energy' => 'moderate_yearning',
                    'voice_tone' => 'longing_wistful',
                    'background_music' => 'yearning_orchestral'
                ],
                'content_style' => [
                    'language' => 'longing_poetic',
                    'pacing' => 'slow_yearning',
                    'emotional_intensity' => 'high'
                ]
            ],

            // Futuristic & Tech Moods
            'cyberpunk' => [
                'name' => 'Cyberpunk',
                'category' => 'futuristic_tech',
                'description' => 'High-tech dystopian future',
                'visual_style' => [
                    'colors' => ['neon_blue', 'electric_pink', 'black'],
                    'lighting' => 'neon_harsh',
                    'movement' => 'digital_glitchy',
                    'composition' => 'urban_tech'
                ],
                'audio_characteristics' => [
                    'tempo' => 'fast_electronic',
                    'energy' => 'high_digital',
                    'voice_tone' => 'robotic_cool',
                    'background_music' => 'cyberpunk_electronic'
                ],
                'content_style' => [
                    'language' => 'tech_futuristic',
                    'pacing' => 'fast_digital',
                    'emotional_intensity' => 'moderate'
                ]
            ],

            'futuristic' => [
                'name' => 'Futuristic',
                'category' => 'futuristic_tech',
                'description' => 'Advanced technology and innovation',
                'visual_style' => [
                    'colors' => ['chrome_silver', 'electric_blue', 'white'],
                    'lighting' => 'clean_technological',
                    'movement' => 'smooth_precise',
                    'composition' => 'geometric_clean'
                ],
                'audio_characteristics' => [
                    'tempo' => 'moderate_precise',
                    'energy' => 'moderate_clean',
                    'voice_tone' => 'clear_technological',
                    'background_music' => 'futuristic_ambient'
                ],
                'content_style' => [
                    'language' => 'precise_innovative',
                    'pacing' => 'measured_futuristic',
                    'emotional_intensity' => 'moderate'
                ]
            ],

            'sci_fi' => [
                'name' => 'Sci-Fi',
                'category' => 'futuristic_tech',
                'description' => 'Science fiction and space exploration',
                'visual_style' => [
                    'colors' => ['deep_space_blue', 'star_white', 'cosmic_purple'],
                    'lighting' => 'cosmic_ethereal',
                    'movement' => 'floating_cosmic',
                    'composition' => 'vast_cosmic'
                ],
                'audio_characteristics' => [
                    'tempo' => 'varied_cosmic',
                    'energy' => 'moderate_ethereal',
                    'voice_tone' => 'ethereal_cosmic',
                    'background_music' => 'space_ambient'
                ],
                'content_style' => [
                    'language' => 'scientific_cosmic',
                    'pacing' => 'varied_cosmic',
                    'emotional_intensity' => 'moderate'
                ]
            ],

            'digital' => [
                'name' => 'Digital',
                'category' => 'futuristic_tech',
                'description' => 'Pure digital and virtual reality',
                'visual_style' => [
                    'colors' => ['matrix_green', 'digital_blue', 'pixel_white'],
                    'lighting' => 'digital_grid',
                    'movement' => 'pixelated_digital',
                    'composition' => 'grid_based'
                ],
                'audio_characteristics' => [
                    'tempo' => 'rhythmic_digital',
                    'energy' => 'moderate_synthetic',
                    'voice_tone' => 'synthetic_digital',
                    'background_music' => 'digital_synthetic'
                ],
                'content_style' => [
                    'language' => 'technical_digital',
                    'pacing' => 'rhythmic_precise',
                    'emotional_intensity' => 'low'
                ]
            ],

            'retro_futuristic' => [
                'name' => 'Retro Futuristic',
                'category' => 'futuristic_tech',
                'description' => '80s vision of the future',
                'visual_style' => [
                    'colors' => ['neon_pink', 'electric_blue', 'purple'],
                    'lighting' => 'neon_retro',
                    'movement' => 'synthwave_flowing',
                    'composition' => 'retro_geometric'
                ],
                'audio_characteristics' => [
                    'tempo' => 'synthwave_rhythm',
                    'energy' => 'high_nostalgic',
                    'voice_tone' => 'retro_cool',
                    'background_music' => 'synthwave_electronic'
                ],
                'content_style' => [
                    'language' => 'retro_cool',
                    'pacing' => 'rhythmic_nostalgic',
                    'emotional_intensity' => 'moderate'
                ]
            ]
        ];
    }
    
    /**
     * Initialize mood categories for organization
     */
    private function initializeCategories() {
        $this->moodCategories = [
            'positive_energy' => [
                'name' => 'Positive Energy',
                'description' => 'High-energy, uplifting moods',
                'icon' => '⚡',
                'color' => '#FFD700'
            ],
            'calm_peaceful' => [
                'name' => 'Calm & Peaceful',
                'description' => 'Tranquil, soothing moods',
                'icon' => '🕊️',
                'color' => '#87CEEB'
            ],
            'dramatic_intense' => [
                'name' => 'Dramatic & Intense',
                'description' => 'Powerful, emotionally charged moods',
                'icon' => '🎭',
                'color' => '#DC143C'
            ],
            'mysterious_dark' => [
                'name' => 'Mysterious & Dark',
                'description' => 'Enigmatic, shadowy moods',
                'icon' => '🌙',
                'color' => '#4B0082'
            ],
            'romantic_emotional' => [
                'name' => 'Romantic & Emotional',
                'description' => 'Love, passion, and deep emotions',
                'icon' => '💖',
                'color' => '#FF69B4'
            ],
            'futuristic_tech' => [
                'name' => 'Futuristic & Tech',
                'description' => 'Modern, technological, sci-fi moods',
                'icon' => '🚀',
                'color' => '#00FFFF'
            ]
        ];
    }

    /**
     * Get all available moods
     */
    public function getAllMoods() {
        return $this->moods;
    }

    /**
     * Get moods by category
     */
    public function getMoodsByCategory($category) {
        $categoryMoods = [];
        foreach ($this->moods as $id => $mood) {
            if ($mood['category'] === $category) {
                $categoryMoods[$id] = $mood;
            }
        }
        return $categoryMoods;
    }

    /**
     * Get all mood categories
     */
    public function getCategories() {
        return $this->moodCategories;
    }

    /**
     * Get specific mood by ID
     */
    public function getMood($moodId) {
        return $this->moods[$moodId] ?? null;
    }

    /**
     * Get mood characteristics for video generation
     */
    public function getMoodCharacteristics($moodId) {
        $mood = $this->getMood($moodId);
        if (!$mood) {
            return null;
        }

        return [
            'visual_style' => $mood['visual_style'],
            'audio_characteristics' => $mood['audio_characteristics'],
            'content_style' => $mood['content_style'],
            'category' => $mood['category'],
            'description' => $mood['description']
        ];
    }

    /**
     * Get mood list for UI display
     */
    public function getMoodList() {
        $moodList = [];
        foreach ($this->moodCategories as $categoryId => $category) {
            $moodList[$categoryId] = [
                'category' => $category,
                'moods' => []
            ];

            foreach ($this->moods as $moodId => $mood) {
                if ($mood['category'] === $categoryId) {
                    $moodList[$categoryId]['moods'][$moodId] = [
                        'id' => $moodId,
                        'name' => $mood['name'],
                        'description' => $mood['description']
                    ];
                }
            }
        }
        return $moodList;
    }

    /**
     * Search moods by characteristics
     */
    public function searchMoods($criteria) {
        $results = [];

        foreach ($this->moods as $moodId => $mood) {
            $match = true;

            // Check category match
            if (isset($criteria['category']) && $mood['category'] !== $criteria['category']) {
                $match = false;
            }

            // Check energy level
            if (isset($criteria['energy_level'])) {
                $energyLevel = $mood['audio_characteristics']['energy'];
                if (!$this->matchesEnergyLevel($energyLevel, $criteria['energy_level'])) {
                    $match = false;
                }
            }

            // Check emotional intensity
            if (isset($criteria['emotional_intensity'])) {
                $intensity = $mood['content_style']['emotional_intensity'];
                if (!$this->matchesIntensity($intensity, $criteria['emotional_intensity'])) {
                    $match = false;
                }
            }

            if ($match) {
                $results[$moodId] = $mood;
            }
        }

        return $results;
    }

    /**
     * Get random mood from category
     */
    public function getRandomMood($category = null) {
        if ($category) {
            $categoryMoods = $this->getMoodsByCategory($category);
            if (empty($categoryMoods)) {
                return null;
            }
            $moodIds = array_keys($categoryMoods);
        } else {
            $moodIds = array_keys($this->moods);
        }

        $randomId = $moodIds[array_rand($moodIds)];
        return ['id' => $randomId, 'mood' => $this->moods[$randomId]];
    }

    /**
     * Get complementary moods
     */
    public function getComplementaryMoods($moodId, $count = 3) {
        $baseMood = $this->getMood($moodId);
        if (!$baseMood) {
            return [];
        }

        $complementary = [];
        $baseCategory = $baseMood['category'];

        // Get moods from same category first
        $sameCategoryMoods = $this->getMoodsByCategory($baseCategory);
        unset($sameCategoryMoods[$moodId]); // Remove the base mood

        // Add some from same category
        $sameCount = min(2, $count, count($sameCategoryMoods));
        $sameMoodIds = array_rand($sameCategoryMoods, $sameCount);
        if (!is_array($sameMoodIds)) {
            $sameMoodIds = [$sameMoodIds];
        }

        foreach ($sameMoodIds as $id) {
            $complementary[] = ['id' => $id, 'mood' => $sameCategoryMoods[$id]];
        }

        // Fill remaining with moods from other categories
        $remaining = $count - count($complementary);
        if ($remaining > 0) {
            $otherMoods = [];
            foreach ($this->moods as $id => $mood) {
                if ($mood['category'] !== $baseCategory && $id !== $moodId) {
                    $otherMoods[$id] = $mood;
                }
            }

            if (!empty($otherMoods)) {
                $otherIds = array_rand($otherMoods, min($remaining, count($otherMoods)));
                if (!is_array($otherIds)) {
                    $otherIds = [$otherIds];
                }

                foreach ($otherIds as $id) {
                    $complementary[] = ['id' => $id, 'mood' => $otherMoods[$id]];
                }
            }
        }

        return $complementary;
    }

    /**
     * Helper method to match energy levels
     */
    private function matchesEnergyLevel($moodEnergy, $targetEnergy) {
        $energyLevels = [
            'very_low' => 1,
            'low' => 2,
            'moderate' => 3,
            'high' => 4,
            'very_high' => 5,
            'maximum' => 6
        ];

        // Extract base energy level
        $moodLevel = $this->extractEnergyLevel($moodEnergy);
        $targetLevel = $energyLevels[$targetEnergy] ?? 3;
        $moodLevelValue = $energyLevels[$moodLevel] ?? 3;

        // Allow ±1 level difference
        return abs($moodLevelValue - $targetLevel) <= 1;
    }

    /**
     * Helper method to match emotional intensity
     */
    private function matchesIntensity($moodIntensity, $targetIntensity) {
        $intensityLevels = [
            'low' => 1,
            'moderate' => 2,
            'high' => 3,
            'very_high' => 4,
            'maximum' => 5
        ];

        $moodLevel = $intensityLevels[$moodIntensity] ?? 2;
        $targetLevel = $intensityLevels[$targetIntensity] ?? 2;

        // Allow ±1 level difference
        return abs($moodLevel - $targetLevel) <= 1;
    }

    /**
     * Extract base energy level from complex energy descriptions
     */
    private function extractEnergyLevel($energyDescription) {
        if (strpos($energyDescription, 'very_low') !== false || strpos($energyDescription, 'very low') !== false) {
            return 'very_low';
        } elseif (strpos($energyDescription, 'low') !== false) {
            return 'low';
        } elseif (strpos($energyDescription, 'very_high') !== false || strpos($energyDescription, 'maximum') !== false) {
            return 'very_high';
        } elseif (strpos($energyDescription, 'high') !== false) {
            return 'high';
        } else {
            return 'moderate';
        }
    }
}
