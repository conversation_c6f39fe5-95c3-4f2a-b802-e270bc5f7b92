/* <PERSON><PERSON><PERSON><PERSON> - Futuristic 2070 Design */

:root {
    /* Futuristic Color Palette */
    --primary-neon: #00ff88;
    --secondary-neon: #0088ff;
    --tertiary-neon: #ff0088;
    --accent-cyan: #00ffff;
    --accent-purple: #8800ff;
    
    /* Dark Theme Base */
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #2a2a2a;
    --bg-glass: rgba(255, 255, 255, 0.05);
    --bg-glass-hover: rgba(255, 255, 255, 0.1);
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #888888;
    --text-accent: var(--primary-neon);
    
    /* Borders and Effects */
    --border-glass: rgba(255, 255, 255, 0.1);
    --border-neon: var(--primary-neon);
    --shadow-neon: 0 0 20px rgba(0, 255, 136, 0.3);
    --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.3);
    
    /* Animations */
    --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-smooth: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    
    /* Typography */
    --font-primary: 'Orbitron', monospace;
    --font-secondary: 'Exo 2', sans-serif;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body.tools-page {
    font-family: var(--font-secondary);
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Glassmorphism Mixin */
.glass-effect {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-md);
}

/* Neon Glow Effect */
.neon-glow {
    box-shadow: var(--shadow-neon);
    border: 1px solid var(--border-neon);
}

/* Navigation */
.futuristic-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-glass);
    padding: var(--spacing-md) 0;
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-brand .brand-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
    color: var(--text-primary);
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: 1.5rem;
}

.brand-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-neon);
}

.nav-menu {
    display: flex;
    gap: var(--spacing-lg);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    text-decoration: none;
    color: var(--text-secondary);
    border-radius: var(--radius-sm);
    transition: var(--transition-smooth);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--text-primary);
    background: var(--bg-glass);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background: var(--primary-neon);
    border-radius: 1px;
    box-shadow: 0 0 10px var(--primary-neon);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.credits-display {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-sm);
    color: var(--primary-neon);
    font-weight: 600;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-smooth);
}

.user-avatar:hover {
    background: var(--bg-glass-hover);
    box-shadow: var(--shadow-neon);
}

/* Hero Section */
.tools-hero {
    margin-top: 80px;
    padding: var(--spacing-2xl) 0;
    background: linear-gradient(135deg, 
        rgba(0, 255, 136, 0.1) 0%, 
        rgba(0, 136, 255, 0.1) 50%, 
        rgba(255, 0, 136, 0.1) 100%);
    position: relative;
    overflow: hidden;
}

.hero-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
}

.hero-title {
    font-family: var(--font-primary);
    margin-bottom: var(--spacing-lg);
}

.title-main {
    display: block;
    font-size: 3.5rem;
    font-weight: 900;
    background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.1;
}

.title-sub {
    display: block;
    font-size: 1.5rem;
    font-weight: 400;
    color: var(--text-secondary);
    margin-top: var(--spacing-sm);
}

.hero-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.7;
}

.hero-stats {
    display: flex;
    gap: var(--spacing-xl);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-family: var(--font-primary);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-neon);
    display: block;
    text-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

/* Floating Tools Animation */
.hero-visual {
    position: relative;
    height: 400px;
}

.floating-tools {
    position: relative;
    width: 100%;
    height: 100%;
}

.tool-icon {
    position: absolute;
    width: 80px;
    height: 80px;
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-neon);
    animation: float 6s ease-in-out infinite;
    animation-delay: var(--delay);
    backdrop-filter: blur(20px);
}

.tool-icon:nth-child(1) { top: 20%; left: 20%; }
.tool-icon:nth-child(2) { top: 10%; right: 30%; }
.tool-icon:nth-child(3) { bottom: 30%; left: 10%; }
.tool-icon:nth-child(4) { bottom: 20%; right: 20%; }

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-20px) rotate(5deg); }
    50% { transform: translateY(-10px) rotate(-5deg); }
    75% { transform: translateY(-15px) rotate(3deg); }
}

/* Tools Interface */
.tools-interface {
    padding: var(--spacing-2xl) 0;
}

.interface-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* Search and Controls */
.tools-controls {
    margin-bottom: var(--spacing-xl);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.search-container {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    transition: var(--transition-smooth);
    backdrop-filter: blur(20px);
}

.search-box:focus-within {
    border-color: var(--primary-neon);
    box-shadow: var(--shadow-neon);
}

.search-box i {
    color: var(--text-muted);
    margin-right: var(--spacing-sm);
}

.search-box input {
    flex: 1;
    background: none;
    border: none;
    outline: none;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-family: var(--font-secondary);
}

.search-box input::placeholder {
    color: var(--text-muted);
}

.search-clear {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 50%;
    transition: var(--transition-fast);
}

.search-clear:hover {
    color: var(--text-primary);
    background: var(--bg-glass);
}

/* Filter Controls */
.filter-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-lg);
}

.category-tabs {
    display: flex;
    gap: var(--spacing-sm);
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xs);
    backdrop-filter: blur(20px);
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    background: none;
    border: none;
    color: var(--text-secondary);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: var(--transition-smooth);
    font-family: var(--font-secondary);
    font-size: 0.9rem;
    white-space: nowrap;
}

.tab-btn:hover {
    color: var(--text-primary);
    background: var(--bg-glass-hover);
}

.tab-btn.active {
    color: var(--text-primary);
    background: var(--primary-neon);
    color: var(--bg-primary);
    box-shadow: 0 0 15px rgba(0, 255, 136, 0.4);
}

.view-controls {
    display: flex;
    gap: var(--spacing-xs);
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-sm);
    padding: var(--spacing-xs);
}

.view-btn,
.favorites-btn {
    width: 40px;
    height: 40px;
    background: none;
    border: none;
    color: var(--text-secondary);
    border-radius: var(--radius-xs);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-btn:hover,
.favorites-btn:hover {
    color: var(--text-primary);
    background: var(--bg-glass-hover);
}

.view-btn.active {
    color: var(--primary-neon);
    background: var(--bg-glass-hover);
}

/* Tools Grid */
.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

/* Loading State */
.loading-state {
    grid-column: 1 / -1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    text-align: center;
}

.loading-spinner {
    position: relative;
    width: 80px;
    height: 80px;
    margin-bottom: var(--spacing-lg);
}

.spinner-ring {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 2px solid transparent;
    border-top: 2px solid var(--primary-neon);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner-ring:nth-child(2) {
    width: 60px;
    height: 60px;
    top: 10px;
    left: 10px;
    border-top-color: var(--secondary-neon);
    animation-duration: 1.5s;
    animation-direction: reverse;
}

.spinner-ring:nth-child(3) {
    width: 40px;
    height: 40px;
    top: 20px;
    left: 20px;
    border-top-color: var(--tertiary-neon);
    animation-duration: 2s;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tool Card */
.tool-card {
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    backdrop-filter: blur(20px);
    transition: var(--transition-smooth);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-neon), var(--secondary-neon));
    transform: scaleX(0);
    transition: var(--transition-smooth);
}

.tool-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-neon);
    box-shadow: var(--shadow-glass), var(--shadow-neon);
}

.tool-card:hover::before {
    transform: scaleX(1);
}

.tool-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.tool-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--bg-primary);
    font-size: 1.2rem;
}

.tool-info h3 {
    font-family: var(--font-primary);
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.tool-info .tool-category {
    font-size: 0.8rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tool-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

.tool-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

.tool-credits {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--primary-neon);
    font-weight: 600;
    font-size: 0.9rem;
}

.tool-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.btn-icon {
    width: 32px;
    height: 32px;
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-xs);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-fast);
    color: var(--text-secondary);
}

.btn-icon:hover {
    color: var(--text-primary);
    background: var(--bg-glass-hover);
    border-color: var(--primary-neon);
}

.btn-icon.favorited {
    color: var(--tertiary-neon);
    border-color: var(--tertiary-neon);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .tools-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }
}

@media (max-width: 768px) {
    .nav-container {
        padding: 0 var(--spacing-md);
    }
    
    .nav-menu {
        display: none; /* Mobile menu would be implemented separately */
    }
    
    .title-main {
        font-size: 2.5rem;
    }
    
    .hero-stats {
        justify-content: center;
        gap: var(--spacing-lg);
    }
    
    .filter-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .category-tabs {
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }
    
    .category-tabs::-webkit-scrollbar {
        display: none;
    }
    
    .tools-grid {
        grid-template-columns: 1fr;
    }
}

/* Quick Actions */
.quick-actions {
    padding: var(--spacing-2xl) 0;
    background: var(--bg-secondary);
}

.actions-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.section-title {
    font-family: var(--font-primary);
    font-size: 2rem;
    font-weight: 600;
    text-align: center;
    margin-bottom: var(--spacing-xl);
    color: var(--text-primary);
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.action-card {
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    cursor: pointer;
    transition: var(--transition-smooth);
    backdrop-filter: blur(20px);
}

.action-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-neon);
    box-shadow: var(--shadow-neon);
}

.action-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    color: var(--bg-primary);
    font-size: 1.5rem;
}

.action-card h3 {
    font-family: var(--font-primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.action-card p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-smooth);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    background: var(--bg-secondary);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-lg);
    max-width: 90vw;
    max-height: 90vh;
    width: 800px;
    overflow: hidden;
    transform: scale(0.9);
    transition: var(--transition-bounce);
}

.modal-overlay.active .modal-container {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-glass);
}

.modal-title {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    width: 40px;
    height: 40px;
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-fast);
    color: var(--text-secondary);
}

.modal-close:hover {
    color: var(--text-primary);
    background: var(--bg-glass-hover);
    border-color: var(--primary-neon);
}

.modal-content {
    padding: var(--spacing-lg);
    max-height: 70vh;
    overflow-y: auto;
}

/* Upload Dropzone */
.upload-dropzone {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 255, 136, 0.1);
    backdrop-filter: blur(10px);
    z-index: 1500;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px dashed var(--primary-neon);
    margin: var(--spacing-lg);
    border-radius: var(--radius-xl);
}

.dropzone-content {
    text-align: center;
    color: var(--text-primary);
}

.dropzone-icon {
    font-size: 4rem;
    color: var(--primary-neon);
    margin-bottom: var(--spacing-lg);
}

.dropzone-content h3 {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    margin-bottom: var(--spacing-sm);
}

/* Notification System */
.notification-container {
    position: fixed;
    top: 100px;
    right: var(--spacing-lg);
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.notification {
    background: var(--bg-glass);
    border: 1px solid var(--border-glass);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    backdrop-filter: blur(20px);
    min-width: 300px;
    transform: translateX(100%);
    transition: var(--transition-smooth);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-color: var(--primary-neon);
    box-shadow: 0 0 15px rgba(0, 255, 136, 0.3);
}

.notification.error {
    border-color: var(--tertiary-neon);
    box-shadow: 0 0 15px rgba(255, 0, 136, 0.3);
}

.notification.warning {
    border-color: #ffaa00;
    box-shadow: 0 0 15px rgba(255, 170, 0, 0.3);
}

/* Empty State */
.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 3rem;
    color: var(--text-muted);
    margin-bottom: var(--spacing-lg);
}

.empty-state h3 {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
    border: none;
    border-radius: var(--radius-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    color: var(--bg-primary);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-smooth);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 255, 136, 0.4);
}

@media (max-width: 480px) {
    .hero-container,
    .interface-container,
    .actions-container {
        padding: 0 var(--spacing-md);
    }

    .title-main {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .modal-container {
        width: 95vw;
        margin: var(--spacing-md);
    }

    .notification-container {
        right: var(--spacing-md);
        left: var(--spacing-md);
    }

    .notification {
        min-width: auto;
    }
}
