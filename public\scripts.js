// Sutradhar Engine Frontend JavaScript

class SutradharEngine {
    constructor() {
        this.currentJobId = null;
        this.progressInterval = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupFormValidation();
        this.loadTemplates();
    }

    setupEventListeners() {
        // Form submission
        document.getElementById('generationForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.generateContent();
        });

        // Content source radio buttons
        document.querySelectorAll('input[name="content_source"]').forEach(radio => {
            radio.addEventListener('change', () => {
                this.toggleContentSections();
            });
        });

        // Flow type selection
        document.querySelectorAll('input[name="flow_type"]').forEach(radio => {
            radio.addEventListener('change', () => {
                this.updateStyleOptions();
            });
        });

        // Voice pack selection
        document.querySelectorAll('input[name="voice_pack"]').forEach(radio => {
            radio.addEventListener('change', () => {
                this.previewVoice();
            });
        });
    }

    setupFormValidation() {
        const form = document.getElementById('generationForm');
        const inputs = form.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            input.addEventListener('blur', () => {
                this.validateField(input);
            });
        });
    }

    validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let errorMessage = '';

        // Remove existing error styling
        field.classList.remove('form-error');
        const existingError = field.parentNode.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }

        // Validation rules
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            errorMessage = 'This field is required';
        }

        if (field.type === 'file' && field.files.length > 0) {
            const file = field.files[0];
            const allowedTypes = ['.txt', '.json', '.md'];
            const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
            
            if (!allowedTypes.includes(fileExtension)) {
                isValid = false;
                errorMessage = 'Please upload a .txt, .json, or .md file';
            }

            if (file.size > 5 * 1024 * 1024) { // 5MB limit
                isValid = false;
                errorMessage = 'File size must be less than 5MB';
            }
        }

        if (!isValid) {
            field.classList.add('form-error');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = errorMessage;
            field.parentNode.appendChild(errorDiv);
        }

        return isValid;
    }

    toggleContentSections() {
        const contentSource = document.querySelector('input[name="content_source"]:checked').value;
        
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.add('hidden');
        });

        switch (contentSource) {
            case 'upload':
                document.getElementById('uploadSection').classList.remove('hidden');
                break;
            case 'template':
                document.getElementById('templateSection').classList.remove('hidden');
                break;
            case 'text':
                document.getElementById('textSection').classList.remove('hidden');
                break;
        }
    }

    updateStyleOptions() {
        const flowType = document.querySelector('input[name="flow_type"]:checked').value;
        const styleSelect = document.getElementById('style');
        
        // Update style options based on flow type
        const styleOptions = {
            'reel': [
                { value: 'funny', text: '😄 Funny - Sarcastic and humorous' },
                { value: 'bollywood', text: '🎬 Bollywood - Dramatic and filmy' },
                { value: 'desi', text: '🇮🇳 Desi - Cultural and relatable' }
            ],
            'audio_story': [
                { value: 'emotional', text: '💝 Emotional - Heartfelt and touching' },
                { value: 'desi', text: '🇮🇳 Desi - Cultural and relatable' },
                { value: 'funny', text: '😄 Funny - Sarcastic and humorous' }
            ],
            'meme_rant': [
                { value: 'funny', text: '😄 Funny - Sarcastic and humorous' },
                { value: 'desi', text: '🇮🇳 Desi - Cultural and relatable' },
                { value: 'bollywood', text: '🎬 Bollywood - Dramatic and filmy' }
            ]
        };

        styleSelect.innerHTML = '';
        styleOptions[flowType].forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.text;
            styleSelect.appendChild(optionElement);
        });
    }

    previewVoice() {
        const voicePack = document.querySelector('input[name="voice_pack"]:checked').value;
        // TODO: Implement voice preview functionality
        console.log('Voice pack selected:', voicePack);
    }

    async loadTemplates() {
        try {
            console.log('Loading templates...');
            const response = await fetch('api.php?action=list_templates');

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const templates = await response.json();
            console.log('Templates loaded:', templates);

            const templateSelect = document.getElementById('template');
            if (!templateSelect) {
                console.error('Template select element not found');
                return;
            }

            templateSelect.innerHTML = '<option value="">Select a template...</option>';

            templates.forEach(template => {
                const option = document.createElement('option');
                option.value = template.id;
                option.textContent = template.name;
                templateSelect.appendChild(option);
            });

            console.log('Templates loaded successfully');
        } catch (error) {
            console.error('Failed to load templates:', error);
            this.showAlert('Failed to load templates: ' + error.message, 'error');
        }
    }

    async generateContent() {
        const form = document.getElementById('generationForm');
        const formData = new FormData(form);

        // Validate form
        let isValid = true;
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });

        if (!isValid) {
            this.showAlert('Please fix the errors in the form', 'error');
            return;
        }

        // Show progress section
        this.showProgress();
        this.disableForm();

        try {
            const response = await fetch('api.php?action=generate', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.currentJobId = result.job_id;
                this.startProgressTracking();
            } else {
                throw new Error(result.message || 'Generation failed');
            }
        } catch (error) {
            this.showAlert('Failed to start generation: ' + error.message, 'error');
            this.hideProgress();
            this.enableForm();
        }
    }

    showProgress() {
        document.getElementById('progressSection').classList.remove('hidden');
        this.resetProgressSteps();
    }

    hideProgress() {
        document.getElementById('progressSection').classList.add('hidden');
    }

    resetProgressSteps() {
        for (let i = 1; i <= 4; i++) {
            const step = document.getElementById(`step${i}`);
            const text = document.getElementById(`step${i}Text`);
            step.className = 'w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center';
            text.className = 'text-gray-600';
        }
        document.getElementById('progressBar').style.width = '0%';
    }

    updateProgressStep(stepNumber, status, message) {
        const step = document.getElementById(`step${stepNumber}`);
        const text = document.getElementById(`step${stepNumber}Text`);
        
        if (status === 'active') {
            step.className = 'w-8 h-8 rounded-full bg-orange-500 text-white flex items-center justify-center step-active';
            text.className = 'text-orange-600 font-medium';
        } else if (status === 'complete') {
            step.className = 'w-8 h-8 rounded-full bg-green-500 text-white flex items-center justify-center step-complete';
            text.className = 'text-green-600';
        }
        
        text.textContent = message;
        
        // Update progress bar
        const progress = (stepNumber / 4) * 100;
        document.getElementById('progressBar').style.width = progress + '%';
    }

    async startProgressTracking() {
        this.progressInterval = setInterval(async () => {
            try {
                const response = await fetch(`api.php?action=status&job_id=${this.currentJobId}`);
                const status = await response.json();

                if (status.step) {
                    this.updateProgressStep(status.step, status.status, status.message);
                }

                if (status.complete) {
                    clearInterval(this.progressInterval);
                    this.showOutput(status.output);
                    this.enableForm();
                } else if (status.error) {
                    clearInterval(this.progressInterval);
                    this.showAlert('Generation failed: ' + status.error, 'error');
                    this.hideProgress();
                    this.enableForm();
                }
            } catch (error) {
                console.error('Progress tracking error:', error);
            }
        }, 2000);
    }

    showOutput(output) {
        const outputSection = document.getElementById('outputSection');
        const outputContent = document.getElementById('outputContent');
        
        outputContent.innerHTML = '';

        if (output.video) {
            outputContent.innerHTML += this.createOutputItem('Video', output.video, 'video');
        }
        
        if (output.audio) {
            outputContent.innerHTML += this.createOutputItem('Audio', output.audio, 'audio');
        }
        
        if (output.subtitles) {
            outputContent.innerHTML += this.createOutputItem('Subtitles', output.subtitles, 'file');
        }

        outputSection.classList.remove('hidden');
        this.hideProgress();
    }

    createOutputItem(title, file, type) {
        let mediaElement = '';
        const downloadUrl = `download.php?file=${encodeURIComponent(file.url)}`;
        const viewUrl = `download.php?file=${encodeURIComponent(file.url)}`;

        if (type === 'video') {
            mediaElement = `<video controls width="100%" style="max-width: 400px;"><source src="${viewUrl}" type="video/mp4"></video>`;
        } else if (type === 'audio') {
            // Determine audio type based on file extension
            const audioType = file.url.includes('.wav') ? 'audio/wav' : 'audio/mpeg';
            mediaElement = `<audio controls style="width: 100%;"><source src="${viewUrl}" type="${audioType}"></audio>`;
        }

        return `
            <div class="output-item">
                <h4>${title}</h4>
                ${mediaElement}
                <div>
                    <a href="${downloadUrl}&download=1" class="download-btn">Download ${title}</a>
                    <a href="${viewUrl}" target="_blank" class="download-btn" style="background-color: #2563eb;">View ${title}</a>
                    <span class="text-sm text-gray-600">Size: ${file.size}</span>
                </div>
            </div>
        `;
    }

    disableForm() {
        const generateBtn = document.getElementById('generateBtn');
        generateBtn.disabled = true;
        generateBtn.classList.add('btn-loading');
        generateBtn.textContent = 'Generating...';
    }

    enableForm() {
        const generateBtn = document.getElementById('generateBtn');
        generateBtn.disabled = false;
        generateBtn.classList.remove('btn-loading');
        generateBtn.textContent = '🎬 Generate Content';
    }

    showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type}`;
        alertDiv.textContent = message;
        
        const form = document.getElementById('generationForm');
        form.parentNode.insertBefore(alertDiv, form);
        
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing Sutradhar Engine...');
    try {
        new SutradharEngine();
        console.log('Sutradhar Engine initialized successfully');
    } catch (error) {
        console.error('Failed to initialize Sutradhar Engine:', error);
    }
});
