<?php
/**
 * Format Converter Tool
 * Convert images between different formats with quality settings
 */

require_once __DIR__ . '/../BaseTool.php';

class FormatConverterTool extends BaseTool {
    protected $toolId = 'format_converter';
    
    public function process($files, $options = [], $userId = null, $operationId = null) {
        $this->validateFiles($files);
        
        $outputFormat = $options['output_format'] ?? 'png';
        $quality = (int)($options['quality'] ?? 85);
        $maintainTransparency = $options['maintain_transparency'] ?? true;
        
        $processedFiles = [];
        $tempFiles = [];
        
        try {
            foreach ($files as $file) {
                // Store uploaded file
                $inputPath = $this->storeUploadedFile($file, $operationId, $userId);
                $tempFiles[] = $inputPath;
                
                // Load image
                $image = $this->loadImage($inputPath);
                $originalWidth = imagesx($image);
                $originalHeight = imagesy($image);
                
                // Create output image
                $outputImage = imagecreatetruecolor($originalWidth, $originalHeight);
                
                // Handle transparency for PNG and GIF
                if ($maintainTransparency && in_array($outputFormat, ['png', 'gif'])) {
                    $this->preserveTransparency($outputImage, $image, $outputFormat);
                }
                
                // Copy image data
                imagecopy($outputImage, $image, 0, 0, 0, 0, $originalWidth, $originalHeight);
                
                // Generate output filename
                $baseName = pathinfo($file['name'], PATHINFO_FILENAME);
                $outputPath = $this->tempDir . $operationId . '_' . $baseName . '_converted.' . $outputFormat;
                
                // Save converted image
                $this->saveImage($outputImage, $outputPath, $outputFormat, $quality);
                
                // Store processed file
                $storedFile = $this->storeProcessedFile($outputPath, $file['name'], $operationId, $userId);
                
                $processedFiles[] = [
                    'original_name' => $file['name'],
                    'converted_name' => basename($storedFile['stored_path']),
                    'download_url' => $storedFile['download_url'],
                    'file_size' => $storedFile['file_size'],
                    'format_from' => pathinfo($file['name'], PATHINFO_EXTENSION),
                    'format_to' => $outputFormat,
                    'quality_used' => $quality,
                    'dimensions' => [
                        'width' => $originalWidth,
                        'height' => $originalHeight
                    ]
                ];
                
                // Clean up
                imagedestroy($image);
                imagedestroy($outputImage);
                $tempFiles[] = $outputPath;
            }
            
            return [
                'success' => true,
                'processed_files' => $processedFiles,
                'total_files' => count($processedFiles),
                'output_format' => $outputFormat,
                'quality_setting' => $quality
            ];
            
        } catch (Exception $e) {
            // Clean up on error
            $this->cleanup($tempFiles);
            throw new ToolException("Format conversion failed: " . $e->getMessage());
        } finally {
            // Clean up temporary input files
            foreach ($tempFiles as $tempFile) {
                if (strpos($tempFile, '_input_') !== false && file_exists($tempFile)) {
                    unlink($tempFile);
                }
            }
        }
    }
    
    /**
     * Preserve transparency for PNG and GIF formats
     */
    private function preserveTransparency($outputImage, $sourceImage, $format) {
        if ($format === 'png') {
            // Enable alpha blending and save alpha channel
            imagealphablending($outputImage, false);
            imagesavealpha($outputImage, true);
            
            // Fill with transparent color
            $transparent = imagecolorallocatealpha($outputImage, 0, 0, 0, 127);
            imagefill($outputImage, 0, 0, $transparent);
            
        } elseif ($format === 'gif') {
            // Get transparent color from source if it exists
            $transparentIndex = imagecolortransparent($sourceImage);
            if ($transparentIndex >= 0) {
                $transparentColor = imagecolorsforindex($sourceImage, $transparentIndex);
                $transparentNew = imagecolorallocate(
                    $outputImage,
                    $transparentColor['red'],
                    $transparentColor['green'],
                    $transparentColor['blue']
                );
                imagecolortransparent($outputImage, $transparentNew);
                imagefill($outputImage, 0, 0, $transparentNew);
            }
        }
    }
}
?>
