<?php
/**
 * Background Remover Tool
 * Remove backgrounds using GD edge detection algorithms
 */

require_once __DIR__ . '/../BaseTool.php';

class BackgroundRemoverTool extends BaseTool {
    protected $toolId = 'background_remover';
    
    public function process($files, $options = [], $userId = null, $operationId = null) {
        $this->validateFiles($files);
        
        $edgeThreshold = (int)($options['edge_threshold'] ?? 30);
        $smoothing = (int)($options['smoothing'] ?? 2);
        $tolerance = (int)($options['tolerance'] ?? 10);
        $method = $options['method'] ?? 'edge_detection';
        
        $processedFiles = [];
        $tempFiles = [];
        
        try {
            foreach ($files as $file) {
                // Store uploaded file
                $inputPath = $this->storeUploadedFile($file, $operationId, $userId);
                $tempFiles[] = $inputPath;
                
                // Load image
                $image = $this->loadImage($inputPath);
                $width = imagesx($image);
                $height = imagesy($image);
                
                // Create output image with transparency
                $outputImage = imagecreatetruecolor($width, $height);
                imagealphablending($outputImage, false);
                imagesavealpha($outputImage, true);
                
                // Fill with transparent background
                $transparent = imagecolorallocatealpha($outputImage, 0, 0, 0, 127);
                imagefill($outputImage, 0, 0, $transparent);
                
                // Apply background removal based on method
                switch ($method) {
                    case 'edge_detection':
                        $this->removeBackgroundEdgeDetection($image, $outputImage, $edgeThreshold, $smoothing);
                        break;
                    case 'color_similarity':
                        $this->removeBackgroundColorSimilarity($image, $outputImage, $tolerance);
                        break;
                    case 'corner_sampling':
                        $this->removeBackgroundCornerSampling($image, $outputImage, $tolerance);
                        break;
                    default:
                        $this->removeBackgroundEdgeDetection($image, $outputImage, $edgeThreshold, $smoothing);
                }
                
                // Generate output filename
                $baseName = pathinfo($file['name'], PATHINFO_FILENAME);
                $outputPath = $this->tempDir . $operationId . '_' . $baseName . '_no_bg.png';
                
                // Save as PNG to preserve transparency
                imagepng($outputImage, $outputPath);
                
                // Store processed file
                $storedFile = $this->storeProcessedFile($outputPath, $file['name'], $operationId, $userId);
                
                $processedFiles[] = [
                    'original_name' => $file['name'],
                    'processed_name' => basename($storedFile['stored_path']),
                    'download_url' => $storedFile['download_url'],
                    'file_size' => $storedFile['file_size'],
                    'method_used' => $method,
                    'settings' => [
                        'edge_threshold' => $edgeThreshold,
                        'smoothing' => $smoothing,
                        'tolerance' => $tolerance
                    ],
                    'dimensions' => [
                        'width' => $width,
                        'height' => $height
                    ]
                ];
                
                // Clean up
                imagedestroy($image);
                imagedestroy($outputImage);
                $tempFiles[] = $outputPath;
            }
            
            return [
                'success' => true,
                'processed_files' => $processedFiles,
                'total_files' => count($processedFiles),
                'method_used' => $method
            ];
            
        } catch (Exception $e) {
            $this->cleanup($tempFiles);
            throw new ToolException("Background removal failed: " . $e->getMessage());
        } finally {
            // Clean up temporary input files
            foreach ($tempFiles as $tempFile) {
                if (strpos($tempFile, '_input_') !== false && file_exists($tempFile)) {
                    unlink($tempFile);
                }
            }
        }
    }
    
    /**
     * Remove background using edge detection
     */
    private function removeBackgroundEdgeDetection($sourceImage, $outputImage, $threshold, $smoothing) {
        $width = imagesx($sourceImage);
        $height = imagesy($sourceImage);
        
        // Create edge detection image
        $edgeImage = imagecreatetruecolor($width, $height);
        imagecopy($edgeImage, $sourceImage, 0, 0, 0, 0, $width, $height);
        
        // Apply edge detection filter
        imagefilter($edgeImage, IMG_FILTER_EDGEDETECT);
        imagefilter($edgeImage, IMG_FILTER_GRAYSCALE);
        
        // Apply smoothing if requested
        for ($i = 0; $i < $smoothing; $i++) {
            imagefilter($edgeImage, IMG_FILTER_SMOOTH, 1);
        }
        
        // Process each pixel
        for ($y = 0; $y < $height; $y++) {
            for ($x = 0; $x < $width; $x++) {
                $edgeColor = imagecolorat($edgeImage, $x, $y);
                $edgeGray = ($edgeColor >> 16) & 0xFF; // Get red component as grayscale
                
                if ($edgeGray > $threshold) {
                    // Strong edge - keep pixel
                    $sourceColor = imagecolorat($sourceImage, $x, $y);
                    imagesetpixel($outputImage, $x, $y, $sourceColor);
                } else {
                    // Weak edge - check surrounding area
                    if ($this->hasStrongEdgeNearby($edgeImage, $x, $y, $threshold, 2)) {
                        $sourceColor = imagecolorat($sourceImage, $x, $y);
                        imagesetpixel($outputImage, $x, $y, $sourceColor);
                    }
                    // Otherwise leave transparent
                }
            }
        }
        
        imagedestroy($edgeImage);
    }
    
    /**
     * Remove background using color similarity to corners
     */
    private function removeBackgroundColorSimilarity($sourceImage, $outputImage, $tolerance) {
        $width = imagesx($sourceImage);
        $height = imagesy($sourceImage);
        
        // Sample corner colors to determine background
        $cornerColors = [
            imagecolorat($sourceImage, 0, 0),
            imagecolorat($sourceImage, $width - 1, 0),
            imagecolorat($sourceImage, 0, $height - 1),
            imagecolorat($sourceImage, $width - 1, $height - 1)
        ];
        
        // Find most common corner color
        $backgroundColorRGB = $this->getMostCommonColor($cornerColors);
        
        // Process each pixel
        for ($y = 0; $y < $height; $y++) {
            for ($x = 0; $x < $width; $x++) {
                $pixelColor = imagecolorat($sourceImage, $x, $y);
                $pixelRGB = [
                    ($pixelColor >> 16) & 0xFF,
                    ($pixelColor >> 8) & 0xFF,
                    $pixelColor & 0xFF
                ];
                
                $colorDistance = $this->calculateColorDistance($pixelRGB, $backgroundColorRGB);
                
                if ($colorDistance > $tolerance) {
                    // Keep pixel - not background
                    imagesetpixel($outputImage, $x, $y, $pixelColor);
                }
                // Otherwise leave transparent
            }
        }
    }
    
    /**
     * Remove background using corner sampling method
     */
    private function removeBackgroundCornerSampling($sourceImage, $outputImage, $tolerance) {
        $width = imagesx($sourceImage);
        $height = imagesy($sourceImage);
        
        // Create a mask for background detection
        $mask = imagecreatetruecolor($width, $height);
        imagefill($mask, 0, 0, imagecolorallocate($mask, 0, 0, 0)); // Black = background
        
        // Flood fill from corners
        $backgroundColor = imagecolorat($sourceImage, 0, 0);
        $this->floodFillBackground($sourceImage, $mask, 0, 0, $backgroundColor, $tolerance);
        $this->floodFillBackground($sourceImage, $mask, $width - 1, 0, $backgroundColor, $tolerance);
        $this->floodFillBackground($sourceImage, $mask, 0, $height - 1, $backgroundColor, $tolerance);
        $this->floodFillBackground($sourceImage, $mask, $width - 1, $height - 1, $backgroundColor, $tolerance);
        
        // Apply mask to output
        for ($y = 0; $y < $height; $y++) {
            for ($x = 0; $x < $width; $x++) {
                $maskColor = imagecolorat($mask, $x, $y);
                if ($maskColor === 0) {
                    // Background pixel - leave transparent
                    continue;
                } else {
                    // Foreground pixel - copy from source
                    $sourceColor = imagecolorat($sourceImage, $x, $y);
                    imagesetpixel($outputImage, $x, $y, $sourceColor);
                }
            }
        }
        
        imagedestroy($mask);
    }
    
    /**
     * Check if there's a strong edge nearby
     */
    private function hasStrongEdgeNearby($edgeImage, $x, $y, $threshold, $radius) {
        $width = imagesx($edgeImage);
        $height = imagesy($edgeImage);
        
        for ($dy = -$radius; $dy <= $radius; $dy++) {
            for ($dx = -$radius; $dx <= $radius; $dx++) {
                $nx = $x + $dx;
                $ny = $y + $dy;
                
                if ($nx >= 0 && $nx < $width && $ny >= 0 && $ny < $height) {
                    $edgeColor = imagecolorat($edgeImage, $nx, $ny);
                    $edgeGray = ($edgeColor >> 16) & 0xFF;
                    
                    if ($edgeGray > $threshold) {
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
    
    /**
     * Get most common color from array
     */
    private function getMostCommonColor($colors) {
        $colorCounts = [];
        
        foreach ($colors as $color) {
            $rgb = [
                ($color >> 16) & 0xFF,
                ($color >> 8) & 0xFF,
                $color & 0xFF
            ];
            $key = implode(',', $rgb);
            $colorCounts[$key] = ($colorCounts[$key] ?? 0) + 1;
        }
        
        $maxCount = 0;
        $mostCommon = [0, 0, 0];
        
        foreach ($colorCounts as $colorKey => $count) {
            if ($count > $maxCount) {
                $maxCount = $count;
                $mostCommon = explode(',', $colorKey);
            }
        }
        
        return array_map('intval', $mostCommon);
    }
    
    /**
     * Calculate color distance
     */
    private function calculateColorDistance($color1, $color2) {
        $dr = $color1[0] - $color2[0];
        $dg = $color1[1] - $color2[1];
        $db = $color1[2] - $color2[2];
        
        return sqrt($dr * $dr + $dg * $dg + $db * $db);
    }
    
    /**
     * Flood fill background detection
     */
    private function floodFillBackground($sourceImage, $mask, $startX, $startY, $targetColor, $tolerance) {
        $width = imagesx($sourceImage);
        $height = imagesy($sourceImage);
        $white = imagecolorallocate($mask, 255, 255, 255);
        
        $stack = [[$startX, $startY]];
        $visited = [];
        
        while (!empty($stack)) {
            [$x, $y] = array_pop($stack);
            
            if ($x < 0 || $x >= $width || $y < 0 || $y >= $height) {
                continue;
            }
            
            $key = "$x,$y";
            if (isset($visited[$key])) {
                continue;
            }
            $visited[$key] = true;
            
            $currentColor = imagecolorat($sourceImage, $x, $y);
            $currentRGB = [
                ($currentColor >> 16) & 0xFF,
                ($currentColor >> 8) & 0xFF,
                $currentColor & 0xFF
            ];
            $targetRGB = [
                ($targetColor >> 16) & 0xFF,
                ($targetColor >> 8) & 0xFF,
                $targetColor & 0xFF
            ];
            
            if ($this->calculateColorDistance($currentRGB, $targetRGB) <= $tolerance) {
                imagesetpixel($mask, $x, $y, $white);
                
                // Add neighbors to stack
                $stack[] = [$x + 1, $y];
                $stack[] = [$x - 1, $y];
                $stack[] = [$x, $y + 1];
                $stack[] = [$x, $y - 1];
            }
        }
    }
}
?>
