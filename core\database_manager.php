<?php
/**
 * Database Manager - Secure database operations for Sutradhar 2070
 * Handles all database interactions with security and performance optimizations
 */

class DatabaseManager {
    private $pdo;
    private $config;
    private $transactionLevel = 0;
    
    public function __construct() {
        $this->loadConfig();
        $this->connect();
    }
    
    /**
     * Establish database connection
     */
    private function connect() {
        try {
            $dsn = sprintf(
                "mysql:host=%s;port=%s;dbname=%s;charset=utf8mb4",
                $this->config['host'],
                $this->config['port'],
                $this->config['database']
            );
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_PERSISTENT => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ];
            
            $this->pdo = new PDO($dsn, $this->config['username'], $this->config['password'], $options);
            
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("Database connection failed");
        }
    }
    
    /**
     * Create table if not exists
     */
    public function createTable($tableName, $columns) {
        try {
            $columnDefinitions = is_array($columns) ? implode(', ', $columns) : $columns;
            $sql = "CREATE TABLE IF NOT EXISTS `{$tableName}` ({$columnDefinitions}) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            return $this->pdo->exec($sql) !== false;
            
        } catch (PDOException $e) {
            error_log("Table creation failed for {$tableName}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Insert record
     */
    public function insert($table, $data) {
        try {
            $columns = array_keys($data);
            $placeholders = array_map(function($col) { return ":$col"; }, $columns);
            
            $sql = sprintf(
                "INSERT INTO `%s` (`%s`) VALUES (%s)",
                $table,
                implode('`, `', $columns),
                implode(', ', $placeholders)
            );
            
            $stmt = $this->pdo->prepare($sql);
            
            foreach ($data as $key => $value) {
                $stmt->bindValue(":$key", $value);
            }
            
            return $stmt->execute();
            
        } catch (PDOException $e) {
            error_log("Insert failed for table {$table}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Select single record
     */
    public function selectOne($table, $conditions = [], $columns = '*') {
        try {
            $whereClause = $this->buildWhereClause($conditions);
            $columnsStr = is_array($columns) ? implode(', ', $columns) : $columns;
            
            $sql = "SELECT {$columnsStr} FROM `{$table}`";
            if ($whereClause['clause']) {
                $sql .= " WHERE " . $whereClause['clause'];
            }
            $sql .= " LIMIT 1";
            
            $stmt = $this->pdo->prepare($sql);
            
            foreach ($whereClause['params'] as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            $stmt->execute();
            return $stmt->fetch();
            
        } catch (PDOException $e) {
            error_log("Select one failed for table {$table}: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Select multiple records
     */
    public function select($table, $conditions = [], $columns = '*', $orderBy = null, $limit = null) {
        try {
            $whereClause = $this->buildWhereClause($conditions);
            $columnsStr = is_array($columns) ? implode(', ', $columns) : $columns;
            
            $sql = "SELECT {$columnsStr} FROM `{$table}`";
            if ($whereClause['clause']) {
                $sql .= " WHERE " . $whereClause['clause'];
            }
            if ($orderBy) {
                $sql .= " ORDER BY {$orderBy}";
            }
            if ($limit) {
                $sql .= " LIMIT {$limit}";
            }
            
            $stmt = $this->pdo->prepare($sql);
            
            foreach ($whereClause['params'] as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            $stmt->execute();
            return $stmt->fetchAll();
            
        } catch (PDOException $e) {
            error_log("Select failed for table {$table}: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Update records
     */
    public function update($table, $data, $conditions) {
        try {
            $setClause = [];
            foreach (array_keys($data) as $column) {
                $setClause[] = "`{$column}` = :set_{$column}";
            }
            
            $whereClause = $this->buildWhereClause($conditions);
            
            $sql = sprintf(
                "UPDATE `%s` SET %s WHERE %s",
                $table,
                implode(', ', $setClause),
                $whereClause['clause']
            );
            
            $stmt = $this->pdo->prepare($sql);
            
            // Bind SET values
            foreach ($data as $key => $value) {
                $stmt->bindValue(":set_{$key}", $value);
            }
            
            // Bind WHERE values
            foreach ($whereClause['params'] as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            return $stmt->execute();
            
        } catch (PDOException $e) {
            error_log("Update failed for table {$table}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete records
     */
    public function delete($table, $conditions) {
        try {
            $whereClause = $this->buildWhereClause($conditions);
            
            $sql = "DELETE FROM `{$table}` WHERE " . $whereClause['clause'];
            $stmt = $this->pdo->prepare($sql);
            
            foreach ($whereClause['params'] as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            return $stmt->execute();
            
        } catch (PDOException $e) {
            error_log("Delete failed for table {$table}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Execute custom query
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            $stmt->execute();
            return $stmt->fetchAll();
            
        } catch (PDOException $e) {
            error_log("Custom query failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get last insert ID
     */
    public function getLastInsertId() {
        return $this->pdo->lastInsertId();
    }
    
    /**
     * Begin transaction
     */
    public function beginTransaction() {
        if ($this->transactionLevel === 0) {
            $this->pdo->beginTransaction();
        }
        $this->transactionLevel++;
    }
    
    /**
     * Commit transaction
     */
    public function commit() {
        $this->transactionLevel--;
        if ($this->transactionLevel === 0) {
            $this->pdo->commit();
        }
    }
    
    /**
     * Rollback transaction
     */
    public function rollback() {
        if ($this->transactionLevel > 0) {
            $this->pdo->rollback();
            $this->transactionLevel = 0;
        }
    }
    
    /**
     * Build WHERE clause from conditions array
     */
    private function buildWhereClause($conditions) {
        $clauses = [];
        $params = [];
        $paramCounter = 0;
        
        foreach ($conditions as $column => $value) {
            $paramCounter++;
            $paramName = ":where_param_{$paramCounter}";
            
            if (strpos($column, ' ') !== false) {
                // Column contains operator (e.g., 'created_at >', 'name LIKE')
                $clauses[] = "`" . trim(explode(' ', $column)[0]) . "` " . substr($column, strpos($column, ' ') + 1) . " {$paramName}";
            } else {
                // Simple equality
                $clauses[] = "`{$column}` = {$paramName}";
            }
            
            $params[$paramName] = $value;
        }
        
        return [
            'clause' => implode(' AND ', $clauses),
            'params' => $params
        ];
    }
    
    /**
     * Load database configuration
     */
    private function loadConfig() {
        $configFile = __DIR__ . '/../config/database.json';
        
        if (file_exists($configFile)) {
            $this->config = json_decode(file_get_contents($configFile), true);
        } else {
            // Default configuration
            $this->config = [
                'host' => 'localhost',
                'port' => 3306,
                'database' => 'sutradhar2070',
                'username' => 'root',
                'password' => '',
                'charset' => 'utf8mb4'
            ];
            
            // Save default config
            if (!is_dir(dirname($configFile))) {
                mkdir(dirname($configFile), 0755, true);
            }
            file_put_contents($configFile, json_encode($this->config, JSON_PRETTY_PRINT));
        }
    }
    
    /**
     * Check database connection
     */
    public function isConnected() {
        try {
            return $this->pdo && $this->pdo->query('SELECT 1') !== false;
        } catch (PDOException $e) {
            return false;
        }
    }
    
    /**
     * Get database statistics
     */
    public function getStats() {
        try {
            $stats = [];
            
            // Get table sizes
            $tables = ['users', 'user_sessions', 'password_resets', 'oauth_accounts', 'user_profiles'];
            foreach ($tables as $table) {
                $result = $this->query("SELECT COUNT(*) as count FROM `{$table}`");
                $stats['tables'][$table] = $result ? $result[0]['count'] : 0;
            }
            
            // Get database size
            $result = $this->query("SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'size_mb' FROM information_schema.tables WHERE table_schema = :db", [':db' => $this->config['database']]);
            $stats['size_mb'] = $result ? $result[0]['size_mb'] : 0;
            
            return $stats;
            
        } catch (Exception $e) {
            error_log("Failed to get database stats: " . $e->getMessage());
            return [];
        }
    }
}
