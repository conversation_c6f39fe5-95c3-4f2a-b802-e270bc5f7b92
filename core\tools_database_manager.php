<?php
/**
 * Tools Database Manager
 * Handles all database operations for the tools system
 */

require_once 'database_manager.php';

class ToolsDatabaseManager extends DatabaseManager {
    
    /**
     * Record tool usage
     */
    public function recordToolUsage($userId, $toolName, $operationId, $options = []) {
        $data = [
            'user_id' => $userId,
            'tool_name' => $toolName,
            'operation_id' => $operationId,
            'files_processed' => $options['files_processed'] ?? 1,
            'credits_used' => $options['credits_used'] ?? 1,
            'processing_time' => $options['processing_time'] ?? 0,
            'file_size_input' => $options['file_size_input'] ?? 0,
            'file_size_output' => $options['file_size_output'] ?? 0,
            'options_used' => json_encode($options['tool_options'] ?? []),
            'status' => 'processing'
        ];
        
        return $this->insert('tool_usage', $data);
    }
    
    /**
     * Update tool usage status
     */
    public function updateToolUsage($operationId, $status, $data = []) {
        $updateData = ['status' => $status];
        
        if ($status === 'completed') {
            $updateData['completed_at'] = date('Y-m-d H:i:s');
        }
        
        if (isset($data['processing_time'])) {
            $updateData['processing_time'] = $data['processing_time'];
        }
        
        if (isset($data['file_size_output'])) {
            $updateData['file_size_output'] = $data['file_size_output'];
        }
        
        if (isset($data['error_message'])) {
            $updateData['error_message'] = $data['error_message'];
        }
        
        return $this->update('tool_usage', $updateData, ['operation_id' => $operationId]);
    }
    
    /**
     * Store file information
     */
    public function storeFileInfo($userId, $operationId, $fileData) {
        $data = [
            'user_id' => $userId,
            'operation_id' => $operationId,
            'original_name' => $fileData['original_name'],
            'stored_name' => $fileData['stored_name'],
            'file_path' => $fileData['file_path'],
            'file_type' => $fileData['file_type'],
            'file_size' => $fileData['file_size'],
            'mime_type' => $fileData['mime_type'],
            'is_input' => $fileData['is_input'] ?? true,
            'is_output' => $fileData['is_output'] ?? false,
            'expires_at' => $fileData['expires_at'] ?? date('Y-m-d H:i:s', strtotime('+24 hours'))
        ];
        
        return $this->insert('tool_files', $data);
    }
    
    /**
     * Get tool configuration
     */
    public function getToolConfig($toolId = null) {
        if ($toolId) {
            return $this->selectOne('tools_config', ['tool_id' => $toolId, 'is_active' => 1]);
        }
        
        $sql = "
            SELECT tc.*, cat.display_name as category_name, cat.icon as category_icon
            FROM tools_config tc
            JOIN tool_categories cat ON tc.category_id = cat.id
            WHERE tc.is_active = 1 AND cat.is_active = 1
            ORDER BY cat.sort_order, tc.sort_order, tc.name
        ";
        
        return $this->query($sql);
    }
    
    /**
     * Get tools by category
     */
    public function getToolsByCategory() {
        $sql = "
            SELECT 
                cat.id as category_id,
                cat.name as category_name,
                cat.display_name,
                cat.description as category_description,
                cat.icon as category_icon,
                JSON_ARRAYAGG(
                    JSON_OBJECT(
                        'tool_id', tc.tool_id,
                        'name', tc.name,
                        'description', tc.description,
                        'icon', tc.icon,
                        'credit_cost', tc.credit_cost,
                        'max_file_size', tc.max_file_size,
                        'max_files_per_request', tc.max_files_per_request,
                        'allowed_formats', tc.allowed_formats,
                        'processing_options', tc.processing_options,
                        'requires_subscription', tc.requires_subscription
                    )
                ) as tools
            FROM tool_categories cat
            LEFT JOIN tools_config tc ON cat.id = tc.category_id AND tc.is_active = 1
            WHERE cat.is_active = 1
            GROUP BY cat.id, cat.name, cat.display_name, cat.description, cat.icon
            ORDER BY cat.sort_order
        ";
        
        $result = $this->query($sql);
        
        // Decode JSON and filter out null tools
        foreach ($result as &$category) {
            $tools = json_decode($category['tools'], true);
            $category['tools'] = array_filter($tools, function($tool) {
                return $tool['tool_id'] !== null;
            });
        }
        
        return $result;
    }
    
    /**
     * Check rate limits
     */
    public function checkRateLimit($userId, $resourceType, $resourceId = null) {
        $user = $this->selectOne('users', ['id' => $userId]);
        $subscriptionType = $user['subscription_type'] ?? 'free';
        
        // Get rate limit for user's subscription
        $limits = [
            'free' => ['hour' => 10, 'day' => 50],
            'pro' => ['hour' => 100, 'day' => 1000],
            'business' => ['hour' => 1000, 'day' => 10000],
            'enterprise' => ['hour' => -1, 'day' => -1] // Unlimited
        ];
        
        $userLimits = $limits[$subscriptionType] ?? $limits['free'];
        
        // Check hourly limit
        if ($userLimits['hour'] > 0) {
            $hourlyCount = $this->getRateLimitCount($userId, $resourceType, $resourceId, 'hour');
            if ($hourlyCount >= $userLimits['hour']) {
                return [
                    'allowed' => false,
                    'limit' => $userLimits['hour'],
                    'current' => $hourlyCount,
                    'window' => 'hour',
                    'reset_time' => strtotime('+1 hour', strtotime(date('Y-m-d H:00:00')))
                ];
            }
        }
        
        // Check daily limit
        if ($userLimits['day'] > 0) {
            $dailyCount = $this->getRateLimitCount($userId, $resourceType, $resourceId, 'day');
            if ($dailyCount >= $userLimits['day']) {
                return [
                    'allowed' => false,
                    'limit' => $userLimits['day'],
                    'current' => $dailyCount,
                    'window' => 'day',
                    'reset_time' => strtotime('tomorrow 00:00:00')
                ];
            }
        }
        
        return ['allowed' => true];
    }
    
    /**
     * Record rate limit usage
     */
    public function recordRateLimit($userId, $resourceType, $resourceId = null) {
        // Record hourly window
        $hourStart = date('Y-m-d H:00:00');
        $hourEnd = date('Y-m-d H:59:59');
        
        $this->upsertRateLimit($userId, $resourceType, $resourceId, $hourStart, $hourEnd);
        
        // Record daily window
        $dayStart = date('Y-m-d 00:00:00');
        $dayEnd = date('Y-m-d 23:59:59');
        
        $this->upsertRateLimit($userId, $resourceType, $resourceId, $dayStart, $dayEnd);
    }
    
    /**
     * Get user tool statistics
     */
    public function getUserToolStats($userId, $period = '30 days') {
        $sql = "
            SELECT 
                tool_name,
                COUNT(*) as usage_count,
                SUM(credits_used) as total_credits,
                AVG(processing_time) as avg_processing_time,
                SUM(files_processed) as total_files,
                MAX(created_at) as last_used,
                (COUNT(CASE WHEN status = 'completed' THEN 1 END) * 100.0 / COUNT(*)) as success_rate
            FROM tool_usage 
            WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY tool_name
            ORDER BY usage_count DESC
        ";
        
        $days = $this->parsePeriodToDays($period);
        return $this->query($sql, [$userId, $days]);
    }
    
    /**
     * Get user's favorite tools
     */
    public function getUserFavoriteTools($userId) {
        $sql = "
            SELECT 
                utf.tool_id,
                tc.name,
                tc.description,
                tc.icon,
                tc.credit_cost,
                utf.created_at as favorited_at
            FROM user_tool_favorites utf
            JOIN tools_config tc ON utf.tool_id = tc.tool_id
            WHERE utf.user_id = ? AND tc.is_active = 1
            ORDER BY utf.created_at DESC
        ";
        
        return $this->query($sql, [$userId]);
    }
    
    /**
     * Add tool to favorites
     */
    public function addToolToFavorites($userId, $toolId) {
        $data = [
            'user_id' => $userId,
            'tool_id' => $toolId
        ];
        
        try {
            return $this->insert('user_tool_favorites', $data);
        } catch (PDOException $e) {
            // Handle duplicate entry (already favorited)
            if ($e->getCode() == 23000) {
                return false; // Already favorited
            }
            throw $e;
        }
    }
    
    /**
     * Remove tool from favorites
     */
    public function removeToolFromFavorites($userId, $toolId) {
        return $this->delete('user_tool_favorites', [
            'user_id' => $userId,
            'tool_id' => $toolId
        ]);
    }
    
    /**
     * Get tool usage analytics
     */
    public function getToolAnalytics($period = '30 days') {
        $days = $this->parsePeriodToDays($period);
        
        $sql = "
            SELECT 
                tool_name,
                COUNT(*) as total_usage,
                COUNT(DISTINCT user_id) as unique_users,
                SUM(credits_used) as total_credits,
                AVG(processing_time) as avg_processing_time,
                (COUNT(CASE WHEN status = 'completed' THEN 1 END) * 100.0 / COUNT(*)) as success_rate,
                DATE(created_at) as usage_date
            FROM tool_usage 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY tool_name, DATE(created_at)
            ORDER BY usage_date DESC, total_usage DESC
        ";
        
        return $this->query($sql, [$days]);
    }
    
    /**
     * Clean up expired files
     */
    public function cleanupExpiredFiles() {
        $sql = "
            SELECT file_path 
            FROM tool_files 
            WHERE expires_at < NOW()
        ";
        
        $expiredFiles = $this->query($sql);
        $deletedCount = 0;
        
        foreach ($expiredFiles as $file) {
            if (file_exists($file['file_path'])) {
                unlink($file['file_path']);
                $deletedCount++;
            }
        }
        
        // Remove database records
        $this->delete('tool_files', ['expires_at <' => date('Y-m-d H:i:s')]);
        
        return $deletedCount;
    }
    
    /**
     * Get operation files
     */
    public function getOperationFiles($operationId, $userId = null) {
        $conditions = ['operation_id' => $operationId];
        if ($userId) {
            $conditions['user_id'] = $userId;
        }
        
        return $this->select('tool_files', $conditions, ['order' => 'is_input DESC, created_at ASC']);
    }
    
    /**
     * Update file download count
     */
    public function incrementDownloadCount($fileId) {
        $sql = "UPDATE tool_files SET download_count = download_count + 1 WHERE id = ?";
        return $this->query($sql, [$fileId]);
    }
    
    // Private helper methods
    
    private function getRateLimitCount($userId, $resourceType, $resourceId, $window) {
        if ($window === 'hour') {
            $start = date('Y-m-d H:00:00');
            $end = date('Y-m-d H:59:59');
        } else {
            $start = date('Y-m-d 00:00:00');
            $end = date('Y-m-d 23:59:59');
        }
        
        $conditions = [
            'user_id' => $userId,
            'resource_type' => $resourceType,
            'window_start >=' => $start,
            'window_end <=' => $end
        ];
        
        if ($resourceId) {
            $conditions['resource_identifier'] = $resourceId;
        }
        
        $result = $this->selectOne('rate_limits', $conditions);
        return $result ? $result['request_count'] : 0;
    }
    
    private function upsertRateLimit($userId, $resourceType, $resourceId, $windowStart, $windowEnd) {
        $conditions = [
            'user_id' => $userId,
            'resource_type' => $resourceType,
            'window_start' => $windowStart,
            'window_end' => $windowEnd
        ];
        
        if ($resourceId) {
            $conditions['resource_identifier'] = $resourceId;
        }
        
        $existing = $this->selectOne('rate_limits', $conditions);
        
        if ($existing) {
            $this->update('rate_limits', 
                ['request_count' => $existing['request_count'] + 1],
                ['id' => $existing['id']]
            );
        } else {
            $data = array_merge($conditions, ['request_count' => 1]);
            $this->insert('rate_limits', $data);
        }
    }
    
    private function parsePeriodToDays($period) {
        if (is_numeric($period)) {
            return (int)$period;
        }
        
        $periods = [
            '7 days' => 7,
            '30 days' => 30,
            '90 days' => 90,
            '1 year' => 365
        ];
        
        return $periods[$period] ?? 30;
    }
}
?>
