{"app": {"name": "Sutradhar Engine", "version": "1.0.0", "description": "Open-source voice AI platform for Indian cultural content", "debug": true}, "api_keys": {"huggingface": "*************************************", "together": "", "openai": "", "groq": ""}, "api_endpoints": {"ollama": "http://localhost:11434/api/", "huggingface": "https://api-inference.huggingface.co/models/", "openai_compatible": "https://api.together.xyz/v1/", "groq": "https://api.groq.com/openai/v1/"}, "content_generation": {"use_external_apis": true, "fallback_to_local": true, "max_tokens": 500, "temperature": 0.8, "preferred_providers": ["ollama", "huggingface", "openai_compatible"]}, "video_generation": {"enabled": true, "duration": 30, "fps": 30, "resolution": {"width": 720, "height": 1280}, "use_huggingface": true, "models": {"text_to_image": "stabilityai/stable-diffusion-2-1", "text_to_image_backup": "runwayml/stable-diffusion-v1-5", "image_to_video": "ali-vilab/text-to-video-ms-1.7b", "background_removal": "briaai/RMBG-1.4", "face_generation": "SG161222/Realistic_Vision_V4.0"}, "female_voice": {"enabled": true, "model": "microsoft/speecht5_tts", "speaker": "female_speaker", "language": "en"}}, "paths": {"assets": "./assets/", "flows": "./flows/", "data": "./data/", "output": "./data/output_history/", "temp": "./temp/"}, "voice": {"engine": "fallback", "sample_rate": 44100, "channels": 2, "default_voice": "babu_rao", "voice_models_path": "./assets/voices/", "cache_enabled": true, "cache_duration": 86400}, "audio": {"format": "mp3", "bitrate": "192k", "background_volume": 0.3, "voice_volume": 0.8, "effects_volume": 0.5}, "video": {"format": "mp4", "resolution": "720x1280", "fps": 30, "codec": "libx264", "subtitle_font": "<PERSON><PERSON>", "subtitle_size": 24}, "generation": {"max_duration": 300, "concurrent_jobs": 2, "cleanup_temp": true, "auto_subtitle": true}, "voice_packs": {"babu_rao": {"name": "<PERSON>", "description": "Iconic <PERSON> character voice", "model_file": "babu_rao.pth", "accent": "gujarati", "tone": "comedic"}, "villain": {"name": "Bollywood Villain", "description": "Classic Bollywood antagonist voice", "model_file": "villain.pth", "accent": "hindi", "tone": "dramatic"}, "dadi": {"name": "<PERSON><PERSON>", "description": "Traditional grandmother voice", "model_file": "dadi.pth", "accent": "punjabi", "tone": "wise"}, "gym_bro": {"name": "<PERSON><PERSON>", "description": "Fitness enthusiast voice", "model_file": "gym_bro.pth", "accent": "delhi", "tone": "energetic"}, "news_anchor": {"name": "News Anchor", "description": "Professional news reader voice", "model_file": "news_anchor.pth", "accent": "neutral", "tone": "formal"}}, "styles": {"funny": {"prompt_tokens": ["sarcastic", "humorous", "witty"], "background_music": "comedy", "effects": ["laugh", "rimshot"]}, "desi": {"prompt_tokens": ["hinglish", "cultural", "relatable"], "background_music": "traditional", "effects": ["tabla", "sitar"]}, "emotional": {"prompt_tokens": ["heartfelt", "touching", "deep"], "background_music": "emotional", "effects": ["soft_piano"]}, "bollywood": {"prompt_tokens": ["filmy", "dramatic", "over_the_top"], "background_music": "bollywood", "effects": ["dhol", "<PERSON><PERSON><PERSON>"]}}}