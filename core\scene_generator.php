<?php
/**
 * Scene Generator - Standalone script for parallel scene generation
 * Used by OptimizedVideoEngine for parallel processing
 */

// Get task data from command line argument
if ($argc < 2) {
    echo "Usage: php scene_generator.php <task_json>\n";
    exit(1);
}

$taskJson = $argv[1];
$task = json_decode($taskJson, true);

if (!$task) {
    echo "Invalid task JSON\n";
    exit(1);
}

try {
    $scene = generateScene($task);
    file_put_contents($task['output_file'], json_encode($scene));
    echo "Scene generated successfully\n";
    exit(0);
} catch (Exception $e) {
    echo "Scene generation failed: " . $e->getMessage() . "\n";
    exit(1);
}

/**
 * Generate scene data based on segment and mood
 */
function generateScene($task) {
    $segment = $task['segment'];
    $mood = $task['mood'];
    $index = $task['index'];
    
    // Extract text content
    $text = $segment['text'] ?? $segment['content'] ?? "Scene {$index}";
    
    // Generate scene description based on mood
    $sceneDescription = generateSceneDescription($text, $mood);
    
    // Generate visual elements
    $visualElements = generateVisualElements($sceneDescription, $mood);
    
    // Generate timing information
    $timing = generateTiming($segment, $index);
    
    return [
        'index' => $index,
        'text' => $text,
        'description' => $sceneDescription,
        'visual_elements' => $visualElements,
        'timing' => $timing,
        'mood_applied' => $mood ? true : false
    ];
}

/**
 * Generate scene description based on text and mood
 */
function generateSceneDescription($text, $mood) {
    if (!$mood) {
        return "Visual representation of: " . substr($text, 0, 100);
    }
    
    // Extract mood characteristics
    $moodName = $mood['name'] ?? 'neutral';
    $category = $mood['category'] ?? 'general';
    $description = $mood['description'] ?? '';
    
    // Generate mood-appropriate scene description
    $templates = [
        'positive_energy' => [
            "Bright and energetic scene showing {text} with vibrant colors and dynamic movement",
            "Uplifting visual of {text} filled with light and positive energy",
            "Dynamic representation of {text} with explosive colors and joyful atmosphere"
        ],
        'calm_peaceful' => [
            "Serene and tranquil scene depicting {text} with soft, flowing elements",
            "Peaceful visualization of {text} in gentle, soothing tones",
            "Calm and meditative representation of {text} with harmonious composition"
        ],
        'dramatic_intense' => [
            "Dramatic and powerful scene showing {text} with bold contrasts and intense lighting",
            "Epic visualization of {text} with theatrical elements and strong emotions",
            "Intense representation of {text} with dramatic shadows and powerful imagery"
        ],
        'mysterious_dark' => [
            "Mysterious and enigmatic scene of {text} shrouded in shadows and intrigue",
            "Dark and atmospheric visualization of {text} with hidden elements",
            "Shadowy representation of {text} with mysterious lighting and depth"
        ],
        'romantic_emotional' => [
            "Romantic and heartfelt scene showing {text} with warm, intimate lighting",
            "Emotional visualization of {text} filled with love and tenderness",
            "Passionate representation of {text} with soft, romantic atmosphere"
        ],
        'futuristic_tech' => [
            "Futuristic and technological scene of {text} with digital elements and neon lighting",
            "High-tech visualization of {text} with cyberpunk aesthetics and glowing effects",
            "Sci-fi representation of {text} with advanced technology and sleek design"
        ]
    ];
    
    $categoryTemplates = $templates[$category] ?? $templates['positive_energy'];
    $template = $categoryTemplates[array_rand($categoryTemplates)];
    
    return str_replace('{text}', substr($text, 0, 50), $template);
}

/**
 * Generate visual elements based on scene and mood
 */
function generateVisualElements($sceneDescription, $mood) {
    $elements = [
        'background' => generateBackground($mood),
        'foreground' => generateForeground($sceneDescription, $mood),
        'effects' => generateEffects($mood),
        'colors' => generateColorPalette($mood),
        'lighting' => generateLighting($mood),
        'composition' => generateComposition($mood)
    ];
    
    return $elements;
}

/**
 * Generate background based on mood
 */
function generateBackground($mood) {
    if (!$mood || !isset($mood['visual_style'])) {
        return ['type' => 'solid', 'color' => '#1A1A2E'];
    }
    
    $colors = $mood['visual_style']['colors'] ?? ['#1A1A2E'];
    $lighting = $mood['visual_style']['lighting'] ?? 'soft_diffused';
    
    $backgroundTypes = [
        'gradient' => [
            'type' => 'gradient',
            'colors' => array_slice($colors, 0, 2),
            'direction' => 'diagonal'
        ],
        'solid' => [
            'type' => 'solid',
            'color' => $colors[0] ?? '#1A1A2E'
        ],
        'textured' => [
            'type' => 'textured',
            'base_color' => $colors[0] ?? '#1A1A2E',
            'texture' => 'subtle_noise'
        ]
    ];
    
    // Choose background type based on lighting
    if (strpos($lighting, 'dramatic') !== false) {
        return $backgroundTypes['gradient'];
    } elseif (strpos($lighting, 'soft') !== false) {
        return $backgroundTypes['textured'];
    } else {
        return $backgroundTypes['solid'];
    }
}

/**
 * Generate foreground elements
 */
function generateForeground($sceneDescription, $mood) {
    $elements = [];
    
    // Add text element
    $elements[] = [
        'type' => 'text',
        'content' => extractKeywords($sceneDescription),
        'position' => 'center',
        'style' => getMoodTextStyle($mood)
    ];
    
    // Add geometric shapes based on mood
    if ($mood && isset($mood['visual_style']['composition'])) {
        $composition = $mood['visual_style']['composition'];
        
        if (strpos($composition, 'geometric') !== false || strpos($composition, 'angular') !== false) {
            $elements[] = [
                'type' => 'shape',
                'shape' => 'triangle',
                'position' => 'background',
                'style' => 'outline'
            ];
        } elseif (strpos($composition, 'flowing') !== false || strpos($composition, 'organic') !== false) {
            $elements[] = [
                'type' => 'shape',
                'shape' => 'circle',
                'position' => 'background',
                'style' => 'filled'
            ];
        }
    }
    
    return $elements;
}

/**
 * Generate effects based on mood
 */
function generateEffects($mood) {
    if (!$mood || !isset($mood['visual_style'])) {
        return ['blur' => 0, 'glow' => 0, 'particles' => false];
    }
    
    $movement = $mood['visual_style']['movement'] ?? 'static';
    $lighting = $mood['visual_style']['lighting'] ?? 'normal';
    
    $effects = [
        'blur' => 0,
        'glow' => 0,
        'particles' => false,
        'animation' => 'none'
    ];
    
    // Apply effects based on movement
    if (strpos($movement, 'dynamic') !== false) {
        $effects['particles'] = true;
        $effects['animation'] = 'pulse';
    } elseif (strpos($movement, 'flowing') !== false) {
        $effects['blur'] = 1;
        $effects['animation'] = 'wave';
    } elseif (strpos($movement, 'energetic') !== false) {
        $effects['glow'] = 2;
        $effects['animation'] = 'vibrate';
    }
    
    // Apply effects based on lighting
    if (strpos($lighting, 'glow') !== false || strpos($lighting, 'neon') !== false) {
        $effects['glow'] = 3;
    } elseif (strpos($lighting, 'soft') !== false) {
        $effects['blur'] = 0.5;
    }
    
    return $effects;
}

/**
 * Generate color palette
 */
function generateColorPalette($mood) {
    if (!$mood || !isset($mood['visual_style']['colors'])) {
        return ['#1A1A2E', '#00D4FF', '#8B5CF6'];
    }
    
    return $mood['visual_style']['colors'];
}

/**
 * Generate lighting setup
 */
function generateLighting($mood) {
    if (!$mood || !isset($mood['visual_style']['lighting'])) {
        return ['type' => 'ambient', 'intensity' => 0.5, 'direction' => 'center'];
    }
    
    $lighting = $mood['visual_style']['lighting'];
    
    $lightingMap = [
        'high_contrast_bright' => ['type' => 'directional', 'intensity' => 0.9, 'direction' => 'top'],
        'soft_upward' => ['type' => 'ambient', 'intensity' => 0.6, 'direction' => 'bottom'],
        'dramatic_contrasts' => ['type' => 'spot', 'intensity' => 0.8, 'direction' => 'side'],
        'neon_harsh' => ['type' => 'neon', 'intensity' => 1.0, 'direction' => 'center'],
        'soft_diffused' => ['type' => 'ambient', 'intensity' => 0.4, 'direction' => 'center']
    ];
    
    return $lightingMap[$lighting] ?? ['type' => 'ambient', 'intensity' => 0.5, 'direction' => 'center'];
}

/**
 * Generate composition rules
 */
function generateComposition($mood) {
    if (!$mood || !isset($mood['visual_style']['composition'])) {
        return ['rule' => 'center', 'balance' => 'symmetric', 'focus' => 'center'];
    }
    
    $composition = $mood['visual_style']['composition'];
    
    $compositionMap = [
        'energetic_angles' => ['rule' => 'diagonal', 'balance' => 'dynamic', 'focus' => 'multiple'],
        'heroic_perspective' => ['rule' => 'thirds', 'balance' => 'bottom_heavy', 'focus' => 'center'],
        'asymmetrical_fun' => ['rule' => 'asymmetric', 'balance' => 'playful', 'focus' => 'offset'],
        'centered_strong' => ['rule' => 'center', 'balance' => 'symmetric', 'focus' => 'center'],
        'wide_expansive' => ['rule' => 'horizontal', 'balance' => 'spread', 'focus' => 'wide']
    ];
    
    return $compositionMap[$composition] ?? ['rule' => 'center', 'balance' => 'symmetric', 'focus' => 'center'];
}

/**
 * Generate timing information
 */
function generateTiming($segment, $index) {
    $duration = $segment['duration'] ?? 3;
    $startTime = $index * $duration;
    
    return [
        'start_time' => $startTime,
        'duration' => $duration,
        'end_time' => $startTime + $duration,
        'transition_in' => 'fade',
        'transition_out' => 'fade'
    ];
}

/**
 * Get mood-appropriate text style
 */
function getMoodTextStyle($mood) {
    if (!$mood) {
        return ['font' => 'Arial', 'size' => 24, 'weight' => 'normal', 'color' => '#FFFFFF'];
    }
    
    $category = $mood['category'] ?? 'general';
    
    $styleMap = [
        'positive_energy' => ['font' => 'Arial', 'size' => 28, 'weight' => 'bold', 'color' => '#FFD700'],
        'calm_peaceful' => ['font' => 'Georgia', 'size' => 22, 'weight' => 'normal', 'color' => '#E6F3FF'],
        'dramatic_intense' => ['font' => 'Impact', 'size' => 32, 'weight' => 'bold', 'color' => '#FF4444'],
        'mysterious_dark' => ['font' => 'Times', 'size' => 20, 'weight' => 'italic', 'color' => '#CCCCCC'],
        'romantic_emotional' => ['font' => 'Brush Script', 'size' => 26, 'weight' => 'normal', 'color' => '#FFB6C1'],
        'futuristic_tech' => ['font' => 'Courier', 'size' => 24, 'weight' => 'bold', 'color' => '#00FFFF']
    ];
    
    return $styleMap[$category] ?? ['font' => 'Arial', 'size' => 24, 'weight' => 'normal', 'color' => '#FFFFFF'];
}

/**
 * Extract keywords from text
 */
function extractKeywords($text) {
    $words = explode(' ', strtolower($text));
    $keywords = array_filter($words, function($word) {
        return strlen($word) > 3 && !in_array($word, ['the', 'and', 'with', 'from', 'that', 'this']);
    });
    
    return implode(' ', array_slice($keywords, 0, 3));
}
?>
