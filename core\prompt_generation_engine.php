<?php
/**
 * Prompt Generation Engine - Intelligent prompt creation system
 * Generates 20-30 unique prompts based on mood, topic, image type, and inspiration
 */

require_once 'mood_database.php';

class PromptGenerationEngine {
    private $moodDatabase;
    private $topicCategories;
    private $imageTypes;
    private $inspirationSources;
    private $promptTemplates;
    
    public function __construct() {
        $this->moodDatabase = new MoodDatabase();
        $this->initializeTopicCategories();
        $this->initializeImageTypes();
        $this->initializeInspirationSources();
        $this->initializePromptTemplates();
    }
    
    /**
     * Generate 20-30 unique prompts based on parameters
     */
    public function generatePrompts($moodId, $topicCategory, $imageType, $inspiration, $count = 25) {
        $mood = $this->moodDatabase->getMood($moodId);
        if (!$mood) {
            throw new Exception("Invalid mood ID: {$moodId}");
        }
        
        $prompts = [];
        $usedCombinations = [];
        
        // Get mood characteristics
        $moodCharacteristics = $this->moodDatabase->getMoodCharacteristics($moodId);
        
        // Get topic details
        $topicDetails = $this->topicCategories[$topicCategory] ?? null;
        if (!$topicDetails) {
            throw new Exception("Invalid topic category: {$topicCategory}");
        }
        
        // Get image type details
        $imageTypeDetails = $this->imageTypes[$imageType] ?? null;
        if (!$imageTypeDetails) {
            throw new Exception("Invalid image type: {$imageType}");
        }
        
        // Generate prompts using different strategies
        $strategies = [
            'mood_focused' => 8,
            'topic_focused' => 7,
            'image_focused' => 5,
            'inspiration_focused' => 5
        ];
        
        foreach ($strategies as $strategy => $strategyCount) {
            $strategyPrompts = $this->generatePromptsWithStrategy(
                $strategy,
                $mood,
                $moodCharacteristics,
                $topicDetails,
                $imageTypeDetails,
                $inspiration,
                $strategyCount,
                $usedCombinations
            );
            
            $prompts = array_merge($prompts, $strategyPrompts);
        }
        
        // Shuffle and limit to requested count
        shuffle($prompts);
        return array_slice($prompts, 0, $count);
    }
    
    /**
     * Generate prompts using specific strategy
     */
    private function generatePromptsWithStrategy($strategy, $mood, $moodCharacteristics, $topicDetails, $imageTypeDetails, $inspiration, $count, &$usedCombinations) {
        $prompts = [];
        $attempts = 0;
        $maxAttempts = $count * 3;
        
        while (count($prompts) < $count && $attempts < $maxAttempts) {
            $attempts++;
            
            switch ($strategy) {
                case 'mood_focused':
                    $prompt = $this->generateMoodFocusedPrompt($mood, $moodCharacteristics, $topicDetails, $imageTypeDetails, $inspiration);
                    break;
                case 'topic_focused':
                    $prompt = $this->generateTopicFocusedPrompt($topicDetails, $mood, $moodCharacteristics, $imageTypeDetails, $inspiration);
                    break;
                case 'image_focused':
                    $prompt = $this->generateImageFocusedPrompt($imageTypeDetails, $mood, $moodCharacteristics, $topicDetails, $inspiration);
                    break;
                case 'inspiration_focused':
                    $prompt = $this->generateInspirationFocusedPrompt($inspiration, $mood, $moodCharacteristics, $topicDetails, $imageTypeDetails);
                    break;
                default:
                    continue 2;
            }
            
            // Check for uniqueness
            $promptHash = md5($prompt['content']);
            if (!in_array($promptHash, $usedCombinations)) {
                $usedCombinations[] = $promptHash;
                $prompts[] = $prompt;
            }
        }
        
        return $prompts;
    }
    
    /**
     * Generate mood-focused prompt
     */
    private function generateMoodFocusedPrompt($mood, $moodCharacteristics, $topicDetails, $imageTypeDetails, $inspiration) {
        $template = $this->getRandomTemplate('mood_focused');
        
        // Build mood-centric content
        $moodElements = [
            'colors' => $this->formatColorPalette($moodCharacteristics['visual_style']['colors']),
            'lighting' => $moodCharacteristics['visual_style']['lighting'],
            'movement' => $moodCharacteristics['visual_style']['movement'],
            'composition' => $moodCharacteristics['visual_style']['composition'],
            'mood_name' => $mood['name'],
            'mood_description' => $mood['description']
        ];
        
        $content = $this->fillTemplate($template, [
            'mood_elements' => $moodElements,
            'topic' => $topicDetails,
            'image_type' => $imageTypeDetails,
            'inspiration' => $inspiration
        ]);
        
        return [
            'content' => $content,
            'strategy' => 'mood_focused',
            'mood' => $mood['name'],
            'topic' => $topicDetails['name'],
            'image_type' => $imageTypeDetails['name'],
            'inspiration' => $inspiration,
            'characteristics' => $moodCharacteristics
        ];
    }
    
    /**
     * Generate topic-focused prompt
     */
    private function generateTopicFocusedPrompt($topicDetails, $mood, $moodCharacteristics, $imageTypeDetails, $inspiration) {
        $template = $this->getRandomTemplate('topic_focused');
        
        // Select random subtopic
        $subtopic = $topicDetails['subtopics'][array_rand($topicDetails['subtopics'])];
        
        $content = $this->fillTemplate($template, [
            'topic' => $topicDetails,
            'subtopic' => $subtopic,
            'mood_elements' => $moodCharacteristics,
            'image_type' => $imageTypeDetails,
            'inspiration' => $inspiration,
            'mood_name' => $mood['name']
        ]);
        
        return [
            'content' => $content,
            'strategy' => 'topic_focused',
            'mood' => $mood['name'],
            'topic' => $topicDetails['name'],
            'subtopic' => $subtopic,
            'image_type' => $imageTypeDetails['name'],
            'inspiration' => $inspiration
        ];
    }
    
    /**
     * Generate image-focused prompt
     */
    private function generateImageFocusedPrompt($imageTypeDetails, $mood, $moodCharacteristics, $topicDetails, $inspiration) {
        $template = $this->getRandomTemplate('image_focused');
        
        // Select random style element
        $styleElement = $imageTypeDetails['style_elements'][array_rand($imageTypeDetails['style_elements'])];
        
        $content = $this->fillTemplate($template, [
            'image_type' => $imageTypeDetails,
            'style_element' => $styleElement,
            'mood_elements' => $moodCharacteristics,
            'topic' => $topicDetails,
            'inspiration' => $inspiration,
            'mood_name' => $mood['name']
        ]);
        
        return [
            'content' => $content,
            'strategy' => 'image_focused',
            'mood' => $mood['name'],
            'topic' => $topicDetails['name'],
            'image_type' => $imageTypeDetails['name'],
            'style_element' => $styleElement,
            'inspiration' => $inspiration
        ];
    }
    
    /**
     * Generate inspiration-focused prompt
     */
    private function generateInspirationFocusedPrompt($inspiration, $mood, $moodCharacteristics, $topicDetails, $imageTypeDetails) {
        $template = $this->getRandomTemplate('inspiration_focused');
        
        // Process inspiration keywords
        $inspirationKeywords = $this->extractKeywords($inspiration);
        
        $content = $this->fillTemplate($template, [
            'inspiration' => $inspiration,
            'inspiration_keywords' => $inspirationKeywords,
            'mood_elements' => $moodCharacteristics,
            'topic' => $topicDetails,
            'image_type' => $imageTypeDetails,
            'mood_name' => $mood['name']
        ]);
        
        return [
            'content' => $content,
            'strategy' => 'inspiration_focused',
            'mood' => $mood['name'],
            'topic' => $topicDetails['name'],
            'image_type' => $imageTypeDetails['name'],
            'inspiration' => $inspiration,
            'keywords' => $inspirationKeywords
        ];
    }
    
    /**
     * Initialize comprehensive topic categories
     */
    private function initializeTopicCategories() {
        $this->topicCategories = [
            'nature_wildlife' => [
                'name' => 'Nature & Wildlife',
                'description' => 'Natural environments and animals in their habitats',
                'icon' => '🌿',
                'color' => '#10B981',
                'subtopics' => [
                    'forest_animals', 'ocean_life', 'mountain_landscapes', 'desert_scenes',
                    'tropical_paradise', 'arctic_wildlife', 'jungle_adventures', 'savanna_life',
                    'underwater_world', 'bird_migration', 'seasonal_changes', 'natural_phenomena',
                    'rainforest_canopy', 'coral_reefs', 'alpine_meadows', 'wetland_ecosystems'
                ],
                'keywords' => ['nature', 'wildlife', 'animals', 'landscape', 'environment', 'ecosystem', 'biodiversity'],
                'visual_themes' => ['organic_shapes', 'natural_lighting', 'earth_tones', 'flowing_movement']
            ],

            'urban_city' => [
                'name' => 'Urban & City Life',
                'description' => 'Modern city environments and urban culture',
                'icon' => '🏙️',
                'color' => '#3B82F6',
                'subtopics' => [
                    'city_skylines', 'street_art', 'urban_culture', 'nightlife', 'transportation',
                    'architecture', 'busy_streets', 'rooftop_views', 'underground_scenes',
                    'market_places', 'urban_nature', 'city_lights', 'metro_systems', 'skyscrapers',
                    'urban_parks', 'street_food', 'public_spaces', 'urban_decay', 'gentrification'
                ],
                'keywords' => ['city', 'urban', 'modern', 'architecture', 'street', 'metropolitan', 'civilization'],
                'visual_themes' => ['geometric_lines', 'artificial_lighting', 'concrete_textures', 'dynamic_angles']
            ],

            'fantasy_magic' => [
                'name' => 'Fantasy & Magic',
                'description' => 'Magical worlds and fantastical creatures',
                'icon' => '🧙‍♀️',
                'color' => '#8B5CF6',
                'subtopics' => [
                    'magical_forests', 'dragon_realms', 'fairy_kingdoms', 'wizard_towers',
                    'enchanted_castles', 'mystical_creatures', 'magical_artifacts', 'spell_casting',
                    'fantasy_battles', 'magical_portals', 'ancient_ruins', 'mythical_beasts',
                    'elemental_magic', 'crystal_caves', 'floating_islands', 'time_magic'
                ],
                'keywords' => ['fantasy', 'magic', 'mystical', 'enchanted', 'mythical', 'supernatural', 'ethereal'],
                'visual_themes' => ['mystical_glows', 'particle_effects', 'ethereal_colors', 'impossible_geometry']
            ],

            'science_technology' => [
                'name' => 'Science & Technology',
                'description' => 'Scientific discoveries and technological innovations',
                'icon' => '🔬',
                'color' => '#06B6D4',
                'subtopics' => [
                    'space_exploration', 'robotics', 'artificial_intelligence', 'biotechnology',
                    'quantum_physics', 'medical_breakthroughs', 'renewable_energy', 'nanotechnology',
                    'virtual_reality', 'genetic_engineering', 'space_stations', 'laboratory_research',
                    'future_cities', 'cybernetics', 'holographic_displays', 'neural_interfaces'
                ],
                'keywords' => ['science', 'technology', 'innovation', 'research', 'discovery', 'future', 'digital'],
                'visual_themes' => ['clean_lines', 'blue_lighting', 'metallic_surfaces', 'precise_geometry']
            ],

            'adventure_travel' => [
                'name' => 'Adventure & Travel',
                'description' => 'Exciting journeys and exploration adventures',
                'icon' => '🗺️',
                'color' => '#F59E0B',
                'subtopics' => [
                    'mountain_climbing', 'ocean_voyages', 'desert_expeditions', 'jungle_treks',
                    'arctic_exploration', 'cave_spelunking', 'sky_diving', 'deep_sea_diving',
                    'cultural_journeys', 'historical_sites', 'extreme_sports', 'wilderness_survival',
                    'treasure_hunting', 'archaeological_digs', 'safari_adventures', 'island_hopping'
                ],
                'keywords' => ['adventure', 'travel', 'exploration', 'journey', 'discovery', 'expedition', 'quest'],
                'visual_themes' => ['dynamic_movement', 'wide_vistas', 'warm_colors', 'action_sequences']
            ],

            'arts_culture' => [
                'name' => 'Arts & Culture',
                'description' => 'Creative expressions and cultural heritage',
                'icon' => '🎨',
                'color' => '#EC4899',
                'subtopics' => [
                    'painting_studios', 'sculpture_gardens', 'music_performances', 'dance_theaters',
                    'cultural_festivals', 'traditional_crafts', 'modern_galleries', 'street_performances',
                    'literary_scenes', 'fashion_shows', 'architectural_marvels', 'cultural_ceremonies',
                    'artistic_collaborations', 'creative_workshops', 'cultural_exchanges', 'heritage_sites'
                ],
                'keywords' => ['art', 'culture', 'creativity', 'expression', 'heritage', 'tradition', 'aesthetic'],
                'visual_themes' => ['artistic_composition', 'vibrant_colors', 'textural_elements', 'creative_framing']
            ],

            'sports_fitness' => [
                'name' => 'Sports & Fitness',
                'description' => 'Athletic activities and physical wellness',
                'icon' => '⚽',
                'color' => '#EF4444',
                'subtopics' => [
                    'team_sports', 'individual_athletics', 'water_sports', 'winter_sports',
                    'martial_arts', 'fitness_training', 'yoga_meditation', 'extreme_sports',
                    'competitive_gaming', 'outdoor_activities', 'gym_workouts', 'sports_events',
                    'athletic_achievements', 'training_regimens', 'sports_technology', 'wellness_lifestyle'
                ],
                'keywords' => ['sports', 'fitness', 'athletics', 'competition', 'training', 'wellness', 'performance'],
                'visual_themes' => ['dynamic_action', 'energy_lines', 'bold_colors', 'motion_blur']
            ],

            'food_culinary' => [
                'name' => 'Food & Culinary',
                'description' => 'Culinary arts and gastronomic experiences',
                'icon' => '🍳',
                'color' => '#F97316',
                'subtopics' => [
                    'gourmet_cooking', 'street_food', 'farm_to_table', 'international_cuisine',
                    'baking_pastries', 'wine_tasting', 'food_festivals', 'restaurant_scenes',
                    'cooking_techniques', 'ingredient_sourcing', 'food_photography', 'culinary_traditions',
                    'molecular_gastronomy', 'food_trucks', 'farmers_markets', 'cooking_competitions'
                ],
                'keywords' => ['food', 'culinary', 'cooking', 'cuisine', 'gastronomy', 'flavors', 'ingredients'],
                'visual_themes' => ['warm_lighting', 'rich_textures', 'appetizing_colors', 'close_up_details']
            ],

            'business_professional' => [
                'name' => 'Business & Professional',
                'description' => 'Corporate environments and professional activities',
                'icon' => '💼',
                'color' => '#6366F1',
                'subtopics' => [
                    'office_environments', 'business_meetings', 'corporate_events', 'startup_culture',
                    'financial_markets', 'professional_networking', 'workplace_innovation', 'team_collaboration',
                    'business_presentations', 'corporate_leadership', 'entrepreneurship', 'workplace_diversity',
                    'remote_work', 'business_travel', 'corporate_responsibility', 'industry_conferences'
                ],
                'keywords' => ['business', 'professional', 'corporate', 'workplace', 'career', 'leadership', 'innovation'],
                'visual_themes' => ['clean_aesthetics', 'professional_lighting', 'modern_design', 'structured_composition']
            ],

            'education_learning' => [
                'name' => 'Education & Learning',
                'description' => 'Educational environments and learning experiences',
                'icon' => '📚',
                'color' => '#059669',
                'subtopics' => [
                    'classroom_scenes', 'university_campuses', 'online_learning', 'research_laboratories',
                    'library_studies', 'educational_technology', 'student_life', 'academic_achievements',
                    'teaching_methods', 'educational_innovation', 'skill_development', 'knowledge_sharing',
                    'educational_games', 'learning_disabilities', 'adult_education', 'vocational_training'
                ],
                'keywords' => ['education', 'learning', 'knowledge', 'teaching', 'academic', 'study', 'development'],
                'visual_themes' => ['bright_lighting', 'organized_layouts', 'inspiring_colors', 'focused_composition']
            ]
        ];
    }
    
    /**
     * Initialize comprehensive image type classification system
     */
    private function initializeImageTypes() {
        $this->imageTypes = [
            'cinematic' => [
                'name' => 'Cinematic',
                'description' => 'Movie-like dramatic scenes with professional cinematography',
                'icon' => '🎬',
                'color' => '#DC2626',
                'category' => 'professional',
                'style_elements' => [
                    'dramatic_lighting', 'wide_shots', 'close_ups', 'depth_of_field',
                    'color_grading', 'dynamic_angles', 'atmospheric_effects', 'lens_flares',
                    'cinematic_composition', 'film_grain', 'anamorphic_bokeh', 'tracking_shots'
                ],
                'characteristics' => ['dramatic', 'professional', 'storytelling', 'emotional', 'immersive'],
                'technical_specs' => [
                    'aspect_ratio' => '16:9_or_21:9',
                    'color_depth' => 'high',
                    'contrast' => 'high',
                    'saturation' => 'enhanced'
                ],
                'lighting_style' => 'dramatic_directional',
                'composition_rules' => ['rule_of_thirds', 'leading_lines', 'symmetry']
            ],

            'artistic' => [
                'name' => 'Artistic',
                'description' => 'Creative and expressive visual art with unique aesthetics',
                'icon' => '🎨',
                'color' => '#7C3AED',
                'category' => 'creative',
                'style_elements' => [
                    'painterly_style', 'abstract_elements', 'creative_composition', 'artistic_filters',
                    'texture_emphasis', 'color_harmony', 'artistic_lighting', 'creative_perspectives',
                    'brush_strokes', 'mixed_media', 'stylized_forms', 'expressive_colors'
                ],
                'characteristics' => ['creative', 'expressive', 'unique', 'aesthetic', 'interpretive'],
                'technical_specs' => [
                    'aspect_ratio' => 'flexible',
                    'color_depth' => 'stylized',
                    'contrast' => 'artistic',
                    'saturation' => 'expressive'
                ],
                'lighting_style' => 'creative_artistic',
                'composition_rules' => ['golden_ratio', 'dynamic_symmetry', 'creative_balance']
            ],

            'photorealistic' => [
                'name' => 'Photorealistic',
                'description' => 'Highly detailed realistic imagery with natural authenticity',
                'icon' => '📷',
                'color' => '#059669',
                'category' => 'realistic',
                'style_elements' => [
                    'high_detail', 'natural_lighting', 'realistic_textures', 'accurate_proportions',
                    'natural_colors', 'sharp_focus', 'realistic_shadows', 'authentic_materials',
                    'environmental_details', 'surface_imperfections', 'natural_wear', 'realistic_physics'
                ],
                'characteristics' => ['realistic', 'detailed', 'natural', 'authentic', 'precise'],
                'technical_specs' => [
                    'aspect_ratio' => 'natural',
                    'color_depth' => 'true_to_life',
                    'contrast' => 'natural',
                    'saturation' => 'realistic'
                ],
                'lighting_style' => 'natural_environmental',
                'composition_rules' => ['natural_framing', 'realistic_perspective', 'environmental_context']
            ],

            'minimalist' => [
                'name' => 'Minimalist',
                'description' => 'Clean, simple designs with focus on essential elements',
                'icon' => '⚪',
                'color' => '#6B7280',
                'category' => 'modern',
                'style_elements' => [
                    'clean_lines', 'negative_space', 'simple_forms', 'limited_palette',
                    'geometric_shapes', 'subtle_textures', 'minimal_details', 'focused_composition',
                    'white_space', 'typography_focus', 'monochromatic', 'essential_elements'
                ],
                'characteristics' => ['clean', 'simple', 'focused', 'elegant', 'uncluttered'],
                'technical_specs' => [
                    'aspect_ratio' => 'clean_ratios',
                    'color_depth' => 'limited',
                    'contrast' => 'subtle',
                    'saturation' => 'muted'
                ],
                'lighting_style' => 'soft_even',
                'composition_rules' => ['negative_space', 'central_focus', 'geometric_balance']
            ],

            'vintage_retro' => [
                'name' => 'Vintage Retro',
                'description' => 'Nostalgic styles from past decades with period authenticity',
                'icon' => '📼',
                'color' => '#D97706',
                'category' => 'nostalgic',
                'style_elements' => [
                    'film_grain', 'vintage_colors', 'retro_filters', 'aged_textures',
                    'period_styling', 'nostalgic_lighting', 'vintage_typography', 'retro_patterns',
                    'sepia_tones', 'faded_colors', 'analog_artifacts', 'period_props'
                ],
                'characteristics' => ['nostalgic', 'warm', 'authentic', 'timeless', 'charming'],
                'technical_specs' => [
                    'aspect_ratio' => 'period_appropriate',
                    'color_depth' => 'vintage',
                    'contrast' => 'soft',
                    'saturation' => 'warm_muted'
                ],
                'lighting_style' => 'warm_nostalgic',
                'composition_rules' => ['classic_framing', 'period_composition', 'nostalgic_perspective']
            ],

            'abstract_conceptual' => [
                'name' => 'Abstract Conceptual',
                'description' => 'Non-representational art focusing on concepts and emotions',
                'icon' => '🌀',
                'color' => '#EC4899',
                'category' => 'experimental',
                'style_elements' => [
                    'abstract_forms', 'conceptual_elements', 'symbolic_imagery', 'experimental_techniques',
                    'non_literal', 'emotional_expression', 'color_psychology', 'form_exploration',
                    'geometric_abstraction', 'fluid_dynamics', 'pattern_generation', 'conceptual_metaphors'
                ],
                'characteristics' => ['abstract', 'conceptual', 'experimental', 'symbolic', 'interpretive'],
                'technical_specs' => [
                    'aspect_ratio' => 'experimental',
                    'color_depth' => 'expressive',
                    'contrast' => 'dynamic',
                    'saturation' => 'conceptual'
                ],
                'lighting_style' => 'conceptual_dramatic',
                'composition_rules' => ['abstract_balance', 'conceptual_flow', 'emotional_weight']
            ],

            'documentary' => [
                'name' => 'Documentary',
                'description' => 'Authentic, journalistic style capturing real moments',
                'icon' => '📹',
                'color' => '#0891B2',
                'category' => 'journalistic',
                'style_elements' => [
                    'natural_moments', 'candid_shots', 'environmental_context', 'authentic_lighting',
                    'real_emotions', 'unposed_subjects', 'storytelling_focus', 'social_context',
                    'handheld_aesthetic', 'natural_framing', 'observational_style', 'truth_telling'
                ],
                'characteristics' => ['authentic', 'truthful', 'observational', 'contextual', 'meaningful'],
                'technical_specs' => [
                    'aspect_ratio' => 'standard',
                    'color_depth' => 'natural',
                    'contrast' => 'realistic',
                    'saturation' => 'true_to_life'
                ],
                'lighting_style' => 'available_natural',
                'composition_rules' => ['natural_framing', 'contextual_composition', 'storytelling_focus']
            ],

            'surreal_fantasy' => [
                'name' => 'Surreal Fantasy',
                'description' => 'Dreamlike, impossible worlds with fantastical elements',
                'icon' => '🦄',
                'color' => '#8B5CF6',
                'category' => 'fantastical',
                'style_elements' => [
                    'impossible_geometry', 'dreamlike_logic', 'fantastical_creatures', 'magical_elements',
                    'surreal_landscapes', 'floating_objects', 'morphing_forms', 'ethereal_lighting',
                    'impossible_physics', 'dream_sequences', 'mythical_beings', 'otherworldly_colors'
                ],
                'characteristics' => ['surreal', 'fantastical', 'dreamlike', 'impossible', 'magical'],
                'technical_specs' => [
                    'aspect_ratio' => 'fantastical',
                    'color_depth' => 'otherworldly',
                    'contrast' => 'magical',
                    'saturation' => 'enhanced_fantasy'
                ],
                'lighting_style' => 'magical_ethereal',
                'composition_rules' => ['dream_logic', 'fantastical_balance', 'surreal_perspective']
            ],

            'cyberpunk_futuristic' => [
                'name' => 'Cyberpunk Futuristic',
                'description' => 'High-tech dystopian future with neon aesthetics',
                'icon' => '🤖',
                'color' => '#06B6D4',
                'category' => 'futuristic',
                'style_elements' => [
                    'neon_lighting', 'cybernetic_elements', 'holographic_displays', 'urban_decay',
                    'high_tech_low_life', 'digital_interfaces', 'augmented_reality', 'dystopian_atmosphere',
                    'chrome_surfaces', 'data_streams', 'neural_networks', 'synthetic_materials'
                ],
                'characteristics' => ['futuristic', 'technological', 'dystopian', 'synthetic', 'digital'],
                'technical_specs' => [
                    'aspect_ratio' => 'widescreen',
                    'color_depth' => 'neon_enhanced',
                    'contrast' => 'high_tech',
                    'saturation' => 'cyberpunk'
                ],
                'lighting_style' => 'neon_dramatic',
                'composition_rules' => ['tech_geometry', 'digital_framing', 'cyberpunk_perspective']
            ],

            'noir_dramatic' => [
                'name' => 'Noir Dramatic',
                'description' => 'High contrast black and white with dramatic shadows',
                'icon' => '🎭',
                'color' => '#374151',
                'category' => 'dramatic',
                'style_elements' => [
                    'high_contrast', 'dramatic_shadows', 'chiaroscuro_lighting', 'film_noir_aesthetic',
                    'venetian_blinds', 'smoke_atmosphere', 'urban_nighttime', 'mysterious_figures',
                    'stark_lighting', 'shadow_play', 'monochromatic', 'dramatic_angles'
                ],
                'characteristics' => ['dramatic', 'mysterious', 'high_contrast', 'atmospheric', 'moody'],
                'technical_specs' => [
                    'aspect_ratio' => 'classic',
                    'color_depth' => 'monochromatic',
                    'contrast' => 'extreme',
                    'saturation' => 'desaturated'
                ],
                'lighting_style' => 'dramatic_chiaroscuro',
                'composition_rules' => ['dramatic_angles', 'shadow_composition', 'noir_framing']
            ]
        ];
    }

    /**
     * Initialize inspiration sources
     */
    private function initializeInspirationSources() {
        $this->inspirationSources = [
            'movies' => ['cinematic', 'dramatic', 'storytelling', 'visual_narrative'],
            'art' => ['artistic', 'creative', 'expressive', 'aesthetic'],
            'nature' => ['organic', 'natural', 'flowing', 'harmonious'],
            'technology' => ['futuristic', 'digital', 'precise', 'innovative'],
            'music' => ['rhythmic', 'emotional', 'dynamic', 'atmospheric'],
            'literature' => ['narrative', 'descriptive', 'imaginative', 'poetic']
        ];
    }

    /**
     * Initialize prompt templates
     */
    private function initializePromptTemplates() {
        $this->promptTemplates = [
            'mood_focused' => [
                "Create a {mood_name} scene featuring {topic.name} with {image_type.name} style. Use {mood_elements.colors} color palette with {mood_elements.lighting} lighting. The composition should be {mood_elements.composition} with {mood_elements.movement} movement. Inspired by {inspiration}.",

                "A {mood_description} visual narrative about {topic.name}. Shot in {image_type.name} style with {mood_elements.lighting} lighting and {mood_elements.colors} colors. The scene should convey {mood_name} emotions through {mood_elements.composition} composition. Drawing inspiration from {inspiration}.",

                "Capture the essence of {mood_name} in a {image_type.name} representation of {topic.name}. Use {mood_elements.colors} tones with {mood_elements.lighting} lighting to create {mood_elements.movement} energy. The overall mood should be {mood_description}, inspired by {inspiration}.",

                "{mood_name} atmosphere surrounding {topic.name} theme. Rendered in {image_type.name} style with {mood_elements.composition} framing and {mood_elements.lighting} illumination. Color scheme: {mood_elements.colors}. Movement style: {mood_elements.movement}. Inspiration: {inspiration}."
            ],

            'topic_focused' => [
                "Explore {topic.name} through the lens of {subtopic}. Create a {mood_name} {image_type.name} scene that captures the essence of {topic.description}. Incorporate {inspiration} elements with atmospheric {mood_elements.visual_style.lighting} lighting.",

                "A deep dive into {subtopic} within the {topic.name} category. Shot with {image_type.name} aesthetics and {mood_name} emotional tone. The scene should showcase {topic.description} while drawing inspiration from {inspiration}.",

                "{topic.name} story focusing on {subtopic}. Visualized through {image_type.name} cinematography with {mood_name} atmosphere. Use {mood_elements.visual_style.colors} color grading and {inspiration}-inspired elements.",

                "Documentary-style exploration of {subtopic} in {topic.name} context. {image_type.name} visual approach with {mood_name} emotional undertones. Lighting: {mood_elements.visual_style.lighting}. Inspired by {inspiration}."
            ],

            'image_focused' => [
                "{image_type.name} masterpiece featuring {topic.name} with {style_element} emphasis. Mood: {mood_name}. The visual should showcase {image_type.description} while incorporating {inspiration} influences.",

                "Stunning {image_type.name} composition of {topic.name} highlighting {style_element}. Emotional tone: {mood_name}. Technical approach: {image_type.characteristics}. Inspiration drawn from {inspiration}.",

                "Professional {image_type.name} shot of {topic.name} with focus on {style_element}. Atmosphere: {mood_name}. Style characteristics: {image_type.characteristics}. Creative influence: {inspiration}.",

                "{image_type.description} visualization of {topic.name} emphasizing {style_element}. Mood setting: {mood_name}. Visual style: {image_type.characteristics}. Artistic inspiration: {inspiration}."
            ],

            'inspiration_focused' => [
                "Inspired by {inspiration}, create a {mood_name} {image_type.name} scene of {topic.name}. Key elements: {inspiration_keywords}. Mood characteristics: {mood_elements.content_style.emotional_intensity} emotional intensity.",

                "Drawing from {inspiration} influences, visualize {topic.name} in {image_type.name} style. Emotional tone: {mood_name}. Incorporate these elements: {inspiration_keywords}.",

                "{inspiration}-inspired interpretation of {topic.name}. Rendered as {image_type.name} with {mood_name} atmosphere. Focus on: {inspiration_keywords}. Emotional depth: {mood_elements.content_style.emotional_intensity}.",

                "Creative fusion of {inspiration} and {topic.name} themes. {image_type.name} visual style with {mood_name} emotional core. Highlight: {inspiration_keywords}."
            ]
        ];
    }

    /**
     * Get random template by type
     */
    private function getRandomTemplate($type) {
        $templates = $this->promptTemplates[$type] ?? [];
        return $templates[array_rand($templates)] ?? '';
    }

    /**
     * Fill template with data
     */
    private function fillTemplate($template, $data) {
        $content = $template;

        // Replace simple placeholders
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                $content = str_replace('{' . $key . '}', $value, $content);
            } elseif (is_array($value)) {
                // Handle array data
                foreach ($value as $subKey => $subValue) {
                    if (is_string($subValue)) {
                        $content = str_replace('{' . $key . '.' . $subKey . '}', $subValue, $content);
                    } elseif (is_array($subValue)) {
                        // Handle nested arrays
                        foreach ($subValue as $nestedKey => $nestedValue) {
                            if (is_string($nestedValue)) {
                                $content = str_replace('{' . $key . '.' . $subKey . '.' . $nestedKey . '}', $nestedValue, $content);
                            }
                        }
                    }
                }
            }
        }

        return $content;
    }

    /**
     * Format color palette for prompts
     */
    private function formatColorPalette($colors) {
        if (is_array($colors)) {
            return implode(', ', array_map(function($color) {
                return str_replace('_', ' ', $color);
            }, $colors));
        }
        return str_replace('_', ' ', $colors);
    }

    /**
     * Extract keywords from inspiration text
     */
    private function extractKeywords($inspiration) {
        // Simple keyword extraction
        $words = explode(' ', strtolower($inspiration));
        $keywords = array_filter($words, function($word) {
            return strlen($word) > 3 && !in_array($word, ['the', 'and', 'with', 'from', 'that', 'this', 'have', 'will', 'been', 'were']);
        });

        return array_slice(array_unique($keywords), 0, 5);
    }

    /**
     * Get topic categories for UI
     */
    public function getTopicCategories() {
        return $this->topicCategories;
    }

    /**
     * Get image types for UI
     */
    public function getImageTypes() {
        return $this->imageTypes;
    }

    /**
     * Get prompt statistics
     */
    public function getPromptStats($prompts) {
        $stats = [
            'total_prompts' => count($prompts),
            'strategies' => [],
            'moods' => [],
            'topics' => [],
            'image_types' => []
        ];

        foreach ($prompts as $prompt) {
            // Count strategies
            $strategy = $prompt['strategy'];
            $stats['strategies'][$strategy] = ($stats['strategies'][$strategy] ?? 0) + 1;

            // Count moods
            $mood = $prompt['mood'];
            $stats['moods'][$mood] = ($stats['moods'][$mood] ?? 0) + 1;

            // Count topics
            $topic = $prompt['topic'];
            $stats['topics'][$topic] = ($stats['topics'][$topic] ?? 0) + 1;

            // Count image types
            $imageType = $prompt['image_type'];
            $stats['image_types'][$imageType] = ($stats['image_types'][$imageType] ?? 0) + 1;
        }

        return $stats;
    }

    /**
     * Validate prompt generation parameters
     */
    public function validateParameters($moodId, $topicCategory, $imageType, $inspiration) {
        $errors = [];

        if (!$this->moodDatabase->getMood($moodId)) {
            $errors[] = "Invalid mood ID: {$moodId}";
        }

        if (!isset($this->topicCategories[$topicCategory])) {
            $errors[] = "Invalid topic category: {$topicCategory}";
        }

        if (!isset($this->imageTypes[$imageType])) {
            $errors[] = "Invalid image type: {$imageType}";
        }

        if (empty(trim($inspiration))) {
            $errors[] = "Inspiration cannot be empty";
        }

        return $errors;
    }
}
