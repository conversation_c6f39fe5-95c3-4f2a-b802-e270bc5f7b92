<?php
/**
 * File Download Handler for Sutradhar Engine
 * Serves generated files from the output directory
 */

// Disable error display for clean file serving
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Get the requested file path
$filePath = $_GET['file'] ?? '';

if (empty($filePath)) {
    http_response_code(400);
    die('File path required');
}

// Security: Prevent directory traversal
$filePath = str_replace(['../', '..\\', '../', '..\\'], '', $filePath);

// Build full path
$fullPath = __DIR__ . '/../' . $filePath;

// Check if file exists
if (!file_exists($fullPath)) {
    http_response_code(404);
    die('File not found');
}

// Check if file is in allowed directory
$allowedPaths = [
    realpath(__DIR__ . '/../data/output_history/'),
    realpath(__DIR__ . '/../assets/'),
];

$realFilePath = realpath($fullPath);
$isAllowed = false;

foreach ($allowedPaths as $allowedPath) {
    if ($allowedPath && strpos($realFilePath, $allowedPath) === 0) {
        $isAllowed = true;
        break;
    }
}

if (!$isAllowed) {
    http_response_code(403);
    die('Access denied');
}

// Get file info
$filename = basename($fullPath);
$filesize = filesize($fullPath);
$extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

// Set appropriate MIME type
$mimeTypes = [
    'mp3' => 'audio/mpeg',
    'wav' => 'audio/wav',
    'mp4' => 'video/mp4',
    'avi' => 'video/x-msvideo',
    'srt' => 'text/plain',
    'txt' => 'text/plain',
    'json' => 'application/json',
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'gif' => 'image/gif'
];

$mimeType = $mimeTypes[$extension] ?? 'application/octet-stream';

// Check if this is a download request or inline view
$download = isset($_GET['download']) && $_GET['download'] === '1';

// Set headers
header('Content-Type: ' . $mimeType);
header('Content-Length: ' . $filesize);

if ($download) {
    header('Content-Disposition: attachment; filename="' . $filename . '"');
} else {
    header('Content-Disposition: inline; filename="' . $filename . '"');
}

// Set cache headers
header('Cache-Control: public, max-age=3600');
header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');

// Handle range requests for media files
if (in_array($extension, ['mp3', 'wav', 'mp4', 'avi']) && isset($_SERVER['HTTP_RANGE'])) {
    $range = $_SERVER['HTTP_RANGE'];
    $ranges = explode('=', $range);
    $offsets = explode('-', $ranges[1]);
    $offset = intval($offsets[0]);
    $length = intval($offsets[1]) ?: $filesize - 1;
    
    if ($offset > 0 || $length < $filesize - 1) {
        header('HTTP/1.1 206 Partial Content');
        header('Accept-Ranges: bytes');
        header("Content-Range: bytes $offset-$length/$filesize");
        header('Content-Length: ' . ($length - $offset + 1));
        
        $file = fopen($fullPath, 'rb');
        fseek($file, $offset);
        echo fread($file, $length - $offset + 1);
        fclose($file);
        exit;
    }
}

// Serve the file
readfile($fullPath);
exit;
?>
