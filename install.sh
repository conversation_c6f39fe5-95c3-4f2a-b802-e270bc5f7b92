#!/bin/bash

# Sutradhar Engine Installation Script
# Supports Ubuntu/Debian, macOS, and basic Windows (via WSL)

set -e

echo "🎭 Sutradhar Engine Installation Script"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Detect OS
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if command -v apt-get &> /dev/null; then
            OS="ubuntu"
        elif command -v yum &> /dev/null; then
            OS="centos"
        else
            OS="linux"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        OS="windows"
    else
        OS="unknown"
    fi
    
    print_status "Detected OS: $OS"
}

# Check if command exists
command_exists() {
    command -v "$1" &> /dev/null
}

# Install PHP
install_php() {
    print_status "Installing PHP 8.1+..."
    
    case $OS in
        "ubuntu")
            sudo apt-get update
            sudo apt-get install -y php8.1 php8.1-cli php8.1-json php8.1-mbstring php8.1-fileinfo
            ;;
        "macos")
            if command_exists brew; then
                brew install php
            else
                print_error "Homebrew not found. Please install Homebrew first."
                exit 1
            fi
            ;;
        "centos")
            sudo yum install -y php php-cli php-json php-mbstring
            ;;
        *)
            print_warning "Please install PHP 8.1+ manually for your system"
            ;;
    esac
    
    if command_exists php; then
        PHP_VERSION=$(php -v | head -n 1 | cut -d " " -f 2)
        print_success "PHP installed: $PHP_VERSION"
    else
        print_error "PHP installation failed"
        exit 1
    fi
}

# Install FFmpeg
install_ffmpeg() {
    print_status "Installing FFmpeg..."
    
    case $OS in
        "ubuntu")
            sudo apt-get install -y ffmpeg
            ;;
        "macos")
            if command_exists brew; then
                brew install ffmpeg
            else
                print_error "Homebrew not found. Please install Homebrew first."
                exit 1
            fi
            ;;
        "centos")
            # Enable EPEL repository first
            sudo yum install -y epel-release
            sudo yum install -y ffmpeg ffmpeg-devel
            ;;
        *)
            print_warning "Please install FFmpeg manually for your system"
            ;;
    esac
    
    if command_exists ffmpeg; then
        FFMPEG_VERSION=$(ffmpeg -version | head -n 1 | cut -d " " -f 3)
        print_success "FFmpeg installed: $FFMPEG_VERSION"
    else
        print_error "FFmpeg installation failed"
        exit 1
    fi
}

# Install Python and TTS
install_python_tts() {
    print_status "Installing Python and TTS engines..."
    
    # Check if Python 3.8+ is available
    if command_exists python3; then
        PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
        print_status "Python version: $PYTHON_VERSION"
        
        if python3 -c 'import sys; exit(0 if sys.version_info >= (3, 8) else 1)'; then
            print_success "Python 3.8+ is available"
        else
            print_error "Python 3.8+ required. Please upgrade Python."
            exit 1
        fi
    else
        print_status "Installing Python..."
        case $OS in
            "ubuntu")
                sudo apt-get install -y python3 python3-pip
                ;;
            "macos")
                if command_exists brew; then
                    brew install python
                else
                    print_error "Homebrew not found. Please install Python manually."
                    exit 1
                fi
                ;;
            "centos")
                sudo yum install -y python3 python3-pip
                ;;
            *)
                print_warning "Please install Python 3.8+ manually"
                return
                ;;
        esac
    fi
    
    # Install TTS engines
    print_status "Installing TTS engines..."
    
    echo "Choose TTS engine to install:"
    echo "1) Bark TTS (Recommended - High quality)"
    echo "2) Coqui TTS (Good quality, faster)"
    echo "3) eSpeak (Basic quality, very fast)"
    echo "4) All of the above"
    
    read -p "Enter your choice (1-4): " tts_choice
    
    case $tts_choice in
        1)
            pip3 install bark-tts
            print_success "Bark TTS installed"
            ;;
        2)
            pip3 install coqui-tts
            print_success "Coqui TTS installed"
            ;;
        3)
            case $OS in
                "ubuntu")
                    sudo apt-get install -y espeak espeak-data
                    ;;
                "macos")
                    brew install espeak
                    ;;
                "centos")
                    sudo yum install -y espeak espeak-devel
                    ;;
            esac
            print_success "eSpeak installed"
            ;;
        4)
            pip3 install bark-tts coqui-tts
            case $OS in
                "ubuntu")
                    sudo apt-get install -y espeak espeak-data
                    ;;
                "macos")
                    brew install espeak
                    ;;
                "centos")
                    sudo yum install -y espeak espeak-devel
                    ;;
            esac
            print_success "All TTS engines installed"
            ;;
        *)
            print_warning "Invalid choice. Skipping TTS installation."
            ;;
    esac
}

# Set up directories and permissions
setup_directories() {
    print_status "Setting up directories and permissions..."
    
    # Create necessary directories
    mkdir -p temp data/jobs data/output_history assets/voices assets/audio/music assets/audio/effects assets/backgrounds
    
    # Set permissions
    chmod -R 755 .
    chmod -R 777 temp data assets
    
    print_success "Directories created and permissions set"
}

# Create sample configuration
create_sample_config() {
    print_status "Creating sample configuration..."
    
    # Check which TTS engine is available and set as default
    TTS_ENGINE="espeak"  # fallback
    
    if command_exists python3; then
        if python3 -c "import bark" 2>/dev/null; then
            TTS_ENGINE="bark"
        elif python3 -c "import TTS" 2>/dev/null; then
            TTS_ENGINE="coqui"
        fi
    fi
    
    # Update config with detected TTS engine
    if [ -f "config/settings.json" ]; then
        # Use sed to update the engine setting
        sed -i.bak "s/\"engine\": \".*\"/\"engine\": \"$TTS_ENGINE\"/" config/settings.json
        print_success "Configuration updated with TTS engine: $TTS_ENGINE"
    fi
}

# Download sample assets
download_sample_assets() {
    print_status "Setting up sample assets..."
    
    # Create placeholder audio files
    if command_exists ffmpeg; then
        # Create sample background music (1 minute of silence)
        ffmpeg -f lavfi -i "anullsrc=channel_layout=stereo:sample_rate=44100" -t 60 assets/audio/music/background.mp3 -y 2>/dev/null
        
        # Create sample sound effects
        ffmpeg -f lavfi -i "sine=frequency=440:duration=0.5" assets/audio/effects/ding.mp3 -y 2>/dev/null
        ffmpeg -f lavfi -i "sine=frequency=220:duration=0.3" assets/audio/effects/pop.mp3 -y 2>/dev/null
        
        print_success "Sample audio assets created"
    fi
    
    # Create sample background images
    if command_exists convert; then
        convert -size 720x1280 xc:lightblue assets/backgrounds/home.jpg 2>/dev/null || true
        convert -size 720x1280 xc:lightgreen assets/backgrounds/nature.jpg 2>/dev/null || true
    fi
}

# Test installation
test_installation() {
    print_status "Testing installation..."
    
    # Test PHP
    if ! command_exists php; then
        print_error "PHP not found in PATH"
        return 1
    fi
    
    # Test FFmpeg
    if ! command_exists ffmpeg; then
        print_error "FFmpeg not found in PATH"
        return 1
    fi
    
    # Test web server
    print_status "Starting test server..."
    php -S localhost:8000 -t public &
    SERVER_PID=$!
    
    sleep 2
    
    # Test if server is responding
    if curl -s http://localhost:8000 > /dev/null; then
        print_success "Web server is working!"
        print_success "You can access Sutradhar Engine at: http://localhost:8000"
    else
        print_warning "Web server test failed, but installation completed"
    fi
    
    # Stop test server
    kill $SERVER_PID 2>/dev/null || true
    
    print_success "Installation completed successfully!"
}

# Main installation process
main() {
    echo
    print_status "Starting Sutradhar Engine installation..."
    echo
    
    detect_os
    
    # Check if running as root (not recommended)
    if [ "$EUID" -eq 0 ]; then
        print_warning "Running as root is not recommended for development"
        read -p "Continue anyway? (y/N): " continue_root
        if [[ ! $continue_root =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    # Install dependencies
    if ! command_exists php; then
        install_php
    else
        print_success "PHP already installed"
    fi
    
    if ! command_exists ffmpeg; then
        install_ffmpeg
    else
        print_success "FFmpeg already installed"
    fi
    
    install_python_tts
    setup_directories
    create_sample_config
    download_sample_assets
    test_installation
    
    echo
    print_success "🎉 Sutradhar Engine installation completed!"
    echo
    print_status "Next steps:"
    echo "1. Start the server: php -S localhost:8000 -t public"
    echo "2. Open http://localhost:8000 in your browser"
    echo "3. Upload voice models to assets/voices/ directory"
    echo "4. Add background music to assets/audio/music/"
    echo "5. Check README.md for detailed usage instructions"
    echo
}

# Run main function
main "$@"
