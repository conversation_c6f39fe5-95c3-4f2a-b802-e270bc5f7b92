<?php
/**
 * Linux-Compatible TTS Engine
 * Supports multiple TTS engines available on Ubuntu/Linux systems
 */

class LinuxTTSEngine {
    private $config;
    private $tempDir;
    private $availableEngines = [];
    
    public function __construct() {
        $this->loadConfig();
        $this->tempDir = __DIR__ . '/../temp/';
        $this->detectAvailableEngines();
        
        if (!is_dir($this->tempDir)) {
            mkdir($this->tempDir, 0755, true);
        }
    }
    
    private function loadConfig() {
        $configPath = __DIR__ . '/../config/settings.json';
        if (file_exists($configPath)) {
            $this->config = json_decode(file_get_contents($configPath), true);
        } else {
            $this->config = $this->getDefaultConfig();
        }
    }
    
    private function getDefaultConfig() {
        return [
            'tts' => [
                'preferred_engine' => 'espeak',
                'voice_speed' => 150,
                'voice_pitch' => 50,
                'voice_volume' => 100,
                'sample_rate' => 22050,
                'output_format' => 'wav'
            ],
            'voices' => [
                'female' => [
                    'espeak' => 'en+f3',
                    'festival' => 'voice_kal_diphone',
                    'pico' => 'en-US'
                ],
                'male' => [
                    'espeak' => 'en+m3',
                    'festival' => 'voice_kal_diphone',
                    'pico' => 'en-US'
                ]
            ]
        ];
    }
    
    private function detectAvailableEngines() {
        $engines = [
            'espeak' => 'espeak --version',
            'festival' => 'festival --version',
            'pico2wave' => 'pico2wave --help',
            'flite' => 'flite --version',
            'mimic' => 'mimic --version'
        ];
        
        foreach ($engines as $engine => $testCommand) {
            exec($testCommand . ' 2>&1', $output, $returnCode);
            if ($returnCode === 0 || ($engine === 'pico2wave' && $returnCode === 1)) {
                $this->availableEngines[] = $engine;
                echo "✅ Detected TTS engine: $engine\n";
            }
        }
        
        if (empty($this->availableEngines)) {
            echo "⚠️  No TTS engines detected. Installing fallback...\n";
            $this->installFallbackTTS();
        }
    }
    
    public function generateSpeech($text, $voiceType = 'female', $outputFile = null) {
        if (empty($this->availableEngines)) {
            return $this->generateFallbackAudio($text, $outputFile);
        }
        
        if (!$outputFile) {
            $outputFile = $this->tempDir . 'tts_' . uniqid() . '.wav';
        }
        
        $success = false;
        
        // Try each available engine in order of preference
        $preferredOrder = ['espeak', 'festival', 'pico2wave', 'flite', 'mimic'];
        
        foreach ($preferredOrder as $engine) {
            if (in_array($engine, $this->availableEngines)) {
                echo "🎤 Trying TTS engine: $engine\n";
                $success = $this->generateWithEngine($engine, $text, $voiceType, $outputFile);
                
                if ($success && file_exists($outputFile) && filesize($outputFile) > 1000) {
                    echo "✅ TTS successful with $engine: " . round(filesize($outputFile)/1024, 1) . " KB\n";
                    break;
                } else {
                    echo "❌ TTS failed with $engine\n";
                }
            }
        }
        
        // Fallback to synthetic audio if all TTS engines fail
        if (!$success || !file_exists($outputFile) || filesize($outputFile) < 1000) {
            echo "🔧 Using fallback audio generation...\n";
            $success = $this->generateFallbackAudio($text, $outputFile);
        }
        
        return $success ? $outputFile : false;
    }
    
    private function generateWithEngine($engine, $text, $voiceType, $outputFile) {
        $text = $this->sanitizeText($text);
        
        switch ($engine) {
            case 'espeak':
                return $this->generateWithEspeak($text, $voiceType, $outputFile);
            case 'festival':
                return $this->generateWithFestival($text, $voiceType, $outputFile);
            case 'pico2wave':
                return $this->generateWithPico($text, $voiceType, $outputFile);
            case 'flite':
                return $this->generateWithFlite($text, $voiceType, $outputFile);
            case 'mimic':
                return $this->generateWithMimic($text, $voiceType, $outputFile);
            default:
                return false;
        }
    }
    
    private function generateWithEspeak($text, $voiceType, $outputFile) {
        $voice = $this->config['voices'][$voiceType]['espeak'] ?? 'en+f3';
        $speed = $this->config['tts']['voice_speed'] ?? 150;
        $pitch = $this->config['tts']['voice_pitch'] ?? 50;
        
        $cmd = sprintf(
            'espeak -v "%s" -s %d -p %d -w "%s" "%s" 2>&1',
            $voice,
            $speed,
            $pitch,
            $outputFile,
            escapeshellarg($text)
        );
        
        exec($cmd, $output, $returnCode);
        return $returnCode === 0;
    }
    
    private function generateWithFestival($text, $voiceType, $outputFile) {
        $tempTextFile = $this->tempDir . 'festival_text_' . uniqid() . '.txt';
        file_put_contents($tempTextFile, $text);
        
        $cmd = sprintf(
            'festival --tts "%s" --otype wav > "%s" 2>&1',
            $tempTextFile,
            $outputFile
        );
        
        exec($cmd, $output, $returnCode);
        
        if (file_exists($tempTextFile)) {
            unlink($tempTextFile);
        }
        
        return file_exists($outputFile) && filesize($outputFile) > 1000;
    }
    
    private function generateWithPico($text, $voiceType, $outputFile) {
        $lang = $this->config['voices'][$voiceType]['pico'] ?? 'en-US';
        
        $cmd = sprintf(
            'pico2wave -l "%s" -w "%s" "%s" 2>&1',
            $lang,
            $outputFile,
            escapeshellarg($text)
        );
        
        exec($cmd, $output, $returnCode);
        return $returnCode === 0;
    }
    
    private function generateWithFlite($text, $voiceType, $outputFile) {
        $voice = $voiceType === 'female' ? 'slt' : 'awb';
        
        $cmd = sprintf(
            'flite -voice "%s" -o "%s" -t "%s" 2>&1',
            $voice,
            $outputFile,
            escapeshellarg($text)
        );
        
        exec($cmd, $output, $returnCode);
        return $returnCode === 0;
    }
    
    private function generateWithMimic($text, $voiceType, $outputFile) {
        $voice = $voiceType === 'female' ? 'slt' : 'awb';
        
        $cmd = sprintf(
            'mimic -voice "%s" -o "%s" -t "%s" 2>&1',
            $voice,
            $outputFile,
            escapeshellarg($text)
        );
        
        exec($cmd, $output, $returnCode);
        return $returnCode === 0;
    }
    
    private function generateFallbackAudio($text, $outputFile) {
        // Generate synthetic audio based on text length and content
        $duration = max(2, strlen($text) * 0.1); // Estimate duration
        $sampleRate = $this->config['tts']['sample_rate'] ?? 22050;
        
        // Create a more sophisticated audio pattern based on text
        $samples = [];
        $words = explode(' ', $text);
        $timePerWord = $duration / count($words);
        
        for ($wordIndex = 0; $wordIndex < count($words); $wordIndex++) {
            $word = $words[$wordIndex];
            $wordDuration = $timePerWord;
            $samplesPerWord = intval($wordDuration * $sampleRate);
            
            // Generate different frequencies for different characters
            for ($i = 0; $i < $samplesPerWord; $i++) {
                $t = $i / $sampleRate;
                $frequency = $this->getFrequencyForWord($word, $wordIndex);
                
                // Add some variation and envelope
                $envelope = sin(pi() * $i / $samplesPerWord); // Word envelope
                $sample = sin(2 * pi() * $frequency * $t) * $envelope * 0.3;
                
                // Add harmonics for more natural sound
                $sample += sin(2 * pi() * $frequency * 2 * $t) * $envelope * 0.1;
                $sample += sin(2 * pi() * $frequency * 3 * $t) * $envelope * 0.05;
                
                $samples[] = intval($sample * 32767);
            }
            
            // Add pause between words
            $pauseSamples = intval(0.1 * $sampleRate);
            for ($i = 0; $i < $pauseSamples; $i++) {
                $samples[] = 0;
            }
        }
        
        return $this->createWAVFile($samples, $outputFile, $sampleRate);
    }
    
    private function getFrequencyForWord($word, $index) {
        // Generate frequency based on word characteristics
        $baseFreq = 200; // Base frequency for speech
        $wordHash = crc32($word);
        $variation = ($wordHash % 100) - 50; // -50 to +50 Hz variation
        
        // Add pattern based on word position
        $positionVariation = sin($index * 0.5) * 30;
        
        return $baseFreq + $variation + $positionVariation;
    }
    
    private function createWAVFile($samples, $outputFile, $sampleRate) {
        // Create WAV header
        $header = pack('V', 0x46464952); // "RIFF"
        $header .= pack('V', 36 + count($samples) * 2); // File size
        $header .= pack('V', 0x45564157); // "WAVE"
        $header .= pack('V', 0x20746d66); // "fmt "
        $header .= pack('V', 16); // Subchunk1Size
        $header .= pack('v', 1); // AudioFormat (PCM)
        $header .= pack('v', 1); // NumChannels (mono)
        $header .= pack('V', $sampleRate); // SampleRate
        $header .= pack('V', $sampleRate * 2); // ByteRate
        $header .= pack('v', 2); // BlockAlign
        $header .= pack('v', 16); // BitsPerSample
        $header .= pack('V', 0x61746164); // "data"
        $header .= pack('V', count($samples) * 2); // Subchunk2Size
        
        // Create audio data
        $audioData = '';
        foreach ($samples as $sample) {
            $audioData .= pack('v', $sample);
        }
        
        $result = file_put_contents($outputFile, $header . $audioData);
        return $result !== false;
    }
    
    private function sanitizeText($text) {
        // Remove or replace problematic characters
        $text = preg_replace('/[^\w\s\.,!?;:-]/', '', $text);
        $text = preg_replace('/\s+/', ' ', $text);
        return trim($text);
    }
    
    private function installFallbackTTS() {
        // Attempt to install espeak as fallback
        echo "🔧 Attempting to install espeak...\n";
        exec('which apt-get 2>&1', $output, $returnCode);
        
        if ($returnCode === 0) {
            exec('sudo apt-get update && sudo apt-get install -y espeak espeak-data 2>&1', $output, $returnCode);
            if ($returnCode === 0) {
                $this->availableEngines[] = 'espeak';
                echo "✅ espeak installed successfully\n";
            }
        }
    }
    
    public function getAvailableEngines() {
        return $this->availableEngines;
    }
    
    public function getVoiceOptions() {
        $options = [];
        
        foreach ($this->availableEngines as $engine) {
            switch ($engine) {
                case 'espeak':
                    $options[$engine] = [
                        'female' => ['en+f1', 'en+f2', 'en+f3', 'en+f4'],
                        'male' => ['en+m1', 'en+m2', 'en+m3', 'en+m4']
                    ];
                    break;
                case 'festival':
                    $options[$engine] = [
                        'female' => ['voice_kal_diphone'],
                        'male' => ['voice_kal_diphone']
                    ];
                    break;
                case 'flite':
                    $options[$engine] = [
                        'female' => ['slt', 'awb'],
                        'male' => ['awb', 'rms']
                    ];
                    break;
            }
        }
        
        return $options;
    }
    
    public function testTTS() {
        $testText = "Hello, this is a test of the Linux TTS engine.";
        $testFile = $this->tempDir . 'tts_test.wav';
        
        $result = $this->generateSpeech($testText, 'female', $testFile);
        
        if ($result && file_exists($testFile)) {
            $fileSize = filesize($testFile);
            unlink($testFile);
            
            return [
                'success' => true,
                'message' => 'TTS test successful',
                'file_size' => $fileSize,
                'engines' => $this->availableEngines
            ];
        } else {
            return [
                'success' => false,
                'message' => 'TTS test failed',
                'engines' => $this->availableEngines
            ];
        }
    }

    /**
     * Generate multiple voice segments
     */
    public function generateVoiceSegments($segments, $voiceType = 'female', $jobId = null) {
        $voiceFiles = [];

        echo "🎤 Generating " . count($segments) . " voice segments with Linux TTS...\n";

        foreach ($segments as $index => $segment) {
            $text = $segment['text'] ?? $segment;
            if (empty($text)) continue;

            echo "  Segment $index: " . substr($text, 0, 50) . "...\n";

            $outputFile = $this->tempDir . ($jobId ?? 'segment') . "_voice_segment_$index.wav";

            $success = $this->generateSpeech($text, $voiceType, $outputFile);

            if ($success && file_exists($outputFile)) {
                $voiceFiles[] = [
                    'file' => $outputFile,
                    'duration' => $this->getAudioDuration($outputFile),
                    'segment' => $index,
                    'text' => $text
                ];
                echo "    ✅ Generated: " . round(filesize($outputFile)/1024, 1) . " KB\n";
            } else {
                echo "    ❌ Failed to generate segment $index\n";
            }
        }

        echo "✅ Generated " . count($voiceFiles) . " voice segments\n";
        return $voiceFiles;
    }

    /**
     * Get audio duration
     */
    private function getAudioDuration($audioFile) {
        if (!file_exists($audioFile)) {
            return 0;
        }

        // Try FFprobe first
        $cmd = "ffprobe -v quiet -show_entries format=duration -of csv=p=0 \"$audioFile\" 2>&1";
        $duration = trim(shell_exec($cmd));

        if (is_numeric($duration) && $duration > 0) {
            return floatval($duration);
        }

        // Fallback: estimate based on file size
        $fileSize = filesize($audioFile);
        $sampleRate = $this->config['tts']['sample_rate'] ?? 22050;
        $channels = 1;
        $bitsPerSample = 16;

        $dataSize = $fileSize - 44; // Subtract WAV header size
        $bytesPerSecond = $sampleRate * $channels * ($bitsPerSample / 8);

        return max(0, $dataSize / $bytesPerSecond);
    }
}
?>
