<?php
/**
 * Content Generator - Creates unique, varied content using open-source models
 * Part of Sutradhar Engine
 */

class ContentGenerator {
    private $config;
    private $templates;
    private $variations;

    public function __construct() {
        $this->loadConfig();
        $this->initializeVariations();
    }

    private function loadConfig() {
        $configPath = __DIR__ . '/../config/settings.json';
        $this->config = json_decode(file_get_contents($configPath), true);
    }

    private function initializeVariations() {
        $this->variations = [
            'characters' => [
                'animals' => ['crow', 'fox', 'elephant', 'mouse', 'lion', 'rabbit', 'tortoise', 'hare', 'ant', 'grasshopper'],
                'people' => ['farmer', 'merchant', 'king', 'wise_man', 'child', 'grandmother', 'teacher', 'student'],
                'mythical' => ['sage', 'demon', 'god', 'fairy', 'spirit', 'genie']
            ],
            'settings' => [
                'natural' => ['forest', 'river', 'mountain', 'village', 'garden', 'field', 'cave', 'tree'],
                'urban' => ['city', 'market', 'palace', 'school', 'temple', 'house', 'street', 'shop'],
                'mystical' => ['magical_forest', 'enchanted_lake', 'sacred_grove', 'ancient_temple', 'hidden_valley']
            ],
            'morals' => [
                'wisdom' => ['think_before_acting', 'patience_pays', 'honesty_best_policy', 'hard_work_succeeds'],
                'relationships' => ['help_others', 'trust_carefully', 'friendship_valuable', 'respect_elders'],
                'character' => ['be_humble', 'stay_determined', 'learn_from_mistakes', 'practice_kindness']
            ],
            'emotions' => ['happy', 'sad', 'excited', 'worried', 'confident', 'curious', 'angry', 'peaceful'],
            'actions' => ['discovered', 'learned', 'helped', 'saved', 'taught', 'warned', 'guided', 'protected']
        ];
    }

    /**
     * Generate unique content based on user input and style
     */
    public function generateUniqueContent($baseContent, $flowType, $style, $voicePack) {
        // Analyze the base content
        $contentAnalysis = $this->analyzeContent($baseContent);
        
        // Generate variations based on flow type
        switch ($flowType) {
            case 'reel':
                return $this->generateReelVariation($contentAnalysis, $style, $voicePack);
            case 'audio_story':
                return $this->generateStoryVariation($contentAnalysis, $style, $voicePack);
            case 'meme_rant':
                return $this->generateRantVariation($contentAnalysis, $style, $voicePack);
            default:
                return $this->generateGenericVariation($contentAnalysis, $style, $voicePack);
        }
    }

    /**
     * Analyze content to understand its structure and themes
     */
    private function analyzeContent($content) {
        $words = str_word_count(strtolower($content), 1);
        $sentences = preg_split('/[.!?]+/', $content, -1, PREG_SPLIT_NO_EMPTY);
        
        // Detect themes and characters
        $detectedCharacters = [];
        $detectedSettings = [];
        $detectedEmotions = [];
        
        foreach ($this->variations['characters'] as $type => $characters) {
            foreach ($characters as $character) {
                if (strpos(strtolower($content), $character) !== false) {
                    $detectedCharacters[] = $character;
                }
            }
        }
        
        foreach ($this->variations['settings'] as $type => $settings) {
            foreach ($settings as $setting) {
                if (strpos(strtolower($content), $setting) !== false) {
                    $detectedSettings[] = $setting;
                }
            }
        }
        
        return [
            'word_count' => count($words),
            'sentence_count' => count($sentences),
            'sentences' => array_map('trim', $sentences),
            'characters' => $detectedCharacters,
            'settings' => $detectedSettings,
            'themes' => $this->extractThemes($content),
            'tone' => $this->detectTone($content)
        ];
    }

    /**
     * Generate reel variation with hooks and engagement
     */
    private function generateReelVariation($analysis, $style, $voicePack) {
        $hooks = $this->getStyleHooks($style);
        $ctas = $this->getCallToActions($style);
        
        // Create unique hook
        $hook = $this->randomizeText($hooks[array_rand($hooks)], $analysis);
        
        // Transform main content
        $mainContent = $this->transformContent($analysis['sentences'], $style, $voicePack);
        
        // Add call to action
        $cta = $this->randomizeText($ctas[array_rand($ctas)], $analysis);
        
        return [
            'hook' => $hook,
            'content' => $mainContent,
            'cta' => $cta,
            'total_text' => $hook . ' ' . implode(' ', $mainContent) . ' ' . $cta
        ];
    }

    /**
     * Generate story variation with character development
     */
    private function generateStoryVariation($analysis, $style, $voicePack) {
        // Create character variations
        $characters = $this->generateCharacterVariations($analysis['characters']);
        
        // Create setting variations
        $setting = $this->generateSettingVariation($analysis['settings']);
        
        // Transform story with variations
        $transformedStory = $this->transformStoryContent($analysis['sentences'], $characters, $setting, $style);
        
        // Add moral lesson
        $moral = $this->generateMoralLesson($analysis['themes'], $style);
        
        return [
            'introduction' => $transformedStory['intro'],
            'development' => $transformedStory['body'],
            'climax' => $transformedStory['climax'],
            'resolution' => $transformedStory['resolution'],
            'moral' => $moral,
            'total_text' => $transformedStory['intro'] . ' ' . implode(' ', $transformedStory['body']) . ' ' . $transformedStory['climax'] . ' ' . $transformedStory['resolution'] . ' ' . $moral
        ];
    }

    /**
     * Generate rant variation with cultural references
     */
    private function generateRantVariation($analysis, $style, $voicePack) {
        $rantStarters = $this->getRantStarters($style);
        $culturalRefs = $this->getCulturalReferences($style);
        $rantEnders = $this->getRantEnders($style);
        
        $starter = $rantStarters[array_rand($rantStarters)];
        $culturalRef = $culturalRefs[array_rand($culturalRefs)];
        $ender = $rantEnders[array_rand($rantEnders)];
        
        // Transform content with rant style
        $rantContent = $this->transformToRant($analysis['sentences'], $style, $voicePack);
        
        return [
            'setup' => $starter,
            'rant_body' => $rantContent,
            'cultural_reference' => $culturalRef,
            'punchline' => $ender,
            'total_text' => $starter . ' ' . implode(' ', $rantContent) . ' ' . $culturalRef . ' ' . $ender
        ];
    }

    /**
     * Get style-specific hooks
     */
    private function getStyleHooks($style) {
        $hooks = [
            'funny' => [
                "Arre yaar, {random_situation} dekha hai kabhi?",
                "Guys, {random_situation} ka scene dekho!",
                "Wait wait wait... {random_situation} seriously?",
                "Bhai log, ye {random_situation} kya hai?"
            ],
            'desi' => [
                "Hamare yahan {cultural_element} ka tradition hai...",
                "Indian families mein {situation} hota hai na...",
                "Bachpan se suna hai {wisdom_element}...",
                "Desi households mein {common_scenario}..."
            ],
            'emotional' => [
                "Life mein kabhi kabhi {emotional_moment} aata hai...",
                "Jab main chota tha, {memory_element}...",
                "Dil se kehna chahta hun {heartfelt_message}...",
                "Zindagi ne sikhaya hai {life_lesson}..."
            ],
            'bollywood' => [
                "Just like {movie_reference} mein...",
                "Bollywood style mein kehun toh {dramatic_statement}...",
                "{actor_name} ki tarah boldly kehta hun...",
                "Filmy dialogue yaad hai? {movie_quote}..."
            ]
        ];
        
        return $hooks[$style] ?? $hooks['funny'];
    }

    /**
     * Transform content based on style and voice pack
     */
    private function transformContent($sentences, $style, $voicePack) {
        $transformed = [];
        
        foreach ($sentences as $sentence) {
            $newSentence = $this->applyStylisticChanges($sentence, $style);
            $newSentence = $this->applyVoiceCharacteristics($newSentence, $voicePack);
            $newSentence = $this->addRandomVariations($newSentence);
            $transformed[] = $newSentence;
        }
        
        return $transformed;
    }

    /**
     * Apply stylistic changes based on selected style
     */
    private function applyStylisticChanges($sentence, $style) {
        switch ($style) {
            case 'funny':
                return $this->addHumor($sentence);
            case 'desi':
                return $this->addCulturalElements($sentence);
            case 'emotional':
                return $this->addEmotionalDepth($sentence);
            case 'bollywood':
                return $this->addBollywoodFlair($sentence);
            default:
                return $sentence;
        }
    }

    /**
     * Add humor to sentences
     */
    private function addHumor($sentence) {
        $humorElements = [
            'seriously yaar', 'obviously', 'matlab kya hai ye', 'are you kidding me',
            'classic', 'typical', 'bilkul bakwas', 'what is this behavior'
        ];
        
        $element = $humorElements[array_rand($humorElements)];
        
        // Randomly insert humor element
        if (rand(0, 1)) {
            return $element . ', ' . $sentence;
        } else {
            return $sentence . ' - ' . $element . '!';
        }
    }

    /**
     * Add cultural elements
     */
    private function addCulturalElements($sentence) {
        $culturalPhrases = [
            'hamare ghar mein', 'typical Indian family', 'desi style mein',
            'bachpan se suna hai', 'mama papa kehte the', 'traditional way'
        ];
        
        $phrase = $culturalPhrases[array_rand($culturalPhrases)];
        return $phrase . ' ' . $sentence;
    }

    /**
     * Add emotional depth
     */
    private function addEmotionalDepth($sentence) {
        $emotionalWords = [
            'dil se', 'sachchi baat', 'honestly speaking', 'from the heart',
            'deeply feel', 'truly believe', 'emotional moment'
        ];
        
        $word = $emotionalWords[array_rand($emotionalWords)];
        return $word . ', ' . $sentence;
    }

    /**
     * Add Bollywood flair
     */
    private function addBollywoodFlair($sentence) {
        $bollywoodElements = [
            'filmy style mein', 'just like SRK says', 'Bollywood mein dikhaya hai',
            'dramatic twist', 'movie scene jaisa', 'hero ki tarah'
        ];
        
        $element = $bollywoodElements[array_rand($bollywoodElements)];
        return $element . ' - ' . $sentence;
    }

    /**
     * Apply voice pack characteristics
     */
    private function applyVoiceCharacteristics($sentence, $voicePack) {
        $characteristics = [
            'babu_rao' => ['Arre', 'yaar', 'kya baat hai', 'bilkul sahi'],
            'villain' => ['Muhahaha', 'foolish', 'you cannot escape', 'my plan'],
            'dadi' => ['beta', 'bachcha', 'sun meri baat', 'samjha karo'],
            'gym_bro' => ['bro', 'pump it up', 'strong hai', 'fitness first'],
            'news_anchor' => ['breaking news', 'important update', 'viewers', 'reporting']
        ];
        
        $voiceWords = $characteristics[$voicePack] ?? $characteristics['babu_rao'];
        $randomWord = $voiceWords[array_rand($voiceWords)];
        
        return $randomWord . ', ' . $sentence;
    }

    /**
     * Add random variations to prevent repetition
     */
    private function addRandomVariations($sentence) {
        // Add random interjections
        $interjections = ['acha', 'theek hai', 'samjha', 'dekho', 'suno', 'arre'];
        
        if (rand(0, 3) === 0) { // 25% chance
            $interjection = $interjections[array_rand($interjections)];
            return $sentence . ' - ' . $interjection . '?';
        }
        
        return $sentence;
    }

    /**
     * Extract themes from content
     */
    private function extractThemes($content) {
        $themes = [];
        $themeKeywords = [
            'wisdom' => ['wise', 'learn', 'lesson', 'teach', 'knowledge'],
            'friendship' => ['friend', 'help', 'together', 'support', 'care'],
            'courage' => ['brave', 'fear', 'strong', 'fight', 'overcome'],
            'honesty' => ['truth', 'honest', 'lie', 'trust', 'faithful']
        ];
        
        foreach ($themeKeywords as $theme => $keywords) {
            foreach ($keywords as $keyword) {
                if (stripos($content, $keyword) !== false) {
                    $themes[] = $theme;
                    break;
                }
            }
        }
        
        return array_unique($themes);
    }

    /**
     * Detect tone of content
     */
    private function detectTone($content) {
        $positiveWords = ['happy', 'good', 'great', 'wonderful', 'amazing', 'beautiful'];
        $negativeWords = ['sad', 'bad', 'terrible', 'awful', 'horrible', 'ugly'];
        
        $positiveCount = 0;
        $negativeCount = 0;
        
        foreach ($positiveWords as $word) {
            $positiveCount += substr_count(strtolower($content), $word);
        }
        
        foreach ($negativeWords as $word) {
            $negativeCount += substr_count(strtolower($content), $word);
        }
        
        if ($positiveCount > $negativeCount) return 'positive';
        if ($negativeCount > $positiveCount) return 'negative';
        return 'neutral';
    }

    /**
     * Randomize text with placeholders
     */
    private function randomizeText($template, $analysis) {
        $replacements = [
            '{random_situation}' => ['traffic jam', 'family function', 'office meeting', 'shopping'],
            '{cultural_element}' => ['respect for elders', 'joint family', 'festivals', 'traditions'],
            '{emotional_moment}' => ['realization', 'breakthrough', 'understanding', 'clarity'],
            '{movie_reference}' => ['3 Idiots', 'Dangal', 'Zindagi Na Milegi Dobara', 'Queen']
        ];
        
        foreach ($replacements as $placeholder => $options) {
            if (strpos($template, $placeholder) !== false) {
                $replacement = $options[array_rand($options)];
                $template = str_replace($placeholder, $replacement, $template);
            }
        }
        
        return $template;
    }

    /**
     * Get call to actions based on style
     */
    private function getCallToActions($style) {
        $ctas = [
            'funny' => [
                "Tag that friend jo aisa karta hai!",
                "Comment mein batao tumhara experience!",
                "Share karo if you can relate!",
                "Double tap if this is you!"
            ],
            'desi' => [
                "Apne ghar mein bhi aisa hota hai?",
                "Indian families can relate!",
                "Share your desi stories!",
                "Tag your family members!"
            ],
            'emotional' => [
                "Share if this touched your heart",
                "Tag someone who needs to hear this",
                "Spread the love and positivity",
                "Let's support each other"
            ],
            'bollywood' => [
                "Which movie scene does this remind you of?",
                "Tag your Bollywood-loving friends!",
                "Share your favorite movie dialogue!",
                "Let's make this viral like a hit song!"
            ]
        ];
        
        return $ctas[$style] ?? $ctas['funny'];
    }

    /**
     * Generate character variations
     */
    private function generateCharacterVariations($originalCharacters) {
        $variations = [];
        
        foreach ($originalCharacters as $character) {
            // Find similar characters
            $similar = $this->findSimilarCharacters($character);
            $variations[$character] = $similar[array_rand($similar)];
        }
        
        return $variations;
    }

    /**
     * Find similar characters for variation
     */
    private function findSimilarCharacters($character) {
        $similarityMap = [
            'crow' => ['raven', 'magpie', 'parrot', 'eagle'],
            'fox' => ['wolf', 'jackal', 'coyote', 'dog'],
            'elephant' => ['rhino', 'hippo', 'buffalo', 'bear'],
            'mouse' => ['rat', 'hamster', 'squirrel', 'rabbit']
        ];
        
        return $similarityMap[$character] ?? [$character];
    }

    /**
     * Generate setting variation
     */
    private function generateSettingVariation($originalSettings) {
        if (empty($originalSettings)) {
            $allSettings = array_merge(...array_values($this->variations['settings']));
            return $allSettings[array_rand($allSettings)];
        }
        
        $setting = $originalSettings[0];
        $similar = $this->findSimilarSettings($setting);
        return $similar[array_rand($similar)];
    }

    /**
     * Find similar settings
     */
    private function findSimilarSettings($setting) {
        $similarityMap = [
            'forest' => ['jungle', 'woods', 'grove', 'wilderness'],
            'village' => ['town', 'hamlet', 'settlement', 'community'],
            'river' => ['stream', 'brook', 'creek', 'waterway'],
            'palace' => ['castle', 'mansion', 'fort', 'royal_court']
        ];
        
        return $similarityMap[$setting] ?? [$setting];
    }

    /**
     * Transform story content with variations
     */
    private function transformStoryContent($sentences, $characters, $setting, $style) {
        $parts = [
            'intro' => '',
            'body' => [],
            'climax' => '',
            'resolution' => ''
        ];
        
        $sentenceCount = count($sentences);
        
        if ($sentenceCount > 0) {
            $parts['intro'] = $this->applyStylisticChanges($sentences[0], $style);
        }
        
        if ($sentenceCount > 2) {
            $bodyStart = 1;
            $bodyEnd = $sentenceCount - 2;
            for ($i = $bodyStart; $i <= $bodyEnd; $i++) {
                $parts['body'][] = $this->applyStylisticChanges($sentences[$i], $style);
            }
        }
        
        if ($sentenceCount > 1) {
            $parts['climax'] = $this->applyStylisticChanges($sentences[$sentenceCount - 2], $style);
            $parts['resolution'] = $this->applyStylisticChanges($sentences[$sentenceCount - 1], $style);
        }
        
        return $parts;
    }

    /**
     * Generate moral lesson
     */
    private function generateMoralLesson($themes, $style) {
        $morals = [
            'wisdom' => "The lesson here is that wisdom comes from experience and careful thinking.",
            'friendship' => "True friendship means supporting each other through thick and thin.",
            'courage' => "Courage isn't the absence of fear, but acting despite being afraid.",
            'honesty' => "Honesty builds trust and creates lasting relationships."
        ];
        
        if (!empty($themes)) {
            $theme = $themes[0];
            $moral = $morals[$theme] ?? "Every experience teaches us something valuable.";
        } else {
            $moral = "Every experience teaches us something valuable.";
        }
        
        return $this->applyStylisticChanges($moral, $style);
    }

    /**
     * Get rant starters
     */
    private function getRantStarters($style) {
        return [
            "Yaar, ye kya scene hai?",
            "Seriously, koi samjhao mujhe...",
            "Arre bhai, dekho kya ho raha hai...",
            "Guys, attention please..."
        ];
    }

    /**
     * Get cultural references
     */
    private function getCulturalReferences($style) {
        return [
            "Typical Indian family drama!",
            "Bollywood mein bhi aisa nahi hota!",
            "Desi problems require desi solutions!",
            "This is so relatable for every Indian!"
        ];
    }

    /**
     * Get rant enders
     */
    private function getRantEnders($style) {
        return [
            "Bas yahi kehna tha!",
            "Case closed!",
            "Mic drop moment!",
            "That's the truth, deal with it!"
        ];
    }

    /**
     * Transform to rant style
     */
    private function transformToRant($sentences, $style, $voicePack) {
        $rantSentences = [];
        
        foreach ($sentences as $sentence) {
            // Make it more emphatic
            $rantSentence = $this->makeEmphatic($sentence);
            $rantSentence = $this->applyStylisticChanges($rantSentence, $style);
            $rantSentence = $this->applyVoiceCharacteristics($rantSentence, $voicePack);
            $rantSentences[] = $rantSentence;
        }
        
        return $rantSentences;
    }

    /**
     * Make sentence more emphatic for rants
     */
    private function makeEmphatic($sentence) {
        $emphaticWords = ['seriously', 'obviously', 'clearly', 'definitely', 'absolutely'];
        $word = $emphaticWords[array_rand($emphaticWords)];
        
        return $word . ', ' . $sentence . '!';
    }

    /**
     * Generate generic variation
     */
    private function generateGenericVariation($analysis, $style, $voicePack) {
        $transformed = $this->transformContent($analysis['sentences'], $style, $voicePack);
        
        return [
            'content' => $transformed,
            'total_text' => implode(' ', $transformed)
        ];
    }
}
