<?php
/**
 * Simple API Test for Sutradhar 2070
 * Tests API without database dependency
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Get request data
$input = file_get_contents('php://input');
$data = json_decode($input, true) ?: $_POST;
$endpoint = $_GET['endpoint'] ?? '';
$method = $_SERVER['REQUEST_METHOD'];

// Simple routing without database
switch ($endpoint) {
    case 'test':
        echo json_encode([
            'success' => true,
            'message' => 'API is working!',
            'timestamp' => date('Y-m-d H:i:s'),
            'method' => $method
        ]);
        break;
        
    case 'generate':
        if ($method === 'POST') {
            $jobId = 'real_' . uniqid() . '_' . time();

            // Try real video generation
            $useRealGeneration = true;
            $videoPath = null;

            if ($useRealGeneration) {
                try {
                    require_once '../core/enhanced_video_engine.php';

                    $engine = new EnhancedVideoEngine();
                    $segments = [
                        [
                            'text' => $data['custom_prompt'] ?? "Create a {$data['mood']} video about {$data['topic']}",
                            'content' => $data['custom_prompt'] ?? "Beautiful {$data['mood']} scene featuring {$data['topic']}"
                        ]
                    ];

                    // Start real generation in background
                    $videoPath = $engine->generateEnhancedVideo(
                        $segments,
                        $data['topic'] ?? 'nature_wildlife',
                        $data['mood'] ?? 'euphoric',
                        $jobId
                    );

                } catch (Exception $e) {
                    error_log("Real video generation failed: " . $e->getMessage());
                    $useRealGeneration = false;
                }
            }

            // Start session to store job
            session_start();
            $_SESSION['demo_jobs'][$jobId] = [
                'job_id' => $jobId,
                'mood' => $data['mood'] ?? 'euphoric',
                'topic' => $data['topic'] ?? 'nature_wildlife',
                'status' => 'pending',
                'progress' => 0,
                'created_at' => time(),
                'complete_time' => time() + ($useRealGeneration ? 15 : 10),
                'real_generation' => $useRealGeneration,
                'video_path' => $videoPath
            ];

            echo json_encode([
                'success' => true,
                'data' => [
                    'job_id' => $jobId,
                    'estimated_time' => $useRealGeneration ? 15 : 10,
                    'status' => 'pending',
                    'generation_type' => $useRealGeneration ? 'real' : 'demo'
                ],
                'message' => $useRealGeneration ? 'Real video generation started' : 'Demo video generation started'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'error' => 'Method not allowed'
            ]);
        }
        break;
        
    case (preg_match('/^generate\/status\/(.+)$/', $endpoint, $matches) ? true : false):
        $jobId = $matches[1];
        session_start();
        
        if (!isset($_SESSION['demo_jobs'][$jobId])) {
            echo json_encode([
                'success' => false,
                'error' => 'Job not found'
            ]);
            break;
        }
        
        $job = $_SESSION['demo_jobs'][$jobId];
        $currentTime = time();
        $elapsed = $currentTime - $job['created_at'];
        $totalTime = $job['complete_time'] - $job['created_at'];
        $progress = min(100, ($elapsed / $totalTime) * 100);
        
        if ($progress >= 100) {
            // Job completed
            $videoUrl = 'final_test_video.mp4';
            $thumbnailUrl = 'final_test_video_thumbnail.jpg';

            // Check if real video was generated
            if ($job['real_generation'] && !empty($job['video_path']) && file_exists($job['video_path'])) {
                // Convert path to URL
                $videoUrl = str_replace(__DIR__ . '/../public/', '', $job['video_path']);
                $videoUrl = str_replace('\\', '/', $videoUrl);
                $videoUrl = '/' . ltrim($videoUrl, '/');

                // Look for thumbnail
                $thumbnailPath = str_replace('.mp4', '.jpg', $job['video_path']);
                if (file_exists($thumbnailPath)) {
                    $thumbnailUrl = str_replace(__DIR__ . '/../public/', '', $thumbnailPath);
                    $thumbnailUrl = str_replace('\\', '/', $thumbnailUrl);
                    $thumbnailUrl = '/' . ltrim($thumbnailUrl, '/');
                }
            }

            echo json_encode([
                'success' => true,
                'data' => [
                    'job_id' => $jobId,
                    'status' => 'completed',
                    'progress' => 100,
                    'message' => $job['real_generation'] ? 'Real video generation completed!' : 'Demo video generation completed!',
                    'video_url' => $videoUrl,
                    'thumbnail_url' => $thumbnailUrl,
                    'processing_time' => $totalTime,
                    'generation_type' => $job['real_generation'] ? 'real' : 'demo',
                    'completed_at' => date('Y-m-d H:i:s'),
                    'created_at' => date('Y-m-d H:i:s', $job['created_at']),
                    'updated_at' => date('Y-m-d H:i:s')
                ]
            ]);
        } else {
            // Job in progress
            $stages = [
                [0, 20, 'Analyzing mood and topic...'],
                [20, 40, 'Generating AI prompts...'],
                [40, 70, 'Creating video content...'],
                [70, 90, 'Adding visual effects...'],
                [90, 100, 'Finalizing video...']
            ];
            
            $message = 'Processing...';
            foreach ($stages as $stage) {
                if ($progress >= $stage[0] && $progress < $stage[1]) {
                    $message = $stage[2];
                    break;
                }
            }
            
            echo json_encode([
                'success' => true,
                'data' => [
                    'job_id' => $jobId,
                    'status' => 'generating',
                    'progress' => round($progress),
                    'message' => $message,
                    'created_at' => date('Y-m-d H:i:s', $job['created_at']),
                    'updated_at' => date('Y-m-d H:i:s')
                ]
            ]);
        }
        break;
        
    case 'auth/status':
        echo json_encode([
            'success' => true,
            'data' => [
                'user' => null,
                'authenticated' => false
            ],
            'message' => 'Demo mode - no authentication required'
        ]);
        break;
        
    default:
        echo json_encode([
            'success' => false,
            'error' => 'Endpoint not found: ' . $endpoint,
            'available_endpoints' => [
                'test',
                'generate',
                'generate/status/{job_id}',
                'auth/status'
            ]
        ]);
}
?>
