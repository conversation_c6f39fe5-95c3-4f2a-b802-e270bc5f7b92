<?php
/**
 * Main Generation Engine - Orchestrates the entire content creation pipeline
 * Part of Sutradhar Engine
 */

// Enable error reporting but disable display for clean JSON
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Set content type
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Include required classes
require_once 'core/story_parser.php';
require_once 'core/voice_engine.php';
require_once 'core/advanced_voice_engine.php';
require_once 'core/real_tts_engine.php';
require_once 'core/content_generator.php';
require_once 'core/openai_integration.php';
require_once 'core/audio_mixer.php';
require_once 'core/video_engine.php';
require_once 'core/real_video_engine.php';
require_once 'core/windows_tts_engine.php';
require_once 'core/working_video_engine.php';
require_once 'core/huggingface_video_engine.php';
require_once 'core/female_voice_engine.php';
require_once 'core/template_engine.php';
require_once 'core/mood_database.php';
require_once 'core/prompt_generation_engine.php';
require_once 'core/optimized_video_engine.php';
require_once 'core/efficient_cache_system.php';

class SutradharGenerator {
    private $config;
    private $jobsDir;
    private $outputDir;
    private $currentJob;
    private $moodDatabase;
    private $cacheSystem;

    public function __construct() {
        $this->loadConfig();
        $this->setupDirectories();

        // Initialize mood system and caching
        $this->moodDatabase = new MoodDatabase();
        $this->cacheSystem = new EfficientCacheSystem([
            'cache_dir' => __DIR__ . '/temp/cache/',
            'max_cache_size' => 512 * 1024 * 1024, // 512MB
            'default_ttl' => 3600,
            'compression' => true
        ]);
    }

    private function loadConfig() {
        $configPath = __DIR__ . '/config/settings.json';
        $this->config = json_decode(file_get_contents($configPath), true);
        $this->jobsDir = __DIR__ . '/data/jobs/';
        $this->outputDir = __DIR__ . '/data/output_history/';
    }

    private function setupDirectories() {
        $dirs = [$this->jobsDir, $this->outputDir, __DIR__ . '/temp/'];
        foreach ($dirs as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }

    /**
     * Handle incoming requests
     */
    public function handleRequest() {
        try {
            $method = $_SERVER['REQUEST_METHOD'];
            
            if ($method === 'POST') {
                return $this->startGeneration();
            } elseif ($method === 'GET') {
                $action = $_GET['action'] ?? '';
                switch ($action) {
                    case 'status':
                        return $this->getJobStatus($_GET['job_id'] ?? '');
                    case 'download':
                        return $this->downloadFile($_GET['file'] ?? '');
                    default:
                        throw new Exception('Invalid action');
                }
            } else {
                throw new Exception('Method not allowed');
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Start content generation process
     */
    private function startGeneration() {
        // Validate input
        $this->validateInput();
        
        // Create job ID
        $jobId = uniqid('sutradhar_', true);
        
        // Create job file
        $jobData = [
            'id' => $jobId,
            'status' => 'started',
            'step' => 1,
            'message' => 'Parsing content...',
            'created_at' => date('Y-m-d H:i:s'),
            'parameters' => $this->getRequestParameters(),
            'progress' => 0
        ];
        
        $this->saveJobData($jobId, $jobData);
        
        // Start generation in background
        $this->startBackgroundGeneration($jobId);
        
        return json_encode([
            'success' => true,
            'job_id' => $jobId,
            'message' => 'Generation started'
        ]);
    }

    /**
     * Validate input parameters
     */
    private function validateInput() {
        $required = ['flow_type', 'style', 'voice_pack', 'background', 'content_source'];
        
        foreach ($required as $field) {
            if (empty($_POST[$field])) {
                throw new Exception("Missing required field: {$field}");
            }
        }

        // Validate content source
        $contentSource = $_POST['content_source'];
        if ($contentSource === 'upload' && empty($_FILES['content_file'])) {
            throw new Exception('No file uploaded');
        } elseif ($contentSource === 'text' && empty($_POST['content_text'])) {
            throw new Exception('No text content provided');
        } elseif ($contentSource === 'template' && empty($_POST['template'])) {
            throw new Exception('No template selected');
        }
    }

    /**
     * Get request parameters
     */
    private function getRequestParameters() {
        return [
            'flow_type' => $_POST['flow_type'],
            'style' => $_POST['style'],
            'voice_pack' => $_POST['voice_pack'],
            'background' => $_POST['background'],
            'content_source' => $_POST['content_source'],
            'content_file' => $_FILES['content_file']['name'] ?? null,
            'content_text' => $_POST['content_text'] ?? null,
            'template' => $_POST['template'] ?? null
        ];
    }

    /**
     * Start background generation process
     */
    private function startBackgroundGeneration($jobId) {
        // In a production environment, this would use a proper job queue
        // For now, we'll use a simple background process
        
        if (function_exists('fastcgi_finish_request')) {
            fastcgi_finish_request();
        }
        
        ignore_user_abort(true);
        set_time_limit(0);
        
        $this->performGeneration($jobId);
    }

    /**
     * Perform the actual content generation
     */
    private function performGeneration($jobId) {
        try {
            $jobData = $this->loadJobData($jobId);
            $params = $jobData['parameters'];
            
            // Step 1: Generate unique content using AI and parse
            $this->updateJobStatus($jobId, 1, 'active', 'Generating unique AI content...');
            $baseContent = $this->getContent($params);

            // Try to generate unique content using external APIs first
            $uniqueContent = null;
            if ($this->config['content_generation']['use_external_apis']) {
                try {
                    $openAI = new OpenAIIntegration();
                    $aiResult = $openAI->generateUniqueContent(
                        $baseContent,
                        $params['style'],
                        $params['flow_type'],
                        $this->config['content_generation']['max_tokens']
                    );

                    if ($aiResult && !empty($aiResult['content'])) {
                        // Use AI-generated content
                        $uniqueContent = ['total_text' => $aiResult['content']];
                        $this->updateJobStatus($jobId, 1, 'active', 'AI content generated using ' . $aiResult['provider']);
                    }
                } catch (Exception $e) {
                    error_log("AI content generation failed: " . $e->getMessage());
                }
            }

            // Fallback to local content generator
            if (!$uniqueContent) {
                $contentGenerator = new ContentGenerator();
                $uniqueContent = $contentGenerator->generateUniqueContent(
                    $baseContent,
                    $params['flow_type'],
                    $params['style'],
                    $params['voice_pack']
                );
                $this->updateJobStatus($jobId, 1, 'active', 'Local content generated');
            }

            // Parse the unique content
            $storyParser = new StoryParser();
            $parsedContent = $storyParser->parseContent(
                $uniqueContent['total_text'],
                $params['flow_type'],
                $params['style'],
                $params['voice_pack']
            );

            // Add unique content metadata
            $parsedContent['unique_content'] = $uniqueContent;
            
            // Step 2: Generate FEMALE VOICE using advanced engines
            $this->updateJobStatus($jobId, 2, 'active', 'Generating female voice with AI...');

            $voiceFiles = [];

            // Check if female voice is requested
            $useFemaleVoice = isset($_POST['female_voice']) && $_POST['female_voice'] === 'true';

            if ($useFemaleVoice) {
                // Use advanced female voice engine
                $femaleVoiceEngine = new FemaleVoiceEngine();
                $voiceFiles = $femaleVoiceEngine->generateFemaleVoiceSegments(
                    $parsedContent['segments'],
                    $params['voice_pack'],
                    $jobId
                );

                if (!empty($voiceFiles)) {
                    $this->updateJobStatus($jobId, 2, 'active', 'Female voice generation successful');
                }
            }

            // Fallback to existing TTS engines if female voice fails or not requested
            if (empty($voiceFiles)) {
                foreach ($parsedContent['segments'] as $index => $segment) {
                    $segmentFile = $this->outputDir . $jobId . "/voice_segment_$index.wav";

                    // Ensure directory exists
                    $segmentDir = dirname($segmentFile);
                    if (!is_dir($segmentDir)) {
                        mkdir($segmentDir, 0755, true);
                    }

                    $success = false;

                    // Try Windows TTS first (works on Windows without dependencies)
                    if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
                        $windowsTTS = new WindowsTTSEngine();
                        $success = $windowsTTS->generateWindowsSpeech(
                            $segment['text'],
                            $params['voice_pack'],
                            $segmentFile
                        );

                        if ($success) {
                            $this->updateJobStatus($jobId, 2, 'active', "Windows TTS successful for segment $index");
                        }
                    }

                    // Fallback to real TTS engine
                    if (!$success) {
                        $realTTS = new RealTTSEngine();
                        $success = $realTTS->generateRealSpeech(
                            $segment['text'],
                            $params['voice_pack'],
                            $segmentFile
                        );

                        if ($success) {
                            $this->updateJobStatus($jobId, 2, 'active', "Enhanced TTS successful for segment $index");
                        }
                    }

                    if ($success) {
                        $voiceFiles[] = [
                            'file' => $segmentFile,
                            'duration' => $this->getAudioDuration($segmentFile),
                            'segment' => $index
                        ];
                    } else {
                        $this->updateJobStatus($jobId, 2, 'active', "All TTS failed for segment $index, using advanced fallback");
                    }
                }

                // Final fallback to advanced voice engine if all TTS fails
                if (empty($voiceFiles)) {
                    $this->updateJobStatus($jobId, 2, 'active', 'Using advanced voice fallback...');
                    $advancedVoiceEngine = new AdvancedVoiceEngine();
                    $voiceFiles = $advancedVoiceEngine->generateAdvancedVoice(
                        $parsedContent['segments'],
                        $params['voice_pack'],
                        $jobId
                    );
                }
            }
            
            // Step 3: Mix audio
            $this->updateJobStatus($jobId, 3, 'active', 'Mixing audio...');
            $audioMixer = new AudioMixer();
            $mixedAudio = $audioMixer->mixAudio(
                $voiceFiles,
                $parsedContent['segments'],
                $params['style'],
                $params['background'],
                $jobId
            );
            
            // Step 4: Create 30-second video with Hugging Face AI
            $this->updateJobStatus($jobId, 4, 'active', 'Creating 30-second AI video...');
            $output = ['audio' => $mixedAudio];

            if ($params['flow_type'] === 'reel' || $params['flow_type'] === 'meme_rant' || $params['flow_type'] === 'story') {
                $videoFile = null;

                // Check for mood characteristics in parameters
                $moodCharacteristics = $params['mood_characteristics'] ?? null;
                $useMoodOptimization = $moodCharacteristics !== null;

                // Check if Hugging Face video generation is requested
                $useHuggingFace = isset($_POST['use_huggingface']) && $_POST['use_huggingface'] === 'true';

                // Try optimized video engine first if mood characteristics are available
                if ($useMoodOptimization) {
                    $this->updateJobStatus($jobId, 4, 'active', 'Generating mood-optimized video...');
                    $optimizedEngine = new OptimizedVideoEngine();
                    $videoFile = $optimizedEngine->generateOptimizedVideo(
                        $parsedContent['segments'],
                        $params['background'],
                        $params['style'],
                        $jobId,
                        $moodCharacteristics
                    );

                    if ($videoFile && file_exists($videoFile) && filesize($videoFile) > 10000) {
                        $this->updateJobStatus($jobId, 4, 'active', 'Mood-optimized video generation successful');
                        $output['video'] = [
                            'file' => $videoFile,
                            'duration' => 30,
                            'mood_applied' => true,
                            'optimization_used' => true
                        ];
                    }
                } elseif ($useHuggingFace) {
                    // Try Hugging Face video engine first
                    $this->updateJobStatus($jobId, 4, 'active', 'Generating video with Hugging Face AI...');
                    $hfVideoEngine = new HuggingFaceVideoEngine();
                    $videoFile = $hfVideoEngine->generateAdvancedVideo(
                        $parsedContent['segments'],
                        $params['background'],
                        $params['style'],
                        $jobId
                    );

                    if ($videoFile && file_exists($videoFile) && filesize($videoFile) > 10000) {
                        $this->updateJobStatus($jobId, 4, 'active', 'Hugging Face video generation successful');
                        $output['video'] = [
                            'file' => $videoFile,
                            'duration' => 30 // Fixed 30-second duration
                        ];
                    }
                }

                // Fallback to working video engine if HF fails or not requested
                if (!$videoFile || !file_exists($videoFile) || filesize($videoFile) < 10000) {
                    $this->updateJobStatus($jobId, 4, 'active', 'Using enhanced local video engine...');
                    $workingVideo = new WorkingVideoEngine();
                    $videoFile = $workingVideo->generateWorkingVideo(
                        $parsedContent['segments'],
                        $params['background'],
                        $params['style'],
                        $jobId
                    );

                    if ($videoFile && file_exists($videoFile) && filesize($videoFile) > 1000) {
                        $this->updateJobStatus($jobId, 4, 'active', 'Enhanced video engine successful');
                        $output['video'] = [
                            'file' => $videoFile,
                            'duration' => $this->getVideoDuration($videoFile)
                        ];
                    } else {
                        // Try real video engine
                        $this->updateJobStatus($jobId, 4, 'active', 'Trying real video engine...');
                        $realVideo = new RealVideoEngine();
                        $videoFile = $realVideo->generateRealVideo(
                            $parsedContent['segments'],
                            $params['background'],
                            $params['style'],
                            $jobId
                        );

                        if ($videoFile && file_exists($videoFile) && filesize($videoFile) > 1000) {
                            $this->updateJobStatus($jobId, 4, 'active', 'Real video engine successful');
                            $output['video'] = [
                                'file' => $videoFile,
                                'duration' => $this->getVideoDuration($videoFile)
                            ];
                        } else {
                            // Final fallback to basic video engine
                            $this->updateJobStatus($jobId, 4, 'active', 'Using basic video fallback...');
                            $videoEngine = new VideoEngine();
                            $video = $videoEngine->createVideo(
                                $mixedAudio['file'],
                                $parsedContent['segments'],
                                $params['background'],
                                $params['flow_type'],
                                $jobId
                            );
                            $output['video'] = $video;
                            $output['subtitles'] = ['file' => $video['subtitles']];
                        }
                    }
                }

                // Generate subtitles if not already created
                if (!isset($output['subtitles'])) {
                    $subtitlesFile = $this->generateSubtitles($parsedContent['segments'], $jobId);
                    $output['subtitles'] = ['file' => $subtitlesFile];
                }
            }
            
            // Save output files
            $finalOutput = $this->saveOutputFiles($jobId, $output);
            
            // Mark job as complete
            $this->updateJobStatus($jobId, 4, 'complete', 'Generation complete!', $finalOutput);
            
            // Cleanup temporary files
            $this->cleanup($jobId);
            
        } catch (Exception $e) {
            $this->updateJobStatus($jobId, 0, 'error', 'Generation failed: ' . $e->getMessage());
        }
    }

    /**
     * Get content based on source type
     */
    private function getContent($params) {
        switch ($params['content_source']) {
            case 'upload':
                $uploadDir = __DIR__ . '/temp/uploads/';
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0755, true);
                }
                
                $uploadedFile = $uploadDir . basename($_FILES['content_file']['name']);
                move_uploaded_file($_FILES['content_file']['tmp_name'], $uploadedFile);
                
                $storyParser = new StoryParser();
                return $storyParser->processFile($uploadedFile);
                
            case 'text':
                return $params['content_text'];
                
            case 'template':
                $templateEngine = new TemplateEngine();
                $template = $templateEngine->getTemplate($params['template']);
                return $template['content']['text'];
                
            default:
                throw new Exception('Invalid content source');
        }
    }

    /**
     * Save output files to permanent location
     */
    private function saveOutputFiles($jobId, $output) {
        $outputDir = $this->outputDir . $jobId . '/';
        if (!is_dir($outputDir)) {
            mkdir($outputDir, 0755, true);
        }
        
        $finalOutput = [];
        
        foreach ($output as $type => $data) {
            if (isset($data['file']) && file_exists($data['file'])) {
                $extension = pathinfo($data['file'], PATHINFO_EXTENSION);
                $finalFile = $outputDir . $type . '.' . $extension;
                copy($data['file'], $finalFile);
                
                $finalOutput[$type] = [
                    'url' => 'data/output_history/' . $jobId . '/' . $type . '.' . $extension,
                    'file' => $finalFile,
                    'size' => $this->formatFileSize(filesize($finalFile)),
                    'duration' => $data['duration'] ?? null
                ];
            }
        }
        
        return $finalOutput;
    }

    /**
     * Format file size for display
     */
    private function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Update job status
     */
    private function updateJobStatus($jobId, $step, $status, $message, $output = null) {
        $jobData = $this->loadJobData($jobId);
        $jobData['step'] = $step;
        $jobData['status'] = $status;
        $jobData['message'] = $message;
        $jobData['progress'] = ($step / 4) * 100;
        $jobData['updated_at'] = date('Y-m-d H:i:s');
        
        if ($status === 'complete') {
            $jobData['complete'] = true;
            $jobData['output'] = $output;
        } elseif ($status === 'error') {
            $jobData['error'] = $message;
        }
        
        $this->saveJobData($jobId, $jobData);
    }

    /**
     * Get job status
     */
    private function getJobStatus($jobId) {
        if (empty($jobId)) {
            throw new Exception('Job ID required');
        }
        
        $jobData = $this->loadJobData($jobId);
        
        return json_encode($jobData);
    }

    /**
     * Save job data
     */
    private function saveJobData($jobId, $data) {
        $jobFile = $this->jobsDir . $jobId . '.json';
        file_put_contents($jobFile, json_encode($data, JSON_PRETTY_PRINT));
    }

    /**
     * Load job data
     */
    private function loadJobData($jobId) {
        $jobFile = $this->jobsDir . $jobId . '.json';
        
        if (!file_exists($jobFile)) {
            throw new Exception('Job not found');
        }
        
        return json_decode(file_get_contents($jobFile), true);
    }

    /**
     * Get audio duration
     */
    private function getAudioDuration($audioFile) {
        if (!file_exists($audioFile)) {
            return 0;
        }

        // Simple duration calculation based on file size and format
        $fileSize = filesize($audioFile);
        $sampleRate = 22050; // Default sample rate
        $channels = 1;
        $bitsPerSample = 16;

        $dataSize = $fileSize - 44; // Subtract WAV header size
        $bytesPerSecond = $sampleRate * $channels * ($bitsPerSample / 8);

        return max(0, $dataSize / $bytesPerSecond);
    }

    /**
     * Get video duration
     */
    private function getVideoDuration($videoFile) {
        if (!file_exists($videoFile)) {
            return 0;
        }

        // Try to get duration using ffprobe if available
        $cmd = "ffprobe -v quiet -show_entries format=duration -of csv=p=0 \"$videoFile\" 2>&1";
        $output = shell_exec($cmd);

        if ($output && is_numeric(trim($output))) {
            return floatval(trim($output));
        }

        // Fallback: estimate based on file size
        $fileSize = filesize($videoFile);
        return max(1, $fileSize / 100000); // Rough estimate
    }

    /**
     * Generate subtitles file
     */
    private function generateSubtitles($segments, $jobId) {
        $outputDir = $this->outputDir . $jobId . '/';
        if (!is_dir($outputDir)) {
            mkdir($outputDir, 0755, true);
        }

        $subtitlesFile = $outputDir . 'subtitles.srt';
        $srtContent = '';
        $currentTime = 0;

        foreach ($segments as $index => $segment) {
            $text = $segment['text'] ?? '';
            $duration = max(2, strlen($text) * 0.1); // Estimate duration

            $startTime = $this->formatSRTTime($currentTime);
            $endTime = $this->formatSRTTime($currentTime + $duration);

            $srtContent .= ($index + 1) . "\n";
            $srtContent .= "$startTime --> $endTime\n";
            $srtContent .= $text . "\n\n";

            $currentTime += $duration;
        }

        file_put_contents($subtitlesFile, $srtContent);
        return $subtitlesFile;
    }

    /**
     * Format time for SRT format
     */
    private function formatSRTTime($seconds) {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $secs = $seconds % 60;
        $milliseconds = ($secs - floor($secs)) * 1000;

        return sprintf('%02d:%02d:%02d,%03d', $hours, $minutes, floor($secs), $milliseconds);
    }

    /**
     * Clean up temporary files
     */
    private function cleanup($jobId) {
        $voiceEngine = new VoiceEngine();
        $audioMixer = new AudioMixer();
        $videoEngine = new VideoEngine();

        $voiceEngine->cleanup($jobId);
        $audioMixer->cleanup($jobId);
        $videoEngine->cleanup($jobId);

        // Clean up real engines
        if (class_exists('RealTTSEngine')) {
            // Clean up TTS temp files
            $tempDir = __DIR__ . '/temp/';
            $pattern = $tempDir . $jobId . '_*';
            foreach (glob($pattern) as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
    }

    /**
     * Download file
     */
    private function downloadFile($filePath) {
        $fullPath = __DIR__ . '/' . $filePath;
        
        if (!file_exists($fullPath)) {
            http_response_code(404);
            return json_encode(['error' => 'File not found']);
        }
        
        $filename = basename($fullPath);
        $mimeType = mime_content_type($fullPath);
        
        header('Content-Type: ' . $mimeType);
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . filesize($fullPath));
        
        readfile($fullPath);
        exit;
    }
}

// Handle the request only if called directly
if (basename($_SERVER['PHP_SELF']) === 'generate.php') {
    $generator = new SutradharGenerator();
    echo $generator->handleRequest();
}
