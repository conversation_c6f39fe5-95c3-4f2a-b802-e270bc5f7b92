# System Architecture Guide

## 🏗️ Overview

The Sutradhar Video Generation Platform is built on a modern, modular architecture designed for scalability, performance, and maintainability. The system combines AI-powered video generation with a comprehensive suite of image processing tools.

## 📊 High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Layer                           │
├─────────────────────────────────────────────────────────────┤
│  • Futuristic UI (HTML5, CSS3, Vanilla JS)                │
│  • Responsive Design (Mobile-first)                        │
│  • Real-time Previews & Animations                         │
│  • Progressive Web App Features                            │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   API Gateway Layer                        │
├─────────────────────────────────────────────────────────────┤
│  • Unified API (api_unified.php)                          │
│  • Authentication & Authorization                          │
│  • Rate Limiting & CSRF Protection                         │
│  • Request Validation & Sanitization                       │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                  Business Logic Layer                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │ Video Generation│  │   Tools Engine  │  │ User Mgmt   │ │
│  │                 │  │                 │  │             │ │
│  │ • HF Engine     │  │ • Image Proc    │  │ • Auth      │ │
│  │ • Real Engine   │  │ • Generators    │  │ • Credits   │ │
│  │ • Optimized     │  │ • Utilities     │  │ • Sessions  │ │
│  │ • Local Fallback│  │ • Advanced      │  │ • Payments  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Data Access Layer                       │
├─────────────────────────────────────────────────────────────┤
│  • Database Manager (PDO)                                  │
│  • File System Manager                                     │
│  • Cache Manager (Optional Redis)                          │
│  • External API Clients                                    │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Infrastructure Layer                     │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │   Database  │  │ File Storage│  │   External APIs     │ │
│  │             │  │             │  │                     │ │
│  │ • MySQL     │  │ • Local FS  │  │ • Hugging Face      │ │
│  │ • Users     │  │ • Temp Files│  │ • Stripe/PayPal     │ │
│  │ • Videos    │  │ • Assets    │  │ • Analytics         │ │
│  │ • Credits   │  │ • Uploads   │  │ • Monitoring        │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🎬 Video Generation Pipeline

### Core Components

#### 1. HuggingFaceVideoEngine
**Location**: `core/huggingface_video_engine.php`

```php
class HuggingFaceVideoEngine {
    // Primary video generation engine
    // Handles AI-powered image generation
    // Manages fallback mechanisms
    // Optimizes performance and quality
}
```

**Key Features**:
- Multiple AI model support with automatic fallback
- Optimized prompt engineering for better results
- Local image generation when APIs fail
- Enhanced cleanup and file management
- Performance monitoring and analytics

#### 2. RealVideoEngine
**Location**: `core/real_video_engine.php`

```php
class RealVideoEngine {
    // High-quality video processing
    // Advanced animation systems
    // Professional output formats
}
```

#### 3. OptimizedVideoEngine
**Location**: `core/optimized_video_engine.php`

```php
class OptimizedVideoEngine {
    // Performance-focused generation
    // Reduced resource usage
    // Fast processing for bulk operations
}
```

### Generation Workflow

```mermaid
graph TD
    A[User Request] --> B[Prompt Analysis]
    B --> C[Scene Segmentation]
    C --> D[Image Generation]
    D --> E{API Available?}
    E -->|Yes| F[Hugging Face API]
    E -->|No| G[Local Generation]
    F --> H[Image Processing]
    G --> H
    H --> I[Frame Generation]
    I --> J[Video Assembly]
    J --> K[Quality Check]
    K --> L[File Optimization]
    L --> M[Cleanup & Delivery]
```

## 🛠️ Tools Architecture

### Tools Engine Structure

```
core/tools/
├── ImageProcessor.php      # Base image processing class
├── GeneratorTools.php      # Content generation tools
├── UtilityTools.php        # Utility and analysis tools
├── AdvancedTools.php       # Advanced processing tools
└── ToolsManager.php        # Central tools coordinator
```

### Tool Categories

#### Image Processing Tools (8 tools)
- Multi-format conversion with quality optimization
- AI-powered background removal using edge detection
- Smart resizing with aspect ratio preservation
- Intelligent compression with quality presets
- Advanced filter studio with real-time preview
- Color palette extraction and scheme generation
- Professional watermarking with batch processing
- Comprehensive metadata management

#### Generation Tools (7 tools)
- QR code studio with custom styling
- Professional barcode generation
- Text-to-image creation with effects
- Thumbnail factory for batch processing
- Collage maker with templates
- Icon generator for multiple formats
- Meme generator with positioning

#### Utility Tools (6 tools)
- Color code converter between formats
- Image analyzer with quality metrics
- Duplicate image finder with similarity detection
- Image organizer with smart sorting
- Batch renamer with pattern support
- Image comparison with difference highlighting

#### Advanced Tools (4 tools)
- Image upscaler using interpolation algorithms
- Noise reducer with adjustable intensity
- Perspective corrector with manual adjustment
- Image stitcher for panorama creation

## 🗄️ Database Architecture

### Core Tables

```sql
-- Users and Authentication
users (id, email, password_hash, created_at, updated_at)
user_sessions (id, user_id, session_token, expires_at)
user_credits (user_id, credits_available, credits_used, last_updated)

-- Video Generation
generation_jobs (id, user_id, job_id, status, progress, created_at)
video_outputs (id, job_id, video_url, thumbnail_url, duration)

-- Tools System
tool_usage (id, user_id, tool_name, usage_count, last_used, processing_time)
tool_files (id, user_id, original_name, stored_name, file_type, created_at)

-- Payments and Subscriptions
subscriptions (id, user_id, plan_type, status, expires_at)
payment_transactions (id, user_id, amount, currency, status, created_at)
```

## 🔐 Security Architecture

### Authentication Flow
1. **Login**: Email/password validation with bcrypt hashing
2. **Session Management**: Secure session tokens with expiration
3. **CSRF Protection**: Token-based request validation
4. **Rate Limiting**: Per-user and per-IP request limits

### File Security
- **Upload Validation**: File type, size, and content validation
- **Secure Storage**: Isolated temp directories with cleanup
- **Access Control**: User-specific file access permissions
- **Malware Prevention**: File content scanning and validation

## 📈 Performance Optimizations

### Frontend Optimizations
- **Lazy Loading**: Progressive content loading
- **Image Compression**: Client-side compression before upload
- **Caching**: Browser caching with proper headers
- **Minification**: CSS/JS minification and compression

### Backend Optimizations
- **Database Indexing**: Optimized queries with proper indexes
- **File Caching**: Temporary file caching for repeated operations
- **Memory Management**: Efficient memory usage and cleanup
- **Process Optimization**: Background job processing

### Video Generation Optimizations
- **Frame Rate Optimization**: 15 FPS for faster generation
- **Parallel Processing**: Concurrent image generation
- **Smart Caching**: Reuse of similar generated content
- **Quality Balancing**: Optimal quality vs. speed trade-offs

## 🔄 Integration Points

### External APIs
- **Hugging Face**: AI image generation with multiple model fallback
- **Payment Gateways**: Stripe and PayPal integration
- **Analytics**: Google Analytics and custom tracking
- **Monitoring**: Error tracking and performance monitoring

### Internal Integrations
- **Credit System**: Unified credit management across all features
- **User Management**: Single sign-on across tools and video generation
- **File Management**: Centralized file handling and cleanup
- **Notification System**: Real-time updates and progress tracking

## 🚀 Scalability Considerations

### Horizontal Scaling
- **Load Balancing**: Multiple server instances
- **Database Sharding**: User-based data distribution
- **CDN Integration**: Static asset distribution
- **Microservices**: Service-based architecture migration

### Vertical Scaling
- **Resource Optimization**: Memory and CPU optimization
- **Database Tuning**: Query optimization and indexing
- **Caching Layers**: Redis/Memcached integration
- **Background Processing**: Queue-based job processing

This architecture ensures the Sutradhar platform can handle high loads while maintaining performance, security, and user experience quality.
