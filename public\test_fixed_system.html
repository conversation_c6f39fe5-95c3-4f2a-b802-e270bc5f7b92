<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sutradhar 2070 - System Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0F0F23 0%, #1A1A2E 50%, #16213E 100%);
            font-family: 'Arial', sans-serif;
            color: white;
        }
        .test-card {
            background: rgba(26, 26, 46, 0.8);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            transition: all 0.3s ease;
        }
        .test-card:hover {
            border-color: #00D4FF;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }
        .success { border-color: #10B981; background: rgba(16, 185, 129, 0.1); }
        .error { border-color: #EF4444; background: rgba(239, 68, 68, 0.1); }
        .loading { border-color: #F59E0B; background: rgba(245, 158, 11, 0.1); }
    </style>
</head>
<body class="min-h-screen p-8">
    <div class="max-w-6xl mx-auto">
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold mb-4">🎭 Sutradhar 2070 - System Test</h1>
            <p class="text-gray-300">Testing all API endpoints and functionality</p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Test Cards -->
            <div class="test-card p-6 rounded-lg" id="test-mood-api">
                <h3 class="text-lg font-semibold mb-3">🎭 Mood API</h3>
                <p class="text-sm text-gray-300 mb-4">Testing mood data loading</p>
                <button onclick="testMoodAPI()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded text-sm">Test</button>
                <div class="mt-3 text-sm" id="mood-result">Ready to test</div>
            </div>

            <div class="test-card p-6 rounded-lg" id="test-auth">
                <h3 class="text-lg font-semibold mb-3">🔐 Authentication</h3>
                <p class="text-sm text-gray-300 mb-4">Testing user registration/login</p>
                <button onclick="testAuth()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded text-sm">Test</button>
                <div class="mt-3 text-sm" id="auth-result">Ready to test</div>
            </div>

            <div class="test-card p-6 rounded-lg" id="test-generation">
                <h3 class="text-lg font-semibold mb-3">🎬 Video Generation</h3>
                <p class="text-sm text-gray-300 mb-4">Testing video generation API</p>
                <button onclick="testGeneration()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded text-sm">Test</button>
                <div class="mt-3 text-sm" id="generation-result">Ready to test</div>
            </div>

            <div class="test-card p-6 rounded-lg" id="test-payments">
                <h3 class="text-lg font-semibold mb-3">💳 Payments</h3>
                <p class="text-sm text-gray-300 mb-4">Testing payment endpoints</p>
                <button onclick="testPayments()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded text-sm">Test</button>
                <div class="mt-3 text-sm" id="payments-result">Ready to test</div>
            </div>

            <div class="test-card p-6 rounded-lg" id="test-dashboard">
                <h3 class="text-lg font-semibold mb-3">📊 Dashboard</h3>
                <p class="text-sm text-gray-300 mb-4">Testing dashboard APIs</p>
                <button onclick="testDashboard()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded text-sm">Test</button>
                <div class="mt-3 text-sm" id="dashboard-result">Ready to test</div>
            </div>

            <div class="test-card p-6 rounded-lg" id="test-mood-generator">
                <h3 class="text-lg font-semibold mb-3">🎨 Mood Generator</h3>
                <p class="text-sm text-gray-300 mb-4">Testing mood video generator page</p>
                <button onclick="testMoodGenerator()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded text-sm">Test</button>
                <div class="mt-3 text-sm" id="mood-generator-result">Ready to test</div>
            </div>
        </div>

        <div class="mt-8 text-center">
            <button onclick="runAllTests()" class="bg-green-600 hover:bg-green-700 px-8 py-3 rounded-lg text-lg font-semibold">
                🚀 Run All Tests
            </button>
        </div>

        <div class="mt-8 test-card p-6 rounded-lg">
            <h3 class="text-lg font-semibold mb-3">📋 Test Results Summary</h3>
            <div id="summary" class="text-sm text-gray-300">
                Click "Run All Tests" to see overall results
            </div>
        </div>
    </div>

    <script>
        let testResults = {};

        async function testMoodAPI() {
            updateTestStatus('mood-api', 'loading', 'Testing...');
            
            try {
                const response = await fetch('mood_api.php?action=get_moods');
                const data = await response.json();
                
                if (data.success && data.moods) {
                    updateTestStatus('mood-api', 'success', `✅ Loaded ${Object.keys(data.moods).length} mood categories`);
                    testResults.moodAPI = true;
                } else {
                    updateTestStatus('mood-api', 'error', '❌ Failed to load moods');
                    testResults.moodAPI = false;
                }
            } catch (error) {
                updateTestStatus('mood-api', 'error', '❌ Error: ' + error.message);
                testResults.moodAPI = false;
            }
        }

        async function testAuth() {
            updateTestStatus('auth', 'loading', 'Testing...');
            
            try {
                // Test auth status
                const response = await fetch('api_unified.php?endpoint=auth/status');
                const data = await response.json();
                
                if (data.success !== undefined) {
                    updateTestStatus('auth', 'success', '✅ Auth API responding');
                    testResults.auth = true;
                } else {
                    updateTestStatus('auth', 'error', '❌ Invalid auth response');
                    testResults.auth = false;
                }
            } catch (error) {
                updateTestStatus('auth', 'error', '❌ Error: ' + error.message);
                testResults.auth = false;
            }
        }

        async function testGeneration() {
            updateTestStatus('generation', 'loading', 'Testing...');
            
            try {
                // Test generation endpoint (should require auth)
                const response = await fetch('api_unified.php?endpoint=generate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        mood: 'euphoric',
                        topic: 'nature_wildlife'
                    })
                });
                const data = await response.json();
                
                // Should get 401 (unauthorized) or proper response
                if (response.status === 401 || data.success !== undefined) {
                    updateTestStatus('generation', 'success', '✅ Generation API responding');
                    testResults.generation = true;
                } else {
                    updateTestStatus('generation', 'error', '❌ Unexpected response');
                    testResults.generation = false;
                }
            } catch (error) {
                updateTestStatus('generation', 'error', '❌ Error: ' + error.message);
                testResults.generation = false;
            }
        }

        async function testPayments() {
            updateTestStatus('payments', 'loading', 'Testing...');
            
            try {
                const response = await fetch('api_unified.php?endpoint=payments/packages');
                const data = await response.json();
                
                if (data.success && data.data && data.data.packages) {
                    updateTestStatus('payments', 'success', `✅ Loaded ${data.data.packages.length} credit packages`);
                    testResults.payments = true;
                } else {
                    updateTestStatus('payments', 'error', '❌ Failed to load packages');
                    testResults.payments = false;
                }
            } catch (error) {
                updateTestStatus('payments', 'error', '❌ Error: ' + error.message);
                testResults.payments = false;
            }
        }

        async function testDashboard() {
            updateTestStatus('dashboard', 'loading', 'Testing...');
            
            try {
                const response = await fetch('api_unified.php?endpoint=dashboard/stats');
                const data = await response.json();
                
                // Should get 401 (unauthorized) or proper response
                if (response.status === 401 || data.success !== undefined) {
                    updateTestStatus('dashboard', 'success', '✅ Dashboard API responding');
                    testResults.dashboard = true;
                } else {
                    updateTestStatus('dashboard', 'error', '❌ Unexpected response');
                    testResults.dashboard = false;
                }
            } catch (error) {
                updateTestStatus('dashboard', 'error', '❌ Error: ' + error.message);
                testResults.dashboard = false;
            }
        }

        async function testMoodGenerator() {
            updateTestStatus('mood-generator', 'loading', 'Testing...');
            
            try {
                const response = await fetch('mood_video_generator.html');
                
                if (response.ok) {
                    updateTestStatus('mood-generator', 'success', '✅ Mood generator page accessible');
                    testResults.moodGenerator = true;
                } else {
                    updateTestStatus('mood-generator', 'error', '❌ Page not accessible');
                    testResults.moodGenerator = false;
                }
            } catch (error) {
                updateTestStatus('mood-generator', 'error', '❌ Error: ' + error.message);
                testResults.moodGenerator = false;
            }
        }

        function updateTestStatus(testId, status, message) {
            const card = document.getElementById(`test-${testId}`);
            const result = document.getElementById(`${testId}-result`);
            
            card.className = `test-card p-6 rounded-lg ${status}`;
            result.textContent = message;
        }

        async function runAllTests() {
            testResults = {};
            
            await testMoodAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAuth();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testGeneration();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testPayments();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testDashboard();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testMoodGenerator();
            
            // Update summary
            const passed = Object.values(testResults).filter(r => r).length;
            const total = Object.keys(testResults).length;
            const summary = document.getElementById('summary');
            
            if (passed === total) {
                summary.innerHTML = `🎉 <strong>ALL TESTS PASSED!</strong> (${passed}/${total})<br>
                    <span class="text-green-400">✅ Your Sutradhar 2070 system is working perfectly!</span><br>
                    <a href="mood_video_generator.html" class="text-blue-400 underline">🎬 Try the Mood Video Generator</a>`;
            } else {
                summary.innerHTML = `⚠️ <strong>SOME TESTS FAILED</strong> (${passed}/${total} passed)<br>
                    <span class="text-yellow-400">Please check the failed tests above and fix any issues.</span>`;
            }
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
