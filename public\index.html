<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sutradhar 2070 - AI-Powered Mood-Based Video Generation</title>
    <meta name="description" content="Create stunning videos with emotional intelligence using Sutradhar 2070's revolutionary AI-powered mood-based video generation platform.">
    
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'cyber-blue': '#00D4FF',
                        'cyber-purple': '#8B5CF6',
                        'cyber-pink': '#F472B6',
                        'cyber-green': '#10B981',
                        'cyber-orange': '#F59E0B',
                        'dark-bg': '#0F0F23',
                        'dark-card': '#1A1A2E'
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #0F0F23 0%, #1A1A2E 50%, #16213E 100%);
            font-family: 'Exo 2', sans-serif;
            color: white;
            min-height: 100vh;
        }

        .hologram-text {
            background: linear-gradient(45deg, #00D4FF, #8B5CF6, #F472B6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: hologram 3s ease-in-out infinite;
        }

        @keyframes hologram {
            0%, 100% { opacity: 0.8; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
        }

        .cyber-button {
            background: linear-gradient(45deg, #00D4FF, #8B5CF6);
            border: 1px solid #00D4FF;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .cyber-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.8);
        }

        .cyber-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .cyber-button:hover::before {
            left: 100%;
        }

        .cyber-button.secondary {
            background: transparent;
            border: 1px solid #8B5CF6;
            color: #8B5CF6;
        }

        .cyber-button.secondary:hover {
            background: #8B5CF6;
            color: white;
        }

        .glass-morphism {
            background: rgba(26, 26, 46, 0.7);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 212, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .floating-element {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .mood-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .mood-card {
            background: rgba(26, 26, 46, 0.8);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .mood-card:hover {
            transform: translateY(-5px);
            border-color: #00D4FF;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 212, 255, 0.5);
        }

        .mood-card.selected {
            border-color: #8B5CF6;
            background: rgba(139, 92, 246, 0.2);
        }

        .generation-progress {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .progress-container {
            background: rgba(26, 26, 46, 0.9);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00D4FF, #8B5CF6);
            width: 0%;
            transition: width 0.3s ease;
        }

        .auth-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 2000;
            justify-content: center;
            align-items: center;
        }

        .auth-container {
            background: rgba(26, 26, 46, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 20px;
            padding: 2rem;
            max-width: 400px;
            width: 90%;
        }

        .nav-item {
            transition: all 0.3s ease;
            padding: 0.5rem 1rem;
            border-radius: 8px;
        }

        .nav-item:hover {
            background: rgba(0, 212, 255, 0.1);
            color: #00D4FF;
        }

        .credit-display {
            background: linear-gradient(45deg, rgba(0, 212, 255, 0.1), rgba(139, 92, 246, 0.1));
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 10px;
            padding: 0.5rem 1rem;
            font-weight: bold;
        }

        .user-menu {
            position: relative;
        }

        .user-dropdown {
            display: none;
            position: absolute;
            top: 100%;
            right: 0;
            background: rgba(26, 26, 46, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 10px;
            padding: 1rem;
            min-width: 200px;
            z-index: 100;
        }

        .user-dropdown.show {
            display: block;
        }

        .tier-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .tier-badge.free { background: linear-gradient(45deg, #6B7280, #9CA3AF); }
        .tier-badge.pro { background: linear-gradient(45deg, #10B981, #059669); }
        .tier-badge.business { background: linear-gradient(45deg, #F59E0B, #D97706); }
        .tier-badge.enterprise { background: linear-gradient(45deg, #8B5CF6, #7C3AED); }

        .topic-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .topic-card {
            background: rgba(26, 26, 46, 0.6);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .topic-card:hover {
            border-color: #00D4FF;
            background: rgba(26, 26, 46, 0.8);
            transform: scale(1.05);
        }

        .topic-card.selected {
            border-color: #8B5CF6;
            background: rgba(139, 92, 246, 0.2);
        }

        .inspiration-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 0.5rem;
            margin: 1rem 0;
        }

        .inspiration-item {
            background: rgba(26, 26, 46, 0.4);
            border: 1px solid rgba(0, 212, 255, 0.1);
            border-radius: 8px;
            padding: 0.5rem;
            text-align: center;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .inspiration-item:hover {
            border-color: #00D4FF;
            background: rgba(26, 26, 46, 0.6);
        }

        .inspiration-item.selected {
            border-color: #8B5CF6;
            background: rgba(139, 92, 246, 0.2);
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(26, 26, 46, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 10px;
            padding: 1rem;
            max-width: 300px;
            z-index: 3000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            border-color: #10B981;
        }

        .notification.error {
            border-color: #EF4444;
        }

        .notification.warning {
            border-color: #F59E0B;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="fixed top-0 w-full z-50 glass-morphism">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-cyber-blue to-cyber-purple rounded-full flex items-center justify-center floating-element">
                        <span class="text-white font-bold text-lg">🎭</span>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold hologram-text">SUTRADHAR 2070</h1>
                        <p class="text-cyber-blue text-xs">AI Video Generation</p>
                    </div>
                </div>

                <!-- Navigation Items -->
                <div class="hidden md:flex items-center space-x-6">
                    <a href="#home" class="nav-item">Home</a>
                    <a href="#features" class="nav-item">Features</a>
                    <a href="tools/" class="nav-item">🛠️ Tools</a>
                    <a href="pricing.html" class="nav-item">Pricing</a>
                    <a href="#about" class="nav-item">About</a>
                </div>

                <!-- User Section -->
                <div class="flex items-center space-x-4">
                    <!-- Guest User -->
                    <div id="guest-section" class="flex items-center space-x-4">
                        <button onclick="showAuthModal('login')" class="nav-item">Sign In</button>
                        <button onclick="showAuthModal('register')" class="cyber-button px-4 py-2 rounded-lg font-bold">
                            Get Started
                        </button>
                    </div>

                    <!-- Authenticated User -->
                    <div id="user-section" class="hidden flex items-center space-x-4">
                        <div class="credit-display">
                            <span id="user-credits">0</span> Credits
                        </div>
                        <div class="user-menu">
                            <button onclick="toggleUserMenu()" class="flex items-center space-x-2 nav-item">
                                <div class="w-8 h-8 bg-gradient-to-r from-cyber-blue to-cyber-purple rounded-full flex items-center justify-center">
                                    <span id="user-avatar" class="text-white font-bold text-sm">U</span>
                                </div>
                                <span id="user-name">User</span>
                                <span class="tier-badge" id="user-tier">Free</span>
                            </button>
                            <div id="user-dropdown" class="user-dropdown">
                                <div class="space-y-2">
                                    <a href="dashboard.html" class="block nav-item">📊 Dashboard</a>
                                    <a href="tools/" class="block nav-item">🛠️ Tools</a>
                                    <a href="pricing.html" class="block nav-item">💎 Upgrade</a>
                                    <a href="#" class="block nav-item">⚙️ Settings</a>
                                    <hr class="border-gray-600">
                                    <button onclick="logout()" class="block w-full text-left nav-item text-red-400">
                                        🚪 Sign Out
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="pt-32 pb-20 text-center relative overflow-hidden">
        <div class="container mx-auto px-6 relative z-10">
            <div class="max-w-5xl mx-auto">
                <h1 class="text-6xl md:text-8xl font-bold hologram-text mb-8 leading-tight">
                    Create Videos with
                    <span class="block">Emotional Intelligence</span>
                </h1>
                <p class="text-xl md:text-2xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed">
                    Sutradhar 2070 revolutionizes video creation with AI-powered mood-based generation.
                    Transform your ideas into stunning 30-second videos that capture the perfect emotional tone.
                </p>

                <div class="flex flex-col md:flex-row justify-center items-center space-y-4 md:space-y-0 md:space-x-6 mb-16">
                    <button onclick="startVideoGeneration()" class="cyber-button px-8 py-4 rounded-lg font-bold text-lg">
                        🚀 Start Creating Free
                    </button>
                    <button onclick="window.location.href='marketing_landing.html'" class="cyber-button secondary px-8 py-4 rounded-lg font-bold text-lg">
                        📺 Watch Demo
                    </button>
                </div>

                <!-- Quick Stats -->
                <div class="grid md:grid-cols-4 gap-8 mb-16">
                    <div class="text-center">
                        <div class="text-4xl font-bold text-cyber-blue mb-2">50K+</div>
                        <p class="text-gray-400">Videos Generated</p>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold text-cyber-purple mb-2">30</div>
                        <p class="text-gray-400">Emotional Moods</p>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold text-cyber-green mb-2">95%</div>
                        <p class="text-gray-400">Satisfaction Rate</p>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold text-cyber-orange mb-2">2min</div>
                        <p class="text-gray-400">Avg Generation Time</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Background Elements -->
        <div class="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
            <div class="absolute top-20 left-10 w-32 h-32 bg-cyber-blue/10 rounded-full blur-xl floating-element"></div>
            <div class="absolute top-40 right-20 w-48 h-48 bg-cyber-purple/10 rounded-full blur-xl floating-element" style="animation-delay: -2s;"></div>
            <div class="absolute bottom-20 left-1/4 w-24 h-24 bg-cyber-pink/10 rounded-full blur-xl floating-element" style="animation-delay: -4s;"></div>
        </div>
    </section>

    <!-- Video Generation Section -->
    <section id="generator" class="py-20 bg-gradient-to-r from-dark-bg to-dark-card">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-5xl font-bold hologram-text mb-6">
                    AI-Powered Video Generation
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Select your mood, choose your topic, and let our AI create stunning videos that perfectly capture your vision.
                </p>
            </div>

            <!-- Generation Interface -->
            <div class="max-w-6xl mx-auto">
                <!-- Step 1: Mood Selection -->
                <div class="glass-morphism p-8 rounded-2xl mb-8">
                    <h3 class="text-2xl font-bold mb-6 text-center">
                        <span class="text-cyber-blue">Step 1:</span> Choose Your Mood
                    </h3>
                    <div class="mood-grid" id="mood-grid">
                        <!-- Mood cards will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Step 2: Topic Selection -->
                <div class="glass-morphism p-8 rounded-2xl mb-8">
                    <h3 class="text-2xl font-bold mb-6 text-center">
                        <span class="text-cyber-purple">Step 2:</span> Select Your Topic
                    </h3>
                    <div class="topic-grid" id="topic-grid">
                        <!-- Topic cards will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Step 3: Inspiration (Optional) -->
                <div class="glass-morphism p-8 rounded-2xl mb-8">
                    <h3 class="text-2xl font-bold mb-6 text-center">
                        <span class="text-cyber-green">Step 3:</span> Add Inspiration (Optional)
                    </h3>
                    <div class="inspiration-grid" id="inspiration-grid">
                        <!-- Inspiration items will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Generation Controls -->
                <div class="text-center">
                    <div class="glass-morphism p-6 rounded-2xl inline-block">
                        <div class="mb-4">
                            <p class="text-gray-300 mb-2">Generation Cost: <span id="generation-cost" class="text-cyber-green font-bold">15 Credits</span></p>
                            <p class="text-sm text-gray-400">Your Balance: <span id="user-balance" class="text-cyber-blue">0 Credits</span></p>
                        </div>
                        <button id="generate-btn" onclick="generateVideo()" class="cyber-button px-8 py-4 rounded-lg font-bold text-lg disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            🎬 Generate Video
                        </button>
                        <p class="text-xs text-gray-400 mt-2">Estimated time: 2-3 minutes</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-5xl font-bold hologram-text mb-6">
                    Revolutionary Features
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Experience the future of video creation with cutting-edge AI technology.
                </p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <div class="glass-morphism p-8 rounded-2xl text-center">
                    <div class="w-16 h-16 bg-gradient-to-r from-cyber-blue to-cyber-purple rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-2xl">🎭</span>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">30 Emotional Moods</h3>
                    <p class="text-gray-300">
                        From euphoric to melancholic, our AI understands and generates videos
                        that perfectly capture the emotional tone you want to convey.
                    </p>
                </div>

                <div class="glass-morphism p-8 rounded-2xl text-center">
                    <div class="w-16 h-16 bg-gradient-to-r from-cyber-green to-cyber-blue rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-2xl">⚡</span>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">Lightning Fast</h3>
                    <p class="text-gray-300">
                        Generate professional-quality 30-second videos in just 2-3 minutes.
                        Our optimized AI pipeline ensures rapid processing.
                    </p>
                </div>

                <div class="glass-morphism p-8 rounded-2xl text-center">
                    <div class="w-16 h-16 bg-gradient-to-r from-cyber-purple to-cyber-pink rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-2xl">🎬</span>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">Professional Quality</h3>
                    <p class="text-gray-300">
                        HD and 4K output with advanced effects, perfect audio synchronization,
                        and cinema-grade color grading.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Preview -->
    <section class="py-20 bg-gradient-to-r from-dark-card to-dark-bg">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-5xl font-bold hologram-text mb-6">
                    Choose Your Plan
                </h2>
                <p class="text-xl text-gray-300">
                    From hobbyists to enterprises, we have the perfect plan for your needs.
                </p>
            </div>

            <div class="grid md:grid-cols-4 gap-6 max-w-6xl mx-auto">
                <!-- Free Plan -->
                <div class="glass-morphism p-6 rounded-2xl text-center">
                    <span class="tier-badge free">Free</span>
                    <h3 class="text-xl font-bold mt-4 mb-2">Starter</h3>
                    <div class="text-3xl font-bold mb-4">$0</div>
                    <ul class="text-sm space-y-2 mb-6">
                        <li>50 credits/month</li>
                        <li>Standard quality</li>
                        <li>Watermarked videos</li>
                    </ul>
                    <button onclick="showAuthModal('register')" class="cyber-button secondary w-full py-2 rounded-lg">
                        Get Started
                    </button>
                </div>

                <!-- Pro Plan -->
                <div class="glass-morphism p-6 rounded-2xl text-center border-cyber-purple">
                    <span class="tier-badge pro">Pro</span>
                    <h3 class="text-xl font-bold mt-4 mb-2">Creator</h3>
                    <div class="text-3xl font-bold mb-4">$9.99</div>
                    <ul class="text-sm space-y-2 mb-6">
                        <li>500 credits/month</li>
                        <li>HD quality</li>
                        <li>No watermarks</li>
                    </ul>
                    <button onclick="window.location.href='pricing.html'" class="cyber-button w-full py-2 rounded-lg">
                        Start Trial
                    </button>
                </div>

                <!-- Business Plan -->
                <div class="glass-morphism p-6 rounded-2xl text-center">
                    <span class="tier-badge business">Business</span>
                    <h3 class="text-xl font-bold mt-4 mb-2">Professional</h3>
                    <div class="text-3xl font-bold mb-4">$29.99</div>
                    <ul class="text-sm space-y-2 mb-6">
                        <li>2000 credits/month</li>
                        <li>API access</li>
                        <li>Priority support</li>
                    </ul>
                    <button onclick="window.location.href='pricing.html'" class="cyber-button secondary w-full py-2 rounded-lg">
                        Learn More
                    </button>
                </div>

                <!-- Enterprise Plan -->
                <div class="glass-morphism p-6 rounded-2xl text-center">
                    <span class="tier-badge enterprise">Enterprise</span>
                    <h3 class="text-xl font-bold mt-4 mb-2">Enterprise</h3>
                    <div class="text-3xl font-bold mb-4">Custom</div>
                    <ul class="text-sm space-y-2 mb-6">
                        <li>Unlimited credits</li>
                        <li>4K quality</li>
                        <li>Dedicated support</li>
                    </ul>
                    <button onclick="window.location.href='pricing.html'" class="cyber-button secondary w-full py-2 rounded-lg">
                        Contact Sales
                    </button>
                </div>
            </div>

            <div class="text-center mt-12">
                <a href="pricing.html" class="cyber-button px-8 py-4 rounded-lg font-bold text-lg">
                    View All Plans & Features
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-12 border-t border-gray-800">
        <div class="container mx-auto px-6">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-8 h-8 bg-gradient-to-r from-cyber-blue to-cyber-purple rounded-full flex items-center justify-center">
                            <span class="text-white font-bold">🎭</span>
                        </div>
                        <span class="text-xl font-bold hologram-text">SUTRADHAR 2070</span>
                    </div>
                    <p class="text-gray-400 mb-4">
                        Revolutionizing video creation with AI-powered emotional intelligence.
                    </p>
                </div>

                <div>
                    <h3 class="font-bold mb-4">Product</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#features" class="hover:text-white transition-colors">Features</a></li>
                        <li><a href="pricing.html" class="hover:text-white transition-colors">Pricing</a></li>
                        <li><a href="dashboard.html" class="hover:text-white transition-colors">Dashboard</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-bold mb-4">Company</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="marketing_landing.html" class="hover:text-white transition-colors">About</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Blog</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Contact</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-bold mb-4">Support</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition-colors">Help Center</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Documentation</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Status</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 Sutradhar 2070. All rights reserved. Built with AI for the future of video creation.</p>
            </div>
        </div>
    </footer>

    <!-- Authentication Modal -->
    <div id="auth-modal" class="auth-modal">
        <div class="auth-container">
            <div class="flex justify-between items-center mb-6">
                <h2 id="auth-title" class="text-2xl font-bold hologram-text">Sign In</h2>
                <button onclick="hideAuthModal()" class="text-gray-400 hover:text-white text-2xl">&times;</button>
            </div>

            <!-- Login Form -->
            <form id="login-form" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium mb-2">Email</label>
                    <input type="email" id="login-email" class="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 text-white" required>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">Password</label>
                    <input type="password" id="login-password" class="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 text-white" required>
                </div>
                <button type="submit" class="w-full cyber-button py-3 rounded-lg font-bold">
                    Sign In
                </button>
                <div class="text-center">
                    <button type="button" onclick="showAuthModal('register')" class="text-cyber-blue hover:text-white transition-colors">
                        Don't have an account? Sign up
                    </button>
                </div>
            </form>

            <!-- Register Form -->
            <form id="register-form" class="space-y-4 hidden">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium mb-2">First Name</label>
                        <input type="text" id="register-firstname" class="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 text-white" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">Last Name</label>
                        <input type="text" id="register-lastname" class="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 text-white" required>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">Email</label>
                    <input type="email" id="register-email" class="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 text-white" required>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">Password</label>
                    <input type="password" id="register-password" class="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 text-white" required>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">Confirm Password</label>
                    <input type="password" id="register-confirm" class="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 text-white" required>
                </div>
                <button type="submit" class="w-full cyber-button py-3 rounded-lg font-bold">
                    Create Account
                </button>
                <div class="text-center">
                    <button type="button" onclick="showAuthModal('login')" class="text-cyber-blue hover:text-white transition-colors">
                        Already have an account? Sign in
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Generation Progress Modal -->
    <div id="generation-progress" class="generation-progress">
        <div class="progress-container">
            <h3 class="text-2xl font-bold mb-4">Generating Your Video</h3>
            <div class="mb-4">
                <div class="w-24 h-24 mx-auto mb-4 relative">
                    <div class="w-full h-full border-4 border-cyber-blue/30 rounded-full"></div>
                    <div class="absolute top-0 left-0 w-full h-full border-4 border-cyber-blue rounded-full animate-spin border-t-transparent"></div>
                </div>
            </div>
            <p id="progress-text" class="text-gray-300 mb-4">Analyzing mood and generating prompts...</p>
            <div class="progress-bar">
                <div id="progress-fill" class="progress-fill"></div>
            </div>
            <p id="progress-percentage" class="text-sm text-gray-400">0%</p>
            <div class="mt-4">
                <p class="text-xs text-gray-500">This usually takes 2-3 minutes</p>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notification" class="notification">
        <div class="flex items-start space-x-3">
            <div id="notification-icon" class="text-2xl"></div>
            <div>
                <h4 id="notification-title" class="font-bold"></h4>
                <p id="notification-message" class="text-sm text-gray-300"></p>
            </div>
        </div>
    </div>

    <script>
        /**
         * Sutradhar 2070 - Integrated Application Controller
         * Handles authentication, video generation, and user management
         */

        class Sutradhar2070 {
            constructor() {
                this.currentUser = null;
                this.selectedMood = null;
                this.selectedTopic = null;
                this.selectedInspiration = [];
                this.generationCost = 15;

                this.moods = [
                    { id: 'euphoric', name: 'Euphoric', emoji: '😊', description: 'Joyful and uplifting' },
                    { id: 'serene', name: 'Serene', emoji: '😌', description: 'Calm and peaceful' },
                    { id: 'energetic', name: 'Energetic', emoji: '⚡', description: 'Dynamic and vibrant' },
                    { id: 'mysterious', name: 'Mysterious', emoji: '🔮', description: 'Enigmatic and intriguing' },
                    { id: 'melancholic', name: 'Melancholic', emoji: '😔', description: 'Thoughtful and reflective' },
                    { id: 'adventurous', name: 'Adventurous', emoji: '🗺️', description: 'Bold and exciting' },
                    { id: 'romantic', name: 'Romantic', emoji: '💕', description: 'Loving and tender' },
                    { id: 'dramatic', name: 'Dramatic', emoji: '🎭', description: 'Intense and powerful' },
                    { id: 'playful', name: 'Playful', emoji: '🎈', description: 'Fun and lighthearted' },
                    { id: 'nostalgic', name: 'Nostalgic', emoji: '📸', description: 'Reminiscent and wistful' },
                    { id: 'futuristic', name: 'Futuristic', emoji: '🚀', description: 'Modern and innovative' },
                    { id: 'dreamy', name: 'Dreamy', emoji: '☁️', description: 'Ethereal and surreal' }
                ];

                this.topics = [
                    { id: 'nature_wildlife', name: 'Nature & Wildlife', emoji: '🌿' },
                    { id: 'urban_city', name: 'Urban & City', emoji: '🏙️' },
                    { id: 'abstract_art', name: 'Abstract Art', emoji: '🎨' },
                    { id: 'technology', name: 'Technology', emoji: '💻' },
                    { id: 'food_cooking', name: 'Food & Cooking', emoji: '🍳' },
                    { id: 'travel_adventure', name: 'Travel & Adventure', emoji: '✈️' },
                    { id: 'sports_fitness', name: 'Sports & Fitness', emoji: '⚽' },
                    { id: 'music_dance', name: 'Music & Dance', emoji: '🎵' },
                    { id: 'fashion_beauty', name: 'Fashion & Beauty', emoji: '👗' },
                    { id: 'science_space', name: 'Science & Space', emoji: '🔬' },
                    { id: 'business_office', name: 'Business & Office', emoji: '💼' },
                    { id: 'education', name: 'Education', emoji: '📚' }
                ];

                this.inspirations = [
                    'Cinematic', 'Vintage', 'Modern', 'Minimalist', 'Colorful', 'Monochrome',
                    'Fast-paced', 'Slow-motion', 'Close-up', 'Wide-angle', 'Aerial', 'Underground',
                    'Sunrise', 'Sunset', 'Night', 'Golden hour', 'Rainy', 'Sunny',
                    'Professional', 'Casual', 'Artistic', 'Documentary', 'Commercial', 'Personal'
                ];

                this.init();
            }

            async init() {
                try {
                    console.log('🚀 Initializing Sutradhar 2070...');

                    // Try to load stored user first
                    const hasStoredUser = this.loadStoredUser();

                    if (!hasStoredUser) {
                        // If no stored user, check auth status with server
                        await this.checkAuthStatus();
                    }

                    this.setupEventListeners();
                    this.populateSelectionGrids();
                    this.updateGenerationButton();

                    console.log('✅ Sutradhar 2070 initialized successfully');
                } catch (error) {
                    console.error('❌ Initialization error:', error);
                    // Continue with basic functionality even if init fails
                    this.setupEventListeners();
                    this.populateSelectionGrids();
                    this.updateGenerationButton();
                }
            }

            async checkAuthStatus() {
                try {
                    console.log('🔍 Checking authentication status...');

                    const response = await fetch('/api/auth/status');

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const responseText = await response.text();
                    console.log('Auth status response:', responseText);

                    let data;
                    try {
                        data = JSON.parse(responseText);
                    } catch (parseError) {
                        console.error('Auth status JSON parse error:', parseError);
                        throw new Error('Invalid response from server');
                    }

                    if (data.success) {
                        const userData = data.data?.user || data.user;
                        if (userData) {
                            console.log('✅ User authenticated:', userData.email);
                            this.setCurrentUser(userData);
                        } else {
                            console.log('ℹ️ No user data, demo mode active');
                        }
                    } else {
                        console.log('ℹ️ Auth check returned:', data.message || 'Not authenticated');
                    }
                } catch (error) {
                    console.log('❌ Auth check failed:', error.message);
                    // Try fallback APIs
                    this.tryFallbackAuthCheck();
                }
            }

            async tryFallbackAuthCheck() {
                const fallbackAPIs = [
                    'working_api.php?endpoint=auth/status',
                    'test_api_simple.php?endpoint=auth/status'
                ];

                for (const api of fallbackAPIs) {
                    try {
                        console.log(`🔄 Trying fallback auth check: ${api}`);

                        const response = await fetch(api);
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                const userData = data.data?.user || data.user;
                                if (userData) {
                                    console.log('✅ Fallback auth successful:', userData.email);
                                    this.setCurrentUser(userData);
                                    return;
                                }
                            }
                        }
                    } catch (error) {
                        console.log(`❌ Fallback API ${api} failed:`, error.message);
                    }
                }

                console.log('ℹ️ All auth checks failed, continuing in demo mode');
            }

            setCurrentUser(user) {
                console.log('👤 Setting current user:', user.email);
                this.currentUser = user;

                // Store user data in localStorage for persistence
                try {
                    localStorage.setItem('sutradhar_user', JSON.stringify(user));
                    console.log('✅ User data stored in localStorage');
                } catch (error) {
                    console.warn('⚠️ Failed to store user data in localStorage:', error);
                }

                this.updateUserInterface();
            }

            loadStoredUser() {
                try {
                    const storedUser = localStorage.getItem('sutradhar_user');
                    if (storedUser) {
                        const userData = JSON.parse(storedUser);
                        console.log('📦 Loaded stored user:', userData.email);
                        this.currentUser = userData;
                        this.updateUserInterface();
                        return true;
                    }
                } catch (error) {
                    console.warn('⚠️ Failed to load stored user data:', error);
                    localStorage.removeItem('sutradhar_user');
                }
                return false;
            }

            clearStoredUser() {
                try {
                    localStorage.removeItem('sutradhar_user');
                    console.log('🗑️ Cleared stored user data');
                } catch (error) {
                    console.warn('⚠️ Failed to clear stored user data:', error);
                }
            }

            async refreshUserData() {
                if (!this.currentUser) return;

                try {
                    console.log('🔄 Refreshing user data...');

                    const response = await fetch('working_api.php?endpoint=user/update');

                    if (response.ok) {
                        const data = await response.json();
                        if (data.success && data.data.user) {
                            console.log('✅ User data refreshed');
                            this.setCurrentUser(data.data.user);
                        }
                    } else {
                        console.warn('⚠️ Failed to refresh user data');
                    }
                } catch (error) {
                    console.error('❌ Error refreshing user data:', error);
                }
            }

            updateUserInterface() {
                const guestSection = document.getElementById('guest-section');
                const userSection = document.getElementById('user-section');

                if (this.currentUser) {
                    guestSection.classList.add('hidden');
                    userSection.classList.remove('hidden');

                    document.getElementById('user-name').textContent = this.currentUser.first_name;
                    document.getElementById('user-credits').textContent = this.currentUser.credits || 0;
                    document.getElementById('user-balance').textContent = this.currentUser.credits || 0;
                    document.getElementById('user-tier').textContent = (this.currentUser.tier || 'free').toUpperCase();
                    document.getElementById('user-tier').className = `tier-badge ${this.currentUser.tier || 'free'}`;
                    document.getElementById('user-avatar').textContent = this.currentUser.first_name.charAt(0).toUpperCase();
                } else {
                    guestSection.classList.remove('hidden');
                    userSection.classList.add('hidden');
                }

                this.updateGenerationButton();
            }

            setupEventListeners() {
                // Authentication forms
                document.getElementById('login-form').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleLogin();
                });

                document.getElementById('register-form').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleRegister();
                });

                // Close modals on outside click
                document.getElementById('auth-modal').addEventListener('click', (e) => {
                    if (e.target.id === 'auth-modal') {
                        this.hideAuthModal();
                    }
                });

                // Smooth scrolling for navigation
                document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                    anchor.addEventListener('click', function (e) {
                        e.preventDefault();
                        const target = document.querySelector(this.getAttribute('href'));
                        if (target) {
                            target.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                            });
                        }
                    });
                });
            }

            populateSelectionGrids() {
                // Populate mood grid
                const moodGrid = document.getElementById('mood-grid');
                moodGrid.innerHTML = this.moods.map(mood => `
                    <div class="mood-card" data-mood="${mood.id}" onclick="app.selectMood('${mood.id}')">
                        <div class="text-4xl mb-3">${mood.emoji}</div>
                        <h4 class="font-bold text-lg mb-2">${mood.name}</h4>
                        <p class="text-sm text-gray-400">${mood.description}</p>
                    </div>
                `).join('');

                // Populate topic grid
                const topicGrid = document.getElementById('topic-grid');
                topicGrid.innerHTML = this.topics.map(topic => `
                    <div class="topic-card" data-topic="${topic.id}" onclick="app.selectTopic('${topic.id}')">
                        <div class="text-3xl mb-2">${topic.emoji}</div>
                        <h4 class="font-bold">${topic.name}</h4>
                    </div>
                `).join('');

                // Populate inspiration grid
                const inspirationGrid = document.getElementById('inspiration-grid');
                inspirationGrid.innerHTML = this.inspirations.map(inspiration => `
                    <div class="inspiration-item" data-inspiration="${inspiration}" onclick="app.toggleInspiration('${inspiration}')">
                        ${inspiration}
                    </div>
                `).join('');
            }

            selectMood(moodId) {
                // Remove previous selection
                document.querySelectorAll('.mood-card').forEach(card => {
                    card.classList.remove('selected');
                });

                // Add selection to clicked card
                document.querySelector(`[data-mood="${moodId}"]`).classList.add('selected');
                this.selectedMood = moodId;
                this.updateGenerationButton();
            }

            selectTopic(topicId) {
                // Remove previous selection
                document.querySelectorAll('.topic-card').forEach(card => {
                    card.classList.remove('selected');
                });

                // Add selection to clicked card
                document.querySelector(`[data-topic="${topicId}"]`).classList.add('selected');
                this.selectedTopic = topicId;
                this.updateGenerationButton();
            }

            toggleInspiration(inspiration) {
                const element = document.querySelector(`[data-inspiration="${inspiration}"]`);

                if (this.selectedInspiration.includes(inspiration)) {
                    // Remove inspiration
                    this.selectedInspiration = this.selectedInspiration.filter(i => i !== inspiration);
                    element.classList.remove('selected');
                } else {
                    // Add inspiration (max 5)
                    if (this.selectedInspiration.length < 5) {
                        this.selectedInspiration.push(inspiration);
                        element.classList.add('selected');
                    } else {
                        this.showNotification('warning', 'Limit Reached', 'You can select up to 5 inspiration items.');
                    }
                }
            }

            updateGenerationButton() {
                const generateBtn = document.getElementById('generate-btn');
                const canGenerate = this.selectedMood && this.selectedTopic && this.currentUser &&
                                  (this.currentUser.credits || 0) >= this.generationCost;

                generateBtn.disabled = !canGenerate;

                if (!this.currentUser) {
                    generateBtn.textContent = '🔒 Sign In to Generate';
                } else if (!this.selectedMood || !this.selectedTopic) {
                    generateBtn.textContent = '📝 Select Mood & Topic';
                } else if ((this.currentUser.credits || 0) < this.generationCost) {
                    generateBtn.textContent = '💎 Insufficient Credits';
                } else {
                    generateBtn.textContent = '🎬 Generate Video';
                }
            }

            async handleLogin() {
                const email = document.getElementById('login-email').value;
                const password = document.getElementById('login-password').value;

                if (!email || !password) {
                    this.showNotification('error', 'Missing Information', 'Please enter both email and password');
                    return;
                }

                try {
                    console.log('Attempting login for:', email);

                    const response = await fetch('/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ email, password })
                    });

                    console.log('Login response status:', response.status);

                    // Check if response is ok
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const responseText = await response.text();
                    console.log('Login response text:', responseText);

                    let data;
                    try {
                        data = JSON.parse(responseText);
                    } catch (parseError) {
                        console.error('JSON parse error:', parseError);
                        throw new Error('Invalid response from server');
                    }

                    if (data.success) {
                        // Handle successful login
                        const userData = data.data?.user || data.user;
                        if (userData) {
                            this.setCurrentUser(userData);
                            this.hideAuthModal();
                            this.showNotification('success', 'Welcome Back!', `Signed in successfully. You have ${userData.credits || 0} credits.`);
                        } else {
                            throw new Error('Invalid user data received');
                        }
                    } else {
                        this.showNotification('error', 'Login Failed', data.error || 'Invalid credentials');
                    }
                } catch (error) {
                    console.error('Login error:', error);
                    this.showNotification('error', 'Connection Error', `Unable to connect to server: ${error.message}`);

                    // Try fallback API
                    this.tryFallbackLogin(email, password);
                }
            }

            async handleRegister() {
                const firstName = document.getElementById('register-firstname').value;
                const lastName = document.getElementById('register-lastname').value;
                const email = document.getElementById('register-email').value;
                const password = document.getElementById('register-password').value;
                const confirmPassword = document.getElementById('register-confirm').value;

                // Validation
                if (!firstName || !lastName || !email || !password) {
                    this.showNotification('error', 'Missing Information', 'Please fill in all fields');
                    return;
                }

                if (password !== confirmPassword) {
                    this.showNotification('error', 'Password Mismatch', 'Passwords do not match');
                    return;
                }

                if (password.length < 8) {
                    this.showNotification('error', 'Weak Password', 'Password must be at least 8 characters long');
                    return;
                }

                try {
                    console.log('Attempting registration for:', email);

                    const response = await fetch('/api/auth/register', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            email,
                            password,
                            first_name: firstName,
                            last_name: lastName
                        })
                    });

                    console.log('Registration response status:', response.status);

                    // Check if response is ok
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const responseText = await response.text();
                    console.log('Registration response text:', responseText);

                    let data;
                    try {
                        data = JSON.parse(responseText);
                    } catch (parseError) {
                        console.error('JSON parse error:', parseError);
                        throw new Error('Invalid response from server');
                    }

                    if (data.success) {
                        // Handle successful registration
                        const userData = data.data?.user || data.user;
                        if (userData) {
                            // Auto-login the user
                            this.setCurrentUser(userData);
                            this.hideAuthModal();
                            this.showNotification('success', 'Account Created!', `Welcome ${userData.first_name}! You have ${userData.credits || 100} credits to start.`);
                        } else {
                            this.hideAuthModal();
                            this.showNotification('success', 'Account Created!', 'Registration successful! Please sign in.');
                        }
                    } else {
                        this.showNotification('error', 'Registration Failed', data.error || 'Unable to create account');
                    }
                } catch (error) {
                    console.error('Registration error:', error);
                    this.showNotification('error', 'Connection Error', `Unable to connect to server: ${error.message}`);

                    // Try fallback API
                    this.tryFallbackRegister(firstName, lastName, email, password);
                }
            }

            async generateVideo() {
                if (!this.currentUser) {
                    this.showAuthModal('login');
                    return;
                }

                if (!this.selectedMood || !this.selectedTopic) {
                    this.showNotification('warning', 'Selection Required', 'Please select both mood and topic');
                    return;
                }

                if ((this.currentUser.credits || 0) < this.generationCost) {
                    this.showNotification('warning', 'Insufficient Credits', 'You need more credits to generate a video');
                    window.location.href = 'pricing.html';
                    return;
                }

                this.showGenerationProgress();

                try {
                    console.log('Starting video generation:', {
                        mood: this.selectedMood,
                        topic: this.selectedTopic,
                        user: this.currentUser.email
                    });

                    const response = await fetch('/api/generate', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            mood: this.selectedMood,
                            topic: this.selectedTopic,
                            inspiration: this.selectedInspiration,
                            user_id: this.currentUser.user_id,
                            custom_prompt: `Create a ${this.selectedMood} video about ${this.selectedTopic}`
                        })
                    });

                    console.log('Generation response status:', response.status);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const responseText = await response.text();
                    console.log('Generation response text:', responseText);

                    let data;
                    try {
                        data = JSON.parse(responseText);
                    } catch (parseError) {
                        console.error('JSON parse error:', parseError);
                        throw new Error('Invalid response from server');
                    }

                    if (data.success) {
                        const jobId = data.data?.job_id || data.job_id;
                        if (jobId) {
                            this.pollGenerationStatus(jobId);
                        } else {
                            throw new Error('No job ID received');
                        }
                    } else {
                        this.hideGenerationProgress();
                        this.showNotification('error', 'Generation Failed', data.error || 'Unable to start video generation');
                    }
                } catch (error) {
                    console.error('Generation error:', error);
                    this.hideGenerationProgress();
                    this.showNotification('error', 'Connection Error', `Unable to connect to server: ${error.message}`);

                    // Try fallback generation
                    this.tryFallbackGeneration();
                }
            }

            async pollGenerationStatus(jobId, fallbackAPI = null) {
                let attempts = 0;
                const maxAttempts = 60; // 2 minutes max

                const pollInterval = setInterval(async () => {
                    attempts++;

                    try {
                        let statusURL = `/api/generate/status/${jobId}`;

                        // Use fallback API if provided
                        if (fallbackAPI) {
                            statusURL = fallbackAPI.replace('?endpoint=generate', `?endpoint=generate/status/${jobId}`);
                        }

                        console.log(`Checking status (attempt ${attempts}):`, statusURL);

                        const response = await fetch(statusURL);

                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}`);
                        }

                        const responseText = await response.text();
                        let data;

                        try {
                            data = JSON.parse(responseText);
                        } catch (parseError) {
                            throw new Error('Invalid JSON response');
                        }

                        if (data.success) {
                            const progressData = data.data || data;
                            const progress = progressData.progress || 0;
                            const status = progressData.status || 'processing';
                            const message = progressData.message || 'Processing...';

                            this.updateProgress(progress, status, message);

                            if (status === 'completed') {
                                clearInterval(pollInterval);
                                this.hideGenerationProgress();

                                const videoUrl = progressData.video_url || progressData.video_file;
                                this.showVideoResult(videoUrl, progressData);

                                // Refresh user data from server to get updated credits
                                this.refreshUserData();
                            } else if (status === 'failed' || status === 'error') {
                                clearInterval(pollInterval);
                                this.hideGenerationProgress();
                                this.showNotification('error', 'Generation Failed', progressData.error || 'Video generation failed');
                            }
                        } else {
                            throw new Error(data.error || 'Status check failed');
                        }
                    } catch (error) {
                        console.error(`Status check error (attempt ${attempts}):`, error);

                        // If too many attempts, give up
                        if (attempts >= maxAttempts) {
                            clearInterval(pollInterval);
                            this.hideGenerationProgress();
                            this.showNotification('error', 'Status Check Failed', 'Unable to check generation status. Please check the video generator page.');
                        }
                        // Continue trying for network errors
                    }
                }, 2000);
            }

            updateProgress(percentage, status, message) {
                console.log(`📊 Progress update: ${percentage}% - ${status} - ${message}`);

                const progressFill = document.getElementById('progress-fill');
                const progressText = document.getElementById('progress-text');
                const progressPercentage = document.getElementById('progress-percentage');

                if (progressFill) {
                    progressFill.style.width = `${percentage}%`;
                    console.log('✅ Progress bar updated');
                } else {
                    console.warn('⚠️ Progress fill element not found');
                }

                if (progressPercentage) {
                    progressPercentage.textContent = `${Math.round(percentage)}%`;
                    console.log('✅ Progress percentage updated');
                } else {
                    console.warn('⚠️ Progress percentage element not found');
                }

                if (progressText) {
                    // Use custom message if provided, otherwise use default status messages
                    if (message) {
                        progressText.textContent = message;
                    } else {
                        const statusMessages = {
                            'analyzing': 'Analyzing mood and generating prompts...',
                            'generating': 'Creating your video with AI...',
                            'processing': 'Adding final touches and effects...',
                            'finalizing': 'Almost done! Preparing your video...',
                            'completed': 'Video generation completed!',
                            'pending': 'Initializing video generation...'
                        };
                        progressText.textContent = statusMessages[status] || 'Processing your request...';
                    }
                    console.log('✅ Progress text updated');
                } else {
                    console.warn('⚠️ Progress text element not found');
                }
            }

            showVideoResult(videoUrl, outputData) {
                // Create a result modal or redirect to dashboard
                let message = 'Your video has been generated successfully!';

                if (outputData) {
                    message += ` Duration: ${outputData.duration}s, Mood: ${outputData.mood_applied}`;
                }

                this.showNotification('success', 'Video Ready!', message);

                // Option 1: Open in new tab if video URL is available
                if (videoUrl) {
                    window.open(videoUrl, '_blank');
                }

                // Option 2: Redirect to dashboard after a delay
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 3000);
            }

            showAuthModal(type = 'login') {
                const modal = document.getElementById('auth-modal');
                const title = document.getElementById('auth-title');
                const loginForm = document.getElementById('login-form');
                const registerForm = document.getElementById('register-form');

                if (type === 'login') {
                    title.textContent = 'Sign In';
                    loginForm.classList.remove('hidden');
                    registerForm.classList.add('hidden');
                } else {
                    title.textContent = 'Create Account';
                    loginForm.classList.add('hidden');
                    registerForm.classList.remove('hidden');
                }

                modal.style.display = 'flex';

                // Animate in
                gsap.from('.auth-container', {
                    duration: 0.3,
                    scale: 0.8,
                    opacity: 0,
                    ease: 'back.out(1.7)'
                });
            }

            hideAuthModal() {
                const modal = document.getElementById('auth-modal');
                modal.style.display = 'none';

                // Clear forms
                document.getElementById('login-form').reset();
                document.getElementById('register-form').reset();
            }

            showGenerationProgress() {
                const modal = document.getElementById('generation-progress');
                modal.style.display = 'flex';

                // Reset progress
                document.getElementById('progress-fill').style.width = '0%';
                document.getElementById('progress-percentage').textContent = '0%';
                document.getElementById('progress-text').textContent = 'Initializing generation...';
            }

            hideGenerationProgress() {
                const modal = document.getElementById('generation-progress');
                modal.style.display = 'none';
            }

            showNotification(type, title, message) {
                const notification = document.getElementById('notification');
                const icon = document.getElementById('notification-icon');
                const titleEl = document.getElementById('notification-title');
                const messageEl = document.getElementById('notification-message');

                // Set content
                titleEl.textContent = title;
                messageEl.textContent = message;

                // Set icon and style based on type
                const icons = {
                    success: '✅',
                    error: '❌',
                    warning: '⚠️',
                    info: 'ℹ️'
                };

                icon.textContent = icons[type] || icons.info;
                notification.className = `notification ${type}`;

                // Show notification
                notification.classList.add('show');

                // Auto hide after 5 seconds
                setTimeout(() => {
                    notification.classList.remove('show');
                }, 5000);
            }

            async logout() {
                try {
                    console.log('🚪 Logging out...');

                    // Try main logout endpoint
                    const response = await fetch('/api/auth/logout', { method: 'POST' });

                    if (response.ok) {
                        console.log('✅ Server logout successful');
                    } else {
                        console.log('⚠️ Server logout failed, continuing with local logout');
                    }
                } catch (error) {
                    console.log('❌ Logout error:', error.message);
                }

                // Clear user data locally
                this.currentUser = null;
                this.clearStoredUser();
                this.updateUserInterface();
                this.showNotification('info', 'Signed Out', 'You have been signed out successfully');

                console.log('✅ Local logout completed');
            }

            // Fallback methods for when main API fails
            async tryFallbackLogin(email, password) {
                console.log('Trying fallback login methods...');

                const fallbackAPIs = [
                    'working_api.php?endpoint=auth/login',
                    'test_api_simple.php?endpoint=auth/login'
                ];

                for (const api of fallbackAPIs) {
                    try {
                        console.log(`Trying fallback API: ${api}`);

                        const response = await fetch(api, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ email, password })
                        });

                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                const userData = data.data?.user || data.user;
                                if (userData) {
                                    this.setCurrentUser(userData);
                                    this.hideAuthModal();
                                    this.showNotification('success', 'Welcome Back!', `Signed in successfully via fallback. You have ${userData.credits || 0} credits.`);
                                    return;
                                }
                            }
                        }
                    } catch (error) {
                        console.log(`Fallback API ${api} failed:`, error);
                    }
                }

                // If all fallbacks fail, show demo mode option
                this.showNotification('warning', 'Connection Issues', 'Unable to connect to authentication servers. You can still use demo mode.');
            }

            async tryFallbackRegister(firstName, lastName, email, password) {
                console.log('Trying fallback registration methods...');

                const fallbackAPIs = [
                    'working_api.php?endpoint=auth/register',
                    'test_api_simple.php?endpoint=auth/register'
                ];

                for (const api of fallbackAPIs) {
                    try {
                        console.log(`Trying fallback API: ${api}`);

                        const response = await fetch(api, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                email,
                                password,
                                first_name: firstName,
                                last_name: lastName
                            })
                        });

                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                const userData = data.data?.user || data.user;
                                if (userData) {
                                    this.setCurrentUser(userData);
                                    this.hideAuthModal();
                                    this.showNotification('success', 'Account Created!', `Welcome ${userData.first_name}! You have ${userData.credits || 100} credits.`);
                                    return;
                                } else {
                                    this.hideAuthModal();
                                    this.showNotification('success', 'Account Created!', 'Registration successful via fallback! Please sign in.');
                                    return;
                                }
                            }
                        }
                    } catch (error) {
                        console.log(`Fallback API ${api} failed:`, error);
                    }
                }

                // If all fallbacks fail
                this.showNotification('error', 'Registration Failed', 'Unable to create account. Please try again later or contact support.');
            }

            async tryFallbackGeneration() {
                console.log('Trying fallback generation methods...');

                const fallbackAPIs = [
                    'working_api.php?endpoint=generate',
                    'test_api_simple.php?endpoint=generate',
                    'mood_video_generator.html' // Redirect to dedicated generator
                ];

                for (const api of fallbackAPIs) {
                    try {
                        if (api.includes('.html')) {
                            // Redirect to dedicated generator
                            this.hideGenerationProgress();
                            this.showNotification('info', 'Redirecting', 'Opening dedicated video generator...');
                            setTimeout(() => {
                                window.location.href = api;
                            }, 2000);
                            return;
                        }

                        console.log(`Trying fallback API: ${api}`);

                        const response = await fetch(api, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                mood: this.selectedMood,
                                topic: this.selectedTopic,
                                custom_prompt: `Create a ${this.selectedMood} video about ${this.selectedTopic}`
                            })
                        });

                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                const jobId = data.data?.job_id || data.job_id;
                                if (jobId) {
                                    this.pollGenerationStatus(jobId, api);
                                    return;
                                }
                            }
                        }
                    } catch (error) {
                        console.log(`Fallback API ${api} failed:`, error);
                    }
                }

                // If all fallbacks fail
                this.hideGenerationProgress();
                this.showNotification('error', 'Generation Failed', 'Unable to start video generation. Please try the dedicated video generator.');
            }
        }

        // Global functions for HTML onclick handlers
        function showAuthModal(type) {
            app.showAuthModal(type);
        }

        function hideAuthModal() {
            app.hideAuthModal();
        }

        function generateVideo() {
            app.generateVideo();
        }

        function startVideoGeneration() {
            // Scroll to generator section
            document.getElementById('generator').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }

        function toggleUserMenu() {
            const dropdown = document.getElementById('user-dropdown');
            dropdown.classList.toggle('show');
        }

        function logout() {
            app.logout();
        }

        // Close user menu when clicking outside
        document.addEventListener('click', function(event) {
            const userMenu = document.querySelector('.user-menu');
            const dropdown = document.getElementById('user-dropdown');

            if (userMenu && !userMenu.contains(event.target)) {
                dropdown.classList.remove('show');
            }
        });

        // Initialize application
        let app;
        document.addEventListener('DOMContentLoaded', function() {
            app = new Sutradhar2070();

            // GSAP Animations
            gsap.from('.floating-element', {
                duration: 2,
                y: 20,
                opacity: 0,
                stagger: 0.2,
                ease: 'power2.out'
            });

            gsap.from('.glass-morphism', {
                duration: 1,
                y: 50,
                opacity: 0,
                stagger: 0.1,
                ease: 'power2.out',
                delay: 0.5
            });
        });
    </script>
</body>
</html>
