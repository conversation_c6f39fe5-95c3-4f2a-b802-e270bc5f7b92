# Troubleshooting Guide

## 🔧 Common Issues and Solutions

This guide covers the most common issues encountered with the Sutradhar Video Generation Platform and their solutions.

## 🚨 Installation Issues

### XAMPP Won't Start
**Problem**: Apache or MySQL services won't start in XAMPP.

**Solutions**:
1. **Port Conflicts**:
   ```bash
   # Check what's using port 80
   netstat -ano | findstr :80
   
   # Kill the process using the port
   taskkill /PID [process_id] /F
   ```

2. **Run as Administrator**:
   - Right-click XAMPP Control Panel
   - Select "Run as administrator"

3. **Windows Firewall**:
   - Add XAMPP to Windows Firewall exceptions
   - Allow Apache and MySQL through firewall

### PHP Extensions Not Loading
**Problem**: Required PHP extensions (GD, cURL) not available.

**Solutions**:
1. **Check php.ini location**:
   ```php
   <?php phpinfo(); ?>
   ```
   Look for "Loaded Configuration File"

2. **Enable extensions in php.ini**:
   ```ini
   extension=gd
   extension=curl
   extension=pdo_mysql
   extension=mbstring
   extension=openssl
   ```

3. **Restart Apache** after making changes

4. **Verify extensions loaded**:
   ```php
   <?php
   echo extension_loaded('gd') ? 'GD: OK' : 'GD: Missing';
   echo extension_loaded('curl') ? 'cURL: OK' : 'cURL: Missing';
   ?>
   ```

### FFmpeg Installation Issues
**Problem**: FFmpeg not found or not working.

**Solutions**:
1. **Download correct version**:
   - Use static build from [ffmpeg.org](https://ffmpeg.org)
   - Extract to `C:\ffmpeg`

2. **Add to PATH**:
   ```cmd
   # Test if FFmpeg is in PATH
   ffmpeg -version
   
   # If not found, add C:\ffmpeg\bin to system PATH
   ```

3. **Verify installation**:
   ```php
   <?php
   exec('ffmpeg -version 2>&1', $output, $returnCode);
   if ($returnCode === 0) {
       echo "FFmpeg: OK";
   } else {
       echo "FFmpeg: Not found";
   }
   ?>
   ```

## 🗄️ Database Issues

### Connection Failed
**Problem**: Cannot connect to MySQL database.

**Solutions**:
1. **Check MySQL service**:
   - Ensure MySQL is running in XAMPP
   - Check MySQL logs for errors

2. **Verify credentials**:
   ```json
   {
       "database": {
           "host": "localhost",
           "username": "root",
           "password": "",
           "database": "sutradhar_db"
       }
   }
   ```

3. **Test connection**:
   ```php
   <?php
   try {
       $pdo = new PDO('mysql:host=localhost;dbname=sutradhar_db', 'root', '');
       echo "Database: Connected";
   } catch (PDOException $e) {
       echo "Database Error: " . $e->getMessage();
   }
   ?>
   ```

### Tables Not Found
**Problem**: Database tables don't exist.

**Solutions**:
1. **Import database schema**:
   ```sql
   -- Run in phpMyAdmin or MySQL command line
   SOURCE /path/to/database.sql;
   ```

2. **Check table existence**:
   ```sql
   SHOW TABLES;
   DESCRIBE users;
   ```

3. **Recreate tables if needed**:
   - Use the SQL provided in installation guide
   - Ensure proper character encoding (utf8mb4)

## 🎬 Video Generation Issues

### Video Generation Fails
**Problem**: Video generation returns errors or fails silently.

**Solutions**:
1. **Check Hugging Face API**:
   ```php
   <?php
   $apiKey = 'your_api_key';
   $url = 'https://api-inference.huggingface.co/models/stabilityai/stable-diffusion-2-1';
   
   $context = stream_context_create([
       'http' => [
           'method' => 'POST',
           'header' => 'Authorization: Bearer ' . $apiKey,
           'content' => json_encode(['inputs' => 'test']),
           'timeout' => 30
       ]
   ]);
   
   $response = file_get_contents($url, false, $context);
   if ($response === false) {
       echo "API Error: " . error_get_last()['message'];
   } else {
       echo "API: OK";
   }
   ?>
   ```

2. **Check file permissions**:
   ```bash
   # Ensure temp directory is writable
   chmod 755 temp/
   chmod 755 public/videos/
   chmod 755 public/thumbnails/
   ```

3. **Verify FFmpeg functionality**:
   ```php
   <?php
   $testCmd = 'ffmpeg -f lavfi -i testsrc=duration=1:size=320x240:rate=1 -y test.mp4 2>&1';
   exec($testCmd, $output, $returnCode);
   
   if ($returnCode === 0 && file_exists('test.mp4')) {
       echo "FFmpeg: Working";
       unlink('test.mp4');
   } else {
       echo "FFmpeg Error: " . implode("\n", $output);
   }
   ?>
   ```

### Poor Video Quality
**Problem**: Generated videos have poor quality or artifacts.

**Solutions**:
1. **Adjust quality settings**:
   ```json
   {
       "video_generation": {
           "quality_presets": {
               "high": {
                   "resolution": "720x1280",
                   "bitrate": "2500k",
                   "fps": 30
               }
           }
       }
   }
   ```

2. **Check source image quality**:
   - Ensure Hugging Face API is working
   - Verify local fallback image generation

3. **Optimize FFmpeg settings**:
   ```bash
   ffmpeg -i input.mp4 -c:v libx264 -preset slow -crf 18 -pix_fmt yuv420p output.mp4
   ```

## 🛠️ Tools Issues

### Tool Processing Fails
**Problem**: Image processing tools return errors.

**Solutions**:
1. **Check file upload limits**:
   ```ini
   ; In php.ini
   upload_max_filesize = 50M
   post_max_size = 50M
   max_execution_time = 300
   memory_limit = 1024M
   ```

2. **Verify GD extension**:
   ```php
   <?php
   if (!extension_loaded('gd')) {
       echo "GD extension not loaded";
   } else {
       $info = gd_info();
       print_r($info);
   }
   ?>
   ```

3. **Check file format support**:
   ```php
   <?php
   $formats = [
       'JPEG' => imagetypes() & IMG_JPG,
       'PNG' => imagetypes() & IMG_PNG,
       'GIF' => imagetypes() & IMG_GIF,
       'WebP' => imagetypes() & IMG_WEBP
   ];
   
   foreach ($formats as $format => $supported) {
       echo "$format: " . ($supported ? 'Supported' : 'Not supported') . "\n";
   }
   ?>
   ```

### File Upload Issues
**Problem**: Files won't upload or upload fails.

**Solutions**:
1. **Check upload directory permissions**:
   ```bash
   # Windows
   icacls temp /grant Users:F
   icacls public\uploads /grant Users:F
   
   # Linux
   chmod 755 temp/
   chmod 755 public/uploads/
   ```

2. **Verify file size limits**:
   ```php
   <?php
   echo "Upload max filesize: " . ini_get('upload_max_filesize') . "\n";
   echo "Post max size: " . ini_get('post_max_size') . "\n";
   echo "Max execution time: " . ini_get('max_execution_time') . "\n";
   ?>
   ```

3. **Check file type validation**:
   ```php
   <?php
   $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
   $fileType = $_FILES['upload']['type'];
   
   if (!in_array($fileType, $allowedTypes)) {
       echo "File type not allowed: $fileType";
   }
   ?>
   ```

## 🔐 Authentication Issues

### Login Fails
**Problem**: Users cannot log in with correct credentials.

**Solutions**:
1. **Check session configuration**:
   ```php
   <?php
   session_start();
   echo "Session ID: " . session_id() . "\n";
   echo "Session save path: " . session_save_path() . "\n";
   ?>
   ```

2. **Verify password hashing**:
   ```php
   <?php
   $password = 'user_password';
   $hash = password_hash($password, PASSWORD_DEFAULT);
   
   if (password_verify($password, $hash)) {
       echo "Password verification: OK";
   } else {
       echo "Password verification: Failed";
   }
   ?>
   ```

3. **Check database user table**:
   ```sql
   SELECT id, email, password_hash FROM users WHERE email = '<EMAIL>';
   ```

### Session Expires Too Quickly
**Problem**: Users get logged out frequently.

**Solutions**:
1. **Adjust session lifetime**:
   ```json
   {
       "security": {
           "session": {
               "lifetime": 86400,
               "cookie_secure": false,
               "cookie_httponly": true
           }
       }
   }
   ```

2. **Check session garbage collection**:
   ```ini
   ; In php.ini
   session.gc_maxlifetime = 86400
   session.gc_probability = 1
   session.gc_divisor = 100
   ```

## 💳 Payment Issues

### Stripe Integration Fails
**Problem**: Payment processing with Stripe doesn't work.

**Solutions**:
1. **Verify API keys**:
   ```php
   <?php
   $stripe = new \Stripe\StripeClient('sk_test_...');
   
   try {
       $account = $stripe->account->retrieve();
       echo "Stripe: Connected to " . $account->display_name;
   } catch (\Stripe\Exception\AuthenticationException $e) {
       echo "Stripe Error: " . $e->getMessage();
   }
   ?>
   ```

2. **Check webhook configuration**:
   - Ensure webhook URL is accessible
   - Verify webhook secret matches configuration
   - Check webhook event types

3. **Test in sandbox mode**:
   - Use test API keys for development
   - Use test card numbers for testing

## 📊 Performance Issues

### Slow Page Loading
**Problem**: Pages load slowly or timeout.

**Solutions**:
1. **Enable caching**:
   ```json
   {
       "performance": {
           "caching": {
               "enable_file_cache": true,
               "cache_lifetime": 3600
           }
       }
   }
   ```

2. **Optimize database queries**:
   ```sql
   -- Add indexes for frequently queried columns
   CREATE INDEX idx_user_email ON users(email);
   CREATE INDEX idx_job_status ON generation_jobs(status);
   CREATE INDEX idx_tool_usage_user ON tool_usage(user_id);
   ```

3. **Enable compression**:
   ```apache
   # In .htaccess
   <IfModule mod_deflate.c>
       AddOutputFilterByType DEFLATE text/plain
       AddOutputFilterByType DEFLATE text/html
       AddOutputFilterByType DEFLATE text/xml
       AddOutputFilterByType DEFLATE text/css
       AddOutputFilterByType DEFLATE application/xml
       AddOutputFilterByType DEFLATE application/xhtml+xml
       AddOutputFilterByType DEFLATE application/rss+xml
       AddOutputFilterByType DEFLATE application/javascript
       AddOutputFilterByType DEFLATE application/x-javascript
   </IfModule>
   ```

### High Memory Usage
**Problem**: PHP processes consume too much memory.

**Solutions**:
1. **Increase memory limit**:
   ```ini
   memory_limit = 1024M
   ```

2. **Optimize image processing**:
   ```php
   <?php
   // Free memory after image processing
   imagedestroy($image);
   
   // Use smaller images for processing
   $resized = imagescale($original, 800, 600);
   ?>
   ```

3. **Implement cleanup**:
   ```php
   <?php
   // Clean up temporary files
   $tempFiles = glob('temp/*');
   foreach ($tempFiles as $file) {
       if (filemtime($file) < time() - 3600) { // 1 hour old
           unlink($file);
       }
   }
   ?>
   ```

## 🔍 Debugging Tools

### Enable Debug Mode
```json
{
    "debug": {
        "enable_debug_mode": true,
        "show_error_details": true,
        "log_level": "debug"
    }
}
```

### Check Error Logs
```bash
# Apache error log
tail -f C:\xampp\apache\logs\error.log

# PHP error log
tail -f C:\xampp\php\logs\php_error_log
```

### Database Query Logging
```php
<?php
class DatabaseManager {
    private $queryLog = [];
    
    public function query($sql, $params = []) {
        $start = microtime(true);
        $result = $this->pdo->prepare($sql)->execute($params);
        $time = microtime(true) - $start;
        
        $this->queryLog[] = [
            'sql' => $sql,
            'params' => $params,
            'time' => $time
        ];
        
        return $result;
    }
    
    public function getQueryLog() {
        return $this->queryLog;
    }
}
?>
```

## 📞 Getting Help

### Before Seeking Help
1. Check this troubleshooting guide
2. Review error logs
3. Test with minimal configuration
4. Document steps to reproduce the issue

### Support Channels
- **Documentation**: Check all relevant documentation sections
- **Error Logs**: Include relevant error messages
- **System Info**: Provide PHP version, OS, and configuration details
- **Steps to Reproduce**: Clear steps to reproduce the issue

### Useful Diagnostic Commands
```bash
# PHP version and modules
php -v
php -m

# Check configuration
php --ini
php -i | grep -i gd

# Test database connection
mysql -u root -p -e "SELECT VERSION();"

# Check file permissions
ls -la temp/
ls -la public/uploads/
```

This troubleshooting guide should help resolve most common issues with the Sutradhar platform.
