<?php
/**
 * Simple Hugging Face Video Engine Test
 */

echo "🎬 Simple Hugging Face Test\n";
echo "=" . str_repeat("=", 30) . "\n\n";

try {
    // Test 1: Check if config file exists
    echo "📋 Checking configuration...\n";
    $configPath = 'config/settings.json';
    
    if (!file_exists($configPath)) {
        echo "❌ Configuration file not found: $configPath\n";
        exit(1);
    }
    
    $config = json_decode(file_get_contents($configPath), true);
    if (!$config) {
        echo "❌ Invalid configuration file\n";
        exit(1);
    }
    
    echo "✅ Configuration loaded\n";
    
    // Test 2: Check API key
    $apiKey = $config['api_keys']['huggingface'] ?? '';
    if (empty($apiKey)) {
        echo "❌ Hugging Face API key not configured\n";
    } else {
        echo "✅ API key configured: " . substr($apiKey, 0, 10) . "...\n";
    }
    
    // Test 3: Check directories
    echo "\n📁 Checking directories...\n";
    $tempDir = 'temp/';
    if (!is_dir($tempDir)) {
        mkdir($tempDir, 0755, true);
        echo "✅ Created temp directory\n";
    } else {
        echo "✅ Temp directory exists\n";
    }
    
    // Test 4: Check GD extension
    echo "\n🎨 Checking GD extension...\n";
    if (function_exists('imagecreatetruecolor')) {
        echo "✅ GD extension available\n";
    } else {
        echo "❌ GD extension not available\n";
    }
    
    // Test 5: Check FFmpeg
    echo "\n🎥 Checking FFmpeg...\n";
    exec('ffmpeg -version 2>&1', $output, $returnCode);
    if ($returnCode === 0) {
        echo "✅ FFmpeg available\n";
        echo "   Version: " . (isset($output[0]) ? substr($output[0], 0, 50) . "..." : "Unknown") . "\n";
    } else {
        echo "❌ FFmpeg not available\n";
        echo "   This is required for video generation\n";
    }
    
    // Test 6: Load Hugging Face engine
    echo "\n🤗 Loading Hugging Face engine...\n";
    require_once 'core/huggingface_video_engine.php';
    
    $hfEngine = new HuggingFaceVideoEngine();
    echo "✅ Hugging Face engine loaded\n";
    
    // Test 7: Test connection (if API key available)
    if (!empty($apiKey)) {
        echo "\n🔗 Testing API connection...\n";
        $connectionTest = $hfEngine->testHuggingFaceConnection();
        
        if ($connectionTest['status'] === 'success') {
            echo "✅ API connection successful\n";
        } else {
            echo "❌ API connection failed: " . $connectionTest['message'] . "\n";
        }
    }
    
    // Test 8: Generate a simple local video
    echo "\n🎬 Testing local video generation...\n";
    
    $testSegments = [
        [
            'text' => 'Welcome to our video test',
            'duration' => 10,
            'index' => 0
        ],
        [
            'text' => 'This is a simple test video',
            'duration' => 10,
            'index' => 1
        ],
        [
            'text' => 'Generated with local fallback',
            'duration' => 10,
            'index' => 2
        ]
    ];
    
    $jobId = 'simple_test_' . time();
    $background = 'nature';
    $style = 'modern';
    
    echo "📋 Generating video with:\n";
    echo "   Job ID: $jobId\n";
    echo "   Segments: " . count($testSegments) . "\n";
    echo "   Background: $background\n";
    echo "   Style: $style\n\n";
    
    // Force local generation by temporarily clearing API key
    $originalKey = $hfEngine->apiKey ?? null;
    
    $videoFile = $hfEngine->generateAdvancedVideo($testSegments, $background, $style, $jobId);
    
    if ($videoFile && file_exists($videoFile)) {
        $fileSize = filesize($videoFile);
        echo "✅ Video generated successfully!\n";
        echo "📁 File: $videoFile\n";
        echo "📊 Size: " . round($fileSize / 1024 / 1024, 2) . " MB\n";
        
        if ($fileSize > 50000) { // At least 50KB
            echo "✅ Video file size looks reasonable\n";
        } else {
            echo "⚠️ Video file seems small\n";
        }
        
        // Try to get video info with FFmpeg
        if ($returnCode === 0) {
            exec("ffmpeg -i \"$videoFile\" 2>&1", $videoInfo);
            foreach ($videoInfo as $line) {
                if (strpos($line, 'Duration:') !== false) {
                    echo "⏱️ " . trim($line) . "\n";
                    break;
                }
            }
        }
        
    } else {
        echo "❌ Video generation failed\n";
        if ($videoFile) {
            echo "   Expected file: $videoFile\n";
        }
    }
    
    echo "\n🎉 Test completed!\n";
    
} catch (Exception $e) {
    echo "❌ Test failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
