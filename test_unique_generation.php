<?php
/**
 * Test Unique Content Generation with Open Source Models
 */

echo "🎭 Testing Unique Content Generation\n";
echo "===================================\n\n";

// Include required classes
require_once 'core/content_generator.php';
require_once 'core/openai_integration.php';
require_once 'core/advanced_voice_engine.php';

// Test 1: Content Generator
echo "📝 Testing Local Content Generator...\n";
try {
    $contentGenerator = new ContentGenerator();
    
    $baseContent = "Once upon a time, there was a clever crow who found a piece of cheese.";
    
    echo "Base content: $baseContent\n\n";
    
    $styles = ['funny', 'desi', 'emotional', 'bollywood'];
    $flowTypes = ['reel', 'audio_story', 'meme_rant'];
    
    foreach ($styles as $style) {
        foreach ($flowTypes as $flowType) {
            echo "🎨 Style: $style, Flow: $flowType\n";
            
            $uniqueContent = $contentGenerator->generateUniqueContent(
                $baseContent,
                $flowType,
                $style,
                'babu_rao'
            );
            
            echo "Generated: " . substr($uniqueContent['total_text'], 0, 100) . "...\n";
            echo "Length: " . strlen($uniqueContent['total_text']) . " characters\n\n";
        }
    }
    
    echo "✅ Local Content Generator working!\n\n";
    
} catch (Exception $e) {
    echo "❌ Content Generator failed: " . $e->getMessage() . "\n\n";
}

// Test 2: OpenAI Integration
echo "🤖 Testing OpenAI Integration...\n";
try {
    $openAI = new OpenAIIntegration();
    
    // Test connectivity
    $connectivity = $openAI->testConnectivity();
    echo "Connectivity status:\n";
    foreach ($connectivity as $provider => $status) {
        $statusText = $status ? '✅ Available' : '❌ Not available';
        echo "  - $provider: $statusText\n";
    }
    echo "\n";
    
    // Test content generation
    $prompt = "Create a funny story about an Indian family trying to use technology.";
    
    echo "Testing AI content generation...\n";
    echo "Prompt: $prompt\n\n";
    
    try {
        $aiResult = $openAI->generateUniqueContent($prompt, 'funny', 'reel', 200);
        
        echo "✅ AI Content Generated!\n";
        echo "Provider: " . $aiResult['provider'] . "\n";
        echo "Model: " . $aiResult['model'] . "\n";
        echo "Tokens: " . $aiResult['tokens_used'] . "\n";
        echo "Content: " . substr($aiResult['content'], 0, 200) . "...\n\n";
        
    } catch (Exception $e) {
        echo "⚠️  AI generation failed, using fallback: " . $e->getMessage() . "\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ OpenAI Integration failed: " . $e->getMessage() . "\n\n";
}

// Test 3: Advanced Voice Engine
echo "🎤 Testing Advanced Voice Engine...\n";
try {
    $advancedVoice = new AdvancedVoiceEngine();
    
    $testSegments = [
        [
            'text' => 'Hello everyone, this is a test of the advanced voice engine!',
            'voice_style' => 'energetic'
        ],
        [
            'text' => 'Can you hear the difference in speech patterns?',
            'voice_style' => 'normal'
        ]
    ];
    
    echo "Generating advanced voice samples...\n";
    
    $voiceFiles = $advancedVoice->generateAdvancedVoice($testSegments, 'babu_rao', 'test_' . time());
    
    echo "✅ Advanced Voice Engine working!\n";
    echo "Generated " . count($voiceFiles) . " voice files:\n";
    
    foreach ($voiceFiles as $index => $voiceFile) {
        echo "  - Segment $index: " . basename($voiceFile['file']) . " (" . round($voiceFile['duration'], 2) . "s)\n";
        
        if (file_exists($voiceFile['file'])) {
            $fileSize = filesize($voiceFile['file']);
            echo "    Size: " . round($fileSize / 1024, 1) . " KB\n";
        }
    }
    echo "\n";
    
} catch (Exception $e) {
    echo "❌ Advanced Voice Engine failed: " . $e->getMessage() . "\n\n";
}

// Test 4: Full Integration Test
echo "🔄 Testing Full Integration...\n";
try {
    // Simulate a full generation request
    $_POST = [
        'flow_type' => 'reel',
        'style' => 'funny',
        'voice_pack' => 'babu_rao',
        'background' => 'home',
        'content_source' => 'text',
        'content_text' => 'Indian parents discovering social media for the first time.'
    ];
    
    $_SERVER['REQUEST_METHOD'] = 'POST';
    
    echo "Simulating full generation with new engines...\n";
    echo "Content: " . $_POST['content_text'] . "\n";
    echo "Style: " . $_POST['style'] . ", Flow: " . $_POST['flow_type'] . "\n\n";
    
    // Test the generation process
    require_once 'generate.php';
    $generator = new SutradharGenerator();
    
    $result = $generator->handleRequest();
    $resultData = json_decode($result, true);
    
    if ($resultData && $resultData['success']) {
        echo "✅ Full integration test successful!\n";
        echo "Job ID: " . $resultData['job_id'] . "\n";
        
        // Wait and check status
        sleep(3);
        
        $_GET['action'] = 'status';
        $_GET['job_id'] = $resultData['job_id'];
        $_SERVER['REQUEST_METHOD'] = 'GET';
        
        $statusResult = $generator->handleRequest();
        $statusData = json_decode($statusResult, true);
        
        if ($statusData) {
            echo "Status: " . ($statusData['status'] ?? 'Unknown') . "\n";
            echo "Progress: " . ($statusData['progress'] ?? 0) . "%\n";
            echo "Message: " . ($statusData['message'] ?? 'No message') . "\n";
            
            if (isset($statusData['complete']) && $statusData['complete']) {
                echo "🎉 Generation completed with new engines!\n";
                
                if (isset($statusData['output'])) {
                    echo "\nGenerated files:\n";
                    foreach ($statusData['output'] as $type => $fileInfo) {
                        echo "  - $type: " . $fileInfo['size'] . "\n";
                    }
                }
            }
        }
        
    } else {
        echo "❌ Full integration test failed\n";
        if ($resultData && isset($resultData['error'])) {
            echo "Error: " . $resultData['error'] . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Full integration test failed: " . $e->getMessage() . "\n";
}

echo "\n🏁 Unique content generation testing completed!\n";

// Summary
echo "\n📊 Summary:\n";
echo "==========\n";
echo "✅ Local Content Generator: Creates unique variations of input content\n";
echo "🤖 AI Integration: Connects to open-source models for advanced content\n";
echo "🎤 Advanced Voice Engine: Generates realistic speech patterns\n";
echo "🔄 Full Pipeline: Integrates all components for unique content generation\n\n";

echo "🎯 Next Steps:\n";
echo "- Configure API keys in config/settings.json for external AI models\n";
echo "- Install Ollama locally for free LLM access\n";
echo "- Set up Hugging Face account for free inference API\n";
echo "- Test with different content types and styles\n";
?>
