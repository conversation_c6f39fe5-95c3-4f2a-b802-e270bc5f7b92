<?php
/**
 * Mood API - <PERSON>les requests for mood-based video generation
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Fallback classes for when core files don't exist
class FallbackMoodDatabase {
    public function getMoodList() {
        return [
            'positive_energy' => [
                'category' => ['name' => 'Positive Energy', 'icon' => '⚡'],
                'moods' => [
                    'euphoric' => ['name' => 'Euphoric', 'description' => 'Overwhelming joy and excitement'],
                    'inspiring' => ['name' => 'Inspiring', 'description' => 'Motivational and uplifting'],
                    'playful' => ['name' => 'Playful', 'description' => 'Fun and whimsical'],
                    'confident' => ['name' => 'Confident', 'description' => 'Self-assured and powerful'],
                    'adventurous' => ['name' => 'Adventurous', 'description' => 'Exciting exploration']
                ]
            ],
            'calm_peaceful' => [
                'category' => ['name' => 'Calm & Peaceful', 'icon' => '🕊️'],
                'moods' => [
                    'serene' => ['name' => 'Serene', 'description' => 'Peaceful and tranquil'],
                    'contemplative' => ['name' => 'Contemplative', 'description' => 'Thoughtful and reflective'],
                    'nostalgic' => ['name' => 'Nostalgic', 'description' => 'Wistful remembrance'],
                    'meditative' => ['name' => 'Meditative', 'description' => 'Deep inner peace'],
                    'melancholic' => ['name' => 'Melancholic', 'description' => 'Gentle sadness']
                ]
            ],
            'dramatic_intense' => [
                'category' => ['name' => 'Dramatic & Intense', 'icon' => '🎭'],
                'moods' => [
                    'passionate' => ['name' => 'Passionate', 'description' => 'Intense emotion and fervor'],
                    'heroic' => ['name' => 'Heroic', 'description' => 'Noble courage'],
                    'dramatic' => ['name' => 'Dramatic', 'description' => 'Theatrical and charged'],
                    'intense' => ['name' => 'Intense', 'description' => 'Raw power and emotion'],
                    'rebellious' => ['name' => 'Rebellious', 'description' => 'Defiant spirit']
                ]
            ]
        ];
    }

    public function getAllMoods() {
        $moodList = $this->getMoodList();
        $allMoods = [];
        foreach ($moodList as $category) {
            $allMoods = array_merge($allMoods, $category['moods']);
        }
        return $allMoods;
    }

    public function getCategories() {
        return ['positive_energy', 'calm_peaceful', 'dramatic_intense'];
    }
}

class FallbackPromptEngine {
    public function getTopicCategories() {
        return [
            'nature_wildlife' => [
                'name' => 'Nature & Wildlife',
                'description' => 'Natural environments and animals',
                'subtopics' => ['forest_animals', 'ocean_life', 'mountain_landscapes']
            ],
            'urban_city' => [
                'name' => 'Urban & City Life',
                'description' => 'Modern city environments',
                'subtopics' => ['city_skylines', 'street_art', 'urban_culture']
            ],
            'fantasy_magic' => [
                'name' => 'Fantasy & Magic',
                'description' => 'Magical worlds and creatures',
                'subtopics' => ['magical_forests', 'dragon_realms', 'fairy_kingdoms']
            ]
        ];
    }

    public function getImageTypes() {
        return [
            'cinematic' => [
                'name' => 'Cinematic',
                'description' => 'Movie-like dramatic scenes',
                'characteristics' => ['dramatic', 'professional', 'storytelling']
            ],
            'artistic' => [
                'name' => 'Artistic',
                'description' => 'Creative and expressive',
                'characteristics' => ['creative', 'expressive', 'unique']
            ],
            'photorealistic' => [
                'name' => 'Photorealistic',
                'description' => 'Highly detailed realistic',
                'characteristics' => ['realistic', 'detailed', 'natural']
            ]
        ];
    }

    public function validateParameters($mood, $topic, $imageType, $inspiration) {
        $errors = [];
        if (empty($mood)) $errors[] = 'Mood is required';
        if (empty($topic)) $errors[] = 'Topic is required';
        if (empty($imageType)) $errors[] = 'Image type is required';
        if (empty($inspiration)) $errors[] = 'Inspiration is required';
        return $errors;
    }

    public function generatePrompts($mood, $topic, $imageType, $inspiration, $count = 25) {
        $prompts = [];
        $strategies = ['mood_focused', 'topic_focused', 'image_focused', 'inspiration_focused'];

        for ($i = 0; $i < $count; $i++) {
            $strategy = $strategies[$i % count($strategies)];
            $prompts[] = [
                'content' => $this->generatePromptContent($mood, $topic, $imageType, $inspiration, $strategy),
                'strategy' => $strategy,
                'mood' => $mood,
                'topic' => $topic,
                'image_type' => $imageType,
                'score' => rand(75, 95)
            ];
        }

        return $prompts;
    }

    private function generatePromptContent($mood, $topic, $imageType, $inspiration, $strategy) {
        $templates = [
            'mood_focused' => "A {$mood} scene featuring {$topic} with {$imageType} style, inspired by {$inspiration}",
            'topic_focused' => "Detailed {$topic} scene with {$mood} atmosphere in {$imageType} style: {$inspiration}",
            'image_focused' => "{$imageType} style video of {$topic} with {$mood} mood, incorporating {$inspiration}",
            'inspiration_focused' => "Video inspired by {$inspiration}, showing {$topic} with {$mood} feeling in {$imageType} style"
        ];

        return $templates[$strategy] ?? $templates['mood_focused'];
    }

    public function getPromptStats($prompts) {
        return [
            'total' => count($prompts),
            'strategies' => array_count_values(array_column($prompts, 'strategy')),
            'avg_score' => array_sum(array_column($prompts, 'score')) / count($prompts)
        ];
    }
}

// Use fallback data if core files don't exist
$useFallback = !file_exists('../core/mood_database.php') || !file_exists('../core/prompt_generation_engine.php');

if (!$useFallback) {
    require_once '../core/mood_database.php';
    require_once '../core/prompt_generation_engine.php';
}

try {
    if ($useFallback) {
        $moodDatabase = new FallbackMoodDatabase();
        $promptEngine = new FallbackPromptEngine();
    } else {
        $moodDatabase = new MoodDatabase();
        $promptEngine = new PromptGenerationEngine();
    }
    
    // Get request method and data
    $method = $_SERVER['REQUEST_METHOD'];
    $input = null;
    
    if ($method === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? '';
    } else {
        $action = $_GET['action'] ?? '';
    }
    
    switch ($action) {
        case 'get_moods':
            handleGetMoods($moodDatabase);
            break;
            
        case 'get_topics':
            handleGetTopics($promptEngine);
            break;
            
        case 'get_image_types':
            handleGetImageTypes($promptEngine);
            break;
            
        case 'generate_prompts':
            handleGeneratePrompts($promptEngine, $input);
            break;
            
        case 'get_mood_details':
            handleGetMoodDetails($moodDatabase, $_GET['mood_id'] ?? '');
            break;
            
        case 'search_moods':
            handleSearchMoods($moodDatabase, $input);
            break;
            
        case 'get_complementary_moods':
            handleGetComplementaryMoods($moodDatabase, $_GET['mood_id'] ?? '');
            break;
            
        default:
            throw new Exception('Invalid action: ' . $action);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Handle get moods request
 */
function handleGetMoods($moodDatabase) {
    $moods = $moodDatabase->getMoodList();
    
    echo json_encode([
        'success' => true,
        'moods' => $moods,
        'total_moods' => count($moodDatabase->getAllMoods()),
        'categories' => $moodDatabase->getCategories()
    ]);
}

/**
 * Handle get topics request
 */
function handleGetTopics($promptEngine) {
    $topics = $promptEngine->getTopicCategories();
    
    echo json_encode([
        'success' => true,
        'topics' => $topics,
        'total_topics' => count($topics)
    ]);
}

/**
 * Handle get image types request
 */
function handleGetImageTypes($promptEngine) {
    $imageTypes = $promptEngine->getImageTypes();
    
    echo json_encode([
        'success' => true,
        'imageTypes' => $imageTypes,
        'total_types' => count($imageTypes)
    ]);
}

/**
 * Handle generate prompts request
 */
function handleGeneratePrompts($promptEngine, $input) {
    // Validate required parameters
    $required = ['mood', 'topic', 'imageType', 'inspiration'];
    foreach ($required as $param) {
        if (empty($input[$param])) {
            throw new Exception("Missing required parameter: {$param}");
        }
    }
    
    $moodId = $input['mood'];
    $topicCategory = $input['topic'];
    $imageType = $input['imageType'];
    $inspiration = $input['inspiration'];
    $count = $input['count'] ?? 25;
    
    // Validate parameters
    $errors = $promptEngine->validateParameters($moodId, $topicCategory, $imageType, $inspiration);
    if (!empty($errors)) {
        throw new Exception('Validation errors: ' . implode(', ', $errors));
    }
    
    // Generate prompts
    $startTime = microtime(true);
    $prompts = $promptEngine->generatePrompts($moodId, $topicCategory, $imageType, $inspiration, $count);
    $generationTime = microtime(true) - $startTime;
    
    // Get statistics
    $stats = $promptEngine->getPromptStats($prompts);
    
    echo json_encode([
        'success' => true,
        'prompts' => $prompts,
        'stats' => $stats,
        'generation_time' => round($generationTime, 3),
        'parameters' => [
            'mood' => $moodId,
            'topic' => $topicCategory,
            'image_type' => $imageType,
            'inspiration' => $inspiration,
            'count' => $count
        ]
    ]);
}

/**
 * Handle get mood details request
 */
function handleGetMoodDetails($moodDatabase, $moodId) {
    if (empty($moodId)) {
        throw new Exception('Mood ID is required');
    }
    
    $mood = $moodDatabase->getMood($moodId);
    if (!$mood) {
        throw new Exception('Mood not found: ' . $moodId);
    }
    
    $characteristics = $moodDatabase->getMoodCharacteristics($moodId);
    $complementary = $moodDatabase->getComplementaryMoods($moodId, 5);
    
    echo json_encode([
        'success' => true,
        'mood' => $mood,
        'characteristics' => $characteristics,
        'complementary_moods' => $complementary
    ]);
}

/**
 * Handle search moods request
 */
function handleSearchMoods($moodDatabase, $input) {
    $criteria = $input['criteria'] ?? [];
    
    if (empty($criteria)) {
        throw new Exception('Search criteria is required');
    }
    
    $results = $moodDatabase->searchMoods($criteria);
    
    echo json_encode([
        'success' => true,
        'results' => $results,
        'total_results' => count($results),
        'criteria' => $criteria
    ]);
}

/**
 * Handle get complementary moods request
 */
function handleGetComplementaryMoods($moodDatabase, $moodId) {
    if (empty($moodId)) {
        throw new Exception('Mood ID is required');
    }
    
    $complementary = $moodDatabase->getComplementaryMoods($moodId, 8);
    
    echo json_encode([
        'success' => true,
        'complementary_moods' => $complementary,
        'base_mood' => $moodId
    ]);
}

/**
 * Log API usage for analytics
 */
function logApiUsage($action, $parameters = []) {
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'action' => $action,
        'parameters' => $parameters,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];
    
    $logFile = __DIR__ . '/../data/api_usage.log';
    file_put_contents($logFile, json_encode($logData) . "\n", FILE_APPEND | LOCK_EX);
}

/**
 * Rate limiting function
 */
function checkRateLimit($action) {
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $cacheFile = __DIR__ . '/../temp/rate_limit_' . md5($ip) . '.json';
    
    $limits = [
        'get_moods' => ['requests' => 100, 'window' => 3600], // 100 per hour
        'generate_prompts' => ['requests' => 50, 'window' => 3600], // 50 per hour
        'default' => ['requests' => 200, 'window' => 3600] // 200 per hour
    ];
    
    $limit = $limits[$action] ?? $limits['default'];
    
    if (file_exists($cacheFile)) {
        $data = json_decode(file_get_contents($cacheFile), true);
        $now = time();
        
        // Clean old entries
        $data = array_filter($data, function($timestamp) use ($now, $limit) {
            return ($now - $timestamp) < $limit['window'];
        });
        
        if (count($data) >= $limit['requests']) {
            throw new Exception('Rate limit exceeded. Please try again later.');
        }
        
        $data[] = $now;
    } else {
        $data = [time()];
    }
    
    file_put_contents($cacheFile, json_encode($data));
}

/**
 * Cache response for performance
 */
function getCachedResponse($cacheKey, $ttl = 3600) {
    $cacheFile = __DIR__ . '/../temp/cache_' . md5($cacheKey) . '.json';
    
    if (file_exists($cacheFile)) {
        $cacheData = json_decode(file_get_contents($cacheFile), true);
        
        if (time() - $cacheData['timestamp'] < $ttl) {
            return $cacheData['data'];
        }
    }
    
    return null;
}

/**
 * Store response in cache
 */
function setCachedResponse($cacheKey, $data) {
    $cacheFile = __DIR__ . '/../temp/cache_' . md5($cacheKey) . '.json';
    $cacheData = [
        'timestamp' => time(),
        'data' => $data
    ];
    
    file_put_contents($cacheFile, json_encode($cacheData));
}
?>
