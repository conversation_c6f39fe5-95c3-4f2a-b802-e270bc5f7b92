# Installation Guide

## 🚀 Quick Start

This guide will help you set up the Sutradhar Video Generation Platform on a Windows development environment using XAMPP.

## 📋 Prerequisites

### System Requirements
- **Operating System**: Windows 10/11 (64-bit)
- **RAM**: Minimum 8GB, Recommended 16GB
- **Storage**: 10GB free space
- **Internet**: Stable connection for API integrations

### Required Software
- **XAMPP**: 8.1.0 or higher
- **Git**: For version control
- **Text Editor**: VS Code, PhpStorm, or similar
- **Web Browser**: Chrome, Firefox, or Edge (latest versions)

## 🔧 Step 1: Install XAMPP

### Download and Install
1. Visit [https://www.apachefriends.org/](https://www.apachefriends.org/)
2. Download XAMPP for Windows (PHP 8.1 or higher)
3. Run the installer as Administrator
4. Select components:
   - ✅ Apache
   - ✅ MySQL
   - ✅ PHP
   - ✅ phpMyAdmin
   - ❌ Mercury (not needed)
   - ❌ Tomcat (not needed)

### Configure XAMPP
1. Install to `C:\xampp` (default location)
2. Start XAMPP Control Panel
3. Start **Apache** and **MySQL** services
4. Verify installation by visiting `http://localhost`

### Enable Required PHP Extensions
Edit `C:\xampp\php\php.ini`:

```ini
; Uncomment these lines (remove semicolon)
extension=gd
extension=curl
extension=pdo_mysql
extension=mbstring
extension=openssl
extension=fileinfo
extension=zip

; Increase memory limits
memory_limit = 1024M
upload_max_filesize = 50M
post_max_size = 50M
max_execution_time = 300
max_input_time = 300
```

**Restart Apache** after making changes.

## 📁 Step 2: Install FFmpeg

### Download FFmpeg
1. Visit [https://ffmpeg.org/download.html](https://ffmpeg.org/download.html)
2. Download the Windows build (static version recommended)
3. Extract to `C:\ffmpeg`

### Add to System PATH
1. Open **System Properties** → **Environment Variables**
2. Edit **System PATH** variable
3. Add `C:\ffmpeg\bin`
4. Click **OK** and restart Command Prompt

### Verify Installation
```cmd
ffmpeg -version
```
You should see FFmpeg version information.

## 🗂️ Step 3: Setup Project

### Clone Repository
```bash
cd C:\xampp\htdocs
git clone [repository-url] sutradhar
cd sutradhar
```

### Set Directory Permissions
Ensure these directories are writable:
```
temp/
public/videos/
public/thumbnails/
public/uploads/
assets/generated/
```

Right-click each folder → **Properties** → **Security** → **Edit** → Grant **Full Control** to **Users**.

## 🗄️ Step 4: Database Setup

### Create Database
1. Open phpMyAdmin: `http://localhost/phpmyadmin`
2. Click **New** to create database
3. Database name: `sutradhar_db`
4. Collation: `utf8mb4_unicode_ci`
5. Click **Create**

### Import Database Schema
```sql
-- Run this SQL in phpMyAdmin
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    credits_available INT DEFAULT 50,
    subscription_type ENUM('free', 'pro', 'business', 'enterprise') DEFAULT 'free',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE TABLE generation_jobs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    job_id VARCHAR(255) UNIQUE NOT NULL,
    status ENUM('queued', 'processing', 'completed', 'failed') DEFAULT 'queued',
    progress INT DEFAULT 0,
    video_url VARCHAR(500),
    thumbnail_url VARCHAR(500),
    duration INT DEFAULT 30,
    credits_used INT DEFAULT 10,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE TABLE tool_usage (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    tool_name VARCHAR(100) NOT NULL,
    usage_count INT DEFAULT 1,
    credits_used INT DEFAULT 1,
    processing_time DECIMAL(5,2),
    last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE TABLE subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    plan_type ENUM('free', 'pro', 'business', 'enterprise') NOT NULL,
    status ENUM('active', 'cancelled', 'expired') DEFAULT 'active',
    credits_monthly INT NOT NULL,
    price_monthly DECIMAL(10,2),
    expires_at TIMESTAMP,
    auto_renew BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE TABLE payment_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    transaction_id VARCHAR(255) UNIQUE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    payment_method VARCHAR(50),
    credits_purchased INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

## ⚙️ Step 5: Configuration

### Create Configuration File
Copy and edit the configuration:

```bash
cp config/settings.example.json config/settings.json
```

Edit `config/settings.json`:

```json
{
    "database": {
        "host": "localhost",
        "username": "root",
        "password": "",
        "database": "sutradhar_db",
        "charset": "utf8mb4"
    },
    "api_keys": {
        "huggingface": "YOUR_HUGGINGFACE_API_KEY",
        "stripe_public": "YOUR_STRIPE_PUBLIC_KEY",
        "stripe_secret": "YOUR_STRIPE_SECRET_KEY",
        "paypal_client_id": "YOUR_PAYPAL_CLIENT_ID",
        "paypal_secret": "YOUR_PAYPAL_SECRET"
    },
    "video_generation": {
        "default_duration": 30,
        "max_duration": 60,
        "output_format": "mp4",
        "quality": "high",
        "models": {
            "text_to_image": "stabilityai/stable-diffusion-2-1",
            "text_to_image_backup": "runwayml/stable-diffusion-v1-5",
            "image_to_video": "ali-vilab/text-to-video-ms-1.7b"
        }
    },
    "tools": {
        "max_file_size": 52428800,
        "allowed_formats": ["jpg", "jpeg", "png", "gif", "webp", "bmp", "tiff"],
        "temp_cleanup_hours": 24,
        "rate_limits": {
            "free": 10,
            "pro": 100,
            "business": 1000,
            "enterprise": -1
        }
    },
    "security": {
        "session_lifetime": 86400,
        "csrf_token_lifetime": 3600,
        "max_login_attempts": 5,
        "lockout_duration": 900
    },
    "file_paths": {
        "temp_dir": "temp/",
        "upload_dir": "public/uploads/",
        "video_dir": "public/videos/",
        "thumbnail_dir": "public/thumbnails/"
    }
}
```

### Get API Keys

#### Hugging Face API Key
1. Visit [https://huggingface.co/](https://huggingface.co/)
2. Create account and verify email
3. Go to **Settings** → **Access Tokens**
4. Create new token with **Read** permissions
5. Copy token to configuration

#### Stripe API Keys (Optional)
1. Visit [https://stripe.com/](https://stripe.com/)
2. Create account and complete verification
3. Go to **Developers** → **API Keys**
4. Copy **Publishable key** and **Secret key**
5. Add to configuration

## 🧪 Step 6: Testing Installation

### Run System Tests
```bash
cd C:\xampp\htdocs\sutradhar
php test_installation.php
```

### Create Test User
```bash
php create_test_user.php
```

### Test Video Generation
1. Open `http://localhost/sutradhar`
2. Register new account or login with test user
3. Try generating a test video
4. Verify all tools are accessible

## 🔧 Step 7: Development Setup

### Enable Error Reporting
Add to `public/.htaccess`:
```apache
# Development only - remove in production
php_flag display_errors on
php_value error_reporting E_ALL
```

### Setup IDE
For **VS Code**, install extensions:
- PHP Intelephense
- PHP Debug
- HTML CSS Support
- JavaScript (ES6) code snippets

### Configure Debugging
Add to `php.ini`:
```ini
; Enable Xdebug for development
zend_extension=xdebug
xdebug.mode=debug
xdebug.start_with_request=yes
xdebug.client_host=127.0.0.1
xdebug.client_port=9003
```

## ✅ Verification Checklist

- [ ] XAMPP installed and running
- [ ] PHP extensions enabled (GD, cURL, PDO)
- [ ] FFmpeg installed and in PATH
- [ ] Database created and schema imported
- [ ] Configuration file created with API keys
- [ ] Directory permissions set correctly
- [ ] Test user created successfully
- [ ] Video generation working
- [ ] Tools accessible and functional
- [ ] No PHP errors in logs

## 🚨 Troubleshooting

### Common Issues

**Apache won't start**:
- Check if port 80 is in use
- Run XAMPP as Administrator
- Check Windows Firewall settings

**PHP extensions not loading**:
- Verify `php.ini` path in phpinfo()
- Restart Apache after changes
- Check extension files exist in `ext/` directory

**Database connection failed**:
- Verify MySQL is running
- Check database credentials
- Ensure database exists

**FFmpeg not found**:
- Verify PATH environment variable
- Restart Command Prompt/IDE
- Check FFmpeg executable permissions

**File upload issues**:
- Check directory permissions
- Verify `upload_max_filesize` setting
- Ensure temp directory exists

### Getting Help
- Check error logs in `C:\xampp\apache\logs\error.log`
- Enable PHP error display for debugging
- Verify all requirements are met
- Check firewall and antivirus settings

Your Sutradhar installation should now be ready for development and testing!
