/**
 * <PERSON><PERSON><PERSON><PERSON> Tools Application
 * Main application class for the tools interface
 */

class ToolsApp {
    constructor() {
        this.tools = [];
        this.filteredTools = [];
        this.currentCategory = 'all';
        this.currentView = 'grid';
        this.showFavoritesOnly = false;
        this.searchQuery = '';
        this.userCredits = 0;
        this.favorites = new Set();
        
        // API client
        this.api = new APIClient('/api_unified.php');
        
        // UI elements
        this.elements = {};
        
        // Debounced search
        this.searchDebounce = this.debounce(this.performSearch.bind(this), 300);
    }
    
    /**
     * Initialize the application
     */
    async init() {
        this.bindElements();
        this.bindEvents();
        this.setupKeyboardShortcuts();
        
        try {
            await this.loadUserData();
            await this.loadTools();
            this.renderTools();
            this.updateStats();
        } catch (error) {
            console.error('Failed to initialize tools app:', error);
            this.showNotification('Failed to load tools. Please refresh the page.', 'error');
        }
    }
    
    /**
     * Bind DOM elements
     */
    bindElements() {
        this.elements = {
            // Search and filters
            searchInput: document.getElementById('tools-search'),
            searchClear: document.getElementById('search-clear'),
            categoryTabs: document.getElementById('category-tabs'),
            viewButtons: document.querySelectorAll('.view-btn'),
            favoritesToggle: document.getElementById('favorites-toggle'),
            
            // Grid and states
            toolsGrid: document.getElementById('tools-grid'),
            loadingState: document.getElementById('loading-state'),
            emptyState: document.getElementById('empty-state'),
            
            // User interface
            userCredits: document.getElementById('user-credits'),
            
            // Modal
            toolModal: document.getElementById('tool-modal'),
            modalTitle: document.getElementById('modal-title'),
            modalContent: document.getElementById('modal-content'),
            modalClose: document.getElementById('modal-close'),
            
            // Upload
            uploadDropzone: document.getElementById('upload-dropzone'),
            
            // Notifications
            notifications: document.getElementById('notifications')
        };
    }
    
    /**
     * Bind event listeners
     */
    bindEvents() {
        // Search
        this.elements.searchInput.addEventListener('input', (e) => {
            this.searchQuery = e.target.value.trim();
            this.searchDebounce();
            this.toggleSearchClear();
        });
        
        this.elements.searchClear.addEventListener('click', () => {
            this.clearSearch();
        });
        
        // Category tabs
        this.elements.categoryTabs.addEventListener('click', (e) => {
            const tabBtn = e.target.closest('.tab-btn');
            if (tabBtn) {
                this.setCategory(tabBtn.dataset.category);
            }
        });
        
        // View controls
        this.elements.viewButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                this.setView(btn.dataset.view);
            });
        });
        
        this.elements.favoritesToggle.addEventListener('click', () => {
            this.toggleFavorites();
        });
        
        // Modal
        this.elements.modalClose.addEventListener('click', () => {
            this.closeModal();
        });
        
        this.elements.toolModal.addEventListener('click', (e) => {
            if (e.target === this.elements.toolModal) {
                this.closeModal();
            }
        });
        
        // Quick actions
        document.querySelectorAll('.action-card').forEach(card => {
            card.addEventListener('click', () => {
                this.handleQuickAction(card.dataset.action);
            });
        });
        
        // File drag and drop
        this.setupDragAndDrop();
    }
    
    /**
     * Setup keyboard shortcuts
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K for search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                this.elements.searchInput.focus();
            }
            
            // Number keys for categories
            if (e.key >= '1' && e.key <= '5' && !e.ctrlKey && !e.metaKey) {
                const categories = ['all', 'image_processing', 'generators', 'utilities', 'advanced'];
                const index = parseInt(e.key) - 1;
                if (categories[index]) {
                    this.setCategory(categories[index]);
                }
            }
        });
    }
    
    /**
     * Load user data
     */
    async loadUserData() {
        try {
            const userData = await this.api.request('user/profile');
            this.userCredits = userData.credits_available || 0;
            this.updateCreditsDisplay();
            
            // Load user favorites
            const favoritesData = await this.api.request('tools/favorites');
            this.favorites = new Set(favoritesData.map(fav => fav.tool_id));
            
        } catch (error) {
            console.error('Failed to load user data:', error);
            this.userCredits = 0;
            this.updateCreditsDisplay();
        }
    }
    
    /**
     * Load available tools
     */
    async loadTools() {
        try {
            const toolsData = await this.api.request('tools/list');
            this.tools = this.flattenToolsData(toolsData.categories);
            this.filteredTools = [...this.tools];
            
        } catch (error) {
            console.error('Failed to load tools:', error);
            throw error;
        }
    }
    
    /**
     * Flatten tools data from categories
     */
    flattenToolsData(categories) {
        const tools = [];
        
        Object.entries(categories).forEach(([categoryId, categoryData]) => {
            if (categoryData.tools) {
                categoryData.tools.forEach(tool => {
                    tools.push({
                        ...tool,
                        category_id: categoryId,
                        category_name: categoryData.display_name,
                        is_favorite: this.favorites.has(tool.tool_id)
                    });
                });
            }
        });
        
        return tools;
    }
    
    /**
     * Render tools grid
     */
    renderTools() {
        const grid = this.elements.toolsGrid;
        
        // Hide loading state
        this.elements.loadingState.style.display = 'none';
        
        // Check if we have tools to show
        if (this.filteredTools.length === 0) {
            this.elements.emptyState.style.display = 'block';
            grid.innerHTML = '';
            return;
        }
        
        this.elements.emptyState.style.display = 'none';
        
        // Render tool cards
        grid.innerHTML = this.filteredTools.map(tool => this.createToolCard(tool)).join('');
        
        // Add event listeners to tool cards
        this.bindToolCardEvents();
    }
    
    /**
     * Create tool card HTML
     */
    createToolCard(tool) {
        const favoriteClass = tool.is_favorite ? 'favorited' : '';
        const favoriteIcon = tool.is_favorite ? 'heart' : 'heart';
        
        return `
            <div class="tool-card" data-tool-id="${tool.tool_id}">
                <div class="tool-header">
                    <div class="tool-icon">
                        <i data-feather="${tool.icon || 'tool'}"></i>
                    </div>
                    <div class="tool-info">
                        <h3>${tool.name}</h3>
                        <div class="tool-category">${tool.category_name}</div>
                    </div>
                </div>
                
                <div class="tool-description">
                    ${tool.description}
                </div>
                
                <div class="tool-footer">
                    <div class="tool-credits">
                        <i data-feather="star"></i>
                        <span>${tool.credit_cost} credit${tool.credit_cost !== 1 ? 's' : ''}</span>
                    </div>
                    
                    <div class="tool-actions">
                        <button class="btn-icon favorite-btn ${favoriteClass}" 
                                data-tool-id="${tool.tool_id}" 
                                title="${tool.is_favorite ? 'Remove from favorites' : 'Add to favorites'}">
                            <i data-feather="${favoriteIcon}"></i>
                        </button>
                        <button class="btn-icon info-btn" 
                                data-tool-id="${tool.tool_id}" 
                                title="Tool information">
                            <i data-feather="info"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Bind tool card events
     */
    bindToolCardEvents() {
        // Tool card clicks
        document.querySelectorAll('.tool-card').forEach(card => {
            card.addEventListener('click', (e) => {
                // Don't trigger if clicking on action buttons
                if (e.target.closest('.tool-actions')) return;
                
                const toolId = card.dataset.toolId;
                this.openTool(toolId);
            });
        });
        
        // Favorite buttons
        document.querySelectorAll('.favorite-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const toolId = btn.dataset.toolId;
                this.toggleFavorite(toolId);
            });
        });
        
        // Info buttons
        document.querySelectorAll('.info-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const toolId = btn.dataset.toolId;
                this.showToolInfo(toolId);
            });
        });
        
        // Re-initialize Feather icons
        feather.replace();
    }
    
    /**
     * Filter tools based on current criteria
     */
    filterTools() {
        let filtered = [...this.tools];
        
        // Category filter
        if (this.currentCategory !== 'all') {
            filtered = filtered.filter(tool => tool.category_id === this.currentCategory);
        }
        
        // Search filter
        if (this.searchQuery) {
            const query = this.searchQuery.toLowerCase();
            filtered = filtered.filter(tool => 
                tool.name.toLowerCase().includes(query) ||
                tool.description.toLowerCase().includes(query) ||
                tool.category_name.toLowerCase().includes(query)
            );
        }
        
        // Favorites filter
        if (this.showFavoritesOnly) {
            filtered = filtered.filter(tool => tool.is_favorite);
        }
        
        this.filteredTools = filtered;
    }
    
    /**
     * Perform search
     */
    performSearch() {
        this.filterTools();
        this.renderTools();
    }
    
    /**
     * Set active category
     */
    setCategory(category) {
        this.currentCategory = category;
        
        // Update active tab
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.category === category);
        });
        
        this.filterTools();
        this.renderTools();
    }
    
    /**
     * Set view mode
     */
    setView(view) {
        this.currentView = view;
        
        // Update active view button
        this.elements.viewButtons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.view === view);
        });
        
        // Update grid class
        this.elements.toolsGrid.className = `tools-grid view-${view}`;
    }
    
    /**
     * Toggle favorites filter
     */
    toggleFavorites() {
        this.showFavoritesOnly = !this.showFavoritesOnly;
        this.elements.favoritesToggle.classList.toggle('active', this.showFavoritesOnly);
        
        this.filterTools();
        this.renderTools();
    }
    
    /**
     * Clear search
     */
    clearSearch() {
        this.searchQuery = '';
        this.elements.searchInput.value = '';
        this.toggleSearchClear();
        this.performSearch();
    }
    
    /**
     * Toggle search clear button
     */
    toggleSearchClear() {
        const show = this.elements.searchInput.value.length > 0;
        this.elements.searchClear.style.display = show ? 'block' : 'none';
    }
    
    /**
     * Update credits display
     */
    updateCreditsDisplay() {
        this.elements.userCredits.textContent = this.userCredits.toLocaleString();
    }
    
    /**
     * Update statistics
     */
    updateStats() {
        document.getElementById('total-tools').textContent = `${this.tools.length}+`;
        // Additional stats would be loaded from API
    }
    
    /**
     * Utility function for debouncing
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    /**
     * Open tool interface
     */
    async openTool(toolId) {
        const tool = this.tools.find(t => t.tool_id === toolId);
        if (!tool) return;

        // Check credits
        if (this.userCredits < tool.credit_cost) {
            this.showNotification(`Insufficient credits. Need ${tool.credit_cost}, have ${this.userCredits}`, 'error');
            return;
        }

        this.elements.modalTitle.textContent = tool.name;
        this.elements.modalContent.innerHTML = this.createToolInterface(tool);
        this.showModal();
    }

    /**
     * Create tool interface
     */
    createToolInterface(tool) {
        return `
            <div class="tool-interface">
                <div class="tool-description">
                    <p>${tool.description}</p>
                </div>

                <div class="tool-upload">
                    <div class="upload-area" id="upload-area-${tool.tool_id}">
                        <div class="upload-icon">
                            <i data-feather="upload"></i>
                        </div>
                        <h3>Upload Files</h3>
                        <p>Drag and drop files here or click to browse</p>
                        <input type="file" id="file-input-${tool.tool_id}" multiple accept="${this.getAcceptedFormats(tool)}" style="display: none;">
                        <button class="btn-primary" onclick="document.getElementById('file-input-${tool.tool_id}').click()">
                            Choose Files
                        </button>
                    </div>
                </div>

                <div class="tool-options" id="tool-options-${tool.tool_id}">
                    ${this.createToolOptions(tool)}
                </div>

                <div class="tool-actions">
                    <button class="btn-primary" id="process-btn-${tool.tool_id}" disabled>
                        <i data-feather="play"></i>
                        Process Files (${tool.credit_cost} credits)
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Show modal
     */
    showModal() {
        this.elements.toolModal.classList.add('active');
        document.body.style.overflow = 'hidden';

        // Re-initialize Feather icons
        setTimeout(() => feather.replace(), 100);
    }

    /**
     * Close modal
     */
    closeModal() {
        this.elements.toolModal.classList.remove('active');
        document.body.style.overflow = '';
    }

    /**
     * Toggle favorite status
     */
    async toggleFavorite(toolId) {
        try {
            const tool = this.tools.find(t => t.tool_id === toolId);
            const isFavorite = this.favorites.has(toolId);

            if (isFavorite) {
                await this.api.request('tools/favorites/remove', 'POST', { tool_id: toolId });
                this.favorites.delete(toolId);
                tool.is_favorite = false;
            } else {
                await this.api.request('tools/favorites/add', 'POST', { tool_id: toolId });
                this.favorites.add(toolId);
                tool.is_favorite = true;
            }

            this.renderTools();
            this.showNotification(
                isFavorite ? 'Removed from favorites' : 'Added to favorites',
                'success'
            );

        } catch (error) {
            console.error('Failed to toggle favorite:', error);
            this.showNotification('Failed to update favorites', 'error');
        }
    }

    /**
     * Show tool information
     */
    showToolInfo(toolId) {
        const tool = this.tools.find(t => t.tool_id === toolId);
        if (!tool) return;

        this.elements.modalTitle.textContent = `${tool.name} - Information`;
        this.elements.modalContent.innerHTML = `
            <div class="tool-info-detailed">
                <div class="info-section">
                    <h3>Description</h3>
                    <p>${tool.description}</p>
                </div>

                <div class="info-section">
                    <h3>Specifications</h3>
                    <ul>
                        <li><strong>Credit Cost:</strong> ${tool.credit_cost} credit${tool.credit_cost !== 1 ? 's' : ''}</li>
                        <li><strong>Max File Size:</strong> ${this.formatFileSize(tool.max_file_size)}</li>
                        <li><strong>Max Files:</strong> ${tool.max_files_per_request}</li>
                        <li><strong>Supported Formats:</strong> ${tool.allowed_formats.join(', ').toUpperCase()}</li>
                    </ul>
                </div>

                <div class="info-actions">
                    <button class="btn-primary" onclick="window.toolsApp.openTool('${toolId}')">
                        <i data-feather="play"></i>
                        Use This Tool
                    </button>
                </div>
            </div>
        `;
        this.showModal();
    }

    /**
     * Handle quick actions
     */
    handleQuickAction(action) {
        const actionMap = {
            'upload-convert': 'format_converter',
            'qr-generator': 'qr_generator',
            'background-remove': 'background_remover',
            'batch-resize': 'smart_resizer'
        };

        const toolId = actionMap[action];
        if (toolId) {
            this.openTool(toolId);
        }
    }

    /**
     * Setup drag and drop
     */
    setupDragAndDrop() {
        let dragCounter = 0;

        document.addEventListener('dragenter', (e) => {
            e.preventDefault();
            dragCounter++;
            if (dragCounter === 1) {
                this.elements.uploadDropzone.style.display = 'flex';
            }
        });

        document.addEventListener('dragleave', (e) => {
            e.preventDefault();
            dragCounter--;
            if (dragCounter === 0) {
                this.elements.uploadDropzone.style.display = 'none';
            }
        });

        document.addEventListener('dragover', (e) => {
            e.preventDefault();
        });

        document.addEventListener('drop', (e) => {
            e.preventDefault();
            dragCounter = 0;
            this.elements.uploadDropzone.style.display = 'none';

            const files = Array.from(e.dataTransfer.files);
            if (files.length > 0) {
                this.handleFilesDrop(files);
            }
        });
    }

    /**
     * Handle dropped files
     */
    handleFilesDrop(files) {
        // Determine best tool based on file types
        const imageFiles = files.filter(f => f.type.startsWith('image/'));

        if (imageFiles.length > 0) {
            // Default to format converter for images
            this.openTool('format_converter');
        } else {
            this.showNotification('Please select a tool first, then upload files', 'warning');
        }
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <p>${message}</p>
            </div>
        `;

        this.elements.notifications.appendChild(notification);

        // Trigger animation
        setTimeout(() => notification.classList.add('show'), 100);

        // Auto remove
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);
    }

    /**
     * Utility methods
     */
    getAcceptedFormats(tool) {
        return tool.allowed_formats.map(format => `.${format}`).join(',');
    }

    formatFileSize(bytes) {
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        if (bytes === 0) return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    createToolOptions(tool) {
        // This would create tool-specific options based on tool.processing_options
        return '<p>Tool options would be rendered here based on the specific tool requirements.</p>';
    }
}

/**
 * API Client for making requests
 */
class APIClient {
    constructor(baseUrl) {
        this.baseUrl = baseUrl;
        this.csrfToken = document.querySelector('meta[name="csrf-token"]')?.content || '';
    }
    
    async request(endpoint, method = 'GET', data = null) {
        const config = {
            method,
            credentials: 'include',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        };
        
        if (method === 'POST') {
            if (data instanceof FormData) {
                config.headers['X-CSRF-Token'] = this.csrfToken;
                config.body = data;
            } else {
                config.headers['Content-Type'] = 'application/json';
                config.headers['X-CSRF-Token'] = this.csrfToken;
                config.body = JSON.stringify(data);
            }
        }
        
        try {
            const response = await fetch(`${this.baseUrl}?endpoint=${endpoint}`, config);
            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.error || 'Request failed');
            }
            
            return result.data;
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }
}
