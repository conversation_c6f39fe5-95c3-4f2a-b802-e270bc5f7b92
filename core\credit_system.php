<?php
/**
 * Credit System - Manages credit-based pricing for Sutradhar 2070
 * Handles credit consumption, tracking, balance management, and purchase functionality
 */

require_once 'database_manager.php';
require_once 'email_service.php';

class CreditSystem {
    private $db;
    private $emailService;
    private $creditCosts;
    private $creditPackages;
    private $lowCreditThreshold;
    
    public function __construct() {
        $this->db = new DatabaseManager();
        $this->emailService = new EmailService();
        $this->lowCreditThreshold = 10;
        
        $this->initializeCreditCosts();
        $this->initializeCreditPackages();
        $this->initializeDatabase();
    }
    
    /**
     * Initialize credit costs for different generation types
     */
    private function initializeCreditCosts() {
        $this->creditCosts = [
            'basic_generation' => [
                'cost' => 10,
                'name' => 'Basic Generation',
                'description' => 'Standard video generation with basic features',
                'features' => ['Standard quality', 'Basic templates', 'Watermarked output']
            ],
            'mood_optimized' => [
                'cost' => 15,
                'name' => 'Mood-Optimized Generation',
                'description' => 'Enhanced generation with mood-based optimization',
                'features' => ['Mood analysis', 'Optimized prompts', 'Enhanced quality', 'Watermarked output']
            ],
            'premium_quality' => [
                'cost' => 25,
                'name' => 'Premium Quality',
                'description' => 'Highest quality generation with all features',
                'features' => ['HD quality', 'No watermarks', 'Priority processing', 'Advanced effects']
            ],
            'bulk_generation' => [
                'cost' => 8,
                'name' => 'Bulk Generation',
                'description' => 'Discounted rate for bulk video generation (10+ videos)',
                'features' => ['Bulk discount', 'Standard quality', 'Batch processing']
            ],
            'api_generation' => [
                'cost' => 12,
                'name' => 'API Generation',
                'description' => 'Video generation via API access',
                'features' => ['API access', 'Programmatic generation', 'Webhook support']
            ]
        ];
    }
    
    /**
     * Initialize credit packages for purchase
     */
    private function initializeCreditPackages() {
        $this->creditPackages = [
            'starter' => [
                'credits' => 100,
                'price' => 9.99,
                'currency' => 'USD',
                'name' => 'Starter Pack',
                'description' => 'Perfect for trying out premium features',
                'bonus_credits' => 0,
                'popular' => false
            ],
            'creator' => [
                'credits' => 500,
                'price' => 39.99,
                'currency' => 'USD',
                'name' => 'Creator Pack',
                'description' => 'Great for regular content creators',
                'bonus_credits' => 50,
                'popular' => true
            ],
            'professional' => [
                'credits' => 1000,
                'price' => 69.99,
                'currency' => 'USD',
                'name' => 'Professional Pack',
                'description' => 'Ideal for professional use',
                'bonus_credits' => 150,
                'popular' => false
            ],
            'enterprise' => [
                'credits' => 2500,
                'price' => 149.99,
                'currency' => 'USD',
                'name' => 'Enterprise Pack',
                'description' => 'Maximum value for heavy users',
                'bonus_credits' => 500,
                'popular' => false
            ]
        ];
    }
    
    /**
     * Get user credit balance
     */
    public function getUserCredits($userId) {
        $user = $this->db->selectOne('users', ['user_id' => $userId], ['credits']);
        return $user ? intval($user['credits']) : 0;
    }
    
    /**
     * Check if user has sufficient credits
     */
    public function hasCredits($userId, $generationType) {
        $userCredits = $this->getUserCredits($userId);
        $requiredCredits = $this->getCreditCost($generationType);
        
        return $userCredits >= $requiredCredits;
    }
    
    /**
     * Get credit cost for generation type
     */
    public function getCreditCost($generationType) {
        return $this->creditCosts[$generationType]['cost'] ?? 10;
    }
    
    /**
     * Consume credits for video generation
     */
    public function consumeCredits($userId, $generationType, $jobId = null) {
        try {
            $this->db->beginTransaction();
            
            // Get current credits
            $user = $this->db->selectOne('users', ['user_id' => $userId]);
            if (!$user) {
                throw new Exception('User not found');
            }
            
            $currentCredits = intval($user['credits']);
            $requiredCredits = $this->getCreditCost($generationType);
            
            // Check if user has enough credits
            if ($currentCredits < $requiredCredits) {
                throw new Exception('Insufficient credits');
            }
            
            // Deduct credits
            $newBalance = $currentCredits - $requiredCredits;
            $updateResult = $this->db->update('users', 
                ['credits' => $newBalance], 
                ['user_id' => $userId]
            );
            
            if (!$updateResult) {
                throw new Exception('Failed to update credit balance');
            }
            
            // Log credit transaction
            $transactionData = [
                'transaction_id' => uniqid('txn_'),
                'user_id' => $userId,
                'type' => 'consumption',
                'generation_type' => $generationType,
                'credits_amount' => -$requiredCredits,
                'balance_before' => $currentCredits,
                'balance_after' => $newBalance,
                'job_id' => $jobId,
                'created_at' => date('Y-m-d H:i:s'),
                'description' => $this->creditCosts[$generationType]['name'] ?? 'Video Generation'
            ];
            
            $this->db->insert('credit_transactions', $transactionData);
            
            $this->db->commit();
            
            // Check for low credits and send notification
            if ($newBalance <= $this->lowCreditThreshold && $newBalance > 0) {
                $this->sendLowCreditsNotification($userId, $newBalance);
            }
            
            return [
                'success' => true,
                'credits_consumed' => $requiredCredits,
                'new_balance' => $newBalance,
                'transaction_id' => $transactionData['transaction_id']
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Add credits to user account
     */
    public function addCredits($userId, $credits, $source = 'purchase', $description = null, $transactionId = null) {
        try {
            $this->db->beginTransaction();
            
            // Get current credits
            $user = $this->db->selectOne('users', ['user_id' => $userId]);
            if (!$user) {
                throw new Exception('User not found');
            }
            
            $currentCredits = intval($user['credits']);
            $newBalance = $currentCredits + $credits;
            
            // Update user credits
            $updateResult = $this->db->update('users', 
                ['credits' => $newBalance], 
                ['user_id' => $userId]
            );
            
            if (!$updateResult) {
                throw new Exception('Failed to update credit balance');
            }
            
            // Log credit transaction
            $transactionData = [
                'transaction_id' => $transactionId ?? uniqid('txn_'),
                'user_id' => $userId,
                'type' => $source,
                'generation_type' => null,
                'credits_amount' => $credits,
                'balance_before' => $currentCredits,
                'balance_after' => $newBalance,
                'created_at' => date('Y-m-d H:i:s'),
                'description' => $description ?? 'Credits added'
            ];
            
            $this->db->insert('credit_transactions', $transactionData);
            
            $this->db->commit();
            
            return [
                'success' => true,
                'credits_added' => $credits,
                'new_balance' => $newBalance,
                'transaction_id' => $transactionData['transaction_id']
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get credit transaction history
     */
    public function getCreditHistory($userId, $limit = 50, $offset = 0) {
        $transactions = $this->db->select(
            'credit_transactions',
            ['user_id' => $userId],
            '*',
            'created_at DESC',
            "$offset, $limit"
        );
        
        return $transactions;
    }
    
    /**
     * Get credit usage statistics
     */
    public function getCreditStats($userId, $period = '30 days') {
        $startDate = date('Y-m-d H:i:s', strtotime("-$period"));
        
        // Get consumption stats
        $consumptionQuery = "
            SELECT 
                generation_type,
                COUNT(*) as count,
                SUM(ABS(credits_amount)) as total_credits
            FROM credit_transactions 
            WHERE user_id = :user_id 
            AND type = 'consumption' 
            AND created_at >= :start_date
            GROUP BY generation_type
        ";
        
        $consumptionStats = $this->db->query($consumptionQuery, [
            ':user_id' => $userId,
            ':start_date' => $startDate
        ]);
        
        // Get total consumption
        $totalQuery = "
            SELECT 
                COUNT(*) as total_generations,
                SUM(ABS(credits_amount)) as total_credits_used
            FROM credit_transactions 
            WHERE user_id = :user_id 
            AND type = 'consumption' 
            AND created_at >= :start_date
        ";
        
        $totalStats = $this->db->query($totalQuery, [
            ':user_id' => $userId,
            ':start_date' => $startDate
        ]);
        
        return [
            'period' => $period,
            'consumption_by_type' => $consumptionStats,
            'total_stats' => $totalStats[0] ?? ['total_generations' => 0, 'total_credits_used' => 0],
            'current_balance' => $this->getUserCredits($userId)
        ];
    }
    
    /**
     * Get available credit packages
     */
    public function getCreditPackages() {
        return $this->creditPackages;
    }
    
    /**
     * Get credit costs for all generation types
     */
    public function getCreditCosts() {
        return $this->creditCosts;
    }
    
    /**
     * Process credit package purchase
     */
    public function processCreditPurchase($userId, $packageId, $paymentData) {
        try {
            if (!isset($this->creditPackages[$packageId])) {
                throw new Exception('Invalid credit package');
            }
            
            $package = $this->creditPackages[$packageId];
            $totalCredits = $package['credits'] + $package['bonus_credits'];
            
            // Add credits to user account
            $result = $this->addCredits(
                $userId, 
                $totalCredits, 
                'purchase', 
                "Credit purchase: {$package['name']}", 
                $paymentData['transaction_id']
            );
            
            if ($result['success']) {
                // Log purchase
                $purchaseData = [
                    'purchase_id' => uniqid('purchase_'),
                    'user_id' => $userId,
                    'package_id' => $packageId,
                    'credits_purchased' => $package['credits'],
                    'bonus_credits' => $package['bonus_credits'],
                    'total_credits' => $totalCredits,
                    'amount_paid' => $package['price'],
                    'currency' => $package['currency'],
                    'payment_method' => $paymentData['payment_method'],
                    'payment_transaction_id' => $paymentData['transaction_id'],
                    'created_at' => date('Y-m-d H:i:s')
                ];
                
                $this->db->insert('credit_purchases', $purchaseData);
                
                // Send confirmation email
                $this->sendPurchaseConfirmationEmail($userId, $package, $paymentData);
                
                return [
                    'success' => true,
                    'credits_added' => $totalCredits,
                    'new_balance' => $result['new_balance'],
                    'purchase_id' => $purchaseData['purchase_id']
                ];
            }
            
            return $result;
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Initialize database tables
     */
    private function initializeDatabase() {
        // Credit transactions table
        $this->db->createTable('credit_transactions', [
            'id INT AUTO_INCREMENT PRIMARY KEY',
            'transaction_id VARCHAR(50) UNIQUE NOT NULL',
            'user_id VARCHAR(50) NOT NULL',
            'type ENUM("consumption", "purchase", "bonus", "refund", "subscription") NOT NULL',
            'generation_type VARCHAR(50)',
            'credits_amount INT NOT NULL',
            'balance_before INT NOT NULL',
            'balance_after INT NOT NULL',
            'job_id VARCHAR(50)',
            'created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'description TEXT',
            'FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE',
            'INDEX idx_user_date (user_id, created_at)',
            'INDEX idx_type (type)'
        ]);

        // Credit purchases table
        $this->db->createTable('credit_purchases', [
            'id INT AUTO_INCREMENT PRIMARY KEY',
            'purchase_id VARCHAR(50) UNIQUE NOT NULL',
            'user_id VARCHAR(50) NOT NULL',
            'package_id VARCHAR(50) NOT NULL',
            'credits_purchased INT NOT NULL',
            'bonus_credits INT DEFAULT 0',
            'total_credits INT NOT NULL',
            'amount_paid DECIMAL(10,2) NOT NULL',
            'currency VARCHAR(3) DEFAULT "USD"',
            'payment_method VARCHAR(50)',
            'payment_transaction_id VARCHAR(100)',
            'created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE',
            'INDEX idx_user_date (user_id, created_at)'
        ]);

        // Referrals table
        $this->db->createTable('referrals', [
            'id INT AUTO_INCREMENT PRIMARY KEY',
            'referrer_id VARCHAR(50) NOT NULL',
            'referred_id VARCHAR(50) NOT NULL',
            'bonus_credits INT DEFAULT 25',
            'created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'FOREIGN KEY (referrer_id) REFERENCES users(user_id) ON DELETE CASCADE',
            'FOREIGN KEY (referred_id) REFERENCES users(user_id) ON DELETE CASCADE',
            'UNIQUE KEY unique_referral (referrer_id, referred_id)'
        ]);
    }

    /**
     * Send low credits notification
     */
    private function sendLowCreditsNotification($userId, $creditsRemaining) {
        $user = $this->db->selectOne('users', ['user_id' => $userId]);

        if ($user && $user['email']) {
            $this->emailService->sendLowCreditsNotification(
                $user['email'],
                $user['first_name'],
                $creditsRemaining
            );
        }
    }

    /**
     * Send purchase confirmation email
     */
    private function sendPurchaseConfirmationEmail($userId, $package, $paymentData) {
        $user = $this->db->selectOne('users', ['user_id' => $userId]);

        if ($user && $user['email']) {
            $emailData = [
                'amount' => $package['price'],
                'currency' => $package['currency'],
                'credits' => $package['credits'] + $package['bonus_credits'],
                'transaction_id' => $paymentData['transaction_id']
            ];

            $this->emailService->sendPaymentConfirmationEmail(
                $user['email'],
                $user['first_name'],
                $emailData
            );
        }
    }

    /**
     * Refund credits
     */
    public function refundCredits($userId, $credits, $reason, $originalTransactionId = null) {
        try {
            $result = $this->addCredits(
                $userId,
                $credits,
                'refund',
                "Credit refund: $reason",
                uniqid('refund_')
            );

            if ($result['success']) {
                // Log refund details
                $refundData = [
                    'refund_id' => uniqid('refund_'),
                    'user_id' => $userId,
                    'credits_refunded' => $credits,
                    'reason' => $reason,
                    'original_transaction_id' => $originalTransactionId,
                    'created_at' => date('Y-m-d H:i:s')
                ];

                $this->db->insert('credit_refunds', $refundData);
            }

            return $result;

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get credit package by ID
     */
    public function getCreditPackage($packageId) {
        return $this->creditPackages[$packageId] ?? null;
    }

    /**
     * Calculate bulk discount
     */
    public function calculateBulkDiscount($generationType, $quantity) {
        $baseCost = $this->getCreditCost($generationType);

        if ($quantity >= 10) {
            // 20% discount for 10+ generations
            return intval($baseCost * 0.8 * $quantity);
        } elseif ($quantity >= 5) {
            // 10% discount for 5+ generations
            return intval($baseCost * 0.9 * $quantity);
        }

        return $baseCost * $quantity;
    }

    /**
     * Get user's credit purchase history
     */
    public function getPurchaseHistory($userId, $limit = 20) {
        return $this->db->select(
            'credit_purchases',
            ['user_id' => $userId],
            '*',
            'created_at DESC',
            $limit
        );
    }

    /**
     * Validate credit transaction
     */
    public function validateTransaction($transactionId) {
        $transaction = $this->db->selectOne('credit_transactions', ['transaction_id' => $transactionId]);
        return $transaction !== null;
    }

    /**
     * Get credit system statistics (admin)
     */
    public function getSystemStats($period = '30 days') {
        $startDate = date('Y-m-d H:i:s', strtotime("-$period"));

        // Total credits consumed
        $consumptionQuery = "
            SELECT
                SUM(ABS(credits_amount)) as total_consumed,
                COUNT(*) as total_transactions
            FROM credit_transactions
            WHERE type = 'consumption'
            AND created_at >= :start_date
        ";

        $consumptionStats = $this->db->query($consumptionQuery, [':start_date' => $startDate]);

        // Total credits purchased
        $purchaseQuery = "
            SELECT
                SUM(total_credits) as total_purchased,
                SUM(amount_paid) as total_revenue,
                COUNT(*) as total_purchases
            FROM credit_purchases
            WHERE created_at >= :start_date
        ";

        $purchaseStats = $this->db->query($purchaseQuery, [':start_date' => $startDate]);

        // Most popular generation types
        $popularQuery = "
            SELECT
                generation_type,
                COUNT(*) as usage_count,
                SUM(ABS(credits_amount)) as total_credits
            FROM credit_transactions
            WHERE type = 'consumption'
            AND created_at >= :start_date
            GROUP BY generation_type
            ORDER BY usage_count DESC
            LIMIT 5
        ";

        $popularTypes = $this->db->query($popularQuery, [':start_date' => $startDate]);

        return [
            'period' => $period,
            'consumption' => $consumptionStats[0] ?? ['total_consumed' => 0, 'total_transactions' => 0],
            'purchases' => $purchaseStats[0] ?? ['total_purchased' => 0, 'total_revenue' => 0, 'total_purchases' => 0],
            'popular_types' => $popularTypes
        ];
    }

    /**
     * Check if user qualifies for bulk discount
     */
    public function qualifiesForBulkDiscount($userId, $generationType, $quantity) {
        if ($quantity < 5) {
            return false;
        }

        $totalCost = $this->calculateBulkDiscount($generationType, $quantity);
        $userCredits = $this->getUserCredits($userId);

        return $userCredits >= $totalCost;
    }

    /**
     * Get recommended credit package for user
     */
    public function getRecommendedPackage($userId) {
        // Get user's usage pattern
        $stats = $this->getCreditStats($userId, '30 days');
        $monthlyUsage = $stats['total_stats']['total_credits_used'];

        if ($monthlyUsage <= 100) {
            return 'starter';
        } elseif ($monthlyUsage <= 500) {
            return 'creator';
        } elseif ($monthlyUsage <= 1000) {
            return 'professional';
        } else {
            return 'enterprise';
        }
    }
}
