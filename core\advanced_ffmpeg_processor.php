<?php
/**
 * Advanced FFmpeg Processor - Optimized video processing with hardware acceleration
 * Provides parallel processing, hardware acceleration, and efficient encoding
 */

class AdvancedFFmpegProcessor {
    private $ffmpegPath;
    private $ffprobePath;
    private $tempDir;
    private $hardwareAcceleration;
    private $parallelProcesses;
    private $encodingPresets;
    private $qualitySettings;
    
    public function __construct($config = []) {
        $this->ffmpegPath = $config['ffmpeg_path'] ?? 'ffmpeg';
        $this->ffprobePath = $config['ffprobe_path'] ?? 'ffprobe';
        $this->tempDir = $config['temp_dir'] ?? __DIR__ . '/../temp/';
        $this->parallelProcesses = $config['parallel_processes'] ?? 4;
        
        $this->initializeHardwareAcceleration();
        $this->initializeEncodingPresets();
        $this->initializeQualitySettings();
    }
    
    /**
     * Process video with advanced optimizations
     */
    public function processVideo($inputFiles, $outputFile, $options = []) {
        $startTime = microtime(true);
        
        try {
            // Detect hardware capabilities
            $hwAccel = $this->detectHardwareAcceleration();
            
            // Prepare processing pipeline
            $pipeline = $this->buildProcessingPipeline($inputFiles, $outputFile, $options, $hwAccel);
            
            // Execute with parallel processing if applicable
            if (count($pipeline) > 1 && $this->parallelProcesses > 1) {
                $result = $this->executeParallelPipeline($pipeline);
            } else {
                $result = $this->executeSequentialPipeline($pipeline);
            }
            
            $processingTime = microtime(true) - $startTime;
            
            return [
                'success' => $result,
                'output_file' => $outputFile,
                'processing_time' => $processingTime,
                'hardware_acceleration' => $hwAccel,
                'pipeline_steps' => count($pipeline)
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'processing_time' => microtime(true) - $startTime
            ];
        }
    }
    
    /**
     * Create optimized video from frames
     */
    public function createVideoFromFrames($frameDir, $outputFile, $fps = 30, $duration = 30) {
        $hwAccel = $this->detectHardwareAcceleration();
        
        // Build optimized command for frame-to-video conversion
        $cmd = $this->buildFrameToVideoCommand($frameDir, $outputFile, $fps, $duration, $hwAccel);
        
        return $this->executeCommand($cmd);
    }
    
    /**
     * Merge audio and video with synchronization
     */
    public function mergeAudioVideo($videoFile, $audioFile, $outputFile, $options = []) {
        $hwAccel = $this->detectHardwareAcceleration();
        
        // Build optimized merge command
        $cmd = $this->buildAudioVideoMergeCommand($videoFile, $audioFile, $outputFile, $hwAccel, $options);
        
        return $this->executeCommand($cmd);
    }
    
    /**
     * Apply video effects and filters
     */
    public function applyEffects($inputFile, $outputFile, $effects = []) {
        $hwAccel = $this->detectHardwareAcceleration();
        
        // Build effects pipeline
        $filterChain = $this->buildEffectsFilterChain($effects);
        
        $cmd = sprintf(
            '%s %s -i "%s" -vf "%s" %s "%s" -y',
            $this->ffmpegPath,
            $hwAccel['decode'],
            $inputFile,
            $filterChain,
            $hwAccel['encode'],
            $outputFile
        );
        
        return $this->executeCommand($cmd);
    }
    
    /**
     * Initialize hardware acceleration detection
     */
    private function initializeHardwareAcceleration() {
        $this->hardwareAcceleration = [
            'nvidia' => $this->checkNvidiaSupport(),
            'intel' => $this->checkIntelSupport(),
            'amd' => $this->checkAMDSupport(),
            'apple' => $this->checkAppleSupport()
        ];
    }
    
    /**
     * Initialize encoding presets for different quality levels
     */
    private function initializeEncodingPresets() {
        $this->encodingPresets = [
            'ultrafast' => [
                'preset' => 'ultrafast',
                'crf' => 28,
                'profile' => 'baseline',
                'level' => '3.0'
            ],
            'fast' => [
                'preset' => 'fast',
                'crf' => 23,
                'profile' => 'main',
                'level' => '3.1'
            ],
            'balanced' => [
                'preset' => 'medium',
                'crf' => 20,
                'profile' => 'high',
                'level' => '4.0'
            ],
            'quality' => [
                'preset' => 'slow',
                'crf' => 18,
                'profile' => 'high',
                'level' => '4.1'
            ],
            'archive' => [
                'preset' => 'veryslow',
                'crf' => 15,
                'profile' => 'high',
                'level' => '4.2'
            ]
        ];
    }
    
    /**
     * Initialize quality settings for different output types
     */
    private function initializeQualitySettings() {
        $this->qualitySettings = [
            'social_media' => [
                'resolution' => '720x1280',
                'fps' => 30,
                'bitrate' => '2M',
                'audio_bitrate' => '128k',
                'preset' => 'fast'
            ],
            'web_optimized' => [
                'resolution' => '1080x1920',
                'fps' => 30,
                'bitrate' => '4M',
                'audio_bitrate' => '192k',
                'preset' => 'balanced'
            ],
            'high_quality' => [
                'resolution' => '1080x1920',
                'fps' => 60,
                'bitrate' => '8M',
                'audio_bitrate' => '320k',
                'preset' => 'quality'
            ],
            'archive_quality' => [
                'resolution' => '2160x3840',
                'fps' => 60,
                'bitrate' => '20M',
                'audio_bitrate' => '320k',
                'preset' => 'archive'
            ]
        ];
    }
    
    /**
     * Detect available hardware acceleration
     */
    private function detectHardwareAcceleration() {
        $hwAccel = [
            'decode' => '',
            'encode' => '',
            'type' => 'software'
        ];
        
        // Check for NVIDIA NVENC
        if ($this->hardwareAcceleration['nvidia']) {
            $hwAccel = [
                'decode' => '-hwaccel cuda -hwaccel_output_format cuda',
                'encode' => '-c:v h264_nvenc -preset fast -rc vbr -cq 20',
                'type' => 'nvidia'
            ];
        }
        // Check for Intel Quick Sync
        elseif ($this->hardwareAcceleration['intel']) {
            $hwAccel = [
                'decode' => '-hwaccel qsv',
                'encode' => '-c:v h264_qsv -preset fast -global_quality 20',
                'type' => 'intel'
            ];
        }
        // Check for AMD VCE
        elseif ($this->hardwareAcceleration['amd']) {
            $hwAccel = [
                'decode' => '-hwaccel d3d11va',
                'encode' => '-c:v h264_amf -quality balanced -rc cqp -qp_i 20 -qp_p 20',
                'type' => 'amd'
            ];
        }
        // Check for Apple VideoToolbox
        elseif ($this->hardwareAcceleration['apple']) {
            $hwAccel = [
                'decode' => '-hwaccel videotoolbox',
                'encode' => '-c:v h264_videotoolbox -b:v 4M',
                'type' => 'apple'
            ];
        }
        // Fallback to software encoding with optimized settings
        else {
            $hwAccel = [
                'decode' => '',
                'encode' => '-c:v libx264 -preset fast -crf 20 -pix_fmt yuv420p',
                'type' => 'software'
            ];
        }
        
        return $hwAccel;
    }
    
    /**
     * Build processing pipeline
     */
    private function buildProcessingPipeline($inputFiles, $outputFile, $options, $hwAccel) {
        $pipeline = [];
        
        // Determine quality preset
        $qualityPreset = $options['quality'] ?? 'social_media';
        $settings = $this->qualitySettings[$qualityPreset];
        
        // If multiple input files, create merge step
        if (count($inputFiles) > 1) {
            $mergedFile = $this->tempDir . 'merged_' . uniqid() . '.mp4';
            $pipeline[] = $this->buildMergeCommand($inputFiles, $mergedFile, $hwAccel);
            $inputFiles = [$mergedFile];
        }
        
        // Main processing step
        $pipeline[] = $this->buildMainProcessingCommand($inputFiles[0], $outputFile, $settings, $hwAccel, $options);
        
        return $pipeline;
    }
    
    /**
     * Build frame-to-video command
     */
    private function buildFrameToVideoCommand($frameDir, $outputFile, $fps, $duration, $hwAccel) {
        $framePattern = $frameDir . '/frame_%04d.png';
        
        return sprintf(
            '%s -framerate %d -i "%s" -t %d -vf "scale=720:1280:force_original_aspect_ratio=decrease,pad=720:1280:(ow-iw)/2:(oh-ih)/2" %s -r %d "%s" -y',
            $this->ffmpegPath,
            $fps,
            $framePattern,
            $duration,
            $hwAccel['encode'],
            $fps,
            $outputFile
        );
    }
    
    /**
     * Build audio-video merge command
     */
    private function buildAudioVideoMergeCommand($videoFile, $audioFile, $outputFile, $hwAccel, $options) {
        $audioSync = $options['audio_sync'] ?? 'auto';
        $videoLength = $options['video_length'] ?? 30;
        
        return sprintf(
            '%s %s -i "%s" -i "%s" -c:v copy -c:a aac -b:a 192k -shortest -t %d "%s" -y',
            $this->ffmpegPath,
            $hwAccel['decode'],
            $videoFile,
            $audioFile,
            $videoLength,
            $outputFile
        );
    }
    
    /**
     * Build effects filter chain
     */
    private function buildEffectsFilterChain($effects) {
        $filters = [];
        
        foreach ($effects as $effect => $params) {
            switch ($effect) {
                case 'fade_in':
                    $filters[] = sprintf('fade=in:0:%d', $params['duration'] ?? 30);
                    break;
                case 'fade_out':
                    $filters[] = sprintf('fade=out:%d:%d', $params['start'] ?? 870, $params['duration'] ?? 30);
                    break;
                case 'blur':
                    $filters[] = sprintf('boxblur=%f', $params['strength'] ?? 2.0);
                    break;
                case 'brightness':
                    $filters[] = sprintf('eq=brightness=%f', $params['value'] ?? 0.1);
                    break;
                case 'contrast':
                    $filters[] = sprintf('eq=contrast=%f', $params['value'] ?? 1.2);
                    break;
                case 'saturation':
                    $filters[] = sprintf('eq=saturation=%f', $params['value'] ?? 1.1);
                    break;
                case 'scale':
                    $filters[] = sprintf('scale=%s', $params['size'] ?? '720:1280');
                    break;
            }
        }
        
        return implode(',', $filters);
    }
    
    /**
     * Execute command with error handling
     */
    private function executeCommand($cmd) {
        $output = [];
        $returnCode = 0;
        
        exec($cmd . ' 2>&1', $output, $returnCode);
        
        return [
            'success' => $returnCode === 0,
            'output' => $output,
            'return_code' => $returnCode,
            'command' => $cmd
        ];
    }
    
    /**
     * Execute parallel pipeline
     */
    private function executeParallelPipeline($pipeline) {
        $processes = [];
        
        foreach ($pipeline as $cmd) {
            $process = proc_open($cmd . ' 2>&1', [
                0 => ['pipe', 'r'],
                1 => ['pipe', 'w'],
                2 => ['pipe', 'w']
            ], $pipes);
            
            if (is_resource($process)) {
                $processes[] = ['process' => $process, 'pipes' => $pipes];
            }
        }
        
        // Wait for all processes to complete
        $allSuccess = true;
        foreach ($processes as $processInfo) {
            $output = stream_get_contents($processInfo['pipes'][1]);
            $error = stream_get_contents($processInfo['pipes'][2]);
            
            fclose($processInfo['pipes'][0]);
            fclose($processInfo['pipes'][1]);
            fclose($processInfo['pipes'][2]);
            
            $returnCode = proc_close($processInfo['process']);
            
            if ($returnCode !== 0) {
                $allSuccess = false;
            }
        }
        
        return $allSuccess;
    }
    
    /**
     * Execute sequential pipeline
     */
    private function executeSequentialPipeline($pipeline) {
        foreach ($pipeline as $cmd) {
            $result = $this->executeCommand($cmd);
            if (!$result['success']) {
                return false;
            }
        }
        return true;
    }

    /**
     * Check NVIDIA GPU support
     */
    private function checkNvidiaSupport() {
        $cmd = $this->ffmpegPath . ' -hide_banner -encoders 2>/dev/null | grep nvenc';
        $output = shell_exec($cmd);
        return !empty($output);
    }

    /**
     * Check Intel Quick Sync support
     */
    private function checkIntelSupport() {
        $cmd = $this->ffmpegPath . ' -hide_banner -encoders 2>/dev/null | grep qsv';
        $output = shell_exec($cmd);
        return !empty($output);
    }

    /**
     * Check AMD VCE support
     */
    private function checkAMDSupport() {
        $cmd = $this->ffmpegPath . ' -hide_banner -encoders 2>/dev/null | grep amf';
        $output = shell_exec($cmd);
        return !empty($output);
    }

    /**
     * Check Apple VideoToolbox support
     */
    private function checkAppleSupport() {
        $cmd = $this->ffmpegPath . ' -hide_banner -encoders 2>/dev/null | grep videotoolbox';
        $output = shell_exec($cmd);
        return !empty($output);
    }

    /**
     * Build merge command for multiple inputs
     */
    private function buildMergeCommand($inputFiles, $outputFile, $hwAccel) {
        $inputs = '';
        $filterComplex = '';

        foreach ($inputFiles as $file) {
            $inputs .= sprintf('-i "%s" ', $file);
        }

        $count = count($inputFiles);
        if ($count > 1) {
            $filterComplex = sprintf('-filter_complex "concat=n=%d:v=1:a=1[outv][outa]" -map "[outv]" -map "[outa]"', $count);
        }

        return sprintf(
            '%s %s %s %s %s "%s" -y',
            $this->ffmpegPath,
            $hwAccel['decode'],
            $inputs,
            $filterComplex,
            $hwAccel['encode'],
            $outputFile
        );
    }

    /**
     * Build main processing command
     */
    private function buildMainProcessingCommand($inputFile, $outputFile, $settings, $hwAccel, $options) {
        $filters = [];

        // Add scaling filter
        $filters[] = sprintf('scale=%s:force_original_aspect_ratio=decrease', $settings['resolution']);
        $filters[] = sprintf('pad=%s:(ow-iw)/2:(oh-ih)/2', $settings['resolution']);

        // Add FPS filter
        $filters[] = sprintf('fps=%d', $settings['fps']);

        // Add custom effects if specified
        if (isset($options['effects'])) {
            $effectsChain = $this->buildEffectsFilterChain($options['effects']);
            if (!empty($effectsChain)) {
                $filters[] = $effectsChain;
            }
        }

        $filterString = implode(',', $filters);

        return sprintf(
            '%s %s -i "%s" -vf "%s" %s -b:v %s -maxrate %s -bufsize %s "%s" -y',
            $this->ffmpegPath,
            $hwAccel['decode'],
            $inputFile,
            $filterString,
            $hwAccel['encode'],
            $settings['bitrate'],
            $settings['bitrate'],
            $settings['bitrate'],
            $outputFile
        );
    }

    /**
     * Get video information using ffprobe
     */
    public function getVideoInfo($videoFile) {
        $cmd = sprintf(
            '%s -v quiet -print_format json -show_format -show_streams "%s"',
            $this->ffprobePath,
            $videoFile
        );

        $output = shell_exec($cmd);
        $info = json_decode($output, true);

        if (!$info) {
            return null;
        }

        $videoStream = null;
        $audioStream = null;

        foreach ($info['streams'] as $stream) {
            if ($stream['codec_type'] === 'video' && !$videoStream) {
                $videoStream = $stream;
            } elseif ($stream['codec_type'] === 'audio' && !$audioStream) {
                $audioStream = $stream;
            }
        }

        return [
            'format' => $info['format'],
            'video' => $videoStream,
            'audio' => $audioStream,
            'duration' => floatval($info['format']['duration'] ?? 0),
            'size' => intval($info['format']['size'] ?? 0)
        ];
    }

    /**
     * Optimize video for specific platform
     */
    public function optimizeForPlatform($inputFile, $outputFile, $platform = 'instagram') {
        $platformSettings = [
            'instagram' => [
                'resolution' => '720x1280',
                'fps' => 30,
                'bitrate' => '3M',
                'audio_bitrate' => '128k',
                'max_duration' => 60
            ],
            'tiktok' => [
                'resolution' => '720x1280',
                'fps' => 30,
                'bitrate' => '2M',
                'audio_bitrate' => '128k',
                'max_duration' => 60
            ],
            'youtube_shorts' => [
                'resolution' => '1080x1920',
                'fps' => 30,
                'bitrate' => '4M',
                'audio_bitrate' => '192k',
                'max_duration' => 60
            ],
            'twitter' => [
                'resolution' => '720x1280',
                'fps' => 30,
                'bitrate' => '2M',
                'audio_bitrate' => '128k',
                'max_duration' => 140
            ]
        ];

        $settings = $platformSettings[$platform] ?? $platformSettings['instagram'];
        $hwAccel = $this->detectHardwareAcceleration();

        return $this->buildMainProcessingCommand($inputFile, $outputFile, $settings, $hwAccel, []);
    }

    /**
     * Create thumbnail from video
     */
    public function createThumbnail($videoFile, $thumbnailFile, $timeOffset = 5) {
        $cmd = sprintf(
            '%s -i "%s" -ss %d -vframes 1 -vf "scale=720:1280:force_original_aspect_ratio=decrease,pad=720:1280:(ow-iw)/2:(oh-ih)/2" "%s" -y',
            $this->ffmpegPath,
            $videoFile,
            $timeOffset,
            $thumbnailFile
        );

        return $this->executeCommand($cmd);
    }

    /**
     * Extract audio from video
     */
    public function extractAudio($videoFile, $audioFile, $format = 'mp3') {
        $audioCodec = $format === 'mp3' ? 'libmp3lame' : 'aac';

        $cmd = sprintf(
            '%s -i "%s" -vn -acodec %s -ab 192k "%s" -y',
            $this->ffmpegPath,
            $videoFile,
            $audioCodec,
            $audioFile
        );

        return $this->executeCommand($cmd);
    }

    /**
     * Get processing statistics
     */
    public function getProcessingStats() {
        return [
            'hardware_acceleration' => $this->hardwareAcceleration,
            'encoding_presets' => array_keys($this->encodingPresets),
            'quality_settings' => array_keys($this->qualitySettings),
            'parallel_processes' => $this->parallelProcesses,
            'ffmpeg_version' => $this->getFFmpegVersion()
        ];
    }

    /**
     * Get FFmpeg version
     */
    private function getFFmpegVersion() {
        $cmd = $this->ffmpegPath . ' -version 2>&1 | head -n 1';
        $output = shell_exec($cmd);
        return trim($output);
    }
}
