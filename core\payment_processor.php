<?php
/**
 * Payment Processor - Secure payment handling for Sutradhar 2070
 * Integrates with Stripe and PayPal for credit purchases and subscriptions
 */

require_once 'database_manager.php';
require_once 'credit_system.php';
require_once 'subscription_manager.php';
require_once 'email_service.php';

class PaymentProcessor {
    private $db;
    private $creditSystem;
    private $subscriptionManager;
    private $emailService;
    private $config;
    private $stripeSecretKey;
    private $paypalConfig;
    
    public function __construct() {
        $this->db = new DatabaseManager();
        $this->creditSystem = new CreditSystem();
        $this->subscriptionManager = new SubscriptionManager();
        $this->emailService = new EmailService();
        
        $this->loadConfig();
        $this->initializeDatabase();
    }
    
    /**
     * Process credit purchase payment
     */
    public function processCreditPurchase($userId, $packageId, $paymentMethod, $paymentData) {
        try {
            $this->db->beginTransaction();
            
            // Get credit package
            $package = $this->creditSystem->getCreditPackage($packageId);
            if (!$package) {
                throw new Exception('Invalid credit package');
            }
            
            // Create payment record
            $paymentId = uniqid('pay_');
            $paymentRecord = [
                'payment_id' => $paymentId,
                'user_id' => $userId,
                'type' => 'credit_purchase',
                'amount' => $package['price'],
                'currency' => $package['currency'],
                'payment_method' => $paymentMethod,
                'status' => 'pending',
                'package_id' => $packageId,
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $this->db->insert('payments', $paymentRecord);
            
            // Process payment based on method
            $paymentResult = null;
            switch ($paymentMethod) {
                case 'stripe':
                    $paymentResult = $this->processStripePayment($paymentData, $package);
                    break;
                case 'paypal':
                    $paymentResult = $this->processPayPalPayment($paymentData, $package);
                    break;
                default:
                    throw new Exception('Unsupported payment method');
            }
            
            if ($paymentResult['success']) {
                // Update payment status
                $this->db->update('payments', [
                    'status' => 'completed',
                    'transaction_id' => $paymentResult['transaction_id'],
                    'completed_at' => date('Y-m-d H:i:s'),
                    'payment_details' => json_encode($paymentResult['details'])
                ], ['payment_id' => $paymentId]);
                
                // Process credit purchase
                $creditResult = $this->creditSystem->processCreditPurchase($userId, $packageId, [
                    'payment_method' => $paymentMethod,
                    'transaction_id' => $paymentResult['transaction_id']
                ]);
                
                if ($creditResult['success']) {
                    $this->db->commit();
                    
                    return [
                        'success' => true,
                        'payment_id' => $paymentId,
                        'transaction_id' => $paymentResult['transaction_id'],
                        'credits_added' => $creditResult['credits_added'],
                        'new_balance' => $creditResult['new_balance']
                    ];
                } else {
                    throw new Exception('Failed to add credits: ' . $creditResult['error']);
                }
            } else {
                // Update payment status to failed
                $this->db->update('payments', [
                    'status' => 'failed',
                    'error_message' => $paymentResult['error']
                ], ['payment_id' => $paymentId]);
                
                throw new Exception('Payment failed: ' . $paymentResult['error']);
            }
            
        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Process subscription payment
     */
    public function processSubscriptionPayment($userId, $planId, $paymentMethod, $paymentData) {
        try {
            $this->db->beginTransaction();
            
            // Get subscription plan
            $plan = $this->subscriptionManager->getPlan($planId);
            if (!$plan) {
                throw new Exception('Invalid subscription plan');
            }
            
            // Create payment record
            $paymentId = uniqid('pay_');
            $paymentRecord = [
                'payment_id' => $paymentId,
                'user_id' => $userId,
                'type' => 'subscription',
                'amount' => $plan['price'],
                'currency' => $plan['currency'],
                'payment_method' => $paymentMethod,
                'status' => 'pending',
                'plan_id' => $planId,
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $this->db->insert('payments', $paymentRecord);
            
            // Process payment
            $paymentResult = null;
            switch ($paymentMethod) {
                case 'stripe':
                    $paymentResult = $this->processStripeSubscription($paymentData, $plan);
                    break;
                case 'paypal':
                    $paymentResult = $this->processPayPalSubscription($paymentData, $plan);
                    break;
                default:
                    throw new Exception('Unsupported payment method');
            }
            
            if ($paymentResult['success']) {
                // Update payment status
                $this->db->update('payments', [
                    'status' => 'completed',
                    'transaction_id' => $paymentResult['transaction_id'],
                    'completed_at' => date('Y-m-d H:i:s'),
                    'payment_details' => json_encode($paymentResult['details'])
                ], ['payment_id' => $paymentId]);
                
                // Create subscription
                $subscriptionResult = $this->subscriptionManager->subscribeToPlan($userId, $planId, [
                    'payment_method' => $paymentMethod,
                    'transaction_id' => $paymentResult['transaction_id']
                ]);
                
                if ($subscriptionResult['success']) {
                    $this->db->commit();
                    
                    return [
                        'success' => true,
                        'payment_id' => $paymentId,
                        'transaction_id' => $paymentResult['transaction_id'],
                        'subscription_id' => $subscriptionResult['subscription_id']
                    ];
                } else {
                    throw new Exception('Failed to create subscription: ' . $subscriptionResult['error']);
                }
            } else {
                // Update payment status to failed
                $this->db->update('payments', [
                    'status' => 'failed',
                    'error_message' => $paymentResult['error']
                ], ['payment_id' => $paymentId]);
                
                throw new Exception('Payment failed: ' . $paymentResult['error']);
            }
            
        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Process Stripe payment
     */
    private function processStripePayment($paymentData, $package) {
        try {
            // Initialize Stripe (would require Stripe PHP SDK in production)
            // \Stripe\Stripe::setApiKey($this->stripeSecretKey);
            
            // For demo purposes, simulate successful payment
            if ($this->config['demo_mode']) {
                return [
                    'success' => true,
                    'transaction_id' => 'stripe_' . uniqid(),
                    'details' => [
                        'payment_intent_id' => 'pi_' . uniqid(),
                        'amount' => $package['price'] * 100, // Stripe uses cents
                        'currency' => strtolower($package['currency'])
                    ]
                ];
            }
            
            // Real Stripe implementation would go here
            /*
            $paymentIntent = \Stripe\PaymentIntent::create([
                'amount' => $package['price'] * 100,
                'currency' => strtolower($package['currency']),
                'payment_method' => $paymentData['payment_method_id'],
                'confirmation_method' => 'manual',
                'confirm' => true,
                'return_url' => $this->config['return_url']
            ]);
            
            if ($paymentIntent->status === 'succeeded') {
                return [
                    'success' => true,
                    'transaction_id' => $paymentIntent->id,
                    'details' => $paymentIntent->toArray()
                ];
            }
            */
            
            return [
                'success' => false,
                'error' => 'Stripe integration not configured'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Process PayPal payment
     */
    private function processPayPalPayment($paymentData, $package) {
        try {
            // For demo purposes, simulate successful payment
            if ($this->config['demo_mode']) {
                return [
                    'success' => true,
                    'transaction_id' => 'paypal_' . uniqid(),
                    'details' => [
                        'order_id' => $paymentData['order_id'] ?? 'order_' . uniqid(),
                        'amount' => $package['price'],
                        'currency' => $package['currency']
                    ]
                ];
            }
            
            // Real PayPal implementation would go here
            return [
                'success' => false,
                'error' => 'PayPal integration not configured'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Process Stripe subscription
     */
    private function processStripeSubscription($paymentData, $plan) {
        try {
            // For demo purposes, simulate successful subscription
            if ($this->config['demo_mode']) {
                return [
                    'success' => true,
                    'transaction_id' => 'stripe_sub_' . uniqid(),
                    'details' => [
                        'subscription_id' => 'sub_' . uniqid(),
                        'amount' => $plan['price'] * 100,
                        'currency' => strtolower($plan['currency']),
                        'interval' => 'month'
                    ]
                ];
            }
            
            // Real Stripe subscription implementation would go here
            return [
                'success' => false,
                'error' => 'Stripe subscription integration not configured'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Process PayPal subscription
     */
    private function processPayPalSubscription($paymentData, $plan) {
        try {
            // For demo purposes, simulate successful subscription
            if ($this->config['demo_mode']) {
                return [
                    'success' => true,
                    'transaction_id' => 'paypal_sub_' . uniqid(),
                    'details' => [
                        'subscription_id' => $paymentData['subscription_id'] ?? 'sub_' . uniqid(),
                        'amount' => $plan['price'],
                        'currency' => $plan['currency']
                    ]
                ];
            }
            
            // Real PayPal subscription implementation would go here
            return [
                'success' => false,
                'error' => 'PayPal subscription integration not configured'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Handle payment webhook
     */
    public function handleWebhook($provider, $payload, $signature = null) {
        try {
            switch ($provider) {
                case 'stripe':
                    return $this->handleStripeWebhook($payload, $signature);
                case 'paypal':
                    return $this->handlePayPalWebhook($payload);
                default:
                    throw new Exception('Unknown webhook provider');
            }
        } catch (Exception $e) {
            error_log("Webhook error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Handle Stripe webhook
     */
    private function handleStripeWebhook($payload, $signature) {
        // Verify webhook signature (would use Stripe SDK in production)
        // $event = \Stripe\Webhook::constructEvent($payload, $signature, $this->config['stripe_webhook_secret']);
        
        // For demo purposes, decode JSON payload
        $event = json_decode($payload, true);
        
        switch ($event['type']) {
            case 'payment_intent.succeeded':
                $this->handleSuccessfulPayment($event['data']['object']);
                break;
            case 'invoice.payment_succeeded':
                $this->handleSuccessfulSubscriptionPayment($event['data']['object']);
                break;
            case 'customer.subscription.deleted':
                $this->handleSubscriptionCancellation($event['data']['object']);
                break;
        }
        
        return true;
    }
    
    /**
     * Handle PayPal webhook
     */
    private function handlePayPalWebhook($payload) {
        $event = json_decode($payload, true);
        
        switch ($event['event_type']) {
            case 'PAYMENT.CAPTURE.COMPLETED':
                $this->handleSuccessfulPayment($event['resource']);
                break;
            case 'BILLING.SUBSCRIPTION.ACTIVATED':
                $this->handleSuccessfulSubscriptionPayment($event['resource']);
                break;
            case 'BILLING.SUBSCRIPTION.CANCELLED':
                $this->handleSubscriptionCancellation($event['resource']);
                break;
        }
        
        return true;
    }

    /**
     * Initialize database tables
     */
    private function initializeDatabase() {
        // Payments table
        $this->db->createTable('payments', [
            'id INT AUTO_INCREMENT PRIMARY KEY',
            'payment_id VARCHAR(50) UNIQUE NOT NULL',
            'user_id VARCHAR(50) NOT NULL',
            'type ENUM("credit_purchase", "subscription", "refund") NOT NULL',
            'amount DECIMAL(10,2) NOT NULL',
            'currency VARCHAR(3) DEFAULT "USD"',
            'payment_method ENUM("stripe", "paypal", "bank_transfer") NOT NULL',
            'status ENUM("pending", "completed", "failed", "refunded") DEFAULT "pending"',
            'transaction_id VARCHAR(100)',
            'package_id VARCHAR(50)',
            'plan_id VARCHAR(50)',
            'created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'completed_at TIMESTAMP NULL',
            'payment_details JSON',
            'error_message TEXT',
            'FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE',
            'INDEX idx_user_status (user_id, status)',
            'INDEX idx_transaction (transaction_id)',
            'INDEX idx_created (created_at)'
        ]);

        // Payment methods table
        $this->db->createTable('payment_methods', [
            'id INT AUTO_INCREMENT PRIMARY KEY',
            'user_id VARCHAR(50) NOT NULL',
            'provider ENUM("stripe", "paypal") NOT NULL',
            'provider_payment_method_id VARCHAR(100) NOT NULL',
            'type VARCHAR(50)',
            'last_four VARCHAR(4)',
            'brand VARCHAR(50)',
            'exp_month INT',
            'exp_year INT',
            'is_default BOOLEAN DEFAULT FALSE',
            'created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE',
            'INDEX idx_user (user_id)'
        ]);

        // Invoices table
        $this->db->createTable('invoices', [
            'id INT AUTO_INCREMENT PRIMARY KEY',
            'invoice_id VARCHAR(50) UNIQUE NOT NULL',
            'user_id VARCHAR(50) NOT NULL',
            'payment_id VARCHAR(50)',
            'amount DECIMAL(10,2) NOT NULL',
            'currency VARCHAR(3) DEFAULT "USD"',
            'status ENUM("draft", "sent", "paid", "overdue", "cancelled") DEFAULT "draft"',
            'invoice_date DATE NOT NULL',
            'due_date DATE NOT NULL',
            'paid_date DATE NULL',
            'invoice_data JSON',
            'created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE',
            'FOREIGN KEY (payment_id) REFERENCES payments(payment_id) ON DELETE SET NULL',
            'INDEX idx_user_status (user_id, status)',
            'INDEX idx_due_date (due_date)'
        ]);
    }

    /**
     * Handle successful payment
     */
    private function handleSuccessfulPayment($paymentData) {
        // Update payment status in database
        $transactionId = $paymentData['id'] ?? $paymentData['transaction_id'];

        $payment = $this->db->selectOne('payments', ['transaction_id' => $transactionId]);

        if ($payment) {
            $this->db->update('payments', [
                'status' => 'completed',
                'completed_at' => date('Y-m-d H:i:s')
            ], ['payment_id' => $payment['payment_id']]);

            // Send confirmation email
            $this->sendPaymentConfirmationEmail($payment['user_id'], $payment);
        }
    }

    /**
     * Handle successful subscription payment
     */
    private function handleSuccessfulSubscriptionPayment($subscriptionData) {
        // Process subscription renewal or activation
        $subscriptionId = $subscriptionData['subscription'] ?? $subscriptionData['id'];

        // Update subscription status and add credits
        // Implementation would depend on the specific webhook data structure
        // TODO: Implement subscription renewal logic using $subscriptionId
    }

    /**
     * Handle subscription cancellation
     */
    private function handleSubscriptionCancellation($subscriptionData) {
        // Cancel subscription in database
        $subscriptionId = $subscriptionData['id'];

        // Find and cancel subscription
        // Implementation would depend on how subscription IDs are stored
        // TODO: Implement subscription cancellation logic using $subscriptionId
    }

    /**
     * Send payment confirmation email
     */
    private function sendPaymentConfirmationEmail($userId, $payment) {
        $user = $this->db->selectOne('users', ['user_id' => $userId]);

        if ($user && $user['email']) {
            $emailData = [
                'amount' => $payment['amount'],
                'currency' => $payment['currency'],
                'transaction_id' => $payment['transaction_id'],
                'credits' => 0 // Would be calculated based on package
            ];

            $this->emailService->sendPaymentConfirmationEmail(
                $user['email'],
                $user['first_name'],
                $emailData
            );
        }
    }

    /**
     * Get payment history for user
     */
    public function getPaymentHistory($userId, $limit = 20) {
        return $this->db->select(
            'payments',
            ['user_id' => $userId],
            '*',
            'created_at DESC',
            $limit
        );
    }

    /**
     * Get payment by ID
     */
    public function getPayment($paymentId) {
        return $this->db->selectOne('payments', ['payment_id' => $paymentId]);
    }

    /**
     * Refund payment
     */
    public function refundPayment($paymentId, $reason = 'Customer request') {
        try {
            $payment = $this->getPayment($paymentId);

            if (!$payment) {
                throw new Exception('Payment not found');
            }

            if ($payment['status'] !== 'completed') {
                throw new Exception('Payment cannot be refunded');
            }

            $this->db->beginTransaction();

            // Process refund based on payment method
            $refundResult = null;
            switch ($payment['payment_method']) {
                case 'stripe':
                    $refundResult = $this->processStripeRefund($payment);
                    break;
                case 'paypal':
                    $refundResult = $this->processPayPalRefund($payment);
                    break;
                default:
                    throw new Exception('Refund not supported for this payment method');
            }

            if ($refundResult['success']) {
                // Update payment status
                $this->db->update('payments', [
                    'status' => 'refunded'
                ], ['payment_id' => $paymentId]);

                // Create refund record
                $refundData = [
                    'payment_id' => $paymentId,
                    'refund_id' => uniqid('refund_'),
                    'user_id' => $payment['user_id'],
                    'amount' => $payment['amount'],
                    'currency' => $payment['currency'],
                    'reason' => $reason,
                    'refund_transaction_id' => $refundResult['refund_id'],
                    'created_at' => date('Y-m-d H:i:s')
                ];

                $this->db->insert('refunds', $refundData);

                // Refund credits if applicable
                if ($payment['type'] === 'credit_purchase') {
                    // Calculate credits to refund
                    $package = $this->creditSystem->getCreditPackage($payment['package_id']);
                    if ($package) {
                        $creditsToRefund = $package['credits'] + $package['bonus_credits'];
                        $this->creditSystem->refundCredits(
                            $payment['user_id'],
                            $creditsToRefund,
                            $reason,
                            $payment['transaction_id']
                        );
                    }
                }

                $this->db->commit();

                return [
                    'success' => true,
                    'refund_id' => $refundData['refund_id'],
                    'amount' => $payment['amount']
                ];
            } else {
                throw new Exception('Refund processing failed: ' . $refundResult['error']);
            }

        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Process Stripe refund
     */
    private function processStripeRefund($payment) {
        try {
            // For demo purposes, simulate successful refund
            if ($this->config['demo_mode']) {
                return [
                    'success' => true,
                    'refund_id' => 'stripe_refund_' . uniqid()
                ];
            }

            // Real Stripe refund implementation would go here
            /*
            $refund = \Stripe\Refund::create([
                'payment_intent' => $payment['transaction_id'],
                'amount' => $payment['amount'] * 100
            ]);

            return [
                'success' => true,
                'refund_id' => $refund->id
            ];
            */

            // TODO: Use $payment data for actual Stripe refund
            return [
                'success' => false,
                'error' => 'Stripe refund not configured'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Process PayPal refund
     */
    private function processPayPalRefund($payment) {
        try {
            // For demo purposes, simulate successful refund
            if ($this->config['demo_mode']) {
                return [
                    'success' => true,
                    'refund_id' => 'paypal_refund_' . uniqid()
                ];
            }

            // Real PayPal refund implementation would go here
            return [
                'success' => false,
                'error' => 'PayPal refund not configured'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Generate invoice
     */
    public function generateInvoice($userId, $paymentId) {
        try {
            $payment = $this->getPayment($paymentId);
            $user = $this->db->selectOne('users', ['user_id' => $userId]);

            if (!$payment || !$user) {
                throw new Exception('Payment or user not found');
            }

            $invoiceId = uniqid('inv_');
            $invoiceData = [
                'invoice_id' => $invoiceId,
                'user_id' => $userId,
                'payment_id' => $paymentId,
                'amount' => $payment['amount'],
                'currency' => $payment['currency'],
                'status' => 'paid',
                'invoice_date' => date('Y-m-d'),
                'due_date' => date('Y-m-d'),
                'paid_date' => date('Y-m-d'),
                'invoice_data' => json_encode([
                    'user' => $user,
                    'payment' => $payment,
                    'items' => $this->getInvoiceItems($payment)
                ])
            ];

            $this->db->insert('invoices', $invoiceData);

            return [
                'success' => true,
                'invoice_id' => $invoiceId,
                'invoice_data' => $invoiceData
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get invoice items based on payment
     */
    private function getInvoiceItems($payment) {
        $items = [];

        if ($payment['type'] === 'credit_purchase') {
            $package = $this->creditSystem->getCreditPackage($payment['package_id']);
            if ($package) {
                $items[] = [
                    'description' => $package['name'],
                    'quantity' => 1,
                    'unit_price' => $package['price'],
                    'total' => $package['price']
                ];

                if ($package['bonus_credits'] > 0) {
                    $items[] = [
                        'description' => 'Bonus Credits',
                        'quantity' => $package['bonus_credits'],
                        'unit_price' => 0,
                        'total' => 0
                    ];
                }
            }
        } elseif ($payment['type'] === 'subscription') {
            $plan = $this->subscriptionManager->getPlan($payment['plan_id']);
            if ($plan) {
                $items[] = [
                    'description' => $plan['name'] . ' - Monthly Subscription',
                    'quantity' => 1,
                    'unit_price' => $plan['price'],
                    'total' => $plan['price']
                ];
            }
        }

        return $items;
    }

    /**
     * Load payment configuration
     */
    private function loadConfig() {
        $configFile = __DIR__ . '/../config/payment_config.json';

        if (file_exists($configFile)) {
            $this->config = json_decode(file_get_contents($configFile), true);
        } else {
            $this->config = [
                'demo_mode' => true,
                'stripe' => [
                    'publishable_key' => '',
                    'secret_key' => '',
                    'webhook_secret' => ''
                ],
                'paypal' => [
                    'client_id' => '',
                    'client_secret' => '',
                    'mode' => 'sandbox'
                ],
                'return_url' => 'https://sutradhar2070.com/payment/return',
                'cancel_url' => 'https://sutradhar2070.com/payment/cancel'
            ];

            // Save default config
            if (!is_dir(dirname($configFile))) {
                mkdir(dirname($configFile), 0755, true);
            }
            file_put_contents($configFile, json_encode($this->config, JSON_PRETTY_PRINT));
        }

        $this->stripeSecretKey = $this->config['stripe']['secret_key'] ?? '';
        $this->paypalConfig = $this->config['paypal'] ?? [];
    }

    /**
     * Get payment statistics
     */
    public function getPaymentStats($period = '30 days') {
        $startDate = date('Y-m-d H:i:s', strtotime("-$period"));

        // Total revenue
        $revenueQuery = "
            SELECT
                SUM(amount) as total_revenue,
                COUNT(*) as total_payments,
                AVG(amount) as average_payment
            FROM payments
            WHERE status = 'completed'
            AND created_at >= :start_date
        ";

        $revenueStats = $this->db->query($revenueQuery, [':start_date' => $startDate]);

        // Revenue by payment method
        $methodQuery = "
            SELECT
                payment_method,
                SUM(amount) as revenue,
                COUNT(*) as count
            FROM payments
            WHERE status = 'completed'
            AND created_at >= :start_date
            GROUP BY payment_method
        ";

        $methodStats = $this->db->query($methodQuery, [':start_date' => $startDate]);

        // Failed payments
        $failedQuery = "
            SELECT COUNT(*) as failed_count
            FROM payments
            WHERE status = 'failed'
            AND created_at >= :start_date
        ";

        $failedStats = $this->db->query($failedQuery, [':start_date' => $startDate]);

        return [
            'period' => $period,
            'revenue' => $revenueStats[0] ?? ['total_revenue' => 0, 'total_payments' => 0, 'average_payment' => 0],
            'by_method' => $methodStats,
            'failed_payments' => $failedStats[0]['failed_count'] ?? 0
        ];
    }
}
