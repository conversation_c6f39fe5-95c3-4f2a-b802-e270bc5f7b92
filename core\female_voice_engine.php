<?php
/**
 * Advanced Female Voice Engine
 * Uses Hugging Face TTS models for high-quality female voice synthesis
 */

class FemaleVoiceEngine {
    private $config;
    private $apiKey;
    private $tempDir;
    private $models;

    public function __construct() {
        $this->loadConfig();
        $this->tempDir = __DIR__ . '/../temp/';
        
        if (!is_dir($this->tempDir)) {
            mkdir($this->tempDir, 0755, true);
        }
    }

    private function loadConfig() {
        $configPath = __DIR__ . '/../config/settings.json';
        $this->config = json_decode(file_get_contents($configPath), true);
        $this->apiKey = $this->config['api_keys']['huggingface'] ?? '';
        $this->models = $this->config['video_generation']['female_voice'] ?? [];
    }

    /**
     * Generate female voice audio using Hugging Face TTS
     */
    public function generateFemaleVoice($text, $voicePack, $outputFile) {
        echo "🎤 Generating female voice with Hugging Face TTS...\n";
        
        try {
            // Try Hugging Face TTS first
            if (!empty($this->apiKey)) {
                $result = $this->generateWithHuggingFaceTTS($text, $outputFile);
                if ($result && file_exists($outputFile)) {
                    echo "✅ Hugging Face TTS generated: " . round(filesize($outputFile)/1024, 1) . " KB\n";
                    return true;
                }
            }
            
            // Fallback to Windows TTS with female voice
            echo "⚠️  Falling back to Windows TTS with female voice...\n";
            return $this->generateWithWindowsFemaleVoice($text, $outputFile);
            
        } catch (Exception $e) {
            echo "❌ Female voice generation failed: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * Generate voice using Hugging Face TTS API
     */
    private function generateWithHuggingFaceTTS($text, $outputFile) {
        $model = $this->models['model'] ?? 'microsoft/speecht5_tts';
        $url = "https://api-inference.huggingface.co/models/$model";
        
        // Prepare the request data
        $data = json_encode([
            'inputs' => $text,
            'parameters' => [
                'speaker_embeddings' => $this->getFemaleVoiceEmbeddings(),
                'vocoder' => 'microsoft/speecht5_hifigan'
            ]
        ]);
        
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => [
                    'Authorization: Bearer ' . $this->apiKey,
                    'Content-Type: application/json',
                    'Content-Length: ' . strlen($data)
                ],
                'content' => $data,
                'timeout' => 120
            ]
        ]);
        
        $response = file_get_contents($url, false, $context);
        
        if ($response !== false && strlen($response) > 1000) {
            // Check if it's audio data
            $header = substr($response, 0, 12);
            if (strpos($header, 'RIFF') !== false || strpos($header, 'fLaC') !== false) {
                file_put_contents($outputFile, $response);
                return true;
            }
        }
        
        // Try alternative TTS model
        return $this->tryAlternativeTTSModel($text, $outputFile);
    }

    /**
     * Get female voice embeddings for TTS
     */
    private function getFemaleVoiceEmbeddings() {
        // These are example embeddings for a female voice
        // In a real implementation, you'd use pre-computed embeddings
        return array_fill(0, 512, 0.1); // Placeholder embeddings
    }

    /**
     * Try alternative TTS model
     */
    private function tryAlternativeTTSModel($text, $outputFile) {
        $alternativeModels = [
            'espnet/kan-bayashi_ljspeech_vits',
            'facebook/mms-tts-eng',
            'suno/bark'
        ];
        
        foreach ($alternativeModels as $model) {
            echo "  Trying alternative model: $model\n";
            
            $url = "https://api-inference.huggingface.co/models/$model";
            
            $data = json_encode([
                'inputs' => $text,
                'parameters' => [
                    'voice_preset' => 'v2/en_speaker_6' // Female voice preset
                ]
            ]);
            
            $context = stream_context_create([
                'http' => [
                    'method' => 'POST',
                    'header' => [
                        'Authorization: Bearer ' . $this->apiKey,
                        'Content-Type: application/json',
                        'Content-Length: ' . strlen($data)
                    ],
                    'content' => $data,
                    'timeout' => 120
                ]
            ]);
            
            $response = file_get_contents($url, false, $context);
            
            if ($response !== false && strlen($response) > 1000) {
                file_put_contents($outputFile, $response);
                echo "  ✅ Success with $model\n";
                return true;
            }
        }
        
        return false;
    }

    /**
     * Generate voice using Windows TTS with female voice
     */
    private function generateWithWindowsFemaleVoice($text, $outputFile) {
        // Use Windows SAPI with female voice
        $femaleVoices = [
            'Microsoft Zira Desktop',
            'Microsoft Hazel Desktop', 
            'Microsoft Eva Desktop',
            'Microsoft Aria Desktop'
        ];
        
        foreach ($femaleVoices as $voice) {
            echo "  Trying Windows voice: $voice\n";
            
            $success = $this->generateWindowsVoice($text, $voice, $outputFile);
            if ($success) {
                echo "  ✅ Success with $voice\n";
                return true;
            }
        }
        
        // Fallback to default female voice
        return $this->generateWindowsVoice($text, 'female', $outputFile);
    }

    /**
     * Generate voice using Windows SAPI
     */
    private function generateWindowsVoice($text, $voice, $outputFile) {
        // Create VBScript for TTS
        $vbsScript = $this->tempDir . 'tts_female.vbs';
        
        $vbsContent = "
Set objVoice = CreateObject(\"SAPI.SpVoice\")
Set objFileStream = CreateObject(\"SAPI.SpFileStream\")

' Try to set female voice
Set objVoices = objVoice.GetVoices()
For i = 0 To objVoices.Count - 1
    If InStr(LCase(objVoices.Item(i).GetDescription()), \"$voice\") > 0 Or _
       InStr(LCase(objVoices.Item(i).GetDescription()), \"female\") > 0 Or _
       InStr(LCase(objVoices.Item(i).GetDescription()), \"zira\") > 0 Or _
       InStr(LCase(objVoices.Item(i).GetDescription()), \"hazel\") > 0 Then
        Set objVoice.Voice = objVoices.Item(i)
        Exit For
    End If
Next

' Set voice properties for female characteristics
objVoice.Rate = 0
objVoice.Volume = 100

' Configure output file
objFileStream.Open \"$outputFile\", 3
Set objVoice.AudioOutputStream = objFileStream

' Speak the text
objVoice.Speak \"$text\"

' Close the file
objFileStream.Close
Set objFileStream = Nothing
Set objVoice = Nothing
";
        
        file_put_contents($vbsScript, $vbsContent);
        
        // Execute VBScript
        $cmd = "cscript //nologo \"$vbsScript\" 2>&1";
        exec($cmd, $output, $returnCode);
        
        // Clean up
        if (file_exists($vbsScript)) {
            unlink($vbsScript);
        }
        
        return $returnCode === 0 && file_exists($outputFile) && filesize($outputFile) > 1000;
    }

    /**
     * Generate multiple voice segments with female voice
     */
    public function generateFemaleVoiceSegments($segments, $voicePack, $jobId) {
        $voiceFiles = [];
        
        echo "🎤 Generating " . count($segments) . " female voice segments...\n";
        
        foreach ($segments as $index => $segment) {
            $text = $segment['text'] ?? '';
            if (empty($text)) {
                continue;
            }
            
            echo "  Segment $index: " . substr($text, 0, 50) . "...\n";
            
            $outputFile = $this->tempDir . $jobId . "_female_segment_$index.wav";
            
            $success = $this->generateFemaleVoice($text, $voicePack, $outputFile);
            
            if ($success && file_exists($outputFile)) {
                $voiceFiles[] = [
                    'file' => $outputFile,
                    'duration' => $this->getAudioDuration($outputFile),
                    'segment' => $index
                ];
                echo "    ✅ Generated: " . round(filesize($outputFile)/1024, 1) . " KB\n";
            } else {
                echo "    ❌ Failed to generate segment $index\n";
            }
        }
        
        echo "✅ Generated " . count($voiceFiles) . " female voice segments\n";
        return $voiceFiles;
    }

    /**
     * Combine voice segments into single audio file
     */
    public function combineVoiceSegments($voiceFiles, $outputFile) {
        if (empty($voiceFiles)) {
            return false;
        }
        
        echo "🔗 Combining " . count($voiceFiles) . " voice segments...\n";
        
        // Try FFmpeg first
        if ($this->hasFFmpeg()) {
            return $this->combineWithFFmpeg($voiceFiles, $outputFile);
        }
        
        // Fallback to simple concatenation
        return $this->combineWithSimpleConcat($voiceFiles, $outputFile);
    }

    /**
     * Check if FFmpeg is available
     */
    private function hasFFmpeg() {
        exec('ffmpeg -version 2>&1', $output, $returnCode);
        return $returnCode === 0;
    }

    /**
     * Combine audio files using FFmpeg
     */
    private function combineWithFFmpeg($voiceFiles, $outputFile) {
        // Create input list file
        $listFile = $this->tempDir . 'audio_list.txt';
        $listContent = '';
        
        foreach ($voiceFiles as $file) {
            if (file_exists($file)) {
                $listContent .= "file '" . realpath($file) . "'\n";
            }
        }
        
        file_put_contents($listFile, $listContent);
        
        // Combine with FFmpeg
        $cmd = "ffmpeg -f concat -safe 0 -i \"$listFile\" -c copy \"$outputFile\" -y 2>&1";
        exec($cmd, $output, $returnCode);
        
        // Clean up
        unlink($listFile);
        
        if ($returnCode === 0 && file_exists($outputFile)) {
            echo "  ✅ Combined with FFmpeg: " . round(filesize($outputFile)/1024, 1) . " KB\n";
            return true;
        }
        
        return false;
    }

    /**
     * Combine audio files with simple concatenation
     */
    private function combineWithSimpleConcat($voiceFiles, $outputFile) {
        $combinedData = '';
        $firstFile = true;
        
        foreach ($voiceFiles as $file) {
            if (file_exists($file)) {
                $data = file_get_contents($file);
                
                if ($firstFile) {
                    // Keep the full WAV header from the first file
                    $combinedData .= $data;
                    $firstFile = false;
                } else {
                    // Skip the WAV header (first 44 bytes) for subsequent files
                    $combinedData .= substr($data, 44);
                }
            }
        }
        
        if (!empty($combinedData)) {
            file_put_contents($outputFile, $combinedData);
            echo "  ✅ Combined with simple concat: " . round(filesize($outputFile)/1024, 1) . " KB\n";
            return true;
        }
        
        return false;
    }

    /**
     * Test female voice generation
     */
    public function testFemaleVoice() {
        $testText = "Hello, this is a test of the female voice generation system.";
        $testFile = $this->tempDir . 'test_female_voice.wav';
        
        $success = $this->generateFemaleVoice($testText, 'default', $testFile);
        
        if ($success && file_exists($testFile)) {
            $size = filesize($testFile);
            unlink($testFile);
            return [
                'status' => 'success',
                'message' => 'Female voice test successful',
                'file_size' => $size
            ];
        } else {
            return [
                'status' => 'error',
                'message' => 'Female voice test failed'
            ];
        }
    }

    /**
     * Get available female voices
     */
    public function getAvailableFemaleVoices() {
        $voices = [
            'huggingface' => [
                'microsoft/speecht5_tts' => 'Microsoft SpeechT5 (Female)',
                'espnet/kan-bayashi_ljspeech_vits' => 'ESPnet VITS (Female)',
                'facebook/mms-tts-eng' => 'Meta MMS TTS (Female)',
                'suno/bark' => 'Suno Bark (Female)'
            ],
            'windows' => [
                'Microsoft Zira Desktop' => 'Zira (US English Female)',
                'Microsoft Hazel Desktop' => 'Hazel (UK English Female)',
                'Microsoft Eva Desktop' => 'Eva (US English Female)',
                'Microsoft Aria Desktop' => 'Aria (US English Female)'
            ]
        ];
        
        return $voices;
    }

    /**
     * Get audio duration
     */
    private function getAudioDuration($audioFile) {
        if (!file_exists($audioFile)) {
            return 0;
        }

        // Simple duration calculation based on file size and format
        $fileSize = filesize($audioFile);
        $sampleRate = 22050; // Default sample rate
        $channels = 1;
        $bitsPerSample = 16;

        $dataSize = $fileSize - 44; // Subtract WAV header size
        $bytesPerSecond = $sampleRate * $channels * ($bitsPerSample / 8);

        return max(0, $dataSize / $bytesPerSecond);
    }
}
