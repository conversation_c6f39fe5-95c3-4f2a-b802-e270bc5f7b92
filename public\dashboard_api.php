<?php
/**
 * Dashboard API - Backend endpoints for Sutradhar 2070 Dashboard
 * Handles user data, statistics, generation history, and billing information
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../core/user_authentication.php';
require_once '../core/credit_system.php';
require_once '../core/subscription_manager.php';
require_once '../core/payment_processor.php';

class DashboardAPI {
    private $auth;
    private $creditSystem;
    private $subscriptionManager;
    private $paymentProcessor;
    private $currentUser;
    
    public function __construct() {
        $this->auth = new UserAuthentication();
        $this->creditSystem = new CreditSystem();
        $this->subscriptionManager = new SubscriptionManager();
        $this->paymentProcessor = new PaymentProcessor();
        
        // Check authentication
        $this->currentUser = $this->auth->getCurrentUser();
        if (!$this->currentUser) {
            $this->sendError('Authentication required', 401);
        }
    }
    
    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = $_GET['endpoint'] ?? '';
        
        try {
            switch ($path) {
                case 'user/current':
                    $this->getCurrentUser();
                    break;
                    
                case 'user/stats':
                    $this->getUserStats();
                    break;
                    
                case 'user/generations/recent':
                    $this->getRecentGenerations();
                    break;
                    
                case 'user/generations/history':
                    $this->getGenerationHistory();
                    break;
                    
                case 'user/billing':
                    $this->getBillingData();
                    break;
                    
                case 'user/analytics':
                    $this->getAnalytics();
                    break;
                    
                case 'user/profile':
                    if ($method === 'PUT') {
                        $this->updateProfile();
                    } else {
                        $this->getProfile();
                    }
                    break;
                    
                case 'credits/purchase':
                    if ($method === 'POST') {
                        $this->purchaseCredits();
                    }
                    break;
                    
                case 'subscription/change':
                    if ($method === 'POST') {
                        $this->changeSubscription();
                    }
                    break;
                    
                default:
                    $this->sendError('Endpoint not found', 404);
            }
        } catch (Exception $e) {
            $this->sendError($e->getMessage(), 500);
        }
    }
    
    /**
     * Get current user data
     */
    private function getCurrentUser() {
        $this->sendSuccess($this->currentUser);
    }
    
    /**
     * Get user statistics
     */
    private function getUserStats() {
        $userId = $this->currentUser['user_id'];
        
        // Get credit statistics
        $creditStats = $this->creditSystem->getCreditStats($userId, '30 days');
        
        // Get subscription info
        $subscription = $this->subscriptionManager->getUserSubscription($userId);
        
        // Calculate additional stats
        $stats = [
            'total_videos' => $creditStats['total_stats']['total_generations'],
            'credits_used' => $creditStats['total_stats']['total_credits_used'],
            'credits_remaining' => $this->creditSystem->getUserCredits($userId),
            'current_tier' => $subscription['plan_id'],
            'monthly_allowance' => $subscription['plan_details']['monthly_credits'],
            'avg_quality' => $this->calculateAverageQuality($userId),
            'avg_generation_time' => $this->calculateAverageTime($userId),
            'success_rate' => $this->calculateSuccessRate($userId),
            'consumption_by_type' => $creditStats['consumption_by_type']
        ];
        
        $this->sendSuccess($stats);
    }
    
    /**
     * Get recent generations
     */
    private function getRecentGenerations() {
        $userId = $this->currentUser['user_id'];
        $limit = $_GET['limit'] ?? 5;
        
        // Get recent credit transactions (generations)
        $transactions = $this->creditSystem->getCreditHistory($userId, $limit);
        
        // Format for display
        $generations = array_map(function($transaction) {
            return [
                'id' => $transaction['transaction_id'],
                'type' => $transaction['generation_type'] ?? 'basic_generation',
                'mood' => $this->extractMoodFromJobId($transaction['job_id']),
                'topic' => $this->extractTopicFromJobId($transaction['job_id']),
                'created_at' => $transaction['created_at'],
                'status' => 'completed',
                'credits_used' => abs($transaction['credits_amount']),
                'quality_score' => rand(85, 98), // Demo data
                'download_url' => $this->generateDownloadUrl($transaction['job_id'])
            ];
        }, array_filter($transactions, function($t) {
            return $t['type'] === 'consumption';
        }));
        
        $this->sendSuccess($generations);
    }
    
    /**
     * Get generation history with pagination
     */
    private function getGenerationHistory() {
        $userId = $this->currentUser['user_id'];
        $page = $_GET['page'] ?? 1;
        $limit = $_GET['limit'] ?? 20;
        $type = $_GET['type'] ?? null;
        
        $offset = ($page - 1) * $limit;
        $transactions = $this->creditSystem->getCreditHistory($userId, $limit, $offset);
        
        // Filter by type if specified
        if ($type) {
            $transactions = array_filter($transactions, function($t) use ($type) {
                return $t['generation_type'] === $type;
            });
        }
        
        // Format for display
        $history = array_map(function($transaction) {
            return [
                'id' => $transaction['transaction_id'],
                'type' => $transaction['generation_type'] ?? 'basic_generation',
                'mood' => $this->extractMoodFromJobId($transaction['job_id']),
                'topic' => $this->extractTopicFromJobId($transaction['job_id']),
                'created_at' => $transaction['created_at'],
                'status' => 'completed',
                'credits_used' => abs($transaction['credits_amount']),
                'quality_score' => rand(85, 98),
                'file_size' => rand(15, 45) . ' MB',
                'duration' => '30s',
                'download_url' => $this->generateDownloadUrl($transaction['job_id'])
            ];
        }, array_filter($transactions, function($t) {
            return $t['type'] === 'consumption';
        }));
        
        $this->sendSuccess([
            'history' => $history,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => ceil(count($transactions) / $limit),
                'total_items' => count($transactions)
            ]
        ]);
    }
    
    /**
     * Get billing data
     */
    private function getBillingData() {
        $userId = $this->currentUser['user_id'];
        
        // Get subscription info
        $subscription = $this->subscriptionManager->getUserSubscription($userId);
        
        // Get payment history
        $payments = $this->paymentProcessor->getPaymentHistory($userId, 10);
        
        // Get credit packages
        $creditPackages = $this->creditSystem->getCreditPackages();
        
        // Get subscription plans
        $plans = $this->subscriptionManager->getPlans();
        
        $billingData = [
            'current_subscription' => $subscription,
            'payments' => $payments,
            'credit_packages' => $creditPackages,
            'subscription_plans' => $plans,
            'next_billing_date' => $subscription['current_period_end'] ?? null,
            'payment_methods' => $this->getPaymentMethods($userId)
        ];
        
        $this->sendSuccess($billingData);
    }
    
    /**
     * Get analytics data
     */
    private function getAnalytics() {
        $userId = $this->currentUser['user_id'];
        $period = $_GET['period'] ?? '30 days';
        
        // Get credit usage analytics
        $creditStats = $this->creditSystem->getCreditStats($userId, $period);
        
        // Get usage trends
        $usageTrends = $this->getUsageTrends($userId, $period);
        
        // Get performance metrics
        $performance = [
            'avg_generation_time' => $this->calculateAverageTime($userId),
            'success_rate' => $this->calculateSuccessRate($userId),
            'quality_rating' => $this->calculateAverageQuality($userId),
            'credits_per_video' => $this->calculateCreditsPerVideo($userId)
        ];
        
        $analytics = [
            'credit_stats' => $creditStats,
            'usage_trends' => $usageTrends,
            'performance' => $performance,
            'popular_moods' => $this->getPopularMoods($userId),
            'popular_topics' => $this->getPopularTopics($userId)
        ];
        
        $this->sendSuccess($analytics);
    }
    
    /**
     * Update user profile
     */
    private function updateProfile() {
        $data = json_decode(file_get_contents('php://input'), true);
        $userId = $this->currentUser['user_id'];
        
        // Validate input
        $allowedFields = ['first_name', 'last_name', 'bio', 'company', 'website', 'location'];
        $updateData = [];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = trim($data[$field]);
            }
        }
        
        if (empty($updateData)) {
            $this->sendError('No valid fields to update', 400);
        }
        
        // Update user profile
        $db = new DatabaseManager();
        $result = $db->update('user_profiles', $updateData, ['user_id' => $userId]);
        
        if ($result) {
            $this->sendSuccess(['message' => 'Profile updated successfully']);
        } else {
            $this->sendError('Failed to update profile', 500);
        }
    }
    
    /**
     * Purchase credits
     */
    private function purchaseCredits() {
        $data = json_decode(file_get_contents('php://input'), true);
        $userId = $this->currentUser['user_id'];
        
        $packageId = $data['package_id'] ?? null;
        $paymentMethod = $data['payment_method'] ?? null;
        $paymentData = $data['payment_data'] ?? [];
        
        if (!$packageId || !$paymentMethod) {
            $this->sendError('Package ID and payment method required', 400);
        }
        
        // Process payment
        $result = $this->paymentProcessor->processCreditPurchase(
            $userId, 
            $packageId, 
            $paymentMethod, 
            $paymentData
        );
        
        $this->sendSuccess($result);
    }
    
    /**
     * Change subscription
     */
    private function changeSubscription() {
        $data = json_decode(file_get_contents('php://input'), true);
        $userId = $this->currentUser['user_id'];
        
        $planId = $data['plan_id'] ?? null;
        $paymentData = $data['payment_data'] ?? [];
        
        if (!$planId) {
            $this->sendError('Plan ID required', 400);
        }
        
        // Change subscription
        $result = $this->subscriptionManager->changePlan($userId, $planId, $paymentData);
        
        $this->sendSuccess($result);
    }
    
    /**
     * Helper methods
     */
    private function calculateAverageQuality($userId) {
        // Demo calculation - in production, this would query actual quality scores
        return rand(90, 98);
    }
    
    private function calculateAverageTime($userId) {
        // Demo calculation - in production, this would query actual generation times
        return rand(120, 300) . 's';
    }
    
    private function calculateSuccessRate($userId) {
        // Demo calculation - in production, this would query actual success/failure rates
        return rand(92, 99);
    }
    
    private function calculateCreditsPerVideo($userId) {
        $stats = $this->creditSystem->getCreditStats($userId, '30 days');
        $totalCredits = $stats['total_stats']['total_credits_used'];
        $totalVideos = $stats['total_stats']['total_generations'];
        
        return $totalVideos > 0 ? round($totalCredits / $totalVideos, 1) : 0;
    }
    
    private function extractMoodFromJobId($jobId) {
        // Demo extraction - in production, this would parse actual job data
        $moods = ['euphoric', 'serene', 'energetic', 'melancholic', 'mysterious'];
        return $moods[array_rand($moods)];
    }
    
    private function extractTopicFromJobId($jobId) {
        // Demo extraction - in production, this would parse actual job data
        $topics = ['nature_wildlife', 'urban_city', 'abstract_art', 'technology', 'food_cooking'];
        return $topics[array_rand($topics)];
    }
    
    private function generateDownloadUrl($jobId) {
        return "/api/download/" . ($jobId ?? 'demo_' . uniqid());
    }
    
    private function getPaymentMethods($userId) {
        // Demo data - in production, this would query saved payment methods
        return [
            [
                'id' => 'pm_1',
                'type' => 'card',
                'brand' => 'visa',
                'last_four' => '4242',
                'exp_month' => 12,
                'exp_year' => 2025,
                'is_default' => true
            ]
        ];
    }
    
    private function getUsageTrends($userId, $period) {
        // Demo data - in production, this would query actual usage trends
        return [
            'daily_usage' => [
                ['date' => '2024-01-01', 'credits' => 25],
                ['date' => '2024-01-02', 'credits' => 30],
                ['date' => '2024-01-03', 'credits' => 15],
                ['date' => '2024-01-04', 'credits' => 40],
                ['date' => '2024-01-05', 'credits' => 35]
            ]
        ];
    }
    
    private function getPopularMoods($userId) {
        // Demo data - in production, this would query actual mood usage
        return [
            ['mood' => 'euphoric', 'count' => 8],
            ['mood' => 'serene', 'count' => 6],
            ['mood' => 'energetic', 'count' => 5],
            ['mood' => 'mysterious', 'count' => 3],
            ['mood' => 'melancholic', 'count' => 2]
        ];
    }
    
    private function getPopularTopics($userId) {
        // Demo data - in production, this would query actual topic usage
        return [
            ['topic' => 'nature_wildlife', 'count' => 10],
            ['topic' => 'urban_city', 'count' => 7],
            ['topic' => 'abstract_art', 'count' => 4],
            ['topic' => 'technology', 'count' => 2],
            ['topic' => 'food_cooking', 'count' => 1]
        ];
    }
    
    private function sendSuccess($data) {
        echo json_encode(['success' => true, 'data' => $data]);
        exit;
    }
    
    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode(['success' => false, 'error' => $message]);
        exit;
    }
}

// Handle the request
$api = new DashboardAPI();
$api->handleRequest();
?>
