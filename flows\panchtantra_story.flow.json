{"flow_info": {"name": "<PERSON><PERSON><PERSON><PERSON> Story", "description": "Traditional moral stories with modern relevance", "type": "audio_story", "duration_target": "2-5 minutes", "format": "narrative"}, "structure": {"introduction": {"duration": "15-20 seconds", "purpose": "Set the scene and introduce characters", "style_hints": ["traditional", "storytelling", "engaging"], "voice_style": "storytelling", "background_music": "traditional_intro"}, "story_development": {"duration": "120-200 seconds", "purpose": "Narrate the main story with character interactions", "segments": 4, "style_hints": ["character_voices", "dramatic_moments", "pacing"], "voice_style": "varied_characters", "background_music": "ambient_traditional"}, "moral_lesson": {"duration": "20-30 seconds", "purpose": "Deliver the moral with modern context", "style_hints": ["wise", "relatable", "practical"], "voice_style": "wise_narrator", "background_music": "reflective"}, "modern_application": {"duration": "30-45 seconds", "purpose": "Connect ancient wisdom to today's life", "style_hints": ["contemporary", "practical", "inspiring"], "voice_style": "conversational", "background_music": "uplifting"}}, "voice_guidelines": {"narrator": {"tone": "warm and wise", "pace": "measured and clear", "accent": "neutral_indian", "emotional_range": "moderate", "characteristics": ["authoritative", "kind", "experienced"]}, "characters": {"animal_voices": {"differentiation": true, "personality_traits": true, "age_appropriate": true}, "human_voices": {"age_variation": true, "social_context": true, "emotional_depth": true}}}, "audio_guidelines": {"background_music": {"style": "traditional_indian_instrumental", "instruments": ["flute", "tabla", "sitar"], "volume": "25%", "mood_matching": true}, "sound_effects": {"nature_sounds": {"forest": ["birds", "wind", "leaves"], "water": ["stream", "rain", "pond"], "animals": ["specific_to_story"]}, "ambient": {"village": ["distant_voices", "daily_activities"], "palace": ["formal_atmosphere", "grandeur"], "home": ["domestic_sounds", "warmth"]}}, "voice_processing": {"narrator": {"reverb": "slight_warmth", "eq": "clear_speech", "compression": "gentle"}, "characters": {"animal_effects": "subtle_character_enhancement", "human_effects": "natural_voice_variation"}}}, "story_templates": {"the_wise_crow": {"characters": ["crow", "fox"], "setting": "forest", "moral": "don't trust flattery", "modern_context": "social_media_validation", "script": {"intro": "Once upon a time, in a dense forest, there lived a clever crow who had learned many lessons from life.", "story": "One day, the crow found a piece of cheese and perched on a tree branch. A cunning fox saw this and thought of a plan...", "moral": "The story teaches us that flattery can make us lose our common sense.", "application": "In today's world of social media likes and comments, we must remember not to let praise cloud our judgment."}}, "ant_and_grasshopper": {"characters": ["ant", "grasshopper"], "setting": "meadow", "moral": "hard_work_and_preparation", "modern_context": "financial_planning", "script": {"intro": "In a beautiful meadow, two very different creatures lived side by side - a hardworking ant and a carefree grasshopper.", "story": "All summer long, the ant collected food grain by grain, while the grasshopper sang and danced...", "moral": "This timeless tale reminds us that preparation today prevents problems tomorrow.", "application": "Just like the ant, we should save money, learn new skills, and plan for the future."}}, "tortoise_and_hare": {"characters": ["tortoise", "hare"], "setting": "racing_track", "moral": "consistency_over_talent", "modern_context": "career_success", "script": {"intro": "Everyone in the forest knew about the speedy hare and the slow but steady tortoise.", "story": "When the hare challenged the tortoise to a race, everyone thought it was a joke...", "moral": "Slow and steady wins the race - consistency beats raw talent.", "application": "In our careers and studies, regular effort often beats occasional bursts of brilliance."}}}, "cultural_elements": {"traditional_wisdom": ["ancient_sayings", "cultural_values", "family_teachings", "spiritual_insights"], "modern_relevance": ["workplace_scenarios", "relationship_advice", "financial_wisdom", "personal_growth"], "language_style": {"traditional_phrases": ["once upon a time", "in those days", "as our ancestors knew", "wisdom of ages"], "modern_connections": ["just like today", "in our current world", "we can apply this by", "this reminds us that"]}}, "engagement_elements": {"interactive_moments": ["pause_for_reflection", "question_to_audience", "relate_to_experience"], "emotional_hooks": ["suspense_building", "character_empathy", "moral_revelation"], "learning_reinforcement": ["key_point_repetition", "practical_examples", "memorable_phrases"]}, "voice_character_mapping": {"crow": {"voice_type": "smart_confident", "pitch": "medium_high", "pace": "moderate", "personality": "clever_but_prideful"}, "fox": {"voice_type": "smooth_cunning", "pitch": "medium_low", "pace": "persuasive", "personality": "charming_manipulative"}, "ant": {"voice_type": "determined_hardworking", "pitch": "medium", "pace": "steady", "personality": "practical_wise"}, "grasshopper": {"voice_type": "carefree_playful", "pitch": "high", "pace": "quick_light", "personality": "fun_loving_careless"}, "tortoise": {"voice_type": "calm_persistent", "pitch": "low", "pace": "slow_deliberate", "personality": "patient_determined"}, "hare": {"voice_type": "confident_boastful", "pitch": "medium_high", "pace": "fast", "personality": "talented_overconfident"}}, "technical_specs": {"audio": {"sample_rate": 44100, "channels": 2, "bitrate": "192k", "format": "mp3"}, "narration": {"words_per_minute": 140, "pause_duration": "0.5-2.0 seconds", "emphasis_timing": "natural", "breath_marks": "included"}, "music_mixing": {"fade_in_duration": 2, "fade_out_duration": 3, "volume_ducking": "automatic", "crossfade_segments": true}}}