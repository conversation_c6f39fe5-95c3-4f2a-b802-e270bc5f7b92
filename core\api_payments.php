<?php
/**
 * Payment API for Sutradhar 2070
 * Handles credit purchases and subscription management
 */

require_once 'api_base.php';

class PaymentAPI extends APIBase {
    
    public function handleRequest() {
        $path = $this->requestPath;
        $method = $this->requestMethod;
        
        switch ($path) {
            case 'payments/packages':
                if ($method === 'GET') $this->getCreditPackages();
                break;
            case 'payments/plans':
                if ($method === 'GET') $this->getSubscriptionPlans();
                break;
            case 'payments/purchase':
                if ($method === 'POST') $this->purchaseCredits();
                break;
            case 'payments/subscribe':
                if ($method === 'POST') $this->subscribe();
                break;
            case 'payments/cancel-subscription':
                if ($method === 'POST') $this->cancelSubscription();
                break;
            case 'payments/history':
                if ($method === 'GET') $this->getPaymentHistory();
                break;
            case 'payments/invoices':
                if ($method === 'GET') $this->getInvoices();
                break;
            case 'payments/webhook':
                if ($method === 'POST') $this->handleWebhook();
                break;
            default:
                $this->sendError('Endpoint not found', 404);
        }
    }
    
    /**
     * Get credit packages
     */
    private function getCreditPackages() {
        $packages = $this->db->select('credit_packages', 
            ['status' => 'active'], 
            'ORDER BY sort_order ASC'
        );
        
        $formattedPackages = array_map(function($package) {
            return [
                'package_id' => $package['package_id'],
                'name' => $package['name'],
                'description' => $package['description'],
                'credits' => intval($package['credits']),
                'bonus_credits' => intval($package['bonus_credits']),
                'total_credits' => intval($package['credits']) + intval($package['bonus_credits']),
                'price' => floatval($package['price']),
                'currency' => $package['currency'],
                'popular' => (bool)$package['popular'],
                'price_per_credit' => round(floatval($package['price']) / intval($package['credits']), 4)
            ];
        }, $packages);
        
        $this->sendSuccess(['packages' => $formattedPackages]);
    }
    
    /**
     * Get subscription plans
     */
    private function getSubscriptionPlans() {
        $plans = $this->db->select('subscription_plans', 
            ['status' => 'active'], 
            'ORDER BY sort_order ASC'
        );
        
        $formattedPlans = array_map(function($plan) {
            return [
                'plan_id' => $plan['plan_id'],
                'name' => $plan['name'],
                'description' => $plan['description'],
                'price_monthly' => floatval($plan['price_monthly']),
                'price_yearly' => floatval($plan['price_yearly']),
                'credits_monthly' => intval($plan['credits_monthly']),
                'features' => json_decode($plan['features'], true),
                'max_video_quality' => $plan['max_video_quality'],
                'api_access' => (bool)$plan['api_access'],
                'priority_support' => (bool)$plan['priority_support'],
                'custom_branding' => (bool)$plan['custom_branding'],
                'yearly_discount' => $plan['price_yearly'] > 0 ? 
                    round((1 - ($plan['price_yearly'] / ($plan['price_monthly'] * 12))) * 100, 1) : 0
            ];
        }, $plans);
        
        $this->sendSuccess(['plans' => $formattedPlans]);
    }
    
    /**
     * Purchase credits
     */
    private function purchaseCredits() {
        $this->requireAuth();
        
        $this->validateRequired(['package_id', 'payment_method']);
        
        $packageId = $this->requestData['package_id'];
        $paymentMethod = $this->requestData['payment_method'];
        $paymentData = $this->requestData['payment_data'] ?? [];
        
        // Get package details
        $package = $this->db->selectOne('credit_packages', [
            'package_id' => $packageId,
            'status' => 'active'
        ]);
        
        if (!$package) {
            $this->sendError('Invalid package selected', 400);
        }
        
        // Validate payment method
        $validMethods = ['stripe', 'paypal', 'demo'];
        if (!in_array($paymentMethod, $validMethods)) {
            $this->sendError('Invalid payment method', 400);
        }
        
        try {
            $this->db->beginTransaction();
            
            // Create payment transaction
            $transactionId = 'txn_' . uniqid() . '_' . time();
            
            $transactionData = [
                'transaction_id' => $transactionId,
                'user_id' => $this->currentUser['user_id'],
                'type' => 'credit_purchase',
                'status' => 'pending',
                'amount' => $package['price'],
                'currency' => $package['currency'],
                'payment_method' => $paymentMethod,
                'description' => "Purchase of {$package['name']} ({$package['credits']} credits)",
                'metadata' => json_encode([
                    'package_id' => $packageId,
                    'credits' => $package['credits'],
                    'bonus_credits' => $package['bonus_credits']
                ])
            ];
            
            $this->db->insert('payment_transactions', $transactionData);
            
            // Process payment based on method
            $paymentResult = $this->processPayment($paymentMethod, $package, $paymentData, $transactionId);
            
            if ($paymentResult['success']) {
                // Update transaction status
                $this->db->update('payment_transactions', [
                    'status' => 'completed',
                    'payment_provider_id' => $paymentResult['provider_id'] ?? null,
                    'processed_at' => date('Y-m-d H:i:s')
                ], ['transaction_id' => $transactionId]);
                
                // Add credits to user account
                $totalCredits = intval($package['credits']) + intval($package['bonus_credits']);
                $this->addCreditsToUser($this->currentUser['user_id'], $totalCredits, $transactionId);
                
                $this->db->commit();
                
                $this->sendSuccess([
                    'transaction_id' => $transactionId,
                    'credits_added' => $totalCredits,
                    'new_balance' => $this->getUserCredits()
                ], 'Credits purchased successfully');
                
            } else {
                // Update transaction as failed
                $this->db->update('payment_transactions', [
                    'status' => 'failed',
                    'failure_reason' => $paymentResult['error']
                ], ['transaction_id' => $transactionId]);
                
                $this->db->commit();
                
                $this->sendError($paymentResult['error'], 400);
            }
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Credit purchase failed: " . $e->getMessage());
            $this->sendError('Payment processing failed', 500);
        }
    }
    
    /**
     * Subscribe to plan
     */
    private function subscribe() {
        $this->requireAuth();
        
        $this->validateRequired(['plan_id', 'billing_cycle']);
        
        $planId = $this->requestData['plan_id'];
        $billingCycle = $this->requestData['billing_cycle'];
        $paymentData = $this->requestData['payment_data'] ?? [];
        
        // Validate billing cycle
        if (!in_array($billingCycle, ['monthly', 'yearly'])) {
            $this->sendError('Invalid billing cycle. Must be monthly or yearly', 400);
        }
        
        // Get plan details
        $plan = $this->db->selectOne('subscription_plans', [
            'plan_id' => $planId,
            'status' => 'active'
        ]);
        
        if (!$plan) {
            $this->sendError('Invalid subscription plan', 400);
        }
        
        // Check if user already has active subscription
        $existingSubscription = $this->db->selectOne('user_subscriptions', [
            'user_id' => $this->currentUser['user_id'],
            'status' => 'active'
        ]);
        
        if ($existingSubscription && $existingSubscription['plan_id'] !== 'free') {
            $this->sendError('You already have an active subscription. Please cancel it first.', 409);
        }
        
        $amount = $billingCycle === 'yearly' ? $plan['price_yearly'] : $plan['price_monthly'];
        
        try {
            $this->db->beginTransaction();
            
            // Create subscription
            $subscriptionId = 'sub_' . uniqid() . '_' . time();
            $currentPeriodStart = date('Y-m-d H:i:s');
            $currentPeriodEnd = date('Y-m-d H:i:s', strtotime('+1 ' . rtrim($billingCycle, 'ly')));
            
            $subscriptionData = [
                'subscription_id' => $subscriptionId,
                'user_id' => $this->currentUser['user_id'],
                'plan_id' => $planId,
                'status' => 'active',
                'billing_cycle' => $billingCycle,
                'amount' => $amount,
                'current_period_start' => $currentPeriodStart,
                'current_period_end' => $currentPeriodEnd
            ];
            
            // Cancel existing free subscription
            if ($existingSubscription) {
                $this->db->update('user_subscriptions', [
                    'status' => 'cancelled',
                    'cancelled_at' => date('Y-m-d H:i:s')
                ], ['subscription_id' => $existingSubscription['subscription_id']]);
            }
            
            $this->db->insert('user_subscriptions', $subscriptionData);
            
            // Create payment transaction if not free plan
            if ($amount > 0) {
                $transactionId = 'txn_' . uniqid() . '_' . time();
                
                $transactionData = [
                    'transaction_id' => $transactionId,
                    'user_id' => $this->currentUser['user_id'],
                    'type' => 'subscription',
                    'status' => 'completed', // For demo purposes
                    'amount' => $amount,
                    'payment_method' => 'demo',
                    'description' => "Subscription to {$plan['name']} ({$billingCycle})",
                    'processed_at' => date('Y-m-d H:i:s')
                ];
                
                $this->db->insert('payment_transactions', $transactionData);
            }
            
            // Add monthly credits
            $this->addCreditsToUser($this->currentUser['user_id'], $plan['credits_monthly'], $subscriptionId);
            
            $this->db->commit();
            
            $this->sendSuccess([
                'subscription_id' => $subscriptionId,
                'plan_name' => $plan['name'],
                'billing_cycle' => $billingCycle,
                'amount' => $amount,
                'credits_added' => intval($plan['credits_monthly']),
                'current_period_end' => $currentPeriodEnd
            ], 'Subscription activated successfully');
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Subscription failed: " . $e->getMessage());
            $this->sendError('Subscription processing failed', 500);
        }
    }
    
    /**
     * Cancel subscription
     */
    private function cancelSubscription() {
        $this->requireAuth();
        
        $subscription = $this->db->selectOne('user_subscriptions', [
            'user_id' => $this->currentUser['user_id'],
            'status' => 'active'
        ]);
        
        if (!$subscription || $subscription['plan_id'] === 'free') {
            $this->sendError('No active subscription to cancel', 400);
        }
        
        $reason = $this->requestData['reason'] ?? 'User requested cancellation';
        
        try {
            $this->db->beginTransaction();
            
            // Update subscription status
            $this->db->update('user_subscriptions', [
                'status' => 'cancelled',
                'cancelled_at' => date('Y-m-d H:i:s'),
                'cancellation_reason' => $reason
            ], ['subscription_id' => $subscription['subscription_id']]);
            
            // Create free subscription
            $freeSubscriptionId = 'sub_' . uniqid() . '_' . time();
            $this->db->insert('user_subscriptions', [
                'subscription_id' => $freeSubscriptionId,
                'user_id' => $this->currentUser['user_id'],
                'plan_id' => 'free',
                'status' => 'active',
                'billing_cycle' => 'monthly',
                'amount' => 0.00,
                'current_period_start' => date('Y-m-d H:i:s'),
                'current_period_end' => date('Y-m-d H:i:s', strtotime('+1 month'))
            ]);
            
            $this->db->commit();
            
            $this->sendSuccess(null, 'Subscription cancelled successfully. You have been moved to the free plan.');
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Subscription cancellation failed: " . $e->getMessage());
            $this->sendError('Failed to cancel subscription', 500);
        }
    }
    
    /**
     * Get payment history
     */
    private function getPaymentHistory() {
        $this->requireAuth();
        
        $page = intval($this->requestData['page'] ?? 1);
        $limit = min(intval($this->requestData['limit'] ?? 20), 100);
        
        $offset = ($page - 1) * $limit;
        
        $transactions = $this->db->select('payment_transactions', 
            ['user_id' => $this->currentUser['user_id']], 
            "ORDER BY created_at DESC LIMIT $limit OFFSET $offset"
        );
        
        $total = $this->db->count('payment_transactions', ['user_id' => $this->currentUser['user_id']]);
        
        $formattedTransactions = array_map(function($txn) {
            return [
                'transaction_id' => $txn['transaction_id'],
                'type' => $txn['type'],
                'status' => $txn['status'],
                'amount' => floatval($txn['amount']),
                'currency' => $txn['currency'],
                'payment_method' => $txn['payment_method'],
                'description' => $txn['description'],
                'created_at' => $txn['created_at'],
                'processed_at' => $txn['processed_at']
            ];
        }, $transactions);
        
        $this->sendSuccess([
            'transactions' => $formattedTransactions,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]);
    }
    
    /**
     * Get invoices
     */
    private function getInvoices() {
        $this->requireAuth();
        
        $invoices = $this->db->select('invoices', 
            ['user_id' => $this->currentUser['user_id']], 
            'ORDER BY created_at DESC LIMIT 20'
        );
        
        $formattedInvoices = array_map(function($invoice) {
            return [
                'invoice_id' => $invoice['invoice_id'],
                'invoice_number' => $invoice['invoice_number'],
                'status' => $invoice['status'],
                'subtotal' => floatval($invoice['subtotal']),
                'tax_amount' => floatval($invoice['tax_amount']),
                'total_amount' => floatval($invoice['total_amount']),
                'currency' => $invoice['currency'],
                'due_date' => $invoice['due_date'],
                'paid_at' => $invoice['paid_at'],
                'line_items' => json_decode($invoice['line_items'], true),
                'created_at' => $invoice['created_at']
            ];
        }, $invoices);
        
        $this->sendSuccess(['invoices' => $formattedInvoices]);
    }
    
    /**
     * Handle payment webhooks
     */
    private function handleWebhook() {
        $payload = file_get_contents('php://input');
        $signature = $_SERVER['HTTP_STRIPE_SIGNATURE'] ?? '';
        
        // In a real implementation, you would verify the webhook signature
        // and process the webhook data accordingly
        
        $this->sendSuccess(null, 'Webhook received');
    }
    
    /**
     * Process payment based on method
     */
    private function processPayment($method, $package, $paymentData, $transactionId) {
        switch ($method) {
            case 'demo':
                // Demo payment always succeeds
                return [
                    'success' => true,
                    'provider_id' => 'demo_' . $transactionId
                ];
                
            case 'stripe':
                return $this->processStripePayment($package, $paymentData, $transactionId);
                
            case 'paypal':
                return $this->processPayPalPayment($package, $paymentData, $transactionId);
                
            default:
                return [
                    'success' => false,
                    'error' => 'Unsupported payment method'
                ];
        }
    }
    
    /**
     * Process Stripe payment
     */
    private function processStripePayment($package, $paymentData, $transactionId) {
        // In a real implementation, you would integrate with Stripe API
        // For demo purposes, we'll simulate success
        
        if (isset($paymentData['payment_method_id'])) {
            return [
                'success' => true,
                'provider_id' => 'stripe_' . $transactionId
            ];
        }
        
        return [
            'success' => false,
            'error' => 'Invalid Stripe payment data'
        ];
    }
    
    /**
     * Process PayPal payment
     */
    private function processPayPalPayment($package, $paymentData, $transactionId) {
        // In a real implementation, you would integrate with PayPal API
        // For demo purposes, we'll simulate success
        
        if (isset($paymentData['order_id'])) {
            return [
                'success' => true,
                'provider_id' => 'paypal_' . $transactionId
            ];
        }
        
        return [
            'success' => false,
            'error' => 'Invalid PayPal payment data'
        ];
    }
    
    /**
     * Add credits to user account
     */
    private function addCreditsToUser($userId, $credits, $referenceId) {
        // Update credit balance
        $this->db->update('user_credit_balances', [
            'total_credits' => 'total_credits + ' . $credits
        ], ['user_id' => $userId]);
        
        // Record transaction
        $this->db->insert('credit_transactions', [
            'transaction_id' => 'txn_' . uniqid(),
            'user_id' => $userId,
            'type' => 'purchase',
            'credits_amount' => $credits,
            'description' => "Credits purchased",
            'reference_id' => $referenceId,
            'reference_type' => 'payment'
        ]);
    }
    
    /**
     * Get user credits
     */
    private function getUserCredits() {
        $balance = $this->db->selectOne('user_credit_balances', [
            'user_id' => $this->currentUser['user_id']
        ]);
        
        return $balance['available_credits'] ?? 0;
    }
}
?>
