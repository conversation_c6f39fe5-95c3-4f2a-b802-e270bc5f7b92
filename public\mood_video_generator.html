<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sutradhar 2070 - Mood-Based Video Generator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'cyber-blue': '#00D4FF',
                        'cyber-purple': '#8B5CF6',
                        'cyber-pink': '#F472B6',
                        'cyber-green': '#10B981',
                        'cyber-orange': '#F59E0B',
                        'dark-bg': '#0F0F23',
                        'dark-card': '#1A1A2E',
                        'neon-glow': '#00FFFF'
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #0F0F23 0%, #1A1A2E 50%, #16213E 100%);
            font-family: 'Exo 2', sans-serif;
            color: white;
            overflow-x: hidden;
        }

        .cyber-gradient {
            background: linear-gradient(135deg, #00D4FF 0%, #8B5CF6 25%, #F472B6 50%, #10B981 75%, #F59E0B 100%);
        }

        .glass-morphism {
            background: rgba(26, 26, 46, 0.7);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 212, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .mood-card {
            background: rgba(26, 26, 46, 0.8);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .mood-card:hover {
            transform: translateY(-10px) scale(1.02);
            border-color: #00D4FF;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 30px rgba(0, 212, 255, 0.5);
        }

        .mood-card.selected {
            border-color: #00D4FF;
            background: rgba(0, 212, 255, 0.1);
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.8);
        }

        .cyber-button {
            background: linear-gradient(45deg, #00D4FF, #8B5CF6);
            border: 1px solid #00D4FF;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .cyber-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.8);
        }

        .cyber-input {
            background: rgba(26, 26, 46, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            color: white;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .cyber-input:focus {
            border-color: #00D4FF;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            outline: none;
        }

        .cyber-select {
            background: rgba(26, 26, 46, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            color: white;
            backdrop-filter: blur(10px);
        }

        .cyber-select:focus {
            border-color: #00D4FF;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            outline: none;
        }

        .prompt-card {
            background: rgba(26, 26, 46, 0.9);
            border: 1px solid rgba(139, 92, 246, 0.3);
            backdrop-filter: blur(15px);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .prompt-card:hover {
            border-color: #8B5CF6;
            box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
            transform: translateY(-2px);
        }

        .prompt-card.selected {
            border-color: #8B5CF6;
            background: rgba(139, 92, 246, 0.1);
            box-shadow: 0 0 25px rgba(139, 92, 246, 0.7);
        }

        .floating-element {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .hologram-text {
            background: linear-gradient(45deg, #00D4FF, #8B5CF6, #F472B6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: hologram 3s ease-in-out infinite;
        }

        @keyframes hologram {
            0%, 100% { opacity: 0.8; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
        }

        .progress-bar {
            background: rgba(26, 26, 46, 0.8);
            border: 1px solid #00D4FF;
            border-radius: 20px;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, #00D4FF, #8B5CF6);
            height: 100%;
            border-radius: 20px;
            transition: width 0.3s ease;
        }

        .category-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            margin: 2px;
        }

        .category-positive { background: rgba(16, 185, 129, 0.2); color: #10B981; border: 1px solid #10B981; }
        .category-calm { background: rgba(135, 206, 235, 0.2); color: #87CEEB; border: 1px solid #87CEEB; }
        .category-dramatic { background: rgba(220, 20, 60, 0.2); color: #DC143C; border: 1px solid #DC143C; }
        .category-mysterious { background: rgba(75, 0, 130, 0.2); color: #4B0082; border: 1px solid #4B0082; }
        .category-romantic { background: rgba(255, 105, 180, 0.2); color: #FF69B4; border: 1px solid #FF69B4; }
        .category-futuristic { background: rgba(0, 255, 255, 0.2); color: #00FFFF; border: 1px solid #00FFFF; }

        .step-indicator {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
        }

        .step {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(26, 26, 46, 0.8);
            border: 2px solid rgba(0, 212, 255, 0.3);
            color: #00D4FF;
            font-weight: bold;
            margin-right: 1rem;
            position: relative;
        }

        .step.active {
            background: #00D4FF;
            color: #0F0F23;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.8);
        }

        .step.completed {
            background: #10B981;
            border-color: #10B981;
            color: white;
        }

        .step-line {
            flex: 1;
            height: 2px;
            background: rgba(0, 212, 255, 0.3);
            margin-right: 1rem;
        }

        .step-line.completed {
            background: #10B981;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="glass-morphism border-b border-cyber-blue/30 sticky top-0 z-50">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 cyber-gradient rounded-full flex items-center justify-center floating-element">
                        <span class="text-white font-bold text-xl">🎭</span>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold hologram-text">SUTRADHAR 2070</h1>
                        <p class="text-cyber-blue text-sm">Mood-Based Video Generation Engine</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="w-3 h-3 bg-cyber-green rounded-full animate-pulse"></div>
                    <span class="text-cyber-green text-sm font-semibold">NEURAL SYSTEMS ONLINE</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-8">
        <div class="max-w-7xl mx-auto">
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step active" id="step1">1</div>
                <div class="step-line" id="line1"></div>
                <div class="step" id="step2">2</div>
                <div class="step-line" id="line2"></div>
                <div class="step" id="step3">3</div>
                <div class="step-line" id="line3"></div>
                <div class="step" id="step4">4</div>
                <div class="step-line" id="line4"></div>
                <div class="step" id="step5">5</div>
            </div>

            <!-- Step 1: Mood Selection -->
            <section id="moodSelection" class="glass-morphism p-8 rounded-lg mb-8">
                <h2 class="text-2xl font-bold mb-6 text-center hologram-text">
                    STEP 1: SELECT EMOTIONAL MOOD
                </h2>
                <p class="text-center text-gray-300 mb-8">Choose the emotional atmosphere for your video</p>
                
                <div id="moodCategories" class="space-y-6">
                    <!-- Mood categories will be loaded here -->
                </div>
            </section>

            <!-- Step 2: Topic Selection -->
            <section id="topicSelection" class="glass-morphism p-8 rounded-lg mb-8 hidden">
                <h2 class="text-2xl font-bold mb-6 text-center hologram-text">
                    STEP 2: CHOOSE CONTENT TOPIC
                </h2>
                <p class="text-center text-gray-300 mb-8">Select the main theme for your video content</p>
                
                <div class="grid md:grid-cols-3 gap-6" id="topicGrid">
                    <!-- Topic options will be loaded here -->
                </div>
            </section>

            <!-- Step 3: Image Type Selection -->
            <section id="imageTypeSelection" class="glass-morphism p-8 rounded-lg mb-8 hidden">
                <h2 class="text-2xl font-bold mb-6 text-center hologram-text">
                    STEP 3: SELECT VISUAL STYLE
                </h2>
                <p class="text-center text-gray-300 mb-8">Choose the visual approach for your video</p>
                
                <div class="grid md:grid-cols-3 gap-6" id="imageTypeGrid">
                    <!-- Image type options will be loaded here -->
                </div>
            </section>

            <!-- Step 4: Inspiration Input -->
            <section id="inspirationInput" class="glass-morphism p-8 rounded-lg mb-8 hidden">
                <h2 class="text-2xl font-bold mb-6 text-center hologram-text">
                    STEP 4: ADD INSPIRATION
                </h2>
                <p class="text-center text-gray-300 mb-8">Describe what inspires your video concept</p>
                
                <div class="max-w-2xl mx-auto">
                    <textarea 
                        id="inspirationText" 
                        class="cyber-input w-full p-4 rounded-lg h-32 resize-none"
                        placeholder="Describe your inspiration... (e.g., 'A peaceful morning in a Japanese garden with cherry blossoms falling gently while a cat watches from a wooden bridge')"
                    ></textarea>
                    
                    <div class="mt-4 text-center">
                        <button id="generatePromptsBtn" class="cyber-button px-8 py-3 rounded-lg font-bold">
                            🧠 GENERATE PROMPTS
                        </button>
                    </div>
                </div>
            </section>

            <!-- Step 5: Prompt Selection -->
            <section id="promptSelection" class="glass-morphism p-8 rounded-lg mb-8 hidden">
                <h2 class="text-2xl font-bold mb-6 text-center hologram-text">
                    STEP 5: SELECT YOUR PROMPT
                </h2>
                <p class="text-center text-gray-300 mb-8">Choose from AI-generated prompts tailored to your preferences</p>
                
                <div class="mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <span class="text-cyber-blue font-semibold">Generated Prompts:</span>
                        <span id="promptCount" class="text-gray-400">0 prompts</span>
                    </div>
                    <div class="progress-bar h-2">
                        <div id="promptProgress" class="progress-fill" style="width: 0%"></div>
                    </div>
                </div>
                
                <div id="promptGrid" class="grid gap-4 mb-8">
                    <!-- Generated prompts will appear here -->
                </div>
                
                <div class="text-center">
                    <button id="generateVideoBtn" class="cyber-button px-12 py-4 rounded-lg text-lg font-bold hidden">
                        🎬 GENERATE VIDEO
                    </button>
                </div>
            </section>

            <!-- Progress Section -->
            <section id="progressSection" class="glass-morphism p-8 rounded-lg hidden">
                <h3 class="text-xl font-semibold mb-4 text-center text-cyber-blue">
                    NEURAL PROCESSING IN PROGRESS
                </h3>
                <div class="space-y-4">
                    <div class="progress-bar h-4">
                        <div id="videoProgress" class="progress-fill" style="width: 0%"></div>
                    </div>
                    <div id="statusText" class="text-center text-gray-300">Initializing...</div>
                </div>
            </section>
        </div>
    </main>

    <script src="mood_video_generator.js"></script>
</body>
</html>
