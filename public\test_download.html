<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Download - Sutradhar Engine</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        audio, video { width: 100%; max-width: 400px; margin: 10px 0; }
        .download-btn { 
            background-color: #FF9933; 
            color: white; 
            padding: 10px 20px; 
            text-decoration: none; 
            border-radius: 5px; 
            margin: 5px;
            display: inline-block;
        }
        .download-btn:hover { background-color: #e6851a; }
    </style>
</head>
<body>
    <h1>🎭 Sutradhar Engine - Download Test</h1>
    
    <div class="test-section">
        <h2>📁 Latest Generated Files</h2>
        <p>Testing download and playback functionality:</p>
        
        <h3>🎵 Audio File (WAV)</h3>
        <audio controls>
            <source src="download.php?file=data/output_history/sutradhar_68559c00ab2b63.00219948/audio.wav" type="audio/wav">
            Your browser does not support the audio element.
        </audio>
        <br>
        <a href="download.php?file=data/output_history/sutradhar_68559c00ab2b63.00219948/audio.wav&download=1" class="download-btn">Download Audio</a>
        <a href="download.php?file=data/output_history/sutradhar_68559c00ab2b63.00219948/audio.wav" target="_blank" class="download-btn">View Audio</a>
        
        <h3>📹 Video File (MP4)</h3>
        <video controls width="400">
            <source src="download.php?file=data/output_history/sutradhar_68559c00ab2b63.00219948/video.mp4" type="video/mp4">
            Your browser does not support the video element.
        </video>
        <br>
        <a href="download.php?file=data/output_history/sutradhar_68559c00ab2b63.00219948/video.mp4&download=1" class="download-btn">Download Video</a>
        <a href="download.php?file=data/output_history/sutradhar_68559c00ab2b63.00219948/video.mp4" target="_blank" class="download-btn">View Video</a>
        
        <h3>📝 Subtitles File (SRT)</h3>
        <a href="download.php?file=data/output_history/sutradhar_68559c00ab2b63.00219948/subtitles.srt&download=1" class="download-btn">Download Subtitles</a>
        <a href="download.php?file=data/output_history/sutradhar_68559c00ab2b63.00219948/subtitles.srt" target="_blank" class="download-btn">View Subtitles</a>
    </div>
    
    <div class="test-section">
        <h2>🔧 Test Other Files</h2>
        <p>Testing background music files:</p>
        
        <h3>🎵 Background Music</h3>
        <audio controls>
            <source src="download.php?file=assets/audio/music/background.wav" type="audio/wav">
            Your browser does not support the audio element.
        </audio>
        <br>
        <a href="download.php?file=assets/audio/music/background.wav&download=1" class="download-btn">Download Background Music</a>
        
        <h3>🎵 Comedy Music</h3>
        <audio controls>
            <source src="download.php?file=assets/audio/music/comedy.wav" type="audio/wav">
            Your browser does not support the audio element.
        </audio>
        <br>
        <a href="download.php?file=assets/audio/music/comedy.wav&download=1" class="download-btn">Download Comedy Music</a>
        
        <h3>🔊 Sound Effects</h3>
        <audio controls>
            <source src="download.php?file=assets/audio/effects/ding.wav" type="audio/wav">
            Your browser does not support the audio element.
        </audio>
        <br>
        <a href="download.php?file=assets/audio/effects/ding.wav&download=1" class="download-btn">Download Ding Effect</a>
    </div>
    
    <div class="test-section">
        <h2>📊 File Information</h2>
        <p>Click the buttons below to test file serving:</p>
        
        <button onclick="testFileInfo()">Test File Info</button>
        <div id="fileInfo"></div>
    </div>
    
    <script>
        async function testFileInfo() {
            const files = [
                'data/output_history/sutradhar_68559c00ab2b63.00219948/audio.wav',
                'data/output_history/sutradhar_68559c00ab2b63.00219948/video.mp4',
                'data/output_history/sutradhar_68559c00ab2b63.00219948/subtitles.srt',
                'assets/audio/music/background.wav'
            ];
            
            const infoDiv = document.getElementById('fileInfo');
            infoDiv.innerHTML = '<p>Testing files...</p>';
            
            let results = '';
            
            for (const file of files) {
                try {
                    const response = await fetch(`download.php?file=${encodeURIComponent(file)}`, { method: 'HEAD' });
                    const size = response.headers.get('Content-Length');
                    const type = response.headers.get('Content-Type');
                    
                    results += `<p><strong>${file}</strong><br>`;
                    results += `Status: ${response.status} ${response.statusText}<br>`;
                    results += `Size: ${size ? (parseInt(size) / 1024).toFixed(1) + ' KB' : 'Unknown'}<br>`;
                    results += `Type: ${type || 'Unknown'}</p>`;
                } catch (error) {
                    results += `<p><strong>${file}</strong><br>Error: ${error.message}</p>`;
                }
            }
            
            infoDiv.innerHTML = results;
        }
        
        // Auto-test on page load
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Testing download functionality...');
            testFileInfo();
        });
    </script>
</body>
</html>
