<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pricing Plans - Sutradhar 2070</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'cyber-blue': '#00D4FF',
                        'cyber-purple': '#8B5CF6',
                        'cyber-pink': '#F472B6',
                        'cyber-green': '#10B981',
                        'cyber-orange': '#F59E0B',
                        'dark-bg': '#0F0F23',
                        'dark-card': '#1A1A2E'
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #0F0F23 0%, #1A1A2E 50%, #16213E 100%);
            font-family: 'Exo 2', sans-serif;
            color: white;
        }

        .hologram-text {
            background: linear-gradient(45deg, #00D4FF, #8B5CF6, #F472B6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: hologram 3s ease-in-out infinite;
        }

        @keyframes hologram {
            0%, 100% { opacity: 0.8; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
        }

        .pricing-card {
            background: rgba(26, 26, 46, 0.8);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .pricing-card:hover {
            transform: translateY(-10px);
            border-color: #00D4FF;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 30px rgba(0, 212, 255, 0.5);
        }

        .pricing-card.popular {
            border-color: #8B5CF6;
            background: rgba(139, 92, 246, 0.1);
        }

        .pricing-card.popular::before {
            content: 'MOST POPULAR';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(45deg, #8B5CF6, #F472B6);
            color: white;
            text-align: center;
            padding: 8px;
            font-size: 12px;
            font-weight: bold;
            letter-spacing: 1px;
        }

        .cyber-button {
            background: linear-gradient(45deg, #00D4FF, #8B5CF6);
            border: 1px solid #00D4FF;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .cyber-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.8);
        }

        .cyber-button.secondary {
            background: transparent;
            border: 1px solid #8B5CF6;
            color: #8B5CF6;
        }

        .cyber-button.secondary:hover {
            background: #8B5CF6;
            color: white;
        }

        .feature-check {
            color: #10B981;
            font-weight: bold;
        }

        .feature-cross {
            color: #EF4444;
        }

        .comparison-table {
            background: rgba(26, 26, 46, 0.8);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 212, 255, 0.3);
        }

        .testimonial-card {
            background: rgba(26, 26, 46, 0.6);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 212, 255, 0.2);
            transition: all 0.3s ease;
        }

        .testimonial-card:hover {
            border-color: #00D4FF;
            background: rgba(26, 26, 46, 0.8);
        }

        .floating-element {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .tier-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .tier-badge.free { background: linear-gradient(45deg, #6B7280, #9CA3AF); }
        .tier-badge.pro { background: linear-gradient(45deg, #10B981, #059669); }
        .tier-badge.business { background: linear-gradient(45deg, #F59E0B, #D97706); }
        .tier-badge.enterprise { background: linear-gradient(45deg, #8B5CF6, #7C3AED); }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="relative z-50">
        <nav class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-cyber-blue to-cyber-purple rounded-full flex items-center justify-center floating-element">
                        <span class="text-white font-bold text-lg">🎭</span>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold hologram-text">SUTRADHAR 2070</h1>
                        <p class="text-cyber-blue text-xs">AI Video Generation</p>
                    </div>
                </div>
                <div class="flex items-center space-x-6">
                    <a href="index_2070.html" class="text-gray-300 hover:text-white transition-colors">Home</a>
                    <a href="mood_video_generator.html" class="text-gray-300 hover:text-white transition-colors">Generate</a>
                    <a href="dashboard.html" class="text-gray-300 hover:text-white transition-colors">Dashboard</a>
                    <button class="cyber-button px-6 py-2 rounded-lg font-bold">Get Started</button>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="py-20 text-center">
        <div class="container mx-auto px-6">
            <h1 class="text-5xl md:text-7xl font-bold hologram-text mb-6">
                Choose Your Plan
            </h1>
            <p class="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                Unlock the power of AI-driven mood-based video generation. From hobbyists to enterprises, 
                we have the perfect plan for your creative needs.
            </p>
            <div class="flex justify-center items-center space-x-4 mb-12">
                <span class="text-gray-400">Monthly</span>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer" id="billing-toggle">
                    <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyber-blue"></div>
                </label>
                <span class="text-gray-400">Annual <span class="text-cyber-green text-sm">(Save 20%)</span></span>
            </div>
        </div>
    </section>

    <!-- Pricing Cards -->
    <section class="py-20">
        <div class="container mx-auto px-6">
            <div class="grid md:grid-cols-4 gap-8 max-w-7xl mx-auto">
                <!-- Free Tier -->
                <div class="pricing-card p-8 rounded-2xl">
                    <div class="text-center mb-8">
                        <span class="tier-badge free">Free</span>
                        <h3 class="text-2xl font-bold mt-4 mb-2">Starter</h3>
                        <div class="text-4xl font-bold mb-2">$0</div>
                        <p class="text-gray-400">Perfect for trying out</p>
                    </div>
                    
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center">
                            <span class="feature-check mr-3">✓</span>
                            <span>50 credits/month</span>
                        </li>
                        <li class="flex items-center">
                            <span class="feature-check mr-3">✓</span>
                            <span>Standard quality</span>
                        </li>
                        <li class="flex items-center">
                            <span class="feature-check mr-3">✓</span>
                            <span>Basic moods</span>
                        </li>
                        <li class="flex items-center">
                            <span class="feature-cross mr-3">✗</span>
                            <span class="text-gray-500">Watermarked videos</span>
                        </li>
                        <li class="flex items-center">
                            <span class="feature-cross mr-3">✗</span>
                            <span class="text-gray-500">Community support</span>
                        </li>
                    </ul>
                    
                    <button class="w-full cyber-button secondary py-3 rounded-lg font-bold">
                        Get Started Free
                    </button>
                </div>

                <!-- Pro Tier -->
                <div class="pricing-card popular p-8 rounded-2xl pt-12">
                    <div class="text-center mb-8">
                        <span class="tier-badge pro">Pro</span>
                        <h3 class="text-2xl font-bold mt-4 mb-2">Creator</h3>
                        <div class="text-4xl font-bold mb-2">
                            $<span class="monthly-price">9.99</span><span class="annual-price hidden">7.99</span>
                        </div>
                        <p class="text-gray-400">For content creators</p>
                    </div>
                    
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center">
                            <span class="feature-check mr-3">✓</span>
                            <span>500 credits/month</span>
                        </li>
                        <li class="flex items-center">
                            <span class="feature-check mr-3">✓</span>
                            <span>HD quality</span>
                        </li>
                        <li class="flex items-center">
                            <span class="feature-check mr-3">✓</span>
                            <span>All 30 moods</span>
                        </li>
                        <li class="flex items-center">
                            <span class="feature-check mr-3">✓</span>
                            <span>No watermarks</span>
                        </li>
                        <li class="flex items-center">
                            <span class="feature-check mr-3">✓</span>
                            <span>Email support</span>
                        </li>
                    </ul>
                    
                    <button class="w-full cyber-button py-3 rounded-lg font-bold">
                        Start Pro Trial
                    </button>
                </div>

                <!-- Business Tier -->
                <div class="pricing-card p-8 rounded-2xl">
                    <div class="text-center mb-8">
                        <span class="tier-badge business">Business</span>
                        <h3 class="text-2xl font-bold mt-4 mb-2">Professional</h3>
                        <div class="text-4xl font-bold mb-2">
                            $<span class="monthly-price">29.99</span><span class="annual-price hidden">23.99</span>
                        </div>
                        <p class="text-gray-400">For growing businesses</p>
                    </div>
                    
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center">
                            <span class="feature-check mr-3">✓</span>
                            <span>2000 credits/month</span>
                        </li>
                        <li class="flex items-center">
                            <span class="feature-check mr-3">✓</span>
                            <span>Priority processing</span>
                        </li>
                        <li class="flex items-center">
                            <span class="feature-check mr-3">✓</span>
                            <span>API access</span>
                        </li>
                        <li class="flex items-center">
                            <span class="feature-check mr-3">✓</span>
                            <span>Custom branding</span>
                        </li>
                        <li class="flex items-center">
                            <span class="feature-check mr-3">✓</span>
                            <span>Priority support</span>
                        </li>
                    </ul>
                    
                    <button class="w-full cyber-button secondary py-3 rounded-lg font-bold">
                        Start Business Trial
                    </button>
                </div>

                <!-- Enterprise Tier -->
                <div class="pricing-card p-8 rounded-2xl">
                    <div class="text-center mb-8">
                        <span class="tier-badge enterprise">Enterprise</span>
                        <h3 class="text-2xl font-bold mt-4 mb-2">Enterprise</h3>
                        <div class="text-4xl font-bold mb-2">Custom</div>
                        <p class="text-gray-400">For large organizations</p>
                    </div>
                    
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center">
                            <span class="feature-check mr-3">✓</span>
                            <span>Unlimited credits</span>
                        </li>
                        <li class="flex items-center">
                            <span class="feature-check mr-3">✓</span>
                            <span>4K quality</span>
                        </li>
                        <li class="flex items-center">
                            <span class="feature-check mr-3">✓</span>
                            <span>Dedicated support</span>
                        </li>
                        <li class="flex items-center">
                            <span class="feature-check mr-3">✓</span>
                            <span>Custom integrations</span>
                        </li>
                        <li class="flex items-center">
                            <span class="feature-check mr-3">✓</span>
                            <span>SLA guarantee</span>
                        </li>
                    </ul>
                    
                    <button class="w-full cyber-button secondary py-3 rounded-lg font-bold">
                        Contact Sales
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Feature Comparison Table -->
    <section class="py-20">
        <div class="container mx-auto px-6">
            <h2 class="text-4xl font-bold text-center hologram-text mb-12">
                Detailed Feature Comparison
            </h2>
            
            <div class="comparison-table rounded-2xl overflow-hidden max-w-6xl mx-auto">
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gradient-to-r from-cyber-blue to-cyber-purple">
                            <tr>
                                <th class="text-left p-6 font-bold">Features</th>
                                <th class="text-center p-6 font-bold">Free</th>
                                <th class="text-center p-6 font-bold">Pro</th>
                                <th class="text-center p-6 font-bold">Business</th>
                                <th class="text-center p-6 font-bold">Enterprise</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-700">
                            <tr class="hover:bg-gray-800/50">
                                <td class="p-6 font-semibold">Monthly Credits</td>
                                <td class="text-center p-6">50</td>
                                <td class="text-center p-6">500</td>
                                <td class="text-center p-6">2,000</td>
                                <td class="text-center p-6">Unlimited</td>
                            </tr>
                            <tr class="hover:bg-gray-800/50">
                                <td class="p-6 font-semibold">Video Quality</td>
                                <td class="text-center p-6">Standard</td>
                                <td class="text-center p-6">HD</td>
                                <td class="text-center p-6">HD</td>
                                <td class="text-center p-6">4K</td>
                            </tr>
                            <tr class="hover:bg-gray-800/50">
                                <td class="p-6 font-semibold">Mood Options</td>
                                <td class="text-center p-6">10</td>
                                <td class="text-center p-6">30</td>
                                <td class="text-center p-6">30</td>
                                <td class="text-center p-6">30+</td>
                            </tr>
                            <tr class="hover:bg-gray-800/50">
                                <td class="p-6 font-semibold">Watermark</td>
                                <td class="text-center p-6"><span class="feature-cross">✗</span></td>
                                <td class="text-center p-6"><span class="feature-check">✓</span></td>
                                <td class="text-center p-6"><span class="feature-check">✓</span></td>
                                <td class="text-center p-6"><span class="feature-check">✓</span></td>
                            </tr>
                            <tr class="hover:bg-gray-800/50">
                                <td class="p-6 font-semibold">API Access</td>
                                <td class="text-center p-6"><span class="feature-cross">✗</span></td>
                                <td class="text-center p-6"><span class="feature-cross">✗</span></td>
                                <td class="text-center p-6"><span class="feature-check">✓</span></td>
                                <td class="text-center p-6"><span class="feature-check">✓</span></td>
                            </tr>
                            <tr class="hover:bg-gray-800/50">
                                <td class="p-6 font-semibold">Priority Processing</td>
                                <td class="text-center p-6"><span class="feature-cross">✗</span></td>
                                <td class="text-center p-6"><span class="feature-cross">✗</span></td>
                                <td class="text-center p-6"><span class="feature-check">✓</span></td>
                                <td class="text-center p-6"><span class="feature-check">✓</span></td>
                            </tr>
                            <tr class="hover:bg-gray-800/50">
                                <td class="p-6 font-semibold">Support Level</td>
                                <td class="text-center p-6">Community</td>
                                <td class="text-center p-6">Email</td>
                                <td class="text-center p-6">Priority</td>
                                <td class="text-center p-6">Dedicated</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <section class="py-20">
        <div class="container mx-auto px-6">
            <h2 class="text-4xl font-bold text-center hologram-text mb-12">
                What Our Users Say
            </h2>
            
            <div class="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
                <div class="testimonial-card p-6 rounded-xl">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-cyber-blue to-cyber-purple rounded-full flex items-center justify-center mr-4">
                            <span class="text-white font-bold">SM</span>
                        </div>
                        <div>
                            <h4 class="font-bold">Sarah Martinez</h4>
                            <p class="text-sm text-gray-400">Content Creator</p>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-4">
                        "Sutradhar 2070 has revolutionized my content creation process. The mood-based generation 
                        creates videos that perfectly match my brand's emotional tone."
                    </p>
                    <div class="flex text-cyber-orange">
                        ★★★★★
                    </div>
                </div>

                <div class="testimonial-card p-6 rounded-xl">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-cyber-green to-cyber-blue rounded-full flex items-center justify-center mr-4">
                            <span class="text-white font-bold">DJ</span>
                        </div>
                        <div>
                            <h4 class="font-bold">David Johnson</h4>
                            <p class="text-sm text-gray-400">Marketing Director</p>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-4">
                        "The Business plan's API access has allowed us to integrate video generation directly 
                        into our marketing automation. Incredible ROI!"
                    </p>
                    <div class="flex text-cyber-orange">
                        ★★★★★
                    </div>
                </div>

                <div class="testimonial-card p-6 rounded-xl">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-cyber-purple to-cyber-pink rounded-full flex items-center justify-center mr-4">
                            <span class="text-white font-bold">AL</span>
                        </div>
                        <div>
                            <h4 class="font-bold">Alex Liu</h4>
                            <p class="text-sm text-gray-400">Agency Owner</p>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-4">
                        "Enterprise support is phenomenal. Our dedicated account manager helped us scale 
                        video production for 50+ clients seamlessly."
                    </p>
                    <div class="flex text-cyber-orange">
                        ★★★★★
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-20">
        <div class="container mx-auto px-6">
            <h2 class="text-4xl font-bold text-center hologram-text mb-12">
                Frequently Asked Questions
            </h2>
            
            <div class="max-w-4xl mx-auto space-y-6">
                <div class="testimonial-card p-6 rounded-xl">
                    <h3 class="font-bold text-lg mb-2">How do credits work?</h3>
                    <p class="text-gray-300">
                        Credits are consumed based on the type of video generation. Basic generation costs 10 credits, 
                        mood-optimized costs 15 credits, and premium quality costs 25 credits. Unused credits roll over monthly.
                    </p>
                </div>
                
                <div class="testimonial-card p-6 rounded-xl">
                    <h3 class="font-bold text-lg mb-2">Can I upgrade or downgrade anytime?</h3>
                    <p class="text-gray-300">
                        Yes! You can change your plan at any time. Upgrades take effect immediately, while downgrades 
                        take effect at the next billing cycle.
                    </p>
                </div>
                
                <div class="testimonial-card p-6 rounded-xl">
                    <h3 class="font-bold text-lg mb-2">What's included in the free trial?</h3>
                    <p class="text-gray-300">
                        Pro and Business plans include a 7-day and 14-day free trial respectively, with full access 
                        to all features. No credit card required to start.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 text-center">
        <div class="container mx-auto px-6">
            <h2 class="text-4xl font-bold hologram-text mb-6">
                Ready to Transform Your Video Creation?
            </h2>
            <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                Join thousands of creators who are already using Sutradhar 2070 to generate 
                stunning mood-based videos with AI.
            </p>
            <div class="flex justify-center space-x-4">
                <button class="cyber-button px-8 py-4 rounded-lg font-bold text-lg">
                    Start Free Trial
                </button>
                <button class="cyber-button secondary px-8 py-4 rounded-lg font-bold text-lg">
                    Contact Sales
                </button>
            </div>
        </div>
    </section>

    <script>
        // Billing toggle functionality
        document.getElementById('billing-toggle').addEventListener('change', function() {
            const monthlyPrices = document.querySelectorAll('.monthly-price');
            const annualPrices = document.querySelectorAll('.annual-price');
            
            if (this.checked) {
                monthlyPrices.forEach(el => el.classList.add('hidden'));
                annualPrices.forEach(el => el.classList.remove('hidden'));
            } else {
                monthlyPrices.forEach(el => el.classList.remove('hidden'));
                annualPrices.forEach(el => el.classList.add('hidden'));
            }
        });

        // Animate pricing cards on load
        gsap.from('.pricing-card', {
            duration: 0.8,
            y: 50,
            opacity: 0,
            stagger: 0.2,
            ease: 'power2.out'
        });

        // Animate testimonials
        gsap.from('.testimonial-card', {
            duration: 1,
            y: 30,
            opacity: 0,
            stagger: 0.1,
            ease: 'power2.out',
            scrollTrigger: '.testimonial-card'
        });
    </script>
</body>
</html>
