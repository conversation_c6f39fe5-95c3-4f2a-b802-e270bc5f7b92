<?php
/**
 * Test Generation Script for Sutradhar Engine
 * Tests the complete generation pipeline
 */

// Simulate POST data for testing
$_POST = [
    'flow_type' => 'reel',
    'style' => 'funny',
    'voice_pack' => 'babu_rao',
    'background' => 'home',
    'content_source' => 'text',
    'content_text' => 'Once upon a time, there was a clever crow who found a piece of cheese. A cunning fox saw this and decided to trick the crow with flattery.'
];

$_SERVER['REQUEST_METHOD'] = 'POST';

echo "🎭 Testing Sutradhar Engine Generation\n";
echo "=====================================\n\n";

echo "📝 Test Parameters:\n";
echo "   - Flow Type: " . $_POST['flow_type'] . "\n";
echo "   - Style: " . $_POST['style'] . "\n";
echo "   - Voice Pack: " . $_POST['voice_pack'] . "\n";
echo "   - Background: " . $_POST['background'] . "\n";
echo "   - Content Source: " . $_POST['content_source'] . "\n";
echo "   - Content: " . substr($_POST['content_text'], 0, 50) . "...\n\n";

try {
    // Include the generation engine
    require_once 'generate.php';
    
    echo "🚀 Starting generation...\n";
    
    // Create generator instance
    $generator = new SutradharGenerator();
    
    // Handle the request
    $result = $generator->handleRequest();
    
    echo "📊 Generation Result:\n";
    echo $result . "\n\n";
    
    // Parse the result
    $resultData = json_decode($result, true);
    
    if ($resultData && $resultData['success']) {
        $jobId = $resultData['job_id'];
        echo "✅ Generation started successfully!\n";
        echo "   Job ID: $jobId\n\n";
        
        // Wait a moment and check status
        echo "⏳ Waiting for generation to complete...\n";
        sleep(2);
        
        // Check status
        $_GET['action'] = 'status';
        $_GET['job_id'] = $jobId;
        $_SERVER['REQUEST_METHOD'] = 'GET';
        
        $statusResult = $generator->handleRequest();
        echo "📈 Status Result:\n";
        echo $statusResult . "\n\n";
        
        $statusData = json_decode($statusResult, true);
        
        if ($statusData) {
            echo "📋 Generation Status:\n";
            echo "   Step: " . ($statusData['step'] ?? 'Unknown') . "\n";
            echo "   Status: " . ($statusData['status'] ?? 'Unknown') . "\n";
            echo "   Message: " . ($statusData['message'] ?? 'No message') . "\n";
            echo "   Progress: " . ($statusData['progress'] ?? 0) . "%\n";
            
            if (isset($statusData['error'])) {
                echo "   ❌ Error: " . $statusData['error'] . "\n";
            }
            
            if (isset($statusData['complete']) && $statusData['complete']) {
                echo "   🎉 Generation completed!\n";
                
                if (isset($statusData['output'])) {
                    echo "\n📁 Output Files:\n";
                    foreach ($statusData['output'] as $type => $fileInfo) {
                        echo "   - $type: " . $fileInfo['url'] . " (" . $fileInfo['size'] . ")\n";
                    }
                }
            }
        }
        
    } else {
        echo "❌ Generation failed!\n";
        if ($resultData && isset($resultData['error'])) {
            echo "   Error: " . $resultData['error'] . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "💥 Exception occurred: " . $e->getMessage() . "\n";
    echo "   File: " . $e->getFile() . "\n";
    echo "   Line: " . $e->getLine() . "\n";
}

echo "\n🏁 Test completed!\n";
?>
