<?php
/**
 * Optimized Video Engine - High-performance video generation
 * Focuses on speed, efficiency, and quality for 30-second videos
 */

class OptimizedVideoEngine {
    private $tempDir;
    private $outputDir;
    private $config;
    private $ffmpegPath;
    private $parallelProcesses;
    private $cacheDir;
    
    public function __construct() {
        $this->tempDir = __DIR__ . '/../temp/';
        $this->outputDir = __DIR__ . '/../data/output_history/';
        $this->cacheDir = __DIR__ . '/../temp/cache/';
        $this->ffmpegPath = 'ffmpeg'; // Adjust path as needed
        $this->parallelProcesses = 4; // Number of parallel processes
        
        $this->ensureDirectories();
        $this->loadConfig();
    }
    
    /**
     * Generate optimized video with mood characteristics
     */
    public function generateOptimizedVideo($segments, $background, $style, $jobId, $moodCharacteristics = null) {
        echo "🚀 Starting optimized video generation...\n";
        
        $startTime = microtime(true);
        $outputFile = $this->tempDir . $jobId . '_optimized_video.mp4';
        
        try {
            // Step 1: Parallel scene generation
            $scenes = $this->generateScenesParallel($segments, $moodCharacteristics, $jobId);
            
            // Step 2: Optimized frame creation
            $frames = $this->createOptimizedFrames($scenes, $moodCharacteristics, $jobId);
            
            // Step 3: Fast video assembly
            $success = $this->assembleVideoFast($frames, $outputFile, 30);
            
            if ($success && file_exists($outputFile)) {
                $generationTime = microtime(true) - $startTime;
                echo "✅ Optimized video generated in " . round($generationTime, 2) . " seconds\n";
                echo "📁 File size: " . round(filesize($outputFile)/1024/1024, 2) . " MB\n";
                return $outputFile;
            }
            
            throw new Exception("Failed to generate optimized video");
            
        } catch (Exception $e) {
            echo "❌ Optimization failed: " . $e->getMessage() . "\n";
            // Fallback to standard generation
            return $this->fallbackGeneration($segments, $background, $style, $jobId);
        }
    }
    
    /**
     * Generate scenes in parallel for faster processing
     */
    private function generateScenesParallel($segments, $moodCharacteristics, $jobId) {
        echo "🎬 Generating scenes in parallel...\n";
        
        $scenes = [];
        $processes = [];
        $maxProcesses = min($this->parallelProcesses, count($segments));
        
        // Create scene generation tasks
        $tasks = [];
        foreach ($segments as $index => $segment) {
            $tasks[] = [
                'index' => $index,
                'segment' => $segment,
                'mood' => $moodCharacteristics,
                'output_file' => $this->tempDir . $jobId . "_scene_{$index}.json"
            ];
        }
        
        // Process tasks in batches
        $batches = array_chunk($tasks, $maxProcesses);
        
        foreach ($batches as $batch) {
            $batchProcesses = [];
            
            // Start batch processes
            foreach ($batch as $task) {
                $cmd = $this->buildSceneGenerationCommand($task);
                $process = proc_open($cmd, [
                    0 => ['pipe', 'r'],
                    1 => ['pipe', 'w'],
                    2 => ['pipe', 'w']
                ], $pipes);
                
                if (is_resource($process)) {
                    $batchProcesses[] = [
                        'process' => $process,
                        'pipes' => $pipes,
                        'task' => $task
                    ];
                }
            }
            
            // Wait for batch completion
            foreach ($batchProcesses as $processInfo) {
                $output = stream_get_contents($processInfo['pipes'][1]);
                $error = stream_get_contents($processInfo['pipes'][2]);
                
                fclose($processInfo['pipes'][0]);
                fclose($processInfo['pipes'][1]);
                fclose($processInfo['pipes'][2]);
                
                $returnCode = proc_close($processInfo['process']);
                
                if ($returnCode === 0 && file_exists($processInfo['task']['output_file'])) {
                    $sceneData = json_decode(file_get_contents($processInfo['task']['output_file']), true);
                    $scenes[$processInfo['task']['index']] = $sceneData;
                } else {
                    echo "⚠️ Scene generation failed for segment " . $processInfo['task']['index'] . "\n";
                    $scenes[$processInfo['task']['index']] = $this->generateFallbackScene($processInfo['task']['segment']);
                }
            }
        }
        
        ksort($scenes); // Ensure correct order
        return $scenes;
    }
    
    /**
     * Create optimized frames with caching
     */
    private function createOptimizedFrames($scenes, $moodCharacteristics, $jobId) {
        echo "🖼️ Creating optimized frames...\n";
        
        $frames = [];
        $frameRate = 30;
        $duration = 30;
        $totalFrames = $frameRate * $duration;
        $framesPerScene = intval($totalFrames / count($scenes));
        
        foreach ($scenes as $sceneIndex => $scene) {
            $sceneFrames = $this->generateSceneFrames($scene, $framesPerScene, $moodCharacteristics, $jobId, $sceneIndex);
            $frames = array_merge($frames, $sceneFrames);
        }
        
        // Ensure we have exactly the right number of frames
        if (count($frames) > $totalFrames) {
            $frames = array_slice($frames, 0, $totalFrames);
        } elseif (count($frames) < $totalFrames) {
            // Duplicate last frame to fill
            $lastFrame = end($frames);
            while (count($frames) < $totalFrames) {
                $frames[] = $lastFrame;
            }
        }
        
        return $frames;
    }
    
    /**
     * Generate frames for a specific scene
     */
    private function generateSceneFrames($scene, $frameCount, $moodCharacteristics, $jobId, $sceneIndex) {
        $frames = [];
        $cacheKey = md5(json_encode($scene) . json_encode($moodCharacteristics));
        $cachedFrames = $this->getCachedFrames($cacheKey);
        
        if ($cachedFrames && count($cachedFrames) >= $frameCount) {
            echo "📦 Using cached frames for scene {$sceneIndex}\n";
            return array_slice($cachedFrames, 0, $frameCount);
        }
        
        // Generate new frames
        for ($i = 0; $i < $frameCount; $i++) {
            $frameFile = $this->tempDir . $jobId . "_scene_{$sceneIndex}_frame_{$i}.png";
            
            if ($this->generateSingleFrame($scene, $i, $frameCount, $moodCharacteristics, $frameFile)) {
                $frames[] = $frameFile;
            } else {
                // Use fallback frame
                $frames[] = $this->createFallbackFrame($frameFile, $moodCharacteristics);
            }
        }
        
        // Cache the frames
        $this->cacheFrames($cacheKey, $frames);
        
        return $frames;
    }
    
    /**
     * Fast video assembly using optimized FFmpeg settings
     */
    private function assembleVideoFast($frames, $outputFile, $duration) {
        echo "⚡ Fast video assembly...\n";
        
        if (empty($frames)) {
            throw new Exception("No frames to assemble");
        }
        
        // Create frame list file
        $listFile = $this->tempDir . 'frame_list_' . uniqid() . '.txt';
        $listContent = '';
        
        foreach ($frames as $frame) {
            if (file_exists($frame)) {
                $listContent .= "file '" . realpath($frame) . "'\n";
                $listContent .= "duration 0.033333\n"; // 30 FPS
            }
        }
        
        file_put_contents($listFile, $listContent);
        
        // Optimized FFmpeg command for speed
        $cmd = sprintf(
            '%s -f concat -safe 0 -i "%s" -vf "scale=720:1280:force_original_aspect_ratio=decrease,pad=720:1280:(ow-iw)/2:(oh-ih)/2,fps=30" -c:v libx264 -preset ultrafast -crf 23 -pix_fmt yuv420p -t %d "%s" -y 2>&1',
            $this->ffmpegPath,
            $listFile,
            $duration,
            $outputFile
        );
        
        $startTime = microtime(true);
        exec($cmd, $output, $returnCode);
        $assemblyTime = microtime(true) - $startTime;
        
        // Clean up
        unlink($listFile);
        
        if ($returnCode === 0 && file_exists($outputFile)) {
            echo "✅ Video assembled in " . round($assemblyTime, 2) . " seconds\n";
            return true;
        } else {
            echo "❌ FFmpeg assembly failed: " . implode("\n", $output) . "\n";
            return false;
        }
    }
    
    /**
     * Generate single frame with mood characteristics
     */
    private function generateSingleFrame($scene, $frameIndex, $totalFrames, $moodCharacteristics, $outputFile) {
        // Calculate frame progress (0-1)
        $progress = $frameIndex / max(1, $totalFrames - 1);
        
        // Apply mood-based transformations
        $colors = $this->getMoodColors($moodCharacteristics);
        $lighting = $this->getMoodLighting($moodCharacteristics, $progress);
        $composition = $this->getMoodComposition($moodCharacteristics);
        
        // Use ImageMagick or GD for frame generation
        if (extension_loaded('imagick')) {
            return $this->generateFrameWithImageMagick($scene, $colors, $lighting, $composition, $outputFile);
        } elseif (extension_loaded('gd')) {
            return $this->generateFrameWithGD($scene, $colors, $lighting, $composition, $outputFile);
        } else {
            return $this->generateFrameWithFallback($scene, $outputFile);
        }
    }
    
    /**
     * Generate frame using ImageMagick (preferred)
     */
    private function generateFrameWithImageMagick($scene, $colors, $lighting, $composition, $outputFile) {
        try {
            $image = new Imagick();
            $image->newImage(720, 1280, $colors['background']);
            $image->setImageFormat('png');
            
            // Apply mood-based effects
            $this->applyMoodEffectsImageMagick($image, $colors, $lighting, $composition);
            
            // Add scene content
            $this->addSceneContentImageMagick($image, $scene);
            
            $image->writeImage($outputFile);
            $image->destroy();
            
            return true;
        } catch (Exception $e) {
            echo "⚠️ ImageMagick frame generation failed: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * Generate frame using GD (fallback)
     */
    private function generateFrameWithGD($scene, $colors, $lighting, $composition, $outputFile) {
        try {
            $image = imagecreatetruecolor(720, 1280);
            
            // Apply background color
            $bgColor = $this->parseColorForGD($image, $colors['background']);
            imagefill($image, 0, 0, $bgColor);
            
            // Apply mood effects
            $this->applyMoodEffectsGD($image, $colors, $lighting, $composition);
            
            // Add scene content
            $this->addSceneContentGD($image, $scene);
            
            imagepng($image, $outputFile);
            imagedestroy($image);
            
            return true;
        } catch (Exception $e) {
            echo "⚠️ GD frame generation failed: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * Fallback frame generation
     */
    private function generateFrameWithFallback($scene, $outputFile) {
        // Create a simple colored rectangle as fallback
        $cmd = sprintf(
            '%s -f lavfi -i "color=c=blue:size=720x1280:duration=1" -frames:v 1 "%s" -y 2>&1',
            $this->ffmpegPath,
            $outputFile
        );
        
        exec($cmd, $output, $returnCode);
        return $returnCode === 0 && file_exists($outputFile);
    }
    
    /**
     * Get mood-based colors
     */
    private function getMoodColors($moodCharacteristics) {
        if (!$moodCharacteristics || !isset($moodCharacteristics['visual_style']['colors'])) {
            return [
                'background' => '#1A1A2E',
                'primary' => '#00D4FF',
                'secondary' => '#8B5CF6',
                'accent' => '#F472B6'
            ];
        }
        
        $moodColors = $moodCharacteristics['visual_style']['colors'];
        $colorMap = [
            'bright_yellow' => '#FFD700',
            'electric_blue' => '#00D4FF',
            'vibrant_orange' => '#FF6B35',
            'deep_blue' => '#1E3A8A',
            'forest_green' => '#065F46',
            'soft_blue' => '#93C5FD',
            'pale_green' => '#BBF7D0',
            'cream_white' => '#FEF3C7',
            'deep_red' => '#DC2626',
            'purple' => '#8B5CF6',
            'black' => '#000000',
            'white' => '#FFFFFF'
        ];
        
        $colors = [];
        foreach ($moodColors as $index => $colorName) {
            $hexColor = $colorMap[$colorName] ?? '#1A1A2E';
            switch ($index) {
                case 0:
                    $colors['background'] = $hexColor;
                    break;
                case 1:
                    $colors['primary'] = $hexColor;
                    break;
                case 2:
                    $colors['secondary'] = $hexColor;
                    break;
                default:
                    $colors['accent'] = $hexColor;
                    break;
            }
        }
        
        return $colors;
    }
    
    /**
     * Get mood-based lighting
     */
    private function getMoodLighting($moodCharacteristics, $progress) {
        if (!$moodCharacteristics || !isset($moodCharacteristics['visual_style']['lighting'])) {
            return ['intensity' => 0.5, 'direction' => 'center'];
        }
        
        $lighting = $moodCharacteristics['visual_style']['lighting'];
        
        $lightingMap = [
            'high_contrast_bright' => ['intensity' => 0.9, 'direction' => 'top'],
            'soft_upward' => ['intensity' => 0.6, 'direction' => 'bottom'],
            'bouncy_colorful' => ['intensity' => 0.7, 'direction' => 'center'],
            'strong_directional' => ['intensity' => 0.8, 'direction' => 'left'],
            'natural_dynamic' => ['intensity' => 0.5 + ($progress * 0.3), 'direction' => 'center'],
            'soft_diffused' => ['intensity' => 0.4, 'direction' => 'center'],
            'gentle_focused' => ['intensity' => 0.5, 'direction' => 'center'],
            'warm_vintage' => ['intensity' => 0.6, 'direction' => 'top']
        ];
        
        return $lightingMap[$lighting] ?? ['intensity' => 0.5, 'direction' => 'center'];
    }
    
    /**
     * Get mood-based composition
     */
    private function getMoodComposition($moodCharacteristics) {
        if (!$moodCharacteristics || !isset($moodCharacteristics['visual_style']['composition'])) {
            return ['style' => 'centered', 'balance' => 'symmetric'];
        }
        
        $composition = $moodCharacteristics['visual_style']['composition'];
        
        $compositionMap = [
            'energetic_angles' => ['style' => 'dynamic', 'balance' => 'asymmetric'],
            'heroic_perspective' => ['style' => 'wide', 'balance' => 'bottom_heavy'],
            'asymmetrical_fun' => ['style' => 'playful', 'balance' => 'asymmetric'],
            'centered_strong' => ['style' => 'centered', 'balance' => 'symmetric'],
            'wide_expansive' => ['style' => 'wide', 'balance' => 'horizontal'],
            'balanced_harmonious' => ['style' => 'balanced', 'balance' => 'symmetric'],
            'centered_reflective' => ['style' => 'centered', 'balance' => 'symmetric'],
            'soft_dreamy' => ['style' => 'soft', 'balance' => 'floating']
        ];
        
        return $compositionMap[$composition] ?? ['style' => 'centered', 'balance' => 'symmetric'];
    }

    /**
     * Apply mood effects using ImageMagick
     */
    private function applyMoodEffectsImageMagick($image, $colors, $lighting, $composition) {
        // Apply lighting effects
        if ($lighting['intensity'] > 0.7) {
            $image->brightnessContrastImage(20, 10);
        } elseif ($lighting['intensity'] < 0.4) {
            $image->brightnessContrastImage(-10, 5);
        }

        // Apply color tinting based on mood
        $tint = new ImagickPixel($colors['primary']);
        $image->colorizeImage($tint, new ImagickPixel('white'), 0.1);

        // Apply composition effects
        if ($composition['style'] === 'dynamic') {
            $image->swirlImage(5);
        } elseif ($composition['style'] === 'soft') {
            $image->blurImage(1, 0.5);
        }
    }

    /**
     * Apply mood effects using GD
     */
    private function applyMoodEffectsGD($image, $colors, $lighting, $composition) {
        // Apply brightness based on lighting
        if ($lighting['intensity'] > 0.7) {
            imagefilter($image, IMG_FILTER_BRIGHTNESS, 20);
        } elseif ($lighting['intensity'] < 0.4) {
            imagefilter($image, IMG_FILTER_BRIGHTNESS, -20);
        }

        // Apply color effects
        if ($composition['style'] === 'soft') {
            imagefilter($image, IMG_FILTER_GAUSSIAN_BLUR);
        }
    }

    /**
     * Add scene content using ImageMagick
     */
    private function addSceneContentImageMagick($image, $scene) {
        // Add text or shapes based on scene content
        $draw = new ImagickDraw();
        $draw->setFillColor('white');
        $draw->setFont('Arial');
        $draw->setFontSize(24);

        if (isset($scene['text'])) {
            $image->annotateImage($draw, 50, 100, 0, $scene['text']);
        }

        $image->drawImage($draw);
    }

    /**
     * Add scene content using GD
     */
    private function addSceneContentGD($image, $scene) {
        $white = imagecolorallocate($image, 255, 255, 255);

        if (isset($scene['text'])) {
            imagestring($image, 5, 50, 100, $scene['text'], $white);
        }
    }

    /**
     * Parse color for GD
     */
    private function parseColorForGD($image, $hexColor) {
        $hex = ltrim($hexColor, '#');
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));

        return imagecolorallocate($image, $r, $g, $b);
    }

    /**
     * Build scene generation command
     */
    private function buildSceneGenerationCommand($task) {
        $phpScript = __DIR__ . '/scene_generator.php';
        return sprintf(
            'php "%s" %s',
            $phpScript,
            escapeshellarg(json_encode($task))
        );
    }

    /**
     * Generate fallback scene
     */
    private function generateFallbackScene($segment) {
        return [
            'text' => $segment['text'] ?? 'Scene content',
            'background' => '#1A1A2E',
            'duration' => $segment['duration'] ?? 3
        ];
    }

    /**
     * Get cached frames
     */
    private function getCachedFrames($cacheKey) {
        $cacheFile = $this->cacheDir . 'frames_' . $cacheKey . '.json';

        if (file_exists($cacheFile)) {
            $cacheData = json_decode(file_get_contents($cacheFile), true);

            // Check if cached frames still exist
            $validFrames = [];
            foreach ($cacheData['frames'] as $frame) {
                if (file_exists($frame)) {
                    $validFrames[] = $frame;
                }
            }

            if (count($validFrames) === count($cacheData['frames'])) {
                return $validFrames;
            }
        }

        return null;
    }

    /**
     * Cache frames
     */
    private function cacheFrames($cacheKey, $frames) {
        $cacheFile = $this->cacheDir . 'frames_' . $cacheKey . '.json';
        $cacheData = [
            'timestamp' => time(),
            'frames' => $frames
        ];

        file_put_contents($cacheFile, json_encode($cacheData));
    }

    /**
     * Create fallback frame
     */
    private function createFallbackFrame($outputFile, $moodCharacteristics) {
        $colors = $this->getMoodColors($moodCharacteristics);

        $cmd = sprintf(
            '%s -f lavfi -i "color=c=%s:size=720x1280:duration=1" -frames:v 1 "%s" -y 2>&1',
            $this->ffmpegPath,
            ltrim($colors['background'], '#'),
            $outputFile
        );

        exec($cmd, $output, $returnCode);

        if ($returnCode === 0 && file_exists($outputFile)) {
            return $outputFile;
        }

        // Ultimate fallback - create empty file
        touch($outputFile);
        return $outputFile;
    }

    /**
     * Fallback generation method
     */
    private function fallbackGeneration($segments, $background, $style, $jobId) {
        echo "🔄 Using fallback generation method...\n";

        // Use existing video engine as fallback
        require_once 'huggingface_video_engine.php';
        $fallbackEngine = new HuggingFaceVideoEngine();

        return $fallbackEngine->generateAdvancedVideo($segments, $background, $style, $jobId);
    }

    /**
     * Ensure required directories exist
     */
    private function ensureDirectories() {
        $dirs = [$this->tempDir, $this->outputDir, $this->cacheDir];

        foreach ($dirs as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }

    /**
     * Load configuration
     */
    private function loadConfig() {
        $configFile = __DIR__ . '/../config/settings.json';

        if (file_exists($configFile)) {
            $this->config = json_decode(file_get_contents($configFile), true);
        } else {
            $this->config = [
                'video_generation' => [
                    'enabled' => true,
                    'duration' => 30,
                    'fps' => 30,
                    'resolution' => ['width' => 720, 'height' => 1280]
                ]
            ];
        }

        // Override FFmpeg path if specified in config
        if (isset($this->config['ffmpeg_path'])) {
            $this->ffmpegPath = $this->config['ffmpeg_path'];
        }

        // Override parallel processes if specified
        if (isset($this->config['parallel_processes'])) {
            $this->parallelProcesses = $this->config['parallel_processes'];
        }
    }

    /**
     * Clean up temporary files
     */
    public function cleanup($jobId) {
        $pattern = $this->tempDir . $jobId . '_*';
        $files = glob($pattern);

        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }

        echo "🧹 Cleaned up " . count($files) . " temporary files\n";
    }

    /**
     * Get video generation statistics
     */
    public function getStats($jobId) {
        $outputFile = $this->tempDir . $jobId . '_optimized_video.mp4';

        if (!file_exists($outputFile)) {
            return null;
        }

        $stats = [
            'file_size' => filesize($outputFile),
            'file_size_mb' => round(filesize($outputFile) / 1024 / 1024, 2),
            'duration' => 30, // Fixed duration
            'resolution' => '720x1280',
            'fps' => 30,
            'codec' => 'h264'
        ];

        // Get detailed stats using FFprobe if available
        $ffprobeCmd = str_replace('ffmpeg', 'ffprobe', $this->ffmpegPath);
        $cmd = sprintf(
            '%s -v quiet -print_format json -show_format -show_streams "%s" 2>/dev/null',
            $ffprobeCmd,
            $outputFile
        );

        $output = shell_exec($cmd);
        if ($output) {
            $ffprobeData = json_decode($output, true);
            if ($ffprobeData && isset($ffprobeData['format'])) {
                $stats['actual_duration'] = floatval($ffprobeData['format']['duration']);
                $stats['bitrate'] = intval($ffprobeData['format']['bit_rate']);
            }
        }

        return $stats;
    }
}
