#!/usr/bin/env python3
"""
Real Video Generator for Sutradhar 2070
Generates actual videos using Python libraries based on prompts
"""

import os
import sys
import json
import random
import subprocess
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance
import numpy as np
from moviepy.editor import *
import argparse
from datetime import datetime
import colorsys

class RealVideoGenerator:
    def __init__(self):
        self.output_dir = "../public/generated_videos"
        self.temp_dir = "../temp"
        self.width = 1920
        self.height = 1080
        self.fps = 30
        self.duration = 30  # seconds
        
        # Create directories
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)
        
        # Mood color schemes
        self.mood_colors = {
            'euphoric': [(255, 215, 0), (255, 140, 0), (255, 69, 0)],  # Gold to orange
            'serene': [(135, 206, 235), (176, 224, 230), (240, 248, 255)],  # Sky blues
            'dramatic': [(139, 0, 0), (220, 20, 60), (255, 0, 0)],  # Deep reds
            'mysterious': [(75, 0, 130), (138, 43, 226), (148, 0, 211)],  # Purples
            'energetic': [(255, 20, 147), (255, 105, 180), (255, 182, 193)],  # Pinks
            'contemplative': [(119, 136, 153), (176, 196, 222), (230, 230, 250)],  # Grays
            'adventurous': [(34, 139, 34), (50, 205, 50), (144, 238, 144)],  # Greens
            'romantic': [(255, 192, 203), (255, 182, 193), (255, 228, 225)],  # Soft pinks
            'futuristic': [(0, 255, 255), (0, 191, 255), (30, 144, 255)],  # Cyans
            'nostalgic': [(210, 180, 140), (222, 184, 135), (245, 222, 179)]  # Warm browns
        }
        
        # Topic elements
        self.topic_elements = {
            'nature_wildlife': ['trees', 'animals', 'mountains', 'rivers', 'forests'],
            'urban_city': ['buildings', 'streets', 'lights', 'traffic', 'skyline'],
            'abstract_art': ['shapes', 'patterns', 'gradients', 'textures', 'forms'],
            'technology': ['circuits', 'data', 'networks', 'digital', 'cyber'],
            'space': ['stars', 'planets', 'galaxies', 'nebula', 'cosmos'],
            'ocean': ['waves', 'fish', 'coral', 'depths', 'currents']
        }

    def generate_video(self, job_id, mood, topic, prompt, inspiration=None):
        """Generate a real video based on the prompt"""
        print(f"🎬 Starting real video generation for job: {job_id}")
        print(f"📝 Prompt: {prompt}")
        print(f"🎭 Mood: {mood}, 📍 Topic: {topic}")
        
        try:
            # Generate frames
            frames = self.generate_frames(mood, topic, prompt, inspiration)
            
            # Create video from frames
            video_path = self.create_video_from_frames(frames, job_id)
            
            # Generate thumbnail
            thumbnail_path = self.generate_thumbnail(frames[0], job_id)
            
            # Add audio (optional)
            final_video_path = self.add_audio(video_path, mood, job_id)
            
            return {
                'success': True,
                'video_path': final_video_path,
                'thumbnail_path': thumbnail_path,
                'duration': self.duration,
                'resolution': f"{self.width}x{self.height}",
                'fps': self.fps
            }
            
        except Exception as e:
            print(f"❌ Error generating video: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def generate_frames(self, mood, topic, prompt, inspiration):
        """Generate individual frames for the video"""
        print("🖼️ Generating frames...")
        
        frames = []
        total_frames = self.duration * self.fps
        
        # Get color scheme for mood
        colors = self.mood_colors.get(mood, [(128, 128, 128), (160, 160, 160), (192, 192, 192)])
        
        # Generate frames with different scenes
        scenes = self.create_scenes(mood, topic, prompt, colors)
        frames_per_scene = total_frames // len(scenes)
        
        for scene_idx, scene in enumerate(scenes):
            print(f"🎨 Generating scene {scene_idx + 1}/{len(scenes)}: {scene['type']}")
            
            for frame_idx in range(frames_per_scene):
                frame = self.generate_frame(scene, frame_idx, frames_per_scene, colors)
                frames.append(frame)
        
        # Fill remaining frames if needed
        while len(frames) < total_frames:
            frames.append(frames[-1])
            
        return frames[:total_frames]

    def create_scenes(self, mood, topic, prompt, colors):
        """Create different scenes for the video"""
        scenes = []
        
        # Analyze prompt for scene types
        prompt_lower = prompt.lower()
        
        if 'nature' in prompt_lower or topic == 'nature_wildlife':
            scenes.extend([
                {'type': 'landscape', 'elements': ['mountains', 'trees', 'sky']},
                {'type': 'wildlife', 'elements': ['animals', 'movement', 'environment']},
                {'type': 'weather', 'elements': ['clouds', 'light', 'atmosphere']}
            ])
        elif 'city' in prompt_lower or topic == 'urban_city':
            scenes.extend([
                {'type': 'skyline', 'elements': ['buildings', 'lights', 'sky']},
                {'type': 'street', 'elements': ['traffic', 'people', 'movement']},
                {'type': 'night', 'elements': ['neon', 'reflections', 'glow']}
            ])
        elif 'abstract' in prompt_lower or topic == 'abstract_art':
            scenes.extend([
                {'type': 'geometric', 'elements': ['shapes', 'patterns', 'symmetry']},
                {'type': 'fluid', 'elements': ['waves', 'flow', 'organic']},
                {'type': 'particle', 'elements': ['dots', 'movement', 'energy']}
            ])
        else:
            # Default scenes
            scenes.extend([
                {'type': 'opening', 'elements': ['title', 'introduction', 'fade']},
                {'type': 'main', 'elements': ['content', 'movement', 'focus']},
                {'type': 'closing', 'elements': ['conclusion', 'fade', 'end']}
            ])
        
        return scenes

    def generate_frame(self, scene, frame_idx, total_frames, colors):
        """Generate a single frame"""
        # Create base image
        img = Image.new('RGB', (self.width, self.height), colors[0])
        draw = ImageDraw.Draw(img)
        
        # Calculate animation progress
        progress = frame_idx / total_frames
        
        # Generate content based on scene type
        if scene['type'] == 'landscape':
            img = self.draw_landscape(img, draw, colors, progress)
        elif scene['type'] == 'wildlife':
            img = self.draw_wildlife(img, draw, colors, progress)
        elif scene['type'] == 'skyline':
            img = self.draw_skyline(img, draw, colors, progress)
        elif scene['type'] == 'geometric':
            img = self.draw_geometric(img, draw, colors, progress)
        elif scene['type'] == 'fluid':
            img = self.draw_fluid(img, draw, colors, progress)
        else:
            img = self.draw_abstract(img, draw, colors, progress)
        
        # Add effects
        img = self.apply_effects(img, scene, progress)
        
        return img

    def draw_landscape(self, img, draw, colors, progress):
        """Draw landscape scene"""
        # Sky gradient
        for y in range(self.height // 2):
            color_ratio = y / (self.height // 2)
            color = self.interpolate_color(colors[2], colors[1], color_ratio)
            draw.line([(0, y), (self.width, y)], fill=color)
        
        # Mountains
        mountain_points = []
        for x in range(0, self.width, 50):
            height = random.randint(self.height // 3, self.height // 2)
            mountain_points.extend([(x, height), (x + 25, height - random.randint(20, 60))])
        mountain_points.extend([(self.width, self.height), (0, self.height)])
        
        if len(mountain_points) >= 6:
            draw.polygon(mountain_points, fill=colors[0])
        
        # Trees (animated)
        tree_sway = int(10 * np.sin(progress * 4 * np.pi))
        for i in range(20):
            x = random.randint(0, self.width)
            y = random.randint(self.height // 2, self.height - 100)
            tree_height = random.randint(50, 150)
            
            # Tree trunk
            draw.rectangle([x - 5 + tree_sway, y, x + 5 + tree_sway, y + tree_height], 
                         fill=(101, 67, 33))
            
            # Tree crown
            crown_size = random.randint(30, 60)
            draw.ellipse([x - crown_size//2 + tree_sway, y - crown_size//2, 
                         x + crown_size//2 + tree_sway, y + crown_size//2], 
                        fill=(34, 139, 34))
        
        return img

    def draw_wildlife(self, img, draw, colors, progress):
        """Draw wildlife scene"""
        # Background
        img = self.draw_landscape(img, draw, colors, progress)
        
        # Animated animals
        animal_x = int(self.width * 0.2 + (self.width * 0.6 * progress))
        animal_y = self.height - 200
        
        # Simple elephant shape
        # Body
        draw.ellipse([animal_x, animal_y, animal_x + 120, animal_y + 80], fill=(105, 105, 105))
        
        # Head
        draw.ellipse([animal_x - 40, animal_y - 20, animal_x + 40, animal_y + 40], fill=(105, 105, 105))
        
        # Trunk (animated)
        trunk_curve = int(20 * np.sin(progress * 6 * np.pi))
        draw.ellipse([animal_x - 60, animal_y + 10 + trunk_curve, 
                     animal_x - 30, animal_y + 60 + trunk_curve], fill=(105, 105, 105))
        
        # Legs
        for i in range(4):
            leg_x = animal_x + (i * 25)
            draw.rectangle([leg_x, animal_y + 60, leg_x + 15, animal_y + 120], fill=(105, 105, 105))
        
        # Birds flying
        for i in range(5):
            bird_x = int((self.width * 0.8 * progress + i * 100) % self.width)
            bird_y = 100 + int(30 * np.sin(progress * 8 * np.pi + i))
            
            # Simple bird shape
            draw.arc([bird_x - 10, bird_y - 5, bird_x + 10, bird_y + 5], 0, 180, fill=(0, 0, 0))
            draw.arc([bird_x - 5, bird_y - 5, bird_x + 15, bird_y + 5], 0, 180, fill=(0, 0, 0))
        
        return img

    def draw_skyline(self, img, draw, colors, progress):
        """Draw city skyline"""
        # Night sky gradient
        for y in range(self.height // 2):
            color_ratio = y / (self.height // 2)
            night_color = self.interpolate_color((25, 25, 112), (72, 61, 139), color_ratio)
            draw.line([(0, y), (self.width, y)], fill=night_color)
        
        # Buildings
        building_count = 15
        building_width = self.width // building_count
        
        for i in range(building_count):
            x = i * building_width
            height = random.randint(200, 600)
            y = self.height - height
            
            # Building body
            building_color = (random.randint(40, 80), random.randint(40, 80), random.randint(40, 80))
            draw.rectangle([x, y, x + building_width - 5, self.height], fill=building_color)
            
            # Windows (animated)
            window_rows = height // 40
            window_cols = building_width // 20
            
            for row in range(window_rows):
                for col in range(window_cols):
                    if random.random() < 0.7:  # 70% chance of lit window
                        window_x = x + col * 20 + 5
                        window_y = y + row * 40 + 10
                        
                        # Animated window brightness
                        brightness = 0.5 + 0.5 * np.sin(progress * 4 * np.pi + i + row + col)
                        window_color = (int(255 * brightness), int(255 * brightness), int(200 * brightness))
                        
                        draw.rectangle([window_x, window_y, window_x + 10, window_y + 15], 
                                     fill=window_color)
        
        return img

    def draw_geometric(self, img, draw, colors, progress):
        """Draw geometric patterns"""
        # Rotating geometric shapes
        center_x, center_y = self.width // 2, self.height // 2
        
        # Background gradient
        for y in range(self.height):
            color_ratio = y / self.height
            bg_color = self.interpolate_color(colors[0], colors[1], color_ratio)
            draw.line([(0, y), (self.width, y)], fill=bg_color)
        
        # Rotating triangles
        num_triangles = 8
        for i in range(num_triangles):
            angle = (progress * 2 * np.pi) + (i * 2 * np.pi / num_triangles)
            radius = 200 + 100 * np.sin(progress * 4 * np.pi)
            
            # Triangle vertices
            x1 = center_x + radius * np.cos(angle)
            y1 = center_y + radius * np.sin(angle)
            x2 = center_x + radius * np.cos(angle + 2 * np.pi / 3)
            y2 = center_y + radius * np.sin(angle + 2 * np.pi / 3)
            x3 = center_x + radius * np.cos(angle + 4 * np.pi / 3)
            y3 = center_y + radius * np.sin(angle + 4 * np.pi / 3)
            
            color_idx = i % len(colors)
            alpha = int(128 + 127 * np.sin(progress * 6 * np.pi + i))
            
            # Create triangle with transparency effect
            triangle_color = (*colors[color_idx], alpha)
            draw.polygon([(x1, y1), (x2, y2), (x3, y3)], fill=colors[color_idx])
        
        return img

    def draw_fluid(self, img, draw, colors, progress):
        """Draw fluid/wave patterns"""
        # Create wave-like patterns
        for y in range(0, self.height, 5):
            wave_offset = 100 * np.sin(y * 0.01 + progress * 4 * np.pi)
            color_ratio = (y + wave_offset) / self.height
            wave_color = self.interpolate_color(colors[0], colors[2], abs(color_ratio) % 1)
            
            for x in range(self.width):
                wave_x = x + int(wave_offset * np.sin(x * 0.01 + progress * 2 * np.pi))
                if 0 <= wave_x < self.width:
                    draw.point((wave_x, y), fill=wave_color)
        
        return img

    def draw_abstract(self, img, draw, colors, progress):
        """Draw abstract patterns"""
        # Particle system
        num_particles = 100
        
        for i in range(num_particles):
            # Particle position with movement
            x = (i * 137.5 + progress * 200) % self.width
            y = (i * 73.3 + progress * 150) % self.height
            
            # Particle size based on progress
            size = 5 + 10 * np.sin(progress * 8 * np.pi + i)
            
            # Color cycling
            color_idx = int(progress * len(colors) + i) % len(colors)
            particle_color = colors[color_idx]
            
            draw.ellipse([x - size, y - size, x + size, y + size], fill=particle_color)
        
        return img

    def interpolate_color(self, color1, color2, ratio):
        """Interpolate between two colors"""
        ratio = max(0, min(1, ratio))
        r = int(color1[0] + (color2[0] - color1[0]) * ratio)
        g = int(color1[1] + (color2[1] - color1[1]) * ratio)
        b = int(color1[2] + (color2[2] - color1[2]) * ratio)
        return (r, g, b)

    def apply_effects(self, img, scene, progress):
        """Apply visual effects to the frame"""
        # Brightness variation
        brightness = 1.0 + 0.2 * np.sin(progress * 2 * np.pi)
        enhancer = ImageEnhance.Brightness(img)
        img = enhancer.enhance(brightness)
        
        # Slight blur for dreamy effect
        if random.random() < 0.1:  # 10% chance
            img = img.filter(ImageFilter.GaussianBlur(radius=1))
        
        return img

    def create_video_from_frames(self, frames, job_id):
        """Create video from generated frames"""
        print("🎬 Creating video from frames...")
        
        # Save frames as temporary images
        frame_paths = []
        for i, frame in enumerate(frames):
            frame_path = os.path.join(self.temp_dir, f"frame_{job_id}_{i:06d}.png")
            frame.save(frame_path)
            frame_paths.append(frame_path)
        
        # Create video using MoviePy
        video_path = os.path.join(self.output_dir, f"video_{job_id}.mp4")
        
        # Load images as clips
        clips = []
        frame_duration = 1.0 / self.fps
        
        for frame_path in frame_paths:
            clip = ImageClip(frame_path, duration=frame_duration)
            clips.append(clip)
        
        # Concatenate clips
        video = concatenate_videoclips(clips, method="compose")
        
        # Write video file
        video.write_videofile(
            video_path,
            fps=self.fps,
            codec='libx264',
            audio=False,
            verbose=False,
            logger=None
        )
        
        # Clean up temporary frames
        for frame_path in frame_paths:
            try:
                os.remove(frame_path)
            except:
                pass
        
        print(f"✅ Video created: {video_path}")
        return video_path

    def generate_thumbnail(self, first_frame, job_id):
        """Generate thumbnail from first frame"""
        print("🖼️ Generating thumbnail...")
        
        # Resize first frame for thumbnail
        thumbnail_size = (320, 180)  # 16:9 aspect ratio
        thumbnail = first_frame.resize(thumbnail_size, Image.Resampling.LANCZOS)
        
        # Add play button overlay
        draw = ImageDraw.Draw(thumbnail)
        center_x, center_y = thumbnail_size[0] // 2, thumbnail_size[1] // 2
        
        # Play button triangle
        play_size = 20
        play_points = [
            (center_x - play_size//2, center_y - play_size//2),
            (center_x - play_size//2, center_y + play_size//2),
            (center_x + play_size//2, center_y)
        ]
        
        # Semi-transparent background circle
        draw.ellipse([center_x - 30, center_y - 30, center_x + 30, center_y + 30], 
                    fill=(0, 0, 0, 128))
        draw.polygon(play_points, fill=(255, 255, 255))
        
        thumbnail_path = os.path.join(self.output_dir, f"thumbnail_{job_id}.jpg")
        thumbnail.save(thumbnail_path, "JPEG", quality=85)
        
        print(f"✅ Thumbnail created: {thumbnail_path}")
        return thumbnail_path

    def add_audio(self, video_path, mood, job_id):
        """Add background audio based on mood"""
        print("🎵 Adding background audio...")
        
        try:
            # Load video
            video = VideoFileClip(video_path)
            
            # Generate simple audio tone based on mood
            audio_freq = self.get_mood_frequency(mood)
            
            # Create audio using MoviePy's audio generation
            def make_frame_audio(t):
                # Simple sine wave with some variation
                base_freq = audio_freq
                variation = 50 * np.sin(t * 0.5)
                freq = base_freq + variation
                
                # Generate stereo audio
                left = 0.1 * np.sin(2 * np.pi * freq * t)
                right = 0.1 * np.sin(2 * np.pi * (freq + 5) * t)
                
                return np.array([left, right]).T
            
            audio = AudioClip(make_frame_audio, duration=video.duration)
            
            # Combine video with audio
            final_video = video.set_audio(audio)
            
            # Save final video
            final_path = os.path.join(self.output_dir, f"final_{job_id}.mp4")
            final_video.write_videofile(
                final_path,
                fps=self.fps,
                codec='libx264',
                verbose=False,
                logger=None
            )
            
            # Clean up
            video.close()
            final_video.close()
            
            # Remove original video without audio
            try:
                os.remove(video_path)
            except:
                pass
            
            print(f"✅ Final video with audio: {final_path}")
            return final_path
            
        except Exception as e:
            print(f"⚠️ Audio generation failed: {e}")
            print("📹 Returning video without audio")
            return video_path

    def get_mood_frequency(self, mood):
        """Get audio frequency based on mood"""
        mood_frequencies = {
            'euphoric': 440,      # A4 - uplifting
            'serene': 261.63,     # C4 - calm
            'dramatic': 220,      # A3 - deep
            'mysterious': 146.83, # D3 - low and mysterious
            'energetic': 523.25,  # C5 - high energy
            'contemplative': 293.66, # D4 - thoughtful
            'adventurous': 349.23,   # F4 - exciting
            'romantic': 329.63,      # E4 - warm
            'futuristic': 466.16,    # A#4 - modern
            'nostalgic': 246.94      # B3 - wistful
        }
        return mood_frequencies.get(mood, 440)


def main():
    parser = argparse.ArgumentParser(description='Generate real videos for Sutradhar 2070')
    parser.add_argument('--job_id', required=True, help='Job ID for the video')
    parser.add_argument('--mood', required=True, help='Mood for the video')
    parser.add_argument('--topic', required=True, help='Topic for the video')
    parser.add_argument('--prompt', required=True, help='Prompt for the video')
    parser.add_argument('--inspiration', help='Inspiration elements (JSON)')
    
    args = parser.parse_args()
    
    # Parse inspiration if provided
    inspiration = None
    if args.inspiration:
        try:
            inspiration = json.loads(args.inspiration)
        except:
            inspiration = [args.inspiration]
    
    # Generate video
    generator = RealVideoGenerator()
    result = generator.generate_video(
        args.job_id,
        args.mood,
        args.topic,
        args.prompt,
        inspiration
    )
    
    # Output result as JSON
    print(json.dumps(result))


if __name__ == "__main__":
    main()
