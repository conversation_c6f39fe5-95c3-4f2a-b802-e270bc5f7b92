<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎭 Sutradhar 2070 - FINAL SYSTEM TEST</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0F0F23 0%, #1A1A2E 50%, #16213E 100%);
            font-family: 'Arial', sans-serif;
            color: white;
            min-height: 100vh;
        }
        .test-card {
            background: rgba(26, 26, 46, 0.8);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            transition: all 0.3s ease;
        }
        .test-card:hover {
            border-color: #00D4FF;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }
        .success { border-color: #10B981; background: rgba(16, 185, 129, 0.1); }
        .error { border-color: #EF4444; background: rgba(239, 68, 68, 0.1); }
        .loading { border-color: #F59E0B; background: rgba(245, 158, 11, 0.1); }
        .cyber-button {
            background: linear-gradient(45deg, #00D4FF, #0099CC);
            border: none;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        .cyber-button:hover {
            background: linear-gradient(45deg, #0099CC, #00D4FF);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
        }
    </style>
</head>
<body class="p-8">
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-5xl font-bold mb-4 bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent">
                🎭 Sutradhar 2070
            </h1>
            <h2 class="text-2xl font-semibold mb-2">FINAL SYSTEM TEST</h2>
            <p class="text-gray-300">Complete functionality verification</p>
        </div>

        <!-- Quick Links -->
        <div class="grid md:grid-cols-4 gap-4 mb-8">
            <a href="mood_video_generator.html" class="cyber-button p-4 rounded-lg">
                🎬 Mood Video Generator
            </a>
            <a href="index.html" class="cyber-button p-4 rounded-lg">
                🏠 Main Application
            </a>
            <a href="dashboard.html" class="cyber-button p-4 rounded-lg">
                📊 Dashboard
            </a>
            <a href="quick_fix_test.php" class="cyber-button p-4 rounded-lg">
                🔧 System Diagnostics
            </a>
        </div>

        <!-- Test Results -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <!-- API Tests -->
            <div class="test-card p-6 rounded-lg" id="test-simple-api">
                <h3 class="text-lg font-semibold mb-3">🔌 Simple API</h3>
                <p class="text-sm text-gray-300 mb-4">Testing fallback API system</p>
                <button onclick="testSimpleAPI()" class="cyber-button px-4 py-2 rounded text-sm">Test</button>
                <div class="mt-3 text-sm" id="simple-api-result">Ready to test</div>
            </div>

            <div class="test-card p-6 rounded-lg" id="test-generation">
                <h3 class="text-lg font-semibold mb-3">🎬 Video Generation</h3>
                <p class="text-sm text-gray-300 mb-4">Testing video generation flow</p>
                <button onclick="testGeneration()" class="cyber-button px-4 py-2 rounded text-sm">Test</button>
                <div class="mt-3 text-sm" id="generation-result">Ready to test</div>
            </div>

            <div class="test-card p-6 rounded-lg" id="test-mood-api">
                <h3 class="text-lg font-semibold mb-3">🎭 Mood API</h3>
                <p class="text-sm text-gray-300 mb-4">Testing mood data loading</p>
                <button onclick="testMoodAPI()" class="cyber-button px-4 py-2 rounded text-sm">Test</button>
                <div class="mt-3 text-sm" id="mood-api-result">Ready to test</div>
            </div>

            <div class="test-card p-6 rounded-lg" id="test-pages">
                <h3 class="text-lg font-semibold mb-3">📄 Page Loading</h3>
                <p class="text-sm text-gray-300 mb-4">Testing all HTML pages</p>
                <button onclick="testPages()" class="cyber-button px-4 py-2 rounded text-sm">Test</button>
                <div class="mt-3 text-sm" id="pages-result">Ready to test</div>
            </div>

            <div class="test-card p-6 rounded-lg" id="test-javascript">
                <h3 class="text-lg font-semibold mb-3">📜 JavaScript</h3>
                <p class="text-sm text-gray-300 mb-4">Testing JS functionality</p>
                <button onclick="testJavaScript()" class="cyber-button px-4 py-2 rounded text-sm">Test</button>
                <div class="mt-3 text-sm" id="javascript-result">Ready to test</div>
            </div>

            <div class="test-card p-6 rounded-lg" id="test-integration">
                <h3 class="text-lg font-semibold mb-3">🔗 Integration</h3>
                <p class="text-sm text-gray-300 mb-4">Testing end-to-end flow</p>
                <button onclick="testIntegration()" class="cyber-button px-4 py-2 rounded text-sm">Test</button>
                <div class="mt-3 text-sm" id="integration-result">Ready to test</div>
            </div>
        </div>

        <!-- Run All Tests -->
        <div class="text-center mb-8">
            <button onclick="runAllTests()" class="cyber-button px-8 py-4 rounded-lg text-xl font-bold">
                🚀 RUN ALL TESTS
            </button>
        </div>

        <!-- Final Results -->
        <div class="test-card p-8 rounded-lg">
            <h3 class="text-2xl font-semibold mb-4 text-center">📋 FINAL TEST RESULTS</h3>
            <div id="final-results" class="text-center text-gray-300">
                Click "RUN ALL TESTS" to see comprehensive results
            </div>
        </div>

        <!-- Success Actions -->
        <div id="success-actions" class="hidden mt-8 text-center">
            <div class="test-card p-6 rounded-lg success">
                <h3 class="text-xl font-semibold mb-4 text-green-400">🎉 ALL SYSTEMS OPERATIONAL!</h3>
                <p class="mb-4">Your Sutradhar 2070 platform is fully functional and ready to use!</p>
                <div class="grid md:grid-cols-3 gap-4">
                    <a href="mood_video_generator.html" class="cyber-button p-4 rounded-lg">
                        🎬 Start Creating Videos
                    </a>
                    <a href="index.html" class="cyber-button p-4 rounded-lg">
                        🏠 Explore Main App
                    </a>
                    <a href="dashboard.html" class="cyber-button p-4 rounded-lg">
                        📊 View Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        let testResults = {};

        function updateTestStatus(testId, status, message) {
            const card = document.getElementById(`test-${testId}`);
            const result = document.getElementById(`${testId}-result`);
            
            card.className = `test-card p-6 rounded-lg ${status}`;
            result.textContent = message;
        }

        async function testSimpleAPI() {
            updateTestStatus('simple-api', 'loading', 'Testing...');
            
            try {
                const response = await fetch('test_api_simple.php?endpoint=test');
                const data = await response.json();
                
                if (data.success) {
                    updateTestStatus('simple-api', 'success', '✅ Simple API working perfectly');
                    testResults.simpleAPI = true;
                } else {
                    updateTestStatus('simple-api', 'error', '❌ Simple API failed');
                    testResults.simpleAPI = false;
                }
            } catch (error) {
                updateTestStatus('simple-api', 'error', '❌ Error: ' + error.message);
                testResults.simpleAPI = false;
            }
        }

        async function testGeneration() {
            updateTestStatus('generation', 'loading', 'Testing...');
            
            try {
                const response = await fetch('test_api_simple.php?endpoint=generate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        mood: 'euphoric',
                        topic: 'nature_wildlife'
                    })
                });
                const data = await response.json();
                
                if (data.success && data.data && data.data.job_id) {
                    updateTestStatus('generation', 'success', '✅ Video generation API working');
                    testResults.generation = true;
                } else {
                    updateTestStatus('generation', 'error', '❌ Generation API failed');
                    testResults.generation = false;
                }
            } catch (error) {
                updateTestStatus('generation', 'error', '❌ Error: ' + error.message);
                testResults.generation = false;
            }
        }

        async function testMoodAPI() {
            updateTestStatus('mood-api', 'loading', 'Testing...');
            
            try {
                const response = await fetch('mood_api.php?action=get_moods');
                const data = await response.json();
                
                if (data.success && data.moods) {
                    updateTestStatus('mood-api', 'success', `✅ Loaded ${Object.keys(data.moods).length} mood categories`);
                    testResults.moodAPI = true;
                } else {
                    updateTestStatus('mood-api', 'error', '❌ Failed to load moods');
                    testResults.moodAPI = false;
                }
            } catch (error) {
                updateTestStatus('mood-api', 'error', '❌ Error: ' + error.message);
                testResults.moodAPI = false;
            }
        }

        async function testPages() {
            updateTestStatus('pages', 'loading', 'Testing...');
            
            const pages = [
                'mood_video_generator.html',
                'index.html',
                'dashboard.html'
            ];
            
            let passedPages = 0;
            
            for (const page of pages) {
                try {
                    const response = await fetch(page);
                    if (response.ok) {
                        passedPages++;
                    }
                } catch (error) {
                    console.error(`Failed to load ${page}:`, error);
                }
            }
            
            if (passedPages === pages.length) {
                updateTestStatus('pages', 'success', `✅ All ${pages.length} pages accessible`);
                testResults.pages = true;
            } else {
                updateTestStatus('pages', 'error', `❌ Only ${passedPages}/${pages.length} pages accessible`);
                testResults.pages = false;
            }
        }

        async function testJavaScript() {
            updateTestStatus('javascript', 'loading', 'Testing...');
            
            try {
                // Test if mood_video_generator.js loads
                const script = document.createElement('script');
                script.src = 'mood_video_generator.js';
                
                script.onload = () => {
                    updateTestStatus('javascript', 'success', '✅ JavaScript files loading correctly');
                    testResults.javascript = true;
                };
                
                script.onerror = () => {
                    updateTestStatus('javascript', 'error', '❌ JavaScript loading failed');
                    testResults.javascript = false;
                };
                
                document.head.appendChild(script);
                
                // Remove script after test
                setTimeout(() => {
                    document.head.removeChild(script);
                }, 2000);
                
            } catch (error) {
                updateTestStatus('javascript', 'error', '❌ Error: ' + error.message);
                testResults.javascript = false;
            }
        }

        async function testIntegration() {
            updateTestStatus('integration', 'loading', 'Testing...');
            
            try {
                // Test the complete flow: mood API + generation API
                const moodResponse = await fetch('mood_api.php?action=get_moods');
                const moodData = await moodResponse.json();
                
                if (!moodData.success) {
                    throw new Error('Mood API failed');
                }
                
                const genResponse = await fetch('test_api_simple.php?endpoint=generate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        mood: 'euphoric',
                        topic: 'nature_wildlife'
                    })
                });
                const genData = await genResponse.json();
                
                if (!genData.success) {
                    throw new Error('Generation API failed');
                }
                
                updateTestStatus('integration', 'success', '✅ End-to-end integration working');
                testResults.integration = true;
                
            } catch (error) {
                updateTestStatus('integration', 'error', '❌ Integration failed: ' + error.message);
                testResults.integration = false;
            }
        }

        async function runAllTests() {
            testResults = {};
            
            await testSimpleAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testGeneration();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testMoodAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testPages();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testJavaScript();
            await new Promise(resolve => setTimeout(resolve, 2500)); // Wait for JS test
            
            await testIntegration();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Show final results
            const passed = Object.values(testResults).filter(r => r).length;
            const total = Object.keys(testResults).length;
            const finalResults = document.getElementById('final-results');
            const successActions = document.getElementById('success-actions');
            
            if (passed === total) {
                finalResults.innerHTML = `
                    <div class="text-green-400 text-xl font-bold mb-4">
                        🎉 PERFECT SCORE: ${passed}/${total} TESTS PASSED!
                    </div>
                    <div class="text-lg mb-4">
                        🚀 Your Sutradhar 2070 system is <strong>100% FUNCTIONAL</strong>!
                    </div>
                    <div class="text-sm text-gray-300">
                        ✅ All APIs working<br>
                        ✅ All pages accessible<br>
                        ✅ JavaScript functioning<br>
                        ✅ End-to-end integration complete<br>
                        ✅ Ready for production use!
                    </div>
                `;
                successActions.classList.remove('hidden');
            } else {
                finalResults.innerHTML = `
                    <div class="text-yellow-400 text-xl font-bold mb-4">
                        ⚠️ PARTIAL SUCCESS: ${passed}/${total} TESTS PASSED
                    </div>
                    <div class="text-sm text-gray-300">
                        Some components may need attention. Check individual test results above.
                    </div>
                `;
            }
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
