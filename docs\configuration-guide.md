# Configuration Guide

## 📋 Overview

The Sutradhar platform uses a centralized JSON configuration file located at `config/settings.json`. This guide explains all configuration options and their optimal settings for different environments.

## 🗂️ Configuration File Structure

### Main Configuration Sections

```json
{
    "database": { /* Database connection settings */ },
    "api_keys": { /* External API credentials */ },
    "video_generation": { /* Video generation parameters */ },
    "tools": { /* Tools system configuration */ },
    "security": { /* Security and authentication settings */ },
    "file_paths": { /* File system paths */ },
    "performance": { /* Performance optimization settings */ },
    "ui": { /* User interface customization */ },
    "notifications": { /* Notification system settings */ },
    "analytics": { /* Analytics and tracking */ }
}
```

## 🗄️ Database Configuration

```json
{
    "database": {
        "host": "localhost",
        "port": 3306,
        "username": "root",
        "password": "",
        "database": "sutradhar_db",
        "charset": "utf8mb4",
        "options": {
            "PDO::ATTR_ERRMODE": "PDO::ERRMODE_EXCEPTION",
            "PDO::ATTR_DEFAULT_FETCH_MODE": "PDO::FETCH_ASSOC",
            "PDO::ATTR_EMULATE_PREPARES": false
        },
        "pool": {
            "max_connections": 10,
            "timeout": 30
        }
    }
}
```

### Database Options Explained

| Setting | Description | Recommended Value |
|---------|-------------|-------------------|
| `host` | Database server hostname | `localhost` for local dev |
| `port` | MySQL port number | `3306` (default) |
| `username` | Database username | `root` for dev, dedicated user for prod |
| `password` | Database password | Strong password for production |
| `charset` | Character encoding | `utf8mb4` for full Unicode support |
| `max_connections` | Connection pool size | `10` for small apps, `50+` for production |

## 🔑 API Keys Configuration

```json
{
    "api_keys": {
        "huggingface": {
            "token": "hf_xxxxxxxxxxxxxxxxxxxxxxxxxx",
            "rate_limit": 100,
            "timeout": 60,
            "retry_attempts": 3
        },
        "stripe": {
            "public_key": "pk_test_xxxxxxxxxxxxxxxxxx",
            "secret_key": "sk_test_xxxxxxxxxxxxxxxxxx",
            "webhook_secret": "whsec_xxxxxxxxxxxxxxxxxx",
            "api_version": "2023-10-16"
        },
        "paypal": {
            "client_id": "xxxxxxxxxxxxxxxxxx",
            "client_secret": "xxxxxxxxxxxxxxxxxx",
            "environment": "sandbox"
        },
        "analytics": {
            "google_analytics": "G-XXXXXXXXXX",
            "mixpanel": "xxxxxxxxxxxxxxxxxx"
        }
    }
}
```

### Getting API Keys

#### Hugging Face Setup
1. **Create Account**: Visit [huggingface.co](https://huggingface.co)
2. **Generate Token**: Settings → Access Tokens → New Token
3. **Permissions**: Select "Read" permissions
4. **Rate Limits**: Free tier: 1000 requests/month

#### Stripe Setup (Payment Processing)
1. **Create Account**: Visit [stripe.com](https://stripe.com)
2. **Get Keys**: Dashboard → Developers → API Keys
3. **Test Mode**: Use test keys for development
4. **Webhooks**: Configure webhook endpoint for payment events

#### PayPal Setup (Alternative Payment)
1. **Developer Account**: Visit [developer.paypal.com](https://developer.paypal.com)
2. **Create App**: My Apps & Credentials → Create App
3. **Environment**: Use sandbox for testing

## 🎬 Video Generation Configuration

```json
{
    "video_generation": {
        "default_duration": 30,
        "max_duration": 60,
        "min_duration": 10,
        "output_format": "mp4",
        "quality_presets": {
            "low": {
                "resolution": "480x854",
                "bitrate": "500k",
                "fps": 15
            },
            "medium": {
                "resolution": "720x1280",
                "bitrate": "1500k",
                "fps": 24
            },
            "high": {
                "resolution": "720x1280",
                "bitrate": "2500k",
                "fps": 30
            },
            "ultra": {
                "resolution": "1080x1920",
                "bitrate": "4000k",
                "fps": 30
            }
        },
        "models": {
            "text_to_image": "stabilityai/stable-diffusion-2-1",
            "text_to_image_backup": "runwayml/stable-diffusion-v1-5",
            "text_to_image_fallback": "CompVis/stable-diffusion-v1-4",
            "image_to_video": "ali-vilab/text-to-video-ms-1.7b",
            "background_removal": "briaai/RMBG-1.4"
        },
        "generation_settings": {
            "max_concurrent_jobs": 3,
            "timeout_seconds": 300,
            "retry_attempts": 2,
            "cleanup_temp_files": true,
            "enable_local_fallback": true
        },
        "credit_costs": {
            "low_quality": 5,
            "medium_quality": 10,
            "high_quality": 15,
            "ultra_quality": 25
        }
    }
}
```

### Video Quality Settings

| Quality | Resolution | Bitrate | FPS | Credits | Use Case |
|---------|------------|---------|-----|---------|----------|
| Low | 480x854 | 500k | 15 | 5 | Quick previews |
| Medium | 720x1280 | 1500k | 24 | 10 | Standard videos |
| High | 720x1280 | 2500k | 30 | 15 | Professional content |
| Ultra | 1080x1920 | 4000k | 30 | 25 | Premium quality |

## 🛠️ Tools Configuration

```json
{
    "tools": {
        "file_limits": {
            "max_file_size": 52428800,
            "max_files_per_request": 10,
            "max_total_size": 104857600
        },
        "allowed_formats": {
            "images": ["jpg", "jpeg", "png", "gif", "webp", "bmp", "tiff", "svg"],
            "documents": ["pdf", "txt", "docx"],
            "archives": ["zip", "rar", "7z"]
        },
        "processing": {
            "max_processing_time": 300,
            "memory_limit": "512M",
            "temp_cleanup_hours": 24,
            "enable_batch_processing": true
        },
        "rate_limits": {
            "free": {
                "requests_per_hour": 10,
                "requests_per_day": 50
            },
            "pro": {
                "requests_per_hour": 100,
                "requests_per_day": 1000
            },
            "business": {
                "requests_per_hour": 1000,
                "requests_per_day": 10000
            },
            "enterprise": {
                "requests_per_hour": -1,
                "requests_per_day": -1
            }
        },
        "credit_costs": {
            "image_processing": 1,
            "format_conversion": 1,
            "background_removal": 2,
            "advanced_editing": 3,
            "batch_operations": 5
        }
    }
}
```

## 🔐 Security Configuration

```json
{
    "security": {
        "session": {
            "lifetime": 86400,
            "cookie_secure": true,
            "cookie_httponly": true,
            "cookie_samesite": "Strict"
        },
        "csrf": {
            "token_lifetime": 3600,
            "regenerate_on_login": true
        },
        "authentication": {
            "max_login_attempts": 5,
            "lockout_duration": 900,
            "password_min_length": 8,
            "require_special_chars": true,
            "require_numbers": true
        },
        "file_upload": {
            "scan_for_malware": true,
            "check_file_headers": true,
            "quarantine_suspicious": true
        },
        "api": {
            "enable_cors": true,
            "allowed_origins": ["http://localhost", "https://yourdomain.com"],
            "rate_limiting": true
        }
    }
}
```

## 📁 File Paths Configuration

```json
{
    "file_paths": {
        "temp_dir": "temp/",
        "upload_dir": "public/uploads/",
        "video_dir": "public/videos/",
        "thumbnail_dir": "public/thumbnails/",
        "tools_temp": "temp/tools/",
        "logs_dir": "logs/",
        "cache_dir": "cache/",
        "backup_dir": "backups/"
    }
}
```

### Directory Structure
```
sutradhar/
├── temp/                 # Temporary processing files
│   ├── tools/           # Tool-specific temp files
│   └── video/           # Video generation temp files
├── public/
│   ├── uploads/         # User uploaded files
│   ├── videos/          # Generated videos
│   └── thumbnails/      # Video thumbnails
├── logs/                # Application logs
├── cache/               # Cached data
└── backups/             # Database backups
```

## ⚡ Performance Configuration

```json
{
    "performance": {
        "caching": {
            "enable_file_cache": true,
            "cache_lifetime": 3600,
            "enable_redis": false,
            "redis_host": "localhost",
            "redis_port": 6379
        },
        "optimization": {
            "enable_gzip": true,
            "minify_css": true,
            "minify_js": true,
            "optimize_images": true
        },
        "limits": {
            "max_execution_time": 300,
            "memory_limit": "1024M",
            "max_concurrent_users": 100
        }
    }
}
```

## 🎨 UI Configuration

```json
{
    "ui": {
        "theme": {
            "default_theme": "dark",
            "accent_colors": {
                "primary": "#00ff88",
                "secondary": "#0088ff",
                "tertiary": "#ff0088"
            },
            "enable_theme_switching": true
        },
        "features": {
            "enable_animations": true,
            "animation_speed": "normal",
            "enable_sound_effects": false,
            "show_tooltips": true
        },
        "layout": {
            "sidebar_collapsed": false,
            "show_breadcrumbs": true,
            "items_per_page": 20
        }
    }
}
```

## 📊 Analytics Configuration

```json
{
    "analytics": {
        "tracking": {
            "enable_user_analytics": true,
            "enable_performance_tracking": true,
            "track_tool_usage": true,
            "track_video_generation": true
        },
        "privacy": {
            "anonymize_ip": true,
            "respect_do_not_track": true,
            "cookie_consent_required": true
        },
        "retention": {
            "keep_logs_days": 90,
            "keep_analytics_days": 365,
            "auto_cleanup": true
        }
    }
}
```

## 🌍 Environment-Specific Configurations

### Development Environment
```json
{
    "environment": "development",
    "debug": {
        "enable_debug_mode": true,
        "show_error_details": true,
        "log_level": "debug",
        "enable_query_logging": true
    }
}
```

### Production Environment
```json
{
    "environment": "production",
    "debug": {
        "enable_debug_mode": false,
        "show_error_details": false,
        "log_level": "error",
        "enable_query_logging": false
    },
    "security": {
        "force_https": true,
        "enable_hsts": true,
        "content_security_policy": true
    }
}
```

## 🔧 Configuration Validation

### Validation Script
Create `validate_config.php`:

```php
<?php
function validateConfig($configPath) {
    if (!file_exists($configPath)) {
        throw new Exception("Configuration file not found: $configPath");
    }
    
    $config = json_decode(file_get_contents($configPath), true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("Invalid JSON in configuration file");
    }
    
    // Validate required sections
    $required = ['database', 'api_keys', 'video_generation', 'tools', 'security'];
    foreach ($required as $section) {
        if (!isset($config[$section])) {
            throw new Exception("Missing required configuration section: $section");
        }
    }
    
    // Validate database connection
    try {
        $pdo = new PDO(
            "mysql:host={$config['database']['host']};dbname={$config['database']['database']}",
            $config['database']['username'],
            $config['database']['password']
        );
        echo "✅ Database connection successful\n";
    } catch (PDOException $e) {
        throw new Exception("Database connection failed: " . $e->getMessage());
    }
    
    // Validate API keys
    if (empty($config['api_keys']['huggingface']['token'])) {
        echo "⚠️ Warning: Hugging Face API key not configured\n";
    }
    
    echo "✅ Configuration validation passed\n";
}

// Run validation
try {
    validateConfig('config/settings.json');
} catch (Exception $e) {
    echo "❌ Configuration error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
```

## 📝 Configuration Best Practices

### Security
- Never commit API keys to version control
- Use environment variables for sensitive data
- Regularly rotate API keys and passwords
- Enable HTTPS in production
- Set appropriate file permissions (644 for config files)

### Performance
- Enable caching for production environments
- Set appropriate memory limits based on usage
- Configure rate limiting to prevent abuse
- Use CDN for static assets

### Maintenance
- Regularly backup configuration files
- Document any custom modifications
- Test configuration changes in staging first
- Monitor logs for configuration-related errors

This configuration guide ensures your Sutradhar installation is properly configured for optimal performance, security, and functionality.
