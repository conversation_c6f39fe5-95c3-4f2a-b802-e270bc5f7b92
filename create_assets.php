<?php
/**
 * Create sample audio assets for Sutradhar Engine
 */

echo "🎵 Creating sample audio assets...\n";

function createWAVFile($filename, $duration, $frequency, $amplitude = 0.3) {
    $sampleRate = 44100;
    $channels = 2; // Stereo
    $bitsPerSample = 16;
    
    $numSamples = intval($duration * $sampleRate);
    $dataSize = $numSamples * $channels * ($bitsPerSample / 8);
    
    // Create WAV header
    $header = pack('V', 0x46464952); // "RIFF"
    $header .= pack('V', $dataSize + 36); // File size - 8
    $header .= pack('V', 0x45564157); // "WAVE"
    $header .= pack('V', 0x20746d66); // "fmt "
    $header .= pack('V', 16); // Subchunk1Size
    $header .= pack('v', 1); // AudioFormat (PCM)
    $header .= pack('v', $channels); // NumChannels
    $header .= pack('V', $sampleRate); // SampleRate
    $header .= pack('V', $sampleRate * $channels * ($bitsPerSample / 8)); // ByteRate
    $header .= pack('v', $channels * ($bitsPerSample / 8)); // BlockAlign
    $header .= pack('v', $bitsPerSample); // BitsPerSample
    $header .= pack('V', 0x61746164); // "data"
    $header .= pack('V', $dataSize); // Subchunk2Size
    
    // Create audio data
    $audioData = '';
    for ($i = 0; $i < $numSamples; $i++) {
        $time = $i / $sampleRate;
        
        // Create a more complex waveform
        $sample = 0;
        
        if (is_array($frequency)) {
            // Multiple frequencies for harmony
            foreach ($frequency as $freq) {
                $sample += sin($time * 2 * pi() * $freq) * $amplitude / count($frequency);
            }
        } else {
            $sample = sin($time * 2 * pi() * $frequency) * $amplitude;
        }
        
        // Add some variation to make it more interesting
        $baseFreq = is_array($frequency) ? $frequency[0] : $frequency;
        $sample += sin($time * 2 * pi() * ($baseFreq * 1.5)) * $amplitude * 0.3;
        $sample += sin($time * 2 * pi() * ($baseFreq * 0.5)) * $amplitude * 0.2;
        
        // Apply envelope to avoid clicks
        $envelope = 1.0;
        if ($time < 0.1) {
            $envelope = $time / 0.1; // Fade in
        } elseif ($time > $duration - 0.1) {
            $envelope = ($duration - $time) / 0.1; // Fade out
        }
        
        $sample *= $envelope;
        
        // Convert to 16-bit integer
        $intSample = intval($sample * 32767);
        $intSample = max(-32767, min(32767, $intSample));
        
        // Write stereo (same sample for both channels)
        $audioData .= pack('v', $intSample);
        $audioData .= pack('v', $intSample);
    }
    
    // Write the WAV file
    $wavContent = $header . $audioData;
    return file_put_contents($filename, $wavContent);
}

// Create background music files
$musicFiles = [
    'assets/audio/music/background.wav' => [30, 220, 0.2], // 30 seconds, 220Hz, quiet
    'assets/audio/music/comedy.wav' => [20, [330, 440], 0.25], // Upbeat comedy music
    'assets/audio/music/traditional.wav' => [25, [196, 294], 0.2], // Traditional feel
    'assets/audio/music/emotional.wav' => [30, [174, 261], 0.15], // Soft emotional
    'assets/audio/music/bollywood.wav' => [25, [392, 523], 0.3], // Energetic Bollywood
];

foreach ($musicFiles as $filename => $params) {
    list($duration, $frequency, $amplitude) = $params;
    
    if (createWAVFile($filename, $duration, $frequency, $amplitude)) {
        echo "✅ Created: $filename\n";
    } else {
        echo "❌ Failed to create: $filename\n";
    }
}

// Create sound effects
$effectFiles = [
    'assets/audio/effects/ding.wav' => [0.5, 880, 0.5],
    'assets/audio/effects/pop.wav' => [0.3, 440, 0.6],
    'assets/audio/effects/whoosh.wav' => [1.0, [200, 300, 400], 0.4],
    'assets/audio/effects/laugh.wav' => [2.0, [300, 400, 500], 0.3],
    'assets/audio/effects/rimshot.wav' => [0.8, [150, 200], 0.7],
];

foreach ($effectFiles as $filename => $params) {
    list($duration, $frequency, $amplitude) = $params;
    
    if (createWAVFile($filename, $duration, $frequency, $amplitude)) {
        echo "✅ Created: $filename\n";
    } else {
        echo "❌ Failed to create: $filename\n";
    }
}

// Create background images (simple colored rectangles)
function createSimpleImage($filename, $width, $height, $color) {
    // Create a simple image using GD if available
    if (extension_loaded('gd')) {
        $image = imagecreate($width, $height);
        
        // Parse hex color
        $r = hexdec(substr($color, 1, 2));
        $g = hexdec(substr($color, 3, 2));
        $b = hexdec(substr($color, 5, 2));
        
        $bgColor = imagecolorallocate($image, $r, $g, $b);
        imagefill($image, 0, 0, $bgColor);
        
        // Add some text
        $textColor = imagecolorallocate($image, 255, 255, 255);
        $text = basename($filename, '.jpg');
        imagestring($image, 5, 50, $height/2 - 10, ucfirst($text), $textColor);
        
        $result = imagejpeg($image, $filename, 80);
        imagedestroy($image);
        return $result;
    }
    
    return false;
}

// Create background images
if (!is_dir('assets/backgrounds')) {
    mkdir('assets/backgrounds', 0755, true);
}

$backgroundImages = [
    'assets/backgrounds/home.jpg' => '#F5F5DC',      // Beige
    'assets/backgrounds/office.jpg' => '#E6E6FA',    // Lavender
    'assets/backgrounds/nature.jpg' => '#90EE90',    // Light Green
    'assets/backgrounds/city.jpg' => '#708090',      // Slate Gray
    'assets/backgrounds/traditional.jpg' => '#FFE4B5' // Moccasin
];

foreach ($backgroundImages as $filename => $color) {
    if (createSimpleImage($filename, 720, 1280, $color)) {
        echo "✅ Created: $filename\n";
    } else {
        echo "⚠️  Could not create: $filename (GD extension not available)\n";
    }
}

echo "\n🎉 Asset creation completed!\n";
echo "\nCreated files:\n";
echo "📁 Music files: " . count($musicFiles) . "\n";
echo "📁 Effect files: " . count($effectFiles) . "\n";
echo "📁 Background images: " . count($backgroundImages) . "\n";

// List all created files with sizes
echo "\n📊 File sizes:\n";
$allFiles = array_merge(array_keys($musicFiles), array_keys($effectFiles), array_keys($backgroundImages));

foreach ($allFiles as $file) {
    if (file_exists($file)) {
        $size = filesize($file);
        $sizeFormatted = $size > 1024 ? round($size/1024, 1) . ' KB' : $size . ' B';
        echo "   $file: $sizeFormatted\n";
    }
}
?>
