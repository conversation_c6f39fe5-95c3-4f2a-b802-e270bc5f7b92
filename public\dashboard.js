/**
 * Dashboard JavaScript - Interactive functionality for Sutradhar 2070 Dashboard
 * Handles user data, analytics, navigation, and real-time updates
 */

class Dashboard {
    constructor() {
        this.currentUser = null;
        this.currentSection = 'overview';
        this.refreshInterval = null;
        
        this.init();
    }
    
    async init() {
        console.log('🚀 Initializing dashboard...');

        // Try to load stored user first
        const hasStoredUser = this.loadStoredUser();

        if (!hasStoredUser) {
            // If no stored user, load from server
            await this.loadUserData();
        }

        this.setupNavigation();
        this.setupEventListeners();
        this.loadDashboardData();
        this.startAutoRefresh();
        this.initializeAnimations();

        console.log('✅ Dashboard initialized successfully');
    }
    
    /**
     * Load current user data
     */
    async loadUserData() {
        try {
            console.log('🔍 Loading user data...');

            // Try main auth status endpoint
            const response = await fetch('/api/auth/status');

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const responseText = await response.text();
            console.log('User data response:', responseText);

            let data;
            try {
                data = JSON.parse(responseText);
            } catch (parseError) {
                console.error('User data JSON parse error:', parseError);
                throw new Error('Invalid response from server');
            }

            if (data.success) {
                const userData = data.data?.user || data.user;
                if (userData) {
                    console.log('✅ User data loaded:', userData.email);
                    this.currentUser = userData;
                    this.updateUserDisplay();
                    return;
                }
            }

            // Try fallback APIs
            await this.tryFallbackUserData();

        } catch (error) {
            console.error('❌ Failed to load user data:', error.message);
            await this.tryFallbackUserData();
        }
    }

    async tryFallbackUserData() {
        const fallbackAPIs = [
            'working_api.php?endpoint=auth/status',
            'test_api_simple.php?endpoint=auth/status'
        ];

        for (const api of fallbackAPIs) {
            try {
                console.log(`🔄 Trying fallback user data: ${api}`);

                const response = await fetch(api);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        const userData = data.data?.user || data.user;
                        if (userData) {
                            console.log('✅ Fallback user data successful:', userData.email);
                            this.currentUser = userData;
                            this.updateUserDisplay();
                            return;
                        }
                    }
                }
            } catch (error) {
                console.log(`❌ Fallback API ${api} failed:`, error.message);
            }
        }

        // If all methods fail, show demo mode
        console.log('⚠️ All user data methods failed, showing demo mode');
        this.showDemoMode();
    }

    showDemoMode() {
        // Create a demo user for display purposes
        this.currentUser = {
            user_id: 'demo_user',
            first_name: 'Demo',
            last_name: 'User',
            email: '<EMAIL>',
            tier: 'free',
            credits: 50,
            subscription: 'demo',
            created_at: new Date().toISOString()
        };

        this.updateUserDisplay();
        this.showNotification('info', 'Demo Mode', 'You are viewing the dashboard in demo mode. Sign in for full functionality.');
    }
    
    /**
     * Update user display elements
     */
    updateUserDisplay() {
        if (!this.currentUser) return;

        // Store user data for persistence
        try {
            localStorage.setItem('sutradhar_user', JSON.stringify(this.currentUser));
            console.log('✅ User data stored in localStorage');
        } catch (error) {
            console.warn('⚠️ Failed to store user data:', error);
        }

        const userNameEl = document.getElementById('user-name');
        const userTierEl = document.getElementById('user-tier');
        const creditBalanceEl = document.getElementById('credit-balance');

        if (userNameEl) {
            userNameEl.textContent = `${this.currentUser.first_name} ${this.currentUser.last_name}`;
        }

        if (userTierEl) {
            userTierEl.textContent = (this.currentUser.tier || 'free').toUpperCase();
            userTierEl.className = `tier-badge ${this.currentUser.tier || 'free'}`;
        }

        if (creditBalanceEl) {
            creditBalanceEl.textContent = this.currentUser.credits || 0;
        }
    }

    loadStoredUser() {
        try {
            const storedUser = localStorage.getItem('sutradhar_user');
            if (storedUser) {
                const userData = JSON.parse(storedUser);
                console.log('📦 Loaded stored user for dashboard:', userData.email);
                this.currentUser = userData;
                this.updateUserDisplay();
                return true;
            }
        } catch (error) {
            console.warn('⚠️ Failed to load stored user data:', error);
            localStorage.removeItem('sutradhar_user');
        }
        return false;
    }

    clearStoredUser() {
        try {
            localStorage.removeItem('sutradhar_user');
            console.log('🗑️ Cleared stored user data');
        } catch (error) {
            console.warn('⚠️ Failed to clear stored user data:', error);
        }
    }
    
    /**
     * Setup navigation functionality
     */
    setupNavigation() {
        const navItems = document.querySelectorAll('.nav-item');
        
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                
                // Remove active class from all items
                navItems.forEach(nav => nav.classList.remove('active'));
                
                // Add active class to clicked item
                item.classList.add('active');
                
                // Get section from href
                const section = item.getAttribute('href').substring(1);
                this.showSection(section);
            });
        });
    }
    
    /**
     * Show specific dashboard section
     */
    showSection(section) {
        this.currentSection = section;
        
        // Hide all sections
        const sections = document.querySelectorAll('[id$="-section"]');
        sections.forEach(sec => sec.style.display = 'none');
        
        // Show target section
        const targetSection = document.getElementById(`${section}-section`);
        if (targetSection) {
            targetSection.style.display = 'block';
        } else {
            // Load section dynamically
            this.loadSection(section);
        }
        
        // Update header
        this.updateSectionHeader(section);
    }
    
    /**
     * Load section content dynamically
     */
    async loadSection(section) {
        const main = document.querySelector('main');
        
        try {
            let content = '';
            
            switch (section) {
                case 'generate':
                    window.location.href = 'mood_video_generator.html';
                    return;
                    
                case 'history':
                    content = await this.loadHistorySection();
                    break;
                    
                case 'billing':
                    content = await this.loadBillingSection();
                    break;
                    
                case 'analytics':
                    content = await this.loadAnalyticsSection();
                    break;
                    
                case 'settings':
                    content = await this.loadSettingsSection();
                    break;
                    
                default:
                    return;
            }
            
            // Hide overview section
            document.getElementById('overview-section').style.display = 'none';
            
            // Create new section
            const sectionDiv = document.createElement('section');
            sectionDiv.id = `${section}-section`;
            sectionDiv.innerHTML = content;
            main.appendChild(sectionDiv);
            
        } catch (error) {
            console.error(`Failed to load ${section} section:`, error);
        }
    }
    
    /**
     * Load generation history section
     */
    async loadHistorySection() {
        const history = await this.fetchGenerationHistory();
        
        return `
            <div class="space-y-6">
                <div class="dashboard-card p-6 rounded-lg">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-2xl font-semibold hologram-text">Generation History</h3>
                        <div class="flex space-x-4">
                            <select class="bg-dark-card border border-cyber-blue/30 rounded-lg px-4 py-2">
                                <option>All Types</option>
                                <option>Mood-Optimized</option>
                                <option>Premium Quality</option>
                                <option>Basic Generation</option>
                            </select>
                            <button class="cyber-button px-4 py-2 rounded-lg">Export</button>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        ${history.map(item => this.renderHistoryItem(item)).join('')}
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Load billing section
     */
    async loadBillingSection() {
        const billing = await this.fetchBillingData();
        
        return `
            <div class="space-y-6">
                <!-- Current Plan -->
                <div class="dashboard-card p-6 rounded-lg">
                    <h3 class="text-2xl font-semibold hologram-text mb-6">Current Plan</h3>
                    <div class="grid md:grid-cols-3 gap-6">
                        ${this.renderPricingPlans()}
                    </div>
                </div>
                
                <!-- Credit Packages -->
                <div class="dashboard-card p-6 rounded-lg">
                    <h3 class="text-2xl font-semibold hologram-text mb-6">Credit Packages</h3>
                    <div class="grid md:grid-cols-4 gap-4">
                        ${this.renderCreditPackages()}
                    </div>
                </div>
                
                <!-- Payment History -->
                <div class="dashboard-card p-6 rounded-lg">
                    <h3 class="text-2xl font-semibold hologram-text mb-6">Payment History</h3>
                    <div class="space-y-4">
                        ${billing.payments.map(payment => this.renderPaymentItem(payment)).join('')}
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Load analytics section
     */
    async loadAnalyticsSection() {
        return `
            <div class="space-y-6">
                <!-- Usage Analytics -->
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="dashboard-card p-6 rounded-lg">
                        <h3 class="text-xl font-semibold mb-4">Credit Usage Trend</h3>
                        <canvas id="creditChart" width="400" height="200"></canvas>
                    </div>
                    
                    <div class="dashboard-card p-6 rounded-lg">
                        <h3 class="text-xl font-semibold mb-4">Generation Types</h3>
                        <canvas id="typeChart" width="400" height="200"></canvas>
                    </div>
                </div>
                
                <!-- Performance Metrics -->
                <div class="dashboard-card p-6 rounded-lg">
                    <h3 class="text-2xl font-semibold hologram-text mb-6">Performance Metrics</h3>
                    <div class="grid md:grid-cols-4 gap-6">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-cyber-blue mb-2">2.3m</div>
                            <div class="text-sm text-gray-400">Avg Generation Time</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-cyber-green mb-2">94%</div>
                            <div class="text-sm text-gray-400">Success Rate</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-cyber-purple mb-2">4.8/5</div>
                            <div class="text-sm text-gray-400">Quality Rating</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-cyber-orange mb-2">15.2</div>
                            <div class="text-sm text-gray-400">Credits/Video Avg</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Load settings section
     */
    async loadSettingsSection() {
        return `
            <div class="space-y-6">
                <!-- Profile Settings -->
                <div class="dashboard-card p-6 rounded-lg">
                    <h3 class="text-2xl font-semibold hologram-text mb-6">Profile Settings</h3>
                    <form class="space-y-4">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">First Name</label>
                                <input type="text" class="w-full bg-dark-card border border-cyber-blue/30 rounded-lg px-4 py-2" value="${this.currentUser.first_name}">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Last Name</label>
                                <input type="text" class="w-full bg-dark-card border border-cyber-blue/30 rounded-lg px-4 py-2" value="${this.currentUser.last_name}">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">Email</label>
                            <input type="email" class="w-full bg-dark-card border border-cyber-blue/30 rounded-lg px-4 py-2" value="${this.currentUser.email}">
                        </div>
                        <button type="submit" class="cyber-button px-6 py-2 rounded-lg">Update Profile</button>
                    </form>
                </div>
                
                <!-- Notification Settings -->
                <div class="dashboard-card p-6 rounded-lg">
                    <h3 class="text-2xl font-semibold hologram-text mb-6">Notifications</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="font-semibold">Email Notifications</h4>
                                <p class="text-sm text-gray-400">Receive updates about your generations</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" class="sr-only peer" checked>
                                <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyber-blue"></div>
                            </label>
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="font-semibold">Low Credits Alert</h4>
                                <p class="text-sm text-gray-400">Get notified when credits are running low</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" class="sr-only peer" checked>
                                <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyber-blue"></div>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- API Settings -->
                <div class="dashboard-card p-6 rounded-lg">
                    <h3 class="text-2xl font-semibold hologram-text mb-6">API Access</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-2">API Key</label>
                            <div class="flex space-x-2">
                                <input type="password" class="flex-1 bg-dark-card border border-cyber-blue/30 rounded-lg px-4 py-2" value="sk_live_xxxxxxxxxxxxxxxx" readonly>
                                <button class="cyber-button px-4 py-2 rounded-lg">Regenerate</button>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">Webhook URL</label>
                            <input type="url" class="w-full bg-dark-card border border-cyber-blue/30 rounded-lg px-4 py-2" placeholder="https://your-app.com/webhook">
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Credit purchase buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.purchase-credits')) {
                this.handleCreditPurchase(e.target.dataset.package);
            }
            
            if (e.target.matches('.upgrade-plan')) {
                this.handlePlanUpgrade(e.target.dataset.plan);
            }
        });
        
        // Real-time updates
        window.addEventListener('focus', () => {
            this.loadDashboardData();
        });
    }
    
    /**
     * Load dashboard data
     */
    async loadDashboardData() {
        try {
            const [stats, history] = await Promise.all([
                this.fetchUserStats(),
                this.fetchRecentGenerations()
            ]);
            
            this.updateStats(stats);
            this.updateRecentGenerations(history);
            
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            this.loadDemoData();
        }
    }
    
    /**
     * Load demo data for development
     */
    loadDemoData() {
        const demoStats = {
            total_videos: 24,
            credits_used: 350,
            avg_quality: 94,
            avg_time: '2.3m'
        };
        
        const demoHistory = [
            {
                id: 'gen_1',
                type: 'mood_optimized',
                mood: 'euphoric',
                topic: 'nature_wildlife',
                created_at: '2024-01-15 14:30:00',
                status: 'completed',
                credits_used: 15,
                quality_score: 96
            },
            {
                id: 'gen_2',
                type: 'premium_quality',
                mood: 'serene',
                topic: 'urban_city',
                created_at: '2024-01-15 12:15:00',
                status: 'completed',
                credits_used: 25,
                quality_score: 98
            }
        ];
        
        this.updateStats(demoStats);
        this.updateRecentGenerations(demoHistory);
    }
    
    /**
     * Update statistics display
     */
    updateStats(stats) {
        document.getElementById('total-videos').textContent = stats.total_videos;
        document.getElementById('credits-used').textContent = stats.credits_used;
        document.getElementById('avg-quality').textContent = stats.avg_quality + '%';
        document.getElementById('avg-time').textContent = stats.avg_time;
    }
    
    /**
     * Update recent generations display
     */
    updateRecentGenerations(history) {
        const container = document.getElementById('recent-generations');
        container.innerHTML = history.map(item => this.renderHistoryItem(item)).join('');
    }
    
    /**
     * Render history item
     */
    renderHistoryItem(item) {
        const statusColors = {
            completed: 'text-cyber-green',
            processing: 'text-cyber-orange',
            failed: 'text-red-500'
        };
        
        return `
            <div class="generation-item p-4 rounded-lg">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-cyber-blue to-cyber-purple rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold">🎬</span>
                        </div>
                        <div>
                            <h4 class="font-semibold">${item.mood} • ${item.topic}</h4>
                            <p class="text-sm text-gray-400">${item.type.replace('_', ' ')} • ${item.created_at}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="flex items-center space-x-4">
                            <span class="text-sm ${statusColors[item.status]}">${item.status}</span>
                            <span class="text-sm text-gray-400">${item.credits_used} credits</span>
                            <button class="text-cyber-blue hover:text-white transition-colors">Download</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Initialize animations
     */
    initializeAnimations() {
        // Animate stats cards on load
        gsap.from('.stat-card', {
            duration: 0.8,
            y: 50,
            opacity: 0,
            stagger: 0.1,
            ease: 'power2.out'
        });
        
        // Animate dashboard cards
        gsap.from('.dashboard-card', {
            duration: 1,
            y: 30,
            opacity: 0,
            stagger: 0.2,
            ease: 'power2.out',
            delay: 0.3
        });
    }
    
    /**
     * Start auto-refresh
     */
    startAutoRefresh() {
        this.refreshInterval = setInterval(() => {
            this.loadDashboardData();
            this.refreshUserData();
        }, 30000); // Refresh every 30 seconds
    }

    async refreshUserData() {
        if (!this.currentUser) return;

        try {
            console.log('🔄 Refreshing user data...');

            const response = await fetch('working_api.php?endpoint=user/update');

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.data.user) {
                    console.log('✅ User data refreshed');
                    this.currentUser = data.data.user;
                    this.updateUserDisplay();
                }
            } else {
                console.warn('⚠️ Failed to refresh user data');
            }
        } catch (error) {
            console.error('❌ Error refreshing user data:', error);
        }
    }
    
    /**
     * Fetch user statistics
     */
    async fetchUserStats() {
        try {
            console.log('📊 Fetching user statistics...');

            // Try main dashboard stats endpoint
            const response = await fetch('/api/dashboard/stats');

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const responseText = await response.text();
            console.log('Stats response:', responseText);

            let data;
            try {
                data = JSON.parse(responseText);
            } catch (parseError) {
                console.error('Stats JSON parse error:', parseError);
                throw new Error('Invalid response from server');
            }

            if (data.success && data.data) {
                console.log('✅ Stats loaded successfully');
                return this.transformStatsData(data.data);
            }

            throw new Error('Invalid stats response');

        } catch (error) {
            console.error('❌ Failed to fetch stats:', error.message);
            return this.tryFallbackStats();
        }
    }

    async tryFallbackStats() {
        const fallbackAPIs = [
            'working_api.php?endpoint=dashboard/stats',
            'test_api_simple.php?endpoint=dashboard/stats'
        ];

        for (const api of fallbackAPIs) {
            try {
                console.log(`🔄 Trying fallback stats: ${api}`);

                const response = await fetch(api);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data) {
                        console.log('✅ Fallback stats successful');
                        return this.transformStatsData(data.data);
                    }
                }
            } catch (error) {
                console.log(`❌ Fallback stats API ${api} failed:`, error.message);
            }
        }

        // Return demo stats if all fail
        console.log('⚠️ Using demo stats data');
        return {
            total_videos: 0,
            credits_used: 0,
            avg_quality: 0,
            avg_time: '0m'
        };
    }

    transformStatsData(apiData) {
        const genStats = apiData.generation_stats || {};
        const creditStats = apiData.credit_stats || {};

        return {
            total_videos: genStats.total || 0,
            credits_used: creditStats.used || 0,
            avg_quality: genStats.success_rate || 0,
            avg_time: '2.3m' // Default for now
        };
    }

    /**
     * Fetch recent generations
     */
    async fetchRecentGenerations() {
        try {
            console.log('📋 Fetching recent generations...');

            // Try main generation history endpoint
            const response = await fetch('/api/generate/history');

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const responseText = await response.text();
            console.log('History response:', responseText);

            let data;
            try {
                data = JSON.parse(responseText);
            } catch (parseError) {
                console.error('History JSON parse error:', parseError);
                throw new Error('Invalid response from server');
            }

            if (data.success && data.data) {
                console.log('✅ History loaded successfully');
                return this.transformHistoryData(data.data);
            }

            throw new Error('Invalid history response');

        } catch (error) {
            console.error('❌ Failed to fetch history:', error.message);
            return this.tryFallbackHistory();
        }
    }

    async tryFallbackHistory() {
        const fallbackAPIs = [
            'working_api.php?endpoint=generate/history',
            'test_api_simple.php?endpoint=generate/history'
        ];

        for (const api of fallbackAPIs) {
            try {
                console.log(`🔄 Trying fallback history: ${api}`);

                const response = await fetch(api);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data) {
                        console.log('✅ Fallback history successful');
                        return this.transformHistoryData(data.data);
                    }
                }
            } catch (error) {
                console.log(`❌ Fallback history API ${api} failed:`, error.message);
            }
        }

        // Return demo history if all fail
        console.log('⚠️ Using demo history data');
        return [];
    }

    transformHistoryData(apiData) {
        const jobs = apiData.jobs || apiData || [];

        return jobs.map(job => ({
            id: job.job_id || job.id,
            type: job.generation_type || 'standard',
            mood: job.mood || 'unknown',
            topic: job.topic || 'unknown',
            created_at: job.created_at || new Date().toISOString(),
            status: job.status || 'completed',
            credits_used: job.credits_used || 10,
            quality_score: job.quality_score || 95
        }));
    }
    
    /**
     * Show notification to user
     */
    showNotification(type, title, message) {
        console.log(`${type.toUpperCase()}: ${title} - ${message}`);

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type} fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm`;
        notification.style.background = type === 'error' ? '#EF4444' : type === 'success' ? '#10B981' : '#3B82F6';
        notification.style.color = 'white';

        notification.innerHTML = `
            <div class="flex items-start">
                <div class="flex-1">
                    <h4 class="font-semibold">${title}</h4>
                    <p class="text-sm opacity-90">${message}</p>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-white opacity-70 hover:opacity-100">×</button>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
}

// Utility functions
async function logout() {
    if (confirm('Are you sure you want to sign out?')) {
        try {
            console.log('🚪 Logging out...');

            // Try main logout endpoint
            const response = await fetch('/api/auth/logout', { method: 'POST' });

            if (response.ok) {
                console.log('✅ Logout successful');
            } else {
                console.log('⚠️ Logout response not ok, trying fallback');

                // Try fallback logout
                const fallbackResponse = await fetch('working_api.php?endpoint=auth/logout', { method: 'POST' });
                if (fallbackResponse.ok) {
                    console.log('✅ Fallback logout successful');
                }
            }
        } catch (error) {
            console.log('❌ Logout error:', error.message);
        } finally {
            // Clear stored user data
            try {
                localStorage.removeItem('sutradhar_user');
                console.log('🗑️ Cleared stored user data on logout');
            } catch (error) {
                console.warn('⚠️ Failed to clear stored user data:', error);
            }

            // Always redirect regardless of logout success
            console.log('🏠 Redirecting to homepage...');
            window.location.href = 'index.html';
        }
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new Dashboard();
});
