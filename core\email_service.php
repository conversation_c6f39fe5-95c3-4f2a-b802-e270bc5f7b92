<?php
/**
 * Email Service - Professional email handling for Sutradhar 2070
 * Handles verification emails, password resets, notifications, and marketing emails
 */

class EmailService {
    private $config;
    private $templates;
    
    public function __construct() {
        $this->loadConfig();
        $this->loadTemplates();
    }
    
    /**
     * Send verification email
     */
    public function sendVerificationEmail($email, $token, $firstName) {
        $verificationUrl = $this->config['base_url'] . "/verify-email?token=" . $token;
        
        $subject = "Welcome to Sutradhar 2070 - Verify Your Email";
        $template = $this->templates['verification'];
        
        $variables = [
            'first_name' => $firstName,
            'verification_url' => $verificationUrl,
            'company_name' => 'Sutradhar 2070',
            'support_email' => $this->config['support_email']
        ];
        
        $htmlContent = $this->renderTemplate($template['html'], $variables);
        $textContent = $this->renderTemplate($template['text'], $variables);
        
        return $this->sendEmail($email, $subject, $htmlContent, $textContent);
    }
    
    /**
     * Send welcome email
     */
    public function sendWelcomeEmail($email, $firstName) {
        $dashboardUrl = $this->config['base_url'] . "/dashboard";
        
        $subject = "Welcome to Sutradhar 2070 - Your Account is Ready!";
        $template = $this->templates['welcome'];
        
        $variables = [
            'first_name' => $firstName,
            'dashboard_url' => $dashboardUrl,
            'company_name' => 'Sutradhar 2070',
            'support_email' => $this->config['support_email'],
            'free_credits' => 50
        ];
        
        $htmlContent = $this->renderTemplate($template['html'], $variables);
        $textContent = $this->renderTemplate($template['text'], $variables);
        
        return $this->sendEmail($email, $subject, $htmlContent, $textContent);
    }
    
    /**
     * Send password reset email
     */
    public function sendPasswordResetEmail($email, $token, $firstName) {
        $resetUrl = $this->config['base_url'] . "/reset-password?token=" . $token;
        
        $subject = "Reset Your Sutradhar 2070 Password";
        $template = $this->templates['password_reset'];
        
        $variables = [
            'first_name' => $firstName,
            'reset_url' => $resetUrl,
            'company_name' => 'Sutradhar 2070',
            'support_email' => $this->config['support_email'],
            'expiry_time' => '1 hour'
        ];
        
        $htmlContent = $this->renderTemplate($template['html'], $variables);
        $textContent = $this->renderTemplate($template['text'], $variables);
        
        return $this->sendEmail($email, $subject, $htmlContent, $textContent);
    }
    
    /**
     * Send payment confirmation email
     */
    public function sendPaymentConfirmationEmail($email, $firstName, $paymentData) {
        $subject = "Payment Confirmation - Sutradhar 2070";
        $template = $this->templates['payment_confirmation'];
        
        $variables = [
            'first_name' => $firstName,
            'amount' => $paymentData['amount'],
            'currency' => $paymentData['currency'],
            'credits_purchased' => $paymentData['credits'],
            'transaction_id' => $paymentData['transaction_id'],
            'date' => date('F j, Y'),
            'company_name' => 'Sutradhar 2070',
            'support_email' => $this->config['support_email']
        ];
        
        $htmlContent = $this->renderTemplate($template['html'], $variables);
        $textContent = $this->renderTemplate($template['text'], $variables);
        
        return $this->sendEmail($email, $subject, $htmlContent, $textContent);
    }
    
    /**
     * Send subscription confirmation email
     */
    public function sendSubscriptionConfirmationEmail($email, $firstName, $subscriptionData) {
        $subject = "Subscription Activated - Sutradhar 2070";
        $template = $this->templates['subscription_confirmation'];
        
        $variables = [
            'first_name' => $firstName,
            'plan_name' => $subscriptionData['plan_name'],
            'monthly_credits' => $subscriptionData['monthly_credits'],
            'amount' => $subscriptionData['amount'],
            'currency' => $subscriptionData['currency'],
            'next_billing_date' => $subscriptionData['next_billing_date'],
            'company_name' => 'Sutradhar 2070',
            'support_email' => $this->config['support_email']
        ];
        
        $htmlContent = $this->renderTemplate($template['html'], $variables);
        $textContent = $this->renderTemplate($template['text'], $variables);
        
        return $this->sendEmail($email, $subject, $htmlContent, $textContent);
    }
    
    /**
     * Send low credits notification
     */
    public function sendLowCreditsNotification($email, $firstName, $creditsRemaining) {
        $subject = "Low Credits Alert - Sutradhar 2070";
        $template = $this->templates['low_credits'];
        
        $variables = [
            'first_name' => $firstName,
            'credits_remaining' => $creditsRemaining,
            'purchase_url' => $this->config['base_url'] . "/dashboard/billing",
            'company_name' => 'Sutradhar 2070',
            'support_email' => $this->config['support_email']
        ];
        
        $htmlContent = $this->renderTemplate($template['html'], $variables);
        $textContent = $this->renderTemplate($template['text'], $variables);
        
        return $this->sendEmail($email, $subject, $htmlContent, $textContent);
    }
    
    /**
     * Core email sending function
     */
    private function sendEmail($to, $subject, $htmlContent, $textContent = null) {
        try {
            $headers = [
                'MIME-Version: 1.0',
                'Content-Type: text/html; charset=UTF-8',
                'From: ' . $this->config['from_name'] . ' <' . $this->config['from_email'] . '>',
                'Reply-To: ' . $this->config['support_email'],
                'X-Mailer: Sutradhar 2070 Email Service',
                'X-Priority: 3'
            ];
            
            // Use PHP mail() function (can be replaced with SMTP later)
            $success = mail($to, $subject, $htmlContent, implode("\r\n", $headers));
            
            // Log email sending
            $this->logEmail($to, $subject, $success);
            
            return $success;
            
        } catch (Exception $e) {
            error_log("Email sending failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Render email template with variables
     */
    private function renderTemplate($template, $variables) {
        $content = $template;
        
        foreach ($variables as $key => $value) {
            $content = str_replace('{{' . $key . '}}', $value, $content);
        }
        
        return $content;
    }
    
    /**
     * Log email sending attempts
     */
    private function logEmail($to, $subject, $success) {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'to' => $to,
            'subject' => $subject,
            'success' => $success,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ];
        
        $logFile = __DIR__ . '/../logs/email.log';
        if (!is_dir(dirname($logFile))) {
            mkdir(dirname($logFile), 0755, true);
        }
        
        file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Load email configuration
     */
    private function loadConfig() {
        $configFile = __DIR__ . '/../config/email_config.json';
        
        if (file_exists($configFile)) {
            $this->config = json_decode(file_get_contents($configFile), true);
        } else {
            $this->config = [
                'from_email' => '<EMAIL>',
                'from_name' => 'Sutradhar 2070',
                'support_email' => '<EMAIL>',
                'base_url' => 'https://sutradhar2070.com',
                'smtp_enabled' => false,
                'smtp_host' => '',
                'smtp_port' => 587,
                'smtp_username' => '',
                'smtp_password' => ''
            ];
            
            // Save default config
            if (!is_dir(dirname($configFile))) {
                mkdir(dirname($configFile), 0755, true);
            }
            file_put_contents($configFile, json_encode($this->config, JSON_PRETTY_PRINT));
        }
    }
    
    /**
     * Load email templates
     */
    private function loadTemplates() {
        $this->templates = [
            'verification' => [
                'html' => $this->getVerificationTemplate(),
                'text' => $this->getVerificationTextTemplate()
            ],
            'welcome' => [
                'html' => $this->getWelcomeTemplate(),
                'text' => $this->getWelcomeTextTemplate()
            ],
            'password_reset' => [
                'html' => $this->getPasswordResetTemplate(),
                'text' => $this->getPasswordResetTextTemplate()
            ],
            'payment_confirmation' => [
                'html' => $this->getPaymentConfirmationTemplate(),
                'text' => $this->getPaymentConfirmationTextTemplate()
            ],
            'subscription_confirmation' => [
                'html' => $this->getSubscriptionConfirmationTemplate(),
                'text' => $this->getSubscriptionConfirmationTextTemplate()
            ],
            'low_credits' => [
                'html' => $this->getLowCreditsTemplate(),
                'text' => $this->getLowCreditsTextTemplate()
            ]
        ];
    }
    
    /**
     * Get verification email template
     */
    private function getVerificationTemplate() {
        return '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Email - {{company_name}}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background: linear-gradient(135deg, #0F0F23 0%, #1A1A2E 100%); }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; margin-top: 20px; }
        .header { background: linear-gradient(45deg, #00D4FF, #8B5CF6); padding: 30px; text-align: center; }
        .header h1 { color: white; margin: 0; font-size: 28px; }
        .content { padding: 40px 30px; }
        .button { display: inline-block; background: linear-gradient(45deg, #00D4FF, #8B5CF6); color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 20px 0; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎭 {{company_name}}</h1>
            <p style="color: white; margin: 0;">Welcome to the Future of Video Creation</p>
        </div>
        <div class="content">
            <h2>Hi {{first_name}},</h2>
            <p>Welcome to Sutradhar 2070! We\'re excited to have you join our community of creators using AI-powered mood-based video generation.</p>
            <p>To get started, please verify your email address by clicking the button below:</p>
            <div style="text-align: center;">
                <a href="{{verification_url}}" class="button">Verify Email Address</a>
            </div>
            <p>Once verified, you\'ll have access to:</p>
            <ul>
                <li>🎭 30 distinct emotional moods for video generation</li>
                <li>✨ AI-powered prompt generation</li>
                <li>🎬 Professional 30-second video creation</li>
                <li>⚡ Lightning-fast processing with smart caching</li>
            </ul>
            <p>If you didn\'t create this account, please ignore this email.</p>
        </div>
        <div class="footer">
            <p>Need help? Contact us at <a href="mailto:{{support_email}}">{{support_email}}</a></p>
            <p>&copy; 2024 {{company_name}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>';
    }
    
    /**
     * Get verification text template
     */
    private function getVerificationTextTemplate() {
        return 'Hi {{first_name}},

Welcome to {{company_name}}! Please verify your email address by visiting:
{{verification_url}}

If you didn\'t create this account, please ignore this email.

Need help? Contact us at {{support_email}}

{{company_name}} Team';
    }
}
