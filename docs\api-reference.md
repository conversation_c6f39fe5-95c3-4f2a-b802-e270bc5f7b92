# API Reference

## 🌐 Overview

The Sutradhar API provides a unified interface for video generation, tools, user management, and credit system operations. All API endpoints are accessible through the main API gateway at `public/api_unified.php`.

## 🔐 Authentication

### Session-Based Authentication
All API requests require a valid user session. Users must be logged in to access protected endpoints.

```javascript
// Example: Check authentication status
fetch('/api_unified.php?endpoint=auth/status', {
    method: 'GET',
    credentials: 'include'
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('User authenticated:', data.data.user);
    }
});
```

### CSRF Protection
All POST requests require a valid CSRF token included in the request headers or form data.

```javascript
// Get CSRF token
const csrfToken = document.querySelector('meta[name="csrf-token"]').content;

// Include in requests
fetch('/api_unified.php?endpoint=generate', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken
    },
    body: JSON.stringify(requestData)
});
```

## 📊 Standard Response Format

All API responses follow a consistent JSON format:

```json
{
    "success": boolean,
    "data": object|array|null,
    "error": string|null,
    "timestamp": "2024-12-22T10:30:00Z",
    "request_id": "unique-request-identifier"
}
```

### HTTP Status Codes
- `200 OK`: Successful request
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

## 🎬 Video Generation API

### Generate Video
Create a new video generation job.

**Endpoint**: `POST /api_unified.php?endpoint=generate`

**Request Body**:
```json
{
    "mood": "euphoric",
    "topic": "nature_wildlife",
    "inspiration": ["cinematic", "colorful"],
    "duration": 30,
    "style": "professional"
}
```

**Response**:
```json
{
    "success": true,
    "data": {
        "job_id": "job_12345_67890",
        "status": "queued",
        "estimated_time": 45,
        "credits_used": 10
    }
}
```

### Check Job Status
Monitor the progress of a video generation job.

**Endpoint**: `GET /api_unified.php?endpoint=job_status&job_id={job_id}`

**Response**:
```json
{
    "success": true,
    "data": {
        "job_id": "job_12345_67890",
        "status": "processing",
        "progress": 65,
        "message": "Generating scene 2 of 3",
        "estimated_remaining": 15
    }
}
```

### Get Video Result
Retrieve the completed video.

**Endpoint**: `GET /api_unified.php?endpoint=video_result&job_id={job_id}`

**Response**:
```json
{
    "success": true,
    "data": {
        "job_id": "job_12345_67890",
        "status": "completed",
        "video_url": "/videos/job_12345_67890.mp4",
        "thumbnail_url": "/thumbnails/job_12345_67890.jpg",
        "duration": 30,
        "file_size": 2048576,
        "resolution": "720x1280"
    }
}
```

## 🛠️ Tools API

### List Available Tools
Get all available tools with their information.

**Endpoint**: `GET /api_unified.php?endpoint=tools/list`

**Response**:
```json
{
    "success": true,
    "data": {
        "categories": {
            "image_processing": [
                {
                    "id": "format_converter",
                    "name": "Multi-Format Converter",
                    "description": "Convert between PNG, JPG, WebP, GIF, BMP, TIFF",
                    "credit_cost": 1,
                    "max_file_size": 10485760,
                    "supported_formats": ["png", "jpg", "webp", "gif", "bmp", "tiff"]
                }
            ],
            "generators": [...],
            "utilities": [...],
            "advanced": [...]
        }
    }
}
```

### Use Tool
Execute a specific tool operation.

**Endpoint**: `POST /api_unified.php?endpoint=tools/execute`

**Request Body** (multipart/form-data):
```
tool_id: format_converter
files: [uploaded files]
options: {"output_format": "webp", "quality": 85}
```

**Response**:
```json
{
    "success": true,
    "data": {
        "operation_id": "op_12345",
        "processed_files": [
            {
                "original_name": "image1.png",
                "processed_name": "image1_converted.webp",
                "download_url": "/temp/tools/op_12345/image1_converted.webp",
                "file_size": 156789
            }
        ],
        "credits_used": 1,
        "processing_time": 2.3
    }
}
```

### Get Tool Usage Statistics
Retrieve user's tool usage statistics.

**Endpoint**: `GET /api_unified.php?endpoint=tools/stats`

**Response**:
```json
{
    "success": true,
    "data": {
        "total_operations": 156,
        "credits_used": 89,
        "favorite_tools": [
            {"tool_id": "format_converter", "usage_count": 23},
            {"tool_id": "background_remover", "usage_count": 18}
        ],
        "recent_activity": [
            {
                "tool_id": "qr_generator",
                "timestamp": "2024-12-22T09:15:00Z",
                "credits_used": 1
            }
        ]
    }
}
```

## 👤 User Management API

### User Registration
Create a new user account.

**Endpoint**: `POST /api_unified.php?endpoint=auth/register`

**Request Body**:
```json
{
    "email": "<EMAIL>",
    "password": "securePassword123",
    "confirm_password": "securePassword123",
    "terms_accepted": true
}
```

**Response**:
```json
{
    "success": true,
    "data": {
        "user_id": 12345,
        "email": "<EMAIL>",
        "credits_available": 50,
        "subscription_type": "free"
    }
}
```

### User Login
Authenticate user and create session.

**Endpoint**: `POST /api_unified.php?endpoint=auth/login`

**Request Body**:
```json
{
    "email": "<EMAIL>",
    "password": "securePassword123"
}
```

**Response**:
```json
{
    "success": true,
    "data": {
        "user_id": 12345,
        "email": "<EMAIL>",
        "credits_available": 42,
        "subscription_type": "pro",
        "session_expires": "2024-12-23T10:30:00Z"
    }
}
```

### Get User Profile
Retrieve current user's profile information.

**Endpoint**: `GET /api_unified.php?endpoint=user/profile`

**Response**:
```json
{
    "success": true,
    "data": {
        "user_id": 12345,
        "email": "<EMAIL>",
        "credits_available": 42,
        "credits_used_this_month": 158,
        "subscription": {
            "type": "pro",
            "expires_at": "2025-01-22T00:00:00Z",
            "auto_renew": true
        },
        "usage_stats": {
            "videos_generated": 23,
            "tools_used": 156,
            "total_credits_used": 1247
        }
    }
}
```

## 💳 Credit System API

### Get Credit Balance
Check current credit balance and usage.

**Endpoint**: `GET /api_unified.php?endpoint=credits/balance`

**Response**:
```json
{
    "success": true,
    "data": {
        "credits_available": 42,
        "credits_used_today": 8,
        "credits_used_this_month": 158,
        "monthly_limit": 500,
        "next_refill": "2025-01-01T00:00:00Z"
    }
}
```

### Purchase Credits
Buy additional credits (requires payment integration).

**Endpoint**: `POST /api_unified.php?endpoint=credits/purchase`

**Request Body**:
```json
{
    "package": "pro_500",
    "payment_method": "stripe",
    "payment_token": "stripe_payment_token"
}
```

**Response**:
```json
{
    "success": true,
    "data": {
        "transaction_id": "txn_12345",
        "credits_purchased": 500,
        "amount_paid": 9.99,
        "currency": "USD",
        "new_balance": 542
    }
}
```

### Credit Usage History
Get detailed credit usage history.

**Endpoint**: `GET /api_unified.php?endpoint=credits/history?limit=50&offset=0`

**Response**:
```json
{
    "success": true,
    "data": {
        "transactions": [
            {
                "id": 789,
                "type": "usage",
                "amount": -10,
                "description": "Video generation - Nature scene",
                "timestamp": "2024-12-22T09:30:00Z",
                "balance_after": 42
            },
            {
                "id": 788,
                "type": "purchase",
                "amount": 500,
                "description": "Pro package purchase",
                "timestamp": "2024-12-20T14:15:00Z",
                "balance_after": 52
            }
        ],
        "total_count": 156,
        "has_more": true
    }
}
```

## 📊 Analytics API

### Get Dashboard Data
Retrieve user dashboard statistics.

**Endpoint**: `GET /api_unified.php?endpoint=analytics/dashboard`

**Response**:
```json
{
    "success": true,
    "data": {
        "videos_this_month": 23,
        "tools_this_month": 156,
        "credits_used_this_month": 158,
        "popular_tools": [
            {"name": "Format Converter", "usage": 23},
            {"name": "Background Remover", "usage": 18}
        ],
        "recent_videos": [
            {
                "job_id": "job_12345",
                "created_at": "2024-12-22T09:30:00Z",
                "status": "completed",
                "thumbnail_url": "/thumbnails/job_12345.jpg"
            }
        ]
    }
}
```

## ⚠️ Error Handling

### Common Error Responses

**Insufficient Credits**:
```json
{
    "success": false,
    "error": "Insufficient credits. Required: 10, Available: 5",
    "error_code": "INSUFFICIENT_CREDITS",
    "data": {
        "required_credits": 10,
        "available_credits": 5,
        "upgrade_url": "/pricing"
    }
}
```

**Rate Limit Exceeded**:
```json
{
    "success": false,
    "error": "Rate limit exceeded. Try again in 60 seconds",
    "error_code": "RATE_LIMIT_EXCEEDED",
    "data": {
        "retry_after": 60,
        "limit": 10,
        "window": 60
    }
}
```

**File Upload Error**:
```json
{
    "success": false,
    "error": "File too large. Maximum size: 10MB",
    "error_code": "FILE_TOO_LARGE",
    "data": {
        "max_size": 10485760,
        "uploaded_size": 15728640
    }
}
```

## 🔄 Rate Limiting

### Limits by User Type

| User Type | Video Generation | Tools Usage | API Requests |
|-----------|------------------|-------------|--------------|
| Free      | 5/day           | 10/hour     | 100/hour     |
| Pro       | 50/day          | 100/hour    | 1000/hour    |
| Business  | 200/day         | Unlimited   | 5000/hour    |
| Enterprise| Unlimited       | Unlimited   | Unlimited    |

### Rate Limit Headers
All responses include rate limiting information:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640181600
```

## 📝 SDK Examples

### JavaScript SDK Usage
```javascript
class SutradharAPI {
    constructor(baseUrl = '/api_unified.php') {
        this.baseUrl = baseUrl;
        this.csrfToken = this.getCSRFToken();
    }

    async generateVideo(options) {
        return this.request('generate', 'POST', options);
    }

    async getJobStatus(jobId) {
        return this.request(`job_status&job_id=${jobId}`, 'GET');
    }

    async useTool(toolId, files, options = {}) {
        const formData = new FormData();
        formData.append('tool_id', toolId);
        formData.append('options', JSON.stringify(options));

        files.forEach(file => formData.append('files[]', file));

        return this.request('tools/execute', 'POST', formData);
    }

    async request(endpoint, method = 'GET', data = null) {
        const config = {
            method,
            credentials: 'include',
            headers: {}
        };

        if (method === 'POST' && !(data instanceof FormData)) {
            config.headers['Content-Type'] = 'application/json';
            config.headers['X-CSRF-Token'] = this.csrfToken;
            config.body = JSON.stringify(data);
        } else if (data instanceof FormData) {
            config.headers['X-CSRF-Token'] = this.csrfToken;
            config.body = data;
        }

        const response = await fetch(`${this.baseUrl}?endpoint=${endpoint}`, config);
        return response.json();
    }

    getCSRFToken() {
        return document.querySelector('meta[name="csrf-token"]')?.content || '';
    }
}

// Usage example
const api = new SutradharAPI();

// Generate video
const videoResult = await api.generateVideo({
    mood: 'euphoric',
    topic: 'nature_wildlife',
    inspiration: ['cinematic', 'colorful']
});

// Use tool
const files = document.getElementById('fileInput').files;
const toolResult = await api.useTool('format_converter', files, {
    output_format: 'webp',
    quality: 85
});
```

This API reference provides comprehensive documentation for integrating with the Sutradhar platform's video generation and tools functionality.
