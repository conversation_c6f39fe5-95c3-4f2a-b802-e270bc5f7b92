<?php
/**
 * Debug API for Sutradhar 2070
 * Helps identify issues with the main API
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Log request details
$logData = [
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $_SERVER['REQUEST_METHOD'],
    'endpoint' => $_GET['endpoint'] ?? 'none',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
    'request_body' => file_get_contents('php://input')
];

error_log("Debug API Request: " . json_encode($logData));

try {
    echo "Debug API Starting...\n";
    
    // Test 1: Basic PHP functionality
    echo "✅ PHP is working\n";
    
    // Test 2: Check if core files exist
    $coreFiles = [
        'core/database_manager.php',
        'core/api_base.php',
        'core/api_auth.php',
        'core/api_generation.php',
        'core/api_user.php',
        'core/api_payments.php'
    ];
    
    foreach ($coreFiles as $file) {
        if (file_exists("../$file")) {
            echo "✅ $file exists\n";
        } else {
            echo "❌ $file missing\n";
        }
    }
    
    // Test 3: Try to load database manager
    try {
        require_once '../core/database_manager.php';
        echo "✅ DatabaseManager loaded\n";
        
        $db = new DatabaseManager();
        echo "✅ DatabaseManager instantiated\n";
        
        // Test database connection
        $result = $db->query('SELECT 1 as test');
        if ($result) {
            echo "✅ Database connection working\n";
        } else {
            echo "❌ Database query failed\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Database error: " . $e->getMessage() . "\n";
    }
    
    // Test 4: Try to load API base
    try {
        require_once '../core/api_base.php';
        echo "✅ APIBase loaded\n";
    } catch (Exception $e) {
        echo "❌ APIBase error: " . $e->getMessage() . "\n";
    }
    
    // Test 5: Try to load generation API
    try {
        require_once '../core/api_generation.php';
        echo "✅ GenerationAPI loaded\n";
        
        // Test instantiation
        $genAPI = new GenerationAPI();
        echo "✅ GenerationAPI instantiated\n";
        
    } catch (Exception $e) {
        echo "❌ GenerationAPI error: " . $e->getMessage() . "\n";
    }
    
    // Test 6: Simulate the actual API call
    $endpoint = $_GET['endpoint'] ?? '';
    $method = $_SERVER['REQUEST_METHOD'];
    
    echo "📋 Request details:\n";
    echo "   Endpoint: $endpoint\n";
    echo "   Method: $method\n";
    
    if ($endpoint === 'generate' && $method === 'POST') {
        echo "🎬 Testing video generation endpoint...\n";
        
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        echo "   Input data: " . json_encode($data) . "\n";
        
        // Try to process like the real API
        try {
            // Check if demo mode
            $demoMode = $data['demo_mode'] ?? false;
            echo "   Demo mode: " . ($demoMode ? 'true' : 'false') . "\n";
            
            if ($demoMode) {
                // Demo mode - should work without authentication
                $jobId = 'debug_demo_' . time();
                
                $response = [
                    'success' => true,
                    'data' => [
                        'job_id' => $jobId,
                        'status' => 'pending',
                        'estimated_time' => 10
                    ],
                    'message' => 'Debug demo generation started'
                ];
                
                echo "✅ Demo mode response prepared\n";
                echo json_encode($response);
                exit;
                
            } else {
                echo "❌ Non-demo mode requires authentication (not implemented in debug)\n";
                
                $response = [
                    'success' => false,
                    'error' => 'Authentication required. Try with demo_mode: true'
                ];
                
                echo json_encode($response);
                exit;
            }
            
        } catch (Exception $e) {
            echo "❌ Generation processing error: " . $e->getMessage() . "\n";
            
            $response = [
                'success' => false,
                'error' => 'Debug API error: ' . $e->getMessage()
            ];
            
            echo json_encode($response);
            exit;
        }
    }
    
    // Default response for other endpoints
    $response = [
        'success' => true,
        'debug' => true,
        'endpoint' => $endpoint,
        'method' => $method,
        'message' => 'Debug API is working',
        'timestamp' => date('Y-m-d H:i:s'),
        'available_endpoints' => [
            'generate (POST with demo_mode: true)',
            'Any other endpoint for testing'
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    echo "❌ Fatal error in debug API: " . $e->getMessage() . "\n";
    
    $response = [
        'success' => false,
        'error' => 'Debug API fatal error: ' . $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ];
    
    echo json_encode($response);
}
?>
