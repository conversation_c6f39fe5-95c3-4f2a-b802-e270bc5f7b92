/**
 * Sutradhar Engine 2070 - Advanced UI Controller
 * Futuristic interface with neural network animations and real-time processing
 */

class SutradharEngine2070 {
    constructor() {
        this.currentJobId = null;
        this.progressInterval = null;
        this.neuralNetwork = null;
        this.matrixChars = [];
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeNeuralNetwork();
        this.initializeMatrixBackground();
        this.setupFormValidation();
        this.checkSystemStatus();
        
        console.log('🚀 Sutradhar Engine 2070 initialized');
    }

    setupEventListeners() {
        // Form submission
        const form = document.getElementById('generationForm');
        if (form) {
            form.addEventListener('submit', (e) => this.handleGeneration(e));
        }

        // Flow type selection
        document.querySelectorAll('[data-flow]').forEach(card => {
            card.addEventListener('click', () => this.selectFlowType(card));
        });

        // Real-time form updates
        document.querySelectorAll('select, textarea').forEach(input => {
            input.addEventListener('change', () => this.updatePreview());
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
    }

    selectFlowType(selectedCard) {
        // Remove selection from all cards
        document.querySelectorAll('[data-flow]').forEach(card => {
            card.classList.remove('border-cyber-blue', 'bg-cyber-blue/10');
        });

        // Add selection to clicked card
        selectedCard.classList.add('border-cyber-blue', 'bg-cyber-blue/10');
        
        // Store selection
        this.selectedFlow = selectedCard.dataset.flow;
        
        // Animate selection
        gsap.to(selectedCard, {
            scale: 1.05,
            duration: 0.2,
            yoyo: true,
            repeat: 1
        });
    }

    async handleGeneration(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        formData.append('action', 'generate');
        formData.append('flow_type', this.selectedFlow || 'reel');
        formData.append('use_huggingface', 'true');
        formData.append('female_voice', 'true');
        formData.append('video_duration', '30');

        try {
            this.showProgress();
            
            const response = await fetch('api.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            
            if (result.success) {
                this.currentJobId = result.job_id;
                this.startProgressMonitoring();
                this.showNotification('🚀 Neural synthesis initiated!', 'success');
            } else {
                throw new Error(result.error || 'Generation failed');
            }
            
        } catch (error) {
            console.error('Generation error:', error);
            this.showNotification('❌ Synthesis failed: ' + error.message, 'error');
            this.hideProgress();
        }
    }

    showProgress() {
        const progressSection = document.getElementById('progressSection');
        const form = document.getElementById('generationForm');
        
        if (progressSection && form) {
            form.style.display = 'none';
            progressSection.classList.remove('hidden');
            
            // Animate progress section
            gsap.fromTo(progressSection, 
                { opacity: 0, y: 20 },
                { opacity: 1, y: 0, duration: 0.5 }
            );
        }
    }

    hideProgress() {
        const progressSection = document.getElementById('progressSection');
        const form = document.getElementById('generationForm');
        
        if (progressSection && form) {
            progressSection.classList.add('hidden');
            form.style.display = 'block';
        }
        
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }
    }

    startProgressMonitoring() {
        if (!this.currentJobId) return;
        
        this.progressInterval = setInterval(async () => {
            try {
                const response = await fetch(`api.php?action=status&job_id=${this.currentJobId}`);
                const status = await response.json();
                
                this.updateProgress(status);
                
                if (status.status === 'completed') {
                    this.showResults(status);
                    this.hideProgress();
                    clearInterval(this.progressInterval);
                } else if (status.status === 'failed') {
                    this.showNotification('❌ Synthesis failed: ' + status.error, 'error');
                    this.hideProgress();
                    clearInterval(this.progressInterval);
                }
                
            } catch (error) {
                console.error('Status check error:', error);
            }
        }, 2000);
    }

    updateProgress(status) {
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const progressDetails = document.getElementById('progressDetails');
        
        if (progressFill) {
            const progress = status.progress || 0;
            progressFill.style.width = progress + '%';
            
            // Animate progress
            gsap.to(progressFill, {
                width: progress + '%',
                duration: 0.5,
                ease: 'power2.out'
            });
        }
        
        if (progressText) {
            const messages = {
                'parsing': '🧠 Analyzing content with neural networks...',
                'voice_generation': '🎤 Synthesizing female voice with AI...',
                'image_generation': '🎨 Creating visuals with Hugging Face...',
                'video_processing': '🎬 Rendering 30-second video with FFmpeg...',
                'finalizing': '✨ Applying final neural enhancements...'
            };
            
            progressText.textContent = messages[status.stage] || 'Processing...';
        }
        
        if (progressDetails) {
            progressDetails.textContent = status.details || '';
        }
    }

    showResults(status) {
        const resultsSection = document.getElementById('resultsSection');
        const resultsContent = document.getElementById('resultsContent');
        
        if (!resultsSection || !resultsContent) return;
        
        resultsSection.classList.remove('hidden');
        
        // Create results HTML
        const resultsHTML = `
            <div class="grid md:grid-cols-2 gap-6">
                <div class="cyber-card p-6">
                    <h4 class="text-lg font-semibold mb-4 text-cyber-blue">🎬 Generated Video</h4>
                    <video controls class="w-full rounded-lg mb-4" poster="${status.thumbnail || ''}">
                        <source src="download.php?file=${status.video_file}" type="video/mp4">
                        Your browser does not support video playback.
                    </video>
                    <div class="flex space-x-2">
                        <a href="download.php?file=${status.video_file}&download=1" 
                           class="cyber-button px-4 py-2 rounded text-sm">
                            📥 Download Video
                        </a>
                        <button onclick="shareVideo('${status.video_file}')" 
                                class="cyber-button px-4 py-2 rounded text-sm">
                            🔗 Share
                        </button>
                    </div>
                </div>
                
                <div class="cyber-card p-6">
                    <h4 class="text-lg font-semibold mb-4 text-cyber-purple">🎤 Neural Voice</h4>
                    <audio controls class="w-full mb-4">
                        <source src="download.php?file=${status.audio_file}" type="audio/wav">
                        Your browser does not support audio playback.
                    </audio>
                    <div class="space-y-2 text-sm">
                        <p><span class="text-cyber-blue">Duration:</span> ${status.duration || '30s'}</p>
                        <p><span class="text-cyber-blue">Voice:</span> ${status.voice_pack || 'Female AI'}</p>
                        <p><span class="text-cyber-blue">Quality:</span> Neural HD</p>
                    </div>
                    <a href="download.php?file=${status.audio_file}&download=1" 
                       class="cyber-button px-4 py-2 rounded text-sm mt-4 inline-block">
                        📥 Download Audio
                    </a>
                </div>
            </div>
            
            <div class="cyber-card p-6 mt-6">
                <h4 class="text-lg font-semibold mb-4 text-cyber-green">📊 Generation Statistics</h4>
                <div class="grid md:grid-cols-4 gap-4 text-center">
                    <div>
                        <div class="text-2xl font-bold text-cyber-blue">${status.frames_generated || 900}</div>
                        <div class="text-sm text-gray-400">Frames Generated</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-cyber-purple">${status.processing_time || '45s'}</div>
                        <div class="text-sm text-gray-400">Processing Time</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-cyber-pink">${status.file_size || '12.5MB'}</div>
                        <div class="text-sm text-gray-400">Total Size</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-cyber-green">HD</div>
                        <div class="text-sm text-gray-400">Quality</div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-6">
                <button onclick="location.reload()" 
                        class="cyber-button px-8 py-3 rounded-lg">
                    🚀 Generate Another Video
                </button>
            </div>
        `;
        
        resultsContent.innerHTML = resultsHTML;
        
        // Animate results
        gsap.fromTo(resultsSection, 
            { opacity: 0, y: 20 },
            { opacity: 1, y: 0, duration: 0.5 }
        );
        
        this.showNotification('✅ Neural synthesis completed successfully!', 'success');
    }

    initializeNeuralNetwork() {
        const canvas = document.getElementById('neuralNetwork');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        
        const nodes = [];
        const connections = [];
        
        // Create nodes
        for (let i = 0; i < 50; i++) {
            nodes.push({
                x: Math.random() * canvas.width,
                y: Math.random() * canvas.height,
                vx: (Math.random() - 0.5) * 0.5,
                vy: (Math.random() - 0.5) * 0.5,
                radius: Math.random() * 3 + 1
            });
        }
        
        const animate = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Update and draw nodes
            nodes.forEach(node => {
                node.x += node.vx;
                node.y += node.vy;
                
                if (node.x < 0 || node.x > canvas.width) node.vx *= -1;
                if (node.y < 0 || node.y > canvas.height) node.vy *= -1;
                
                ctx.beginPath();
                ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
                ctx.fillStyle = '#00D4FF';
                ctx.fill();
            });
            
            // Draw connections
            nodes.forEach((node, i) => {
                nodes.slice(i + 1).forEach(otherNode => {
                    const distance = Math.sqrt(
                        Math.pow(node.x - otherNode.x, 2) + 
                        Math.pow(node.y - otherNode.y, 2)
                    );
                    
                    if (distance < 100) {
                        ctx.beginPath();
                        ctx.moveTo(node.x, node.y);
                        ctx.lineTo(otherNode.x, otherNode.y);
                        ctx.strokeStyle = `rgba(0, 212, 255, ${1 - distance / 100})`;
                        ctx.stroke();
                    }
                });
            });
            
            requestAnimationFrame(animate);
        };
        
        animate();
        
        // Resize handler
        window.addEventListener('resize', () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        });
    }

    initializeMatrixBackground() {
        const matrixBg = document.getElementById('matrixBg');
        if (!matrixBg) return;
        
        const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
        
        for (let i = 0; i < 50; i++) {
            const char = document.createElement('div');
            char.className = 'matrix-char';
            char.textContent = chars[Math.floor(Math.random() * chars.length)];
            char.style.left = Math.random() * 100 + '%';
            char.style.animationDelay = Math.random() * 20 + 's';
            char.style.animationDuration = (Math.random() * 10 + 10) + 's';
            matrixBg.appendChild(char);
        }
    }

    setupFormValidation() {
        const textarea = document.querySelector('textarea[name="content_text"]');
        if (textarea) {
            textarea.addEventListener('input', (e) => {
                const length = e.target.value.length;
                const maxLength = 500;
                
                if (length > maxLength) {
                    e.target.value = e.target.value.substring(0, maxLength);
                }
                
                // Update character count (if element exists)
                const counter = document.getElementById('charCounter');
                if (counter) {
                    counter.textContent = `${e.target.value.length}/${maxLength}`;
                }
            });
        }
    }

    updatePreview() {
        // Real-time preview updates based on form selections
        const style = document.querySelector('select[name="style"]')?.value;
        const voice = document.querySelector('select[name="voice_pack"]')?.value;
        const background = document.querySelector('select[name="background"]')?.value;
        
        // Update preview indicators or live preview if implemented
        console.log('Preview updated:', { style, voice, background });
    }

    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + Enter to generate
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            const form = document.getElementById('generationForm');
            if (form) {
                form.dispatchEvent(new Event('submit'));
            }
        }
        
        // Escape to cancel
        if (e.key === 'Escape') {
            if (this.progressInterval) {
                this.hideProgress();
            }
        }
    }

    async checkSystemStatus() {
        try {
            const response = await fetch('api.php?action=system_status');
            const status = await response.json();
            
            this.updateSystemIndicators(status);
        } catch (error) {
            console.error('System status check failed:', error);
        }
    }

    updateSystemIndicators(status) {
        const indicators = {
            'huggingface': status.huggingface_status || 'offline',
            'ffmpeg': status.ffmpeg_status || 'offline',
            'voice': status.voice_status || 'offline'
        };
        
        Object.entries(indicators).forEach(([system, state]) => {
            const indicator = document.querySelector(`[data-system="${system}"]`);
            if (indicator) {
                indicator.className = `status-indicator status-${state}`;
            }
        });
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg cyber-card max-w-sm`;
        notification.innerHTML = `
            <div class="flex items-center space-x-3">
                <div class="status-indicator status-${type}"></div>
                <span class="text-white">${message}</span>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        gsap.fromTo(notification, 
            { opacity: 0, x: 100 },
            { opacity: 1, x: 0, duration: 0.3 }
        );
        
        // Remove after 5 seconds
        setTimeout(() => {
            gsap.to(notification, {
                opacity: 0,
                x: 100,
                duration: 0.3,
                onComplete: () => notification.remove()
            });
        }, 5000);
    }
}

// Global functions
function shareVideo(videoFile) {
    if (navigator.share) {
        navigator.share({
            title: 'Check out my AI-generated video!',
            text: 'Created with Sutradhar Engine 2070',
            url: window.location.origin + '/download.php?file=' + videoFile
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.origin + '/download.php?file=' + videoFile);
        app.showNotification('🔗 Video link copied to clipboard!', 'success');
    }
}

// Initialize the application
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new SutradharEngine2070();
});

// Export for global access
window.SutradharEngine2070 = SutradharEngine2070;
