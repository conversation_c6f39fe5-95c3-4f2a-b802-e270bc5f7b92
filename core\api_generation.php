<?php
/**
 * Video Generation API for Sutradhar 2070
 * Handles video generation requests and status tracking
 */

require_once 'api_base.php';

class GenerationAPI extends APIBase {
    
    public function handleRequest() {
        $path = $this->requestPath;
        $method = $this->requestMethod;
        
        // Extract job ID from path if present
        if (preg_match('/^generate\/status\/(.+)$/', $path, $matches)) {
            if ($method === 'GET') $this->getGenerationStatus($matches[1]);
            return;
        }
        
        switch ($path) {
            case 'generate':
                if ($method === 'POST') $this->startGeneration();
                break;
            case 'generate/history':
                if ($method === 'GET') $this->getGenerationHistory();
                break;
            case 'generate/cancel':
                if ($method === 'POST') $this->cancelGeneration();
                break;
            default:
                $this->sendError('Endpoint not found', 404);
        }
    }
    
    /**
     * Start video generation
     */
    private function startGeneration() {
        // Check for demo mode
        $demoMode = $this->requestData['demo_mode'] ?? false;

        if (!$demoMode && $this->db) {
            $this->requireAuth();
        } elseif (!$demoMode && !$this->db) {
            // No database connection, force demo mode
            $demoMode = true;
        }
        
        // Rate limiting for generation requests
        $this->checkRateLimit('generate_' . $this->currentUser['user_id'], 10, 3600);
        
        $this->validateRequired(['mood', 'topic']);
        
        $mood = trim($this->requestData['mood']);
        $topic = trim($this->requestData['topic']);
        $inspiration = $this->requestData['inspiration'] ?? [];
        $customPrompt = trim($this->requestData['custom_prompt'] ?? '');
        $videoQuality = $this->requestData['video_quality'] ?? 'standard';
        $videoLength = intval($this->requestData['video_length'] ?? 30);
        
        // Validate inputs
        $this->validateMood($mood);
        $this->validateTopic($topic);
        $this->validateVideoQuality($videoQuality);
        $this->validateVideoLength($videoLength);
        
        if ($demoMode) {
            // Demo mode - skip credit checks and user validation
            $creditsRequired = 0;
            $jobId = $this->createDemoGenerationJob($mood, $topic, $inspiration, $customPrompt, $videoQuality, $videoLength);
        } else {
            // Check user credits
            $creditsRequired = $this->calculateCreditsRequired($videoQuality, $videoLength);
            $userCredits = $this->getUserCredits();

            if ($userCredits < $creditsRequired) {
                $this->sendError("Insufficient credits. Required: $creditsRequired, Available: $userCredits", 402);
            }

            // Check for duplicate recent requests
            $this->checkDuplicateRequest($mood, $topic);

            // Create generation job
            $jobId = $this->createGenerationJob($mood, $topic, $inspiration, $customPrompt, $videoQuality, $videoLength, $creditsRequired);
        }
        
        // Trigger background processing
        $this->triggerBackgroundProcessing($jobId);
        
        $this->sendSuccess([
            'job_id' => $jobId,
            'estimated_time' => $this->getEstimatedTime($videoQuality, $videoLength),
            'credits_required' => $creditsRequired,
            'status' => 'pending'
        ], 'Video generation started successfully', 201);
    }
    
    /**
     * Get generation status
     */
    private function getGenerationStatus($jobId) {
        // Check if it's a demo job
        if (strpos($jobId, 'demo_') === 0) {
            $this->getDemoJobStatus($jobId);
            return;
        }

        if (!$this->db) {
            $this->sendError('Database not available', 503);
            return;
        }

        $this->requireAuth();

        $job = $this->db->selectOne('generation_jobs', [
            'job_id' => $jobId,
            'user_id' => $this->currentUser['user_id']
        ]);

        if (!$job) {
            $this->sendError('Generation job not found', 404);
        }
        
        $response = [
            'job_id' => $job['job_id'],
            'status' => $job['status'],
            'progress' => intval($job['progress']),
            'message' => $job['message'] ?? $this->getStatusMessage($job['status']),
            'created_at' => $job['created_at'],
            'updated_at' => $job['updated_at']
        ];
        
        // Add completion data if available
        if ($job['status'] === 'completed') {
            $response['video_url'] = $job['video_url'];
            $response['thumbnail_url'] = $job['thumbnail_url'];
            $response['processing_time'] = $job['processing_time'];
            $response['completed_at'] = $job['completed_at'];
            
            if ($job['output_data']) {
                $response['output_data'] = json_decode($job['output_data'], true);
            }
        } elseif ($job['status'] === 'failed') {
            $response['error_message'] = $job['error_message'];
        }
        
        // Calculate estimated remaining time
        if (in_array($job['status'], ['pending', 'analyzing', 'generating', 'processing', 'finalizing'])) {
            $response['estimated_remaining'] = $this->calculateRemainingTime($job);
        }
        
        $this->sendSuccess($response);
    }
    
    /**
     * Get generation history
     */
    private function getGenerationHistory() {
        $this->requireAuth();
        
        $page = intval($this->requestData['page'] ?? 1);
        $limit = min(intval($this->requestData['limit'] ?? 20), 100);
        $status = $this->requestData['status'] ?? null;
        
        $offset = ($page - 1) * $limit;
        
        $conditions = ['user_id' => $this->currentUser['user_id']];
        if ($status) {
            $conditions['status'] = $status;
        }
        
        $jobs = $this->db->select('generation_jobs', $conditions, 
            "ORDER BY created_at DESC LIMIT $limit OFFSET $offset");
        
        $total = $this->db->count('generation_jobs', $conditions);
        
        // Format response
        $formattedJobs = array_map(function($job) {
            return [
                'job_id' => $job['job_id'],
                'mood' => $job['mood'],
                'topic' => $job['topic'],
                'status' => $job['status'],
                'progress' => intval($job['progress']),
                'video_quality' => $job['video_quality'],
                'video_length' => intval($job['video_length']),
                'credits_consumed' => intval($job['credits_consumed']),
                'video_url' => $job['video_url'],
                'thumbnail_url' => $job['thumbnail_url'],
                'created_at' => $job['created_at'],
                'completed_at' => $job['completed_at']
            ];
        }, $jobs);
        
        $this->sendSuccess([
            'jobs' => $formattedJobs,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]);
    }
    
    /**
     * Cancel generation
     */
    private function cancelGeneration() {
        $this->requireAuth();
        
        $this->validateRequired(['job_id']);
        
        $jobId = $this->requestData['job_id'];
        
        $job = $this->db->selectOne('generation_jobs', [
            'job_id' => $jobId,
            'user_id' => $this->currentUser['user_id']
        ]);
        
        if (!$job) {
            $this->sendError('Generation job not found', 404);
        }
        
        if (!in_array($job['status'], ['pending', 'analyzing', 'generating', 'processing'])) {
            $this->sendError('Cannot cancel job in current status: ' . $job['status'], 400);
        }
        
        // Update job status
        $this->db->update('generation_jobs', [
            'status' => 'cancelled',
            'message' => 'Cancelled by user',
            'updated_at' => date('Y-m-d H:i:s')
        ], ['job_id' => $jobId]);
        
        // Refund credits if not yet consumed
        if ($job['credits_consumed'] > 0) {
            $this->refundCredits($this->currentUser['user_id'], $job['credits_consumed'], $jobId);
        }
        
        $this->sendSuccess(null, 'Generation cancelled successfully');
    }
    
    /**
     * Validate mood
     */
    private function validateMood($mood) {
        $validMoods = [
            'euphoric', 'serene', 'energetic', 'mysterious', 'melancholic', 'adventurous',
            'romantic', 'dramatic', 'playful', 'nostalgic', 'futuristic', 'dreamy',
            'intense', 'peaceful', 'vibrant', 'dark', 'uplifting', 'contemplative',
            'whimsical', 'bold', 'gentle', 'powerful', 'ethereal', 'grounded',
            'electric', 'organic', 'minimalist', 'maximalist', 'retro', 'modern'
        ];
        
        if (!in_array($mood, $validMoods)) {
            $this->sendError('Invalid mood. Must be one of: ' . implode(', ', $validMoods), 400);
        }
    }
    
    /**
     * Validate topic
     */
    private function validateTopic($topic) {
        $validTopics = [
            'nature_wildlife', 'urban_city', 'abstract_art', 'technology', 'food_cooking',
            'travel_adventure', 'sports_fitness', 'music_dance', 'fashion_beauty',
            'science_space', 'business_office', 'education', 'health_wellness',
            'entertainment', 'automotive', 'architecture', 'lifestyle', 'animals'
        ];
        
        if (!in_array($topic, $validTopics)) {
            $this->sendError('Invalid topic. Must be one of: ' . implode(', ', $validTopics), 400);
        }
    }
    
    /**
     * Validate video quality
     */
    private function validateVideoQuality($quality) {
        $validQualities = ['standard', 'hd', '4k'];
        
        if (!in_array($quality, $validQualities)) {
            $this->sendError('Invalid video quality. Must be one of: ' . implode(', ', $validQualities), 400);
        }
        
        // Check user subscription for quality access
        $subscription = $this->getUserSubscription();
        $maxQuality = $subscription['max_video_quality'] ?? 'standard';
        
        $qualityLevels = ['standard' => 1, 'hd' => 2, '4k' => 3];
        
        if ($qualityLevels[$quality] > $qualityLevels[$maxQuality]) {
            $this->sendError("Your subscription plan does not support $quality quality. Maximum allowed: $maxQuality", 403);
        }
    }
    
    /**
     * Validate video length
     */
    private function validateVideoLength($length) {
        if ($length < 5 || $length > 60) {
            $this->sendError('Video length must be between 5 and 60 seconds', 400);
        }
    }
    
    /**
     * Calculate credits required
     */
    private function calculateCreditsRequired($quality, $length) {
        $baseCredits = [
            'standard' => 10,
            'hd' => 15,
            '4k' => 25
        ];
        
        $credits = $baseCredits[$quality];
        
        // Add extra credits for longer videos
        if ($length > 30) {
            $credits += ceil(($length - 30) / 10) * 5;
        }
        
        return $credits;
    }
    
    /**
     * Get user credits
     */
    private function getUserCredits() {
        $balance = $this->db->selectOne('user_credit_balances', [
            'user_id' => $this->currentUser['user_id']
        ]);
        
        return $balance['available_credits'] ?? 0;
    }
    
    /**
     * Get user subscription
     */
    private function getUserSubscription() {
        $subscription = $this->db->selectOne('user_subscriptions', [
            'user_id' => $this->currentUser['user_id'],
            'status' => 'active'
        ]);
        
        if ($subscription) {
            $plan = $this->db->selectOne('subscription_plans', [
                'plan_id' => $subscription['plan_id']
            ]);
            
            return array_merge($subscription, $plan);
        }
        
        // Default to free plan
        return $this->db->selectOne('subscription_plans', ['plan_id' => 'free']);
    }
    
    /**
     * Check for duplicate requests
     */
    private function checkDuplicateRequest($mood, $topic) {
        $recentJob = $this->db->selectOne('generation_jobs', [
            'user_id' => $this->currentUser['user_id'],
            'mood' => $mood,
            'topic' => $topic,
            'created_at >' => date('Y-m-d H:i:s', time() - 300) // 5 minutes
        ]);
        
        if ($recentJob && in_array($recentJob['status'], ['pending', 'analyzing', 'generating', 'processing'])) {
            $this->sendError('A similar generation request is already in progress. Please wait for it to complete.', 409);
        }
    }
    
    /**
     * Create generation job
     */
    private function createGenerationJob($mood, $topic, $inspiration, $customPrompt, $quality, $length, $credits) {
        $jobId = 'sutradhar_' . uniqid() . '_' . time();
        
        $jobData = [
            'job_id' => $jobId,
            'user_id' => $this->currentUser['user_id'],
            'mood' => $mood,
            'topic' => $topic,
            'inspiration' => json_encode($inspiration),
            'custom_prompt' => $customPrompt,
            'video_quality' => $quality,
            'video_length' => $length,
            'status' => 'pending',
            'progress' => 0,
            'credits_consumed' => $credits,
            'message' => 'Generation request received'
        ];
        
        $this->db->insert('generation_jobs', $jobData);
        
        // Consume credits immediately
        $this->consumeCredits($credits, $jobId);
        
        return $jobId;
    }

    /**
     * Create demo generation job (no authentication required)
     */
    private function createDemoGenerationJob($mood, $topic, $inspiration, $customPrompt, $quality, $length) {
        $jobId = 'demo_' . uniqid() . '_' . time();

        $jobData = [
            'job_id' => $jobId,
            'user_id' => 'demo_user',
            'mood' => $mood,
            'topic' => $topic,
            'inspiration' => json_encode($inspiration),
            'custom_prompt' => $customPrompt,
            'video_quality' => $quality,
            'video_length' => $length,
            'status' => 'pending',
            'progress' => 0,
            'credits_consumed' => 0,
            'message' => 'Demo generation request received'
        ];

        // For demo mode, we'll simulate the job without database
        // Store in session for tracking
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        $_SESSION['demo_jobs'][$jobId] = $jobData;

        // Trigger demo processing
        $this->triggerDemoProcessing($jobId);

        return $jobId;
    }

    /**
     * Trigger demo processing
     */
    private function triggerDemoProcessing($jobId) {
        // For demo, we'll just set a timer to complete the job
        // In a real implementation, this would trigger background processing

        // Store completion time
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        $_SESSION['demo_jobs'][$jobId]['demo_complete_time'] = time() + 10; // Complete in 10 seconds
    }

    /**
     * Get demo job status
     */
    private function getDemoJobStatus($jobId) {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        if (!isset($_SESSION['demo_jobs'][$jobId])) {
            $this->sendError('Demo job not found', 404);
        }

        $job = $_SESSION['demo_jobs'][$jobId];
        $currentTime = time();
        $completeTime = $job['demo_complete_time'] ?? ($currentTime + 10);

        // Calculate progress based on time elapsed
        $startTime = $completeTime - 10; // 10 seconds total
        $elapsed = $currentTime - $startTime;
        $progress = min(100, max(0, ($elapsed / 10) * 100));

        if ($progress >= 100) {
            // Job completed
            $response = [
                'job_id' => $jobId,
                'status' => 'completed',
                'progress' => 100,
                'message' => 'Demo video generation completed!',
                'video_url' => 'final_test_video.mp4',
                'thumbnail_url' => 'final_test_video_thumbnail.jpg',
                'processing_time' => 10,
                'completed_at' => date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s', $startTime),
                'updated_at' => date('Y-m-d H:i:s')
            ];
        } else {
            // Job in progress
            $stages = [
                [0, 20, 'Analyzing mood and topic...'],
                [20, 40, 'Generating AI prompts...'],
                [40, 70, 'Creating video content...'],
                [70, 90, 'Adding visual effects...'],
                [90, 100, 'Finalizing video...']
            ];

            $message = 'Processing...';
            foreach ($stages as $stage) {
                if ($progress >= $stage[0] && $progress < $stage[1]) {
                    $message = $stage[2];
                    break;
                }
            }

            $response = [
                'job_id' => $jobId,
                'status' => 'generating',
                'progress' => round($progress),
                'message' => $message,
                'created_at' => date('Y-m-d H:i:s', $startTime),
                'updated_at' => date('Y-m-d H:i:s')
            ];
        }

        $this->sendSuccess($response);
    }

    /**
     * Consume user credits
     */
    private function consumeCredits($amount, $jobId) {
        $this->db->update('user_credit_balances', [
            'used_credits' => 'used_credits + ' . $amount
        ], ['user_id' => $this->currentUser['user_id']]);
        
        // Record transaction
        $this->db->insert('credit_transactions', [
            'transaction_id' => 'txn_' . uniqid(),
            'user_id' => $this->currentUser['user_id'],
            'type' => 'consumption',
            'credits_amount' => -$amount,
            'description' => 'Video generation',
            'reference_id' => $jobId,
            'reference_type' => 'generation'
        ]);
    }
    
    /**
     * Refund credits
     */
    private function refundCredits($userId, $amount, $jobId) {
        $this->db->update('user_credit_balances', [
            'used_credits' => 'used_credits - ' . $amount
        ], ['user_id' => $userId]);
        
        // Record refund transaction
        $this->db->insert('credit_transactions', [
            'transaction_id' => 'txn_' . uniqid(),
            'user_id' => $userId,
            'type' => 'refund',
            'credits_amount' => $amount,
            'description' => 'Generation cancellation refund',
            'reference_id' => $jobId,
            'reference_type' => 'generation'
        ]);
    }
    
    /**
     * Trigger background processing
     */
    private function triggerBackgroundProcessing($jobId) {
        // Execute background processor asynchronously
        if (PHP_OS_FAMILY === 'Windows') {
            $command = "php ../core/background_job_processor.php > nul 2>&1 &";
            pclose(popen("start /B " . $command, "r"));
        } else {
            $command = "php ../core/background_job_processor.php > /dev/null 2>&1 &";
            exec($command);
        }
    }
    
    /**
     * Get estimated processing time
     */
    private function getEstimatedTime($quality, $length) {
        $baseTime = [
            'standard' => 120, // 2 minutes
            'hd' => 180,       // 3 minutes
            '4k' => 300        // 5 minutes
        ];
        
        $time = $baseTime[$quality];
        
        // Add time for longer videos
        if ($length > 30) {
            $time += ($length - 30) * 2;
        }
        
        return $time;
    }
    
    /**
     * Calculate remaining time
     */
    private function calculateRemainingTime($job) {
        $elapsed = time() - strtotime($job['created_at']);
        $estimated = $this->getEstimatedTime($job['video_quality'], $job['video_length']);
        $remaining = max(0, $estimated - $elapsed);
        
        return $remaining;
    }
    
    /**
     * Get status message
     */
    private function getStatusMessage($status) {
        $messages = [
            'pending' => 'Generation request received and queued',
            'analyzing' => 'Analyzing mood and generating prompts...',
            'generating' => 'Creating your video with AI...',
            'processing' => 'Adding final touches and effects...',
            'finalizing' => 'Almost done! Preparing your video...',
            'completed' => 'Video generation completed successfully',
            'failed' => 'Video generation failed',
            'cancelled' => 'Generation was cancelled'
        ];
        
        return $messages[$status] ?? 'Processing...';
    }
}
?>
