<?php
/**
 * De<PERSON>ript for Sutradhar Engine
 * Tests core functionality without external dependencies
 */

require_once 'core/story_parser.php';
require_once 'core/template_engine.php';

echo "🎭 Sutradhar Engine Demo\n";
echo "========================\n\n";

// Test 1: Story Parser
echo "📖 Testing Story Parser...\n";
try {
    $parser = new StoryParser();
    
    $sampleText = "Once upon a time, there was a clever crow who found a piece of cheese. A cunning fox saw this and decided to trick the crow with flattery.";
    
    $parsed = $parser->parseContent($sampleText, 'reel', 'funny', 'babu_rao');
    
    echo "✅ Story parsed successfully!\n";
    echo "   - Flow type: " . $parsed['flow_type'] . "\n";
    echo "   - Segments: " . count($parsed['segments']) . "\n";
    echo "   - Estimated duration: " . $parsed['metadata']['estimated_duration'] . " seconds\n";
    
    foreach ($parsed['segments'] as $i => $segment) {
        echo "   - Segment " . ($i + 1) . ": " . substr($segment['text'], 0, 50) . "...\n";
    }
    
} catch (Exception $e) {
    echo "❌ Story Parser failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Template Engine
echo "🎪 Testing Template Engine...\n";
try {
    $templateEngine = new TemplateEngine();
    
    $templates = $templateEngine->getTemplateList();
    echo "✅ Template Engine working!\n";
    echo "   - Available templates: " . count($templates) . "\n";
    
    foreach ($templates as $template) {
        echo "   - " . $template['name'] . " (" . $template['flow_type'] . ")\n";
    }
    
    // Test template generation
    $template = $templateEngine->getTemplate('panchtantra_wisdom');
    echo "   - Sample template content: " . substr($template['content']['text'], 0, 100) . "...\n";
    
} catch (Exception $e) {
    echo "❌ Template Engine failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Configuration
echo "⚙️  Testing Configuration...\n";
try {
    $configPath = __DIR__ . '/config/settings.json';
    if (file_exists($configPath)) {
        $config = json_decode(file_get_contents($configPath), true);
        echo "✅ Configuration loaded!\n";
        echo "   - App name: " . $config['app']['name'] . "\n";
        echo "   - Voice engine: " . $config['voice']['engine'] . "\n";
        echo "   - Voice packs: " . count($config['voice_packs']) . "\n";
        echo "   - Styles: " . count($config['styles']) . "\n";
    } else {
        echo "❌ Configuration file not found\n";
    }
} catch (Exception $e) {
    echo "❌ Configuration test failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Directory Structure
echo "📁 Testing Directory Structure...\n";
$requiredDirs = [
    'public',
    'core', 
    'flows',
    'assets/audio',
    'assets/voices',
    'assets/backgrounds',
    'data/stories',
    'data/output_history',
    'temp',
    'config'
];

$missingDirs = [];
foreach ($requiredDirs as $dir) {
    if (!is_dir(__DIR__ . '/' . $dir)) {
        $missingDirs[] = $dir;
    }
}

if (empty($missingDirs)) {
    echo "✅ All required directories exist!\n";
} else {
    echo "❌ Missing directories: " . implode(', ', $missingDirs) . "\n";
}

echo "\n";

// Test 5: File Permissions
echo "🔐 Testing File Permissions...\n";
$writableDirs = ['temp', 'data', 'assets'];
$permissionIssues = [];

foreach ($writableDirs as $dir) {
    $fullPath = __DIR__ . '/' . $dir;
    if (is_dir($fullPath) && !is_writable($fullPath)) {
        $permissionIssues[] = $dir;
    }
}

if (empty($permissionIssues)) {
    echo "✅ All directories are writable!\n";
} else {
    echo "❌ Permission issues with: " . implode(', ', $permissionIssues) . "\n";
    echo "   Run: chmod -R 777 " . implode(' ', $permissionIssues) . "\n";
}

echo "\n";

// Test 6: External Dependencies
echo "🔧 Testing External Dependencies...\n";

// Test PHP version
$phpVersion = PHP_VERSION;
echo "   - PHP version: $phpVersion ";
if (version_compare($phpVersion, '8.1.0', '>=')) {
    echo "✅\n";
} else {
    echo "❌ (requires 8.1+)\n";
}

// Test PHP extensions
$requiredExtensions = ['json', 'fileinfo', 'mbstring'];
foreach ($requiredExtensions as $ext) {
    echo "   - PHP extension '$ext': ";
    if (extension_loaded($ext)) {
        echo "✅\n";
    } else {
        echo "❌\n";
    }
}

// Test FFmpeg
echo "   - FFmpeg: ";
$ffmpegCheck = shell_exec('ffmpeg -version 2>&1');
if ($ffmpegCheck && strpos($ffmpegCheck, 'ffmpeg version') !== false) {
    echo "✅\n";
} else {
    echo "❌ (not found in PATH)\n";
}

// Test Python
echo "   - Python 3: ";
$pythonCheck = shell_exec('python3 --version 2>&1');
if ($pythonCheck && strpos($pythonCheck, 'Python 3') !== false) {
    echo "✅\n";
} else {
    echo "❌ (not found in PATH)\n";
}

echo "\n";

// Test 7: Sample Content
echo "📚 Testing Sample Content...\n";
$sampleFiles = [
    'data/stories/sample_panchtantra.txt',
    'data/stories/sample_bollywood_startup.txt', 
    'data/stories/sample_dadi_advice.txt'
];

$existingFiles = 0;
foreach ($sampleFiles as $file) {
    if (file_exists(__DIR__ . '/' . $file)) {
        $existingFiles++;
    }
}

echo "✅ Sample story files: $existingFiles/" . count($sampleFiles) . "\n";

echo "\n";

// Summary
echo "📊 Demo Summary\n";
echo "===============\n";

$allGood = true;

// Check critical components
if (!class_exists('StoryParser')) {
    echo "❌ StoryParser class not available\n";
    $allGood = false;
}

if (!class_exists('TemplateEngine')) {
    echo "❌ TemplateEngine class not available\n";
    $allGood = false;
}

if (!file_exists(__DIR__ . '/config/settings.json')) {
    echo "❌ Configuration file missing\n";
    $allGood = false;
}

if (!empty($missingDirs)) {
    echo "❌ Missing directories\n";
    $allGood = false;
}

if ($allGood) {
    echo "🎉 All core components are working!\n";
    echo "\nNext steps:\n";
    echo "1. Install TTS engine (Bark, Coqui, or eSpeak)\n";
    echo "2. Add voice model files to assets/voices/\n";
    echo "3. Add background music to assets/audio/music/\n";
    echo "4. Start web server: php -S localhost:8000 -t public\n";
    echo "5. Open http://localhost:8000 in your browser\n";
} else {
    echo "⚠️  Some components need attention. Check the issues above.\n";
}

echo "\n🎭 Demo completed!\n";
?>
