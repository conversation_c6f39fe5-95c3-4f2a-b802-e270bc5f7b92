<?php
/**
 * Tools API Handler
 * Handles all tools-related API endpoints
 */

require_once 'api_base.php';
require_once 'tools/ToolsManager.php';

class ToolsAPI extends APIBase {
    private $toolsManager;
    
    public function __construct() {
        parent::__construct();
        $this->toolsManager = new ToolsManager();
    }
    
    public function handleRequest() {
        $path = $this->requestPath;
        $method = $this->requestMethod;
        
        switch ($path) {
            case 'tools/list':
                if ($method === 'GET') $this->getToolsList();
                break;
                
            case 'tools/execute':
                if ($method === 'POST') $this->executeTool();
                break;
                
            case 'tools/status':
                if ($method === 'GET') $this->getOperationStatus();
                break;
                
            case 'tools/favorites':
                if ($method === 'GET') $this->getUserFavorites();
                break;
                
            case 'tools/favorites/add':
                if ($method === 'POST') $this->addToFavorites();
                break;
                
            case 'tools/favorites/remove':
                if ($method === 'POST') $this->removeFromFavorites();
                break;
                
            case 'tools/stats':
                if ($method === 'GET') $this->getUserStats();
                break;
                
            case 'tools/download':
                if ($method === 'GET') $this->downloadFile();
                break;
                
            case 'tools/analytics':
                if ($method === 'GET') $this->getAnalytics();
                break;
                
            default:
                $this->sendError('Tools endpoint not found', 404);
        }
    }
    
    /**
     * Get list of available tools
     */
    private function getToolsList() {
        try {
            $userId = null;
            
            // Get user ID if authenticated
            if ($this->isAuthenticated()) {
                $userId = $this->currentUser['user_id'];
            }
            
            $tools = $this->toolsManager->getAvailableTools($userId);
            
            $this->sendSuccess([
                'categories' => $tools,
                'total_tools' => array_sum(array_map(function($cat) {
                    return count($cat['tools']);
                }, $tools))
            ]);
            
        } catch (Exception $e) {
            error_log("Tools list error: " . $e->getMessage());
            $this->sendError('Failed to load tools', 500);
        }
    }
    
    /**
     * Execute a tool operation
     */
    private function executeTool() {
        $this->requireAuth();
        
        try {
            $toolId = $this->requestData['tool_id'] ?? '';
            $options = $this->requestData['options'] ?? [];
            
            if (empty($toolId)) {
                $this->sendError('Tool ID is required');
                return;
            }
            
            // Handle file uploads
            $files = [];
            if (!empty($_FILES)) {
                foreach ($_FILES as $key => $fileArray) {
                    if (is_array($fileArray['name'])) {
                        // Multiple files
                        for ($i = 0; $i < count($fileArray['name']); $i++) {
                            if ($fileArray['error'][$i] === UPLOAD_ERR_OK) {
                                $files[] = [
                                    'name' => $fileArray['name'][$i],
                                    'type' => $fileArray['type'][$i],
                                    'tmp_name' => $fileArray['tmp_name'][$i],
                                    'size' => $fileArray['size'][$i],
                                    'error' => $fileArray['error'][$i]
                                ];
                            }
                        }
                    } else {
                        // Single file
                        if ($fileArray['error'] === UPLOAD_ERR_OK) {
                            $files[] = $fileArray;
                        }
                    }
                }
            }
            
            $userId = $this->currentUser['user_id'];
            $result = $this->toolsManager->executeTool($toolId, $files, $options, $userId);
            
            $this->sendSuccess($result);
            
        } catch (InsufficientCreditsException $e) {
            $this->sendError($e->getMessage(), 402); // Payment Required
        } catch (RateLimitException $e) {
            $this->sendError($e->getMessage(), 429); // Too Many Requests
        } catch (SubscriptionRequiredException $e) {
            $this->sendError($e->getMessage(), 403); // Forbidden
        } catch (ToolException $e) {
            $this->sendError($e->getMessage(), 400);
        } catch (Exception $e) {
            error_log("Tool execution error: " . $e->getMessage());
            $this->sendError('Tool execution failed', 500);
        }
    }
    
    /**
     * Get operation status
     */
    private function getOperationStatus() {
        $this->requireAuth();
        
        try {
            $operationId = $this->requestData['operation_id'] ?? '';
            
            if (empty($operationId)) {
                $this->sendError('Operation ID is required');
                return;
            }
            
            $userId = $this->currentUser['user_id'];
            $status = $this->toolsManager->getOperationStatus($operationId, $userId);
            
            $this->sendSuccess($status);
            
        } catch (Exception $e) {
            error_log("Operation status error: " . $e->getMessage());
            $this->sendError('Failed to get operation status', 500);
        }
    }
    
    /**
     * Get user's favorite tools
     */
    private function getUserFavorites() {
        $this->requireAuth();
        
        try {
            $userId = $this->currentUser['user_id'];
            $favorites = $this->toolsManager->getUserFavorites($userId);
            
            $this->sendSuccess($favorites);
            
        } catch (Exception $e) {
            error_log("User favorites error: " . $e->getMessage());
            $this->sendError('Failed to get favorites', 500);
        }
    }
    
    /**
     * Add tool to favorites
     */
    private function addToFavorites() {
        $this->requireAuth();
        
        try {
            $toolId = $this->requestData['tool_id'] ?? '';
            
            if (empty($toolId)) {
                $this->sendError('Tool ID is required');
                return;
            }
            
            $userId = $this->currentUser['user_id'];
            $result = $this->toolsManager->addToFavorites($userId, $toolId);
            
            if ($result) {
                $this->sendSuccess(['message' => 'Added to favorites']);
            } else {
                $this->sendError('Tool already in favorites');
            }
            
        } catch (Exception $e) {
            error_log("Add to favorites error: " . $e->getMessage());
            $this->sendError('Failed to add to favorites', 500);
        }
    }
    
    /**
     * Remove tool from favorites
     */
    private function removeFromFavorites() {
        $this->requireAuth();
        
        try {
            $toolId = $this->requestData['tool_id'] ?? '';
            
            if (empty($toolId)) {
                $this->sendError('Tool ID is required');
                return;
            }
            
            $userId = $this->currentUser['user_id'];
            $this->toolsManager->removeFromFavorites($userId, $toolId);
            
            $this->sendSuccess(['message' => 'Removed from favorites']);
            
        } catch (Exception $e) {
            error_log("Remove from favorites error: " . $e->getMessage());
            $this->sendError('Failed to remove from favorites', 500);
        }
    }
    
    /**
     * Get user's tool usage statistics
     */
    private function getUserStats() {
        $this->requireAuth();
        
        try {
            $period = $this->requestData['period'] ?? '30 days';
            $userId = $this->currentUser['user_id'];
            
            $stats = $this->toolsManager->getUserStats($userId, $period);
            
            $this->sendSuccess([
                'period' => $period,
                'stats' => $stats
            ]);
            
        } catch (Exception $e) {
            error_log("User stats error: " . $e->getMessage());
            $this->sendError('Failed to get user stats', 500);
        }
    }
    
    /**
     * Download processed file
     */
    private function downloadFile() {
        $this->requireAuth();
        
        try {
            $fileId = $this->requestData['file_id'] ?? '';
            
            if (empty($fileId)) {
                $this->sendError('File ID is required');
                return;
            }
            
            $userId = $this->currentUser['user_id'];
            $fileInfo = $this->toolsManager->downloadFile($fileId, $userId);
            
            // Set headers for file download
            header('Content-Type: ' . $fileInfo['mime_type']);
            header('Content-Disposition: attachment; filename="' . $fileInfo['original_name'] . '"');
            header('Content-Length: ' . $fileInfo['file_size']);
            header('Cache-Control: no-cache, must-revalidate');
            
            // Output file content
            readfile($fileInfo['file_path']);
            exit;
            
        } catch (Exception $e) {
            error_log("File download error: " . $e->getMessage());
            $this->sendError('File not found or access denied', 404);
        }
    }
    
    /**
     * Get tools analytics (admin only)
     */
    private function getAnalytics() {
        $this->requireAuth();
        
        // Check if user is admin
        if (!$this->isAdmin()) {
            $this->sendError('Admin access required', 403);
            return;
        }
        
        try {
            $period = $this->requestData['period'] ?? '30 days';
            $analytics = $this->toolsManager->getToolAnalytics($period);
            
            $this->sendSuccess([
                'period' => $period,
                'analytics' => $analytics
            ]);
            
        } catch (Exception $e) {
            error_log("Tools analytics error: " . $e->getMessage());
            $this->sendError('Failed to get analytics', 500);
        }
    }
    
    /**
     * Check if current user is admin
     */
    private function isAdmin() {
        return isset($this->currentUser['role']) && $this->currentUser['role'] === 'admin';
    }
}
?>
