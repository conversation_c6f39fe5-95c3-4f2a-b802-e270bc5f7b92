# 🎭 Sutradhar Engine

**Open-source, self-hosted voice AI platform for Indian cultural content**

Transform Indian stories, memes, and cultural content into engaging audio/video content using only local, open-source tools. No external APIs, no paid services - complete creative freedom!

## ✨ Features

- **🎯 Logic-Driven Content Creation**: No prompt writing needed - just select flow type and preferences
- **🎭 Authentic Indian Voices**: 5 distinct voice packs (<PERSON>, Bollywood Villain, Dadi, Gym Bro, News Anchor)
- **📱 Multiple Content Types**: Reels, Audio Stories, Meme Rants
- **🎨 Cultural Intelligence**: Built-in Hinglish support, Bollywood references, desi humor
- **🔧 Fully Self-Hosted**: No external dependencies, complete data privacy
- **🎵 Smart Audio Mixing**: Automatic background music and sound effects
- **📹 Video Generation**: Creates 9:16 reels with subtitles
- **🎪 Template System**: Pre-built flows for Panchtantra stories, startup pitches, life advice

## 🏗️ Architecture

```
sutradhar-engine/
├── public/           # Frontend (HTML, CSS, JS)
├── core/            # Backend modules (PHP)
├── flows/           # Content flow templates (JSON)
├── assets/          # Audio, video, voice models
├── data/            # Stories, output history
├── config/          # System configuration
└── generate.php     # Main generation endpoint
```

## 🚀 Quick Start

### Prerequisites

- **PHP 8.1+** with extensions: `json`, `fileinfo`, `mbstring`
- **FFmpeg** for audio/video processing
- **Web server** (Apache/Nginx) or PHP built-in server
- **Python 3.8+** (for TTS engines)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/sutradhar-engine.git
   cd sutradhar-engine
   ```

2. **Set up permissions**
   ```bash
   chmod -R 755 .
   chmod -R 777 temp/ data/ assets/
   ```

3. **Install TTS Engine (Choose one)**

   **Option A: Bark TTS (Recommended)**
   ```bash
   pip install bark-tts
   ```

   **Option B: Coqui TTS**
   ```bash
   pip install coqui-tts
   ```

   **Option C: eSpeak (Fallback)**
   ```bash
   # Ubuntu/Debian
   sudo apt-get install espeak espeak-data
   
   # macOS
   brew install espeak
   
   # Windows
   # Download from http://espeak.sourceforge.net/download.html
   ```

4. **Install FFmpeg**
   ```bash
   # Ubuntu/Debian
   sudo apt-get install ffmpeg
   
   # macOS
   brew install ffmpeg
   
   # Windows
   # Download from https://ffmpeg.org/download.html
   ```

5. **Start the server**
   ```bash
   # Using PHP built-in server
   php -S localhost:8000 -t public
   
   # Or configure your web server to point to the public/ directory
   ```

6. **Open in browser**
   ```
   http://localhost:8000
   ```

## 🎛️ Configuration

### Voice Engine Setup

Edit `config/settings.json` to configure your TTS engine:

```json
{
  "voice": {
    "engine": "bark",  // "bark", "coqui", or "espeak"
    "sample_rate": 44100,
    "cache_enabled": true
  }
}
```

### Voice Models

Place your voice model files in `assets/voices/`:
- `babu_rao.pth` - Hera Pheri character voice
- `villain.pth` - Bollywood antagonist voice
- `dadi.pth` - Grandmother voice
- `gym_bro.pth` - Fitness enthusiast voice
- `news_anchor.pth` - Professional news voice

### Background Assets

Add your audio/video assets:
- `assets/audio/music/` - Background music files
- `assets/audio/effects/` - Sound effects
- `assets/backgrounds/` - Video/image backgrounds

## 🎯 Usage

### Creating Content

1. **Select Content Type**
   - 📱 **Reel**: Short video content (15-60 seconds)
   - 🎧 **Audio Story**: Narrated stories (2-5 minutes)
   - 😂 **Meme Rant**: Humorous commentary (30-90 seconds)

2. **Choose Style**
   - 😄 **Funny**: Sarcastic and humorous
   - 🇮🇳 **Desi**: Cultural and relatable
   - 💝 **Emotional**: Heartfelt and touching
   - 🎬 **Bollywood**: Dramatic and filmy

3. **Select Voice Pack**
   - Choose from 5 authentic Indian voice personalities

4. **Pick Background**
   - Home, Office, Nature, City, or Traditional settings

5. **Provide Content**
   - Upload text/JSON files
   - Use pre-built templates
   - Enter custom text

6. **Generate & Download**
   - Watch real-time progress
   - Download MP4 videos, MP3 audio, and SRT subtitles

### API Usage

**Start Generation**
```bash
curl -X POST http://localhost:8000/generate.php \
  -F "flow_type=reel" \
  -F "style=funny" \
  -F "voice_pack=babu_rao" \
  -F "background=home" \
  -F "content_source=text" \
  -F "content_text=Your story here"
```

**Check Status**
```bash
curl "http://localhost:8000/generate.php?action=status&job_id=YOUR_JOB_ID"
```

## 🎪 Templates

### Built-in Templates

1. **Panchtantra Wisdom**: Traditional moral stories with modern relevance
2. **Bollywood Startup Pitch**: Business advice with movie references  
3. **Dadi's Life Advice**: Traditional wisdom from grandmother

### Custom Templates

Create your own flow templates in `flows/` directory:

```json
{
  "flow_info": {
    "name": "Your Template",
    "type": "reel",
    "duration_target": "30-60 seconds"
  },
  "structure": {
    "hook": { "duration": "5 seconds" },
    "content": { "segments": 3 },
    "call_to_action": { "duration": "5 seconds" }
  }
}
```

## 🔧 Advanced Configuration

### Performance Tuning

- **Concurrent Jobs**: Adjust `generation.concurrent_jobs` in config
- **Cache Duration**: Set `voice.cache_duration` for voice caching
- **Cleanup**: Enable `generation.cleanup_temp` for automatic cleanup

### Custom Voice Packs

1. Train your voice models using Bark or Coqui TTS
2. Place model files in `assets/voices/`
3. Update voice pack configuration in `config/settings.json`

### Audio Quality

Adjust audio settings in config:
```json
{
  "audio": {
    "bitrate": "192k",
    "background_volume": 0.3,
    "voice_volume": 0.8
  }
}
```

## 🐛 Troubleshooting

### Common Issues

**Generation fails with "TTS engine not found"**
- Ensure your chosen TTS engine is properly installed
- Check Python PATH and package installation
- Try switching to eSpeak as fallback

**Audio mixing produces no output**
- Verify FFmpeg installation: `ffmpeg -version`
- Check file permissions on temp/ directory
- Ensure background music files exist

**Voice generation is slow**
- Enable voice caching in config
- Use faster TTS engine (eSpeak for testing)
- Reduce audio quality settings

**Frontend shows connection errors**
- Check PHP error logs
- Verify web server configuration
- Ensure generate.php is accessible

### Debug Mode

Enable debug mode in `config/settings.json`:
```json
{
  "app": {
    "debug": true
  }
}
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Bark TTS** for high-quality voice synthesis
- **FFmpeg** for audio/video processing
- **Indian storytelling tradition** for inspiration
- **Open source community** for making this possible

## 🎬 Sample Outputs

The engine generates:
- **MP4 Videos**: 720p, 9:16 aspect ratio with embedded subtitles
- **MP3 Audio**: 44.1kHz stereo with background music and effects
- **SRT Subtitles**: Properly timed subtitle files
- **Thumbnails**: Auto-generated video previews

## 🔮 Roadmap

- [ ] **Multi-language Support**: Hindi, Tamil, Telugu, Bengali voices
- [ ] **Advanced Voice Cloning**: Train custom voices from audio samples
- [ ] **Real-time Generation**: Live voice synthesis and mixing
- [ ] **Mobile App**: Native iOS/Android applications
- [ ] **Cloud Deployment**: Docker containers and cloud templates
- [ ] **Collaboration Features**: Team workspaces and sharing
- [ ] **Analytics Dashboard**: Usage statistics and performance metrics

## 📞 Support

- 🐛 Issues: [GitHub Issues](https://github.com/yourusername/sutradhar-engine/issues)
- 📖 Wiki: [Project Wiki](https://github.com/yourusername/sutradhar-engine/wiki)
- 💡 Discussions: [GitHub Discussions](https://github.com/yourusername/sutradhar-engine/discussions)

---

**Made with ❤️ for Indian storytelling**

*Sutradhar (सूत्रधार) - The narrator who guides the story*
