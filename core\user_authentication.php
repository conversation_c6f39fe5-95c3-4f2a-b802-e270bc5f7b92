<?php
/**
 * User Authentication System - Secure user management for Sutradhar 2070
 * Handles registration, login, email verification, password reset, OAuth, and sessions
 */

require_once 'database_manager.php';
require_once 'email_service.php';

class UserAuthentication {
    private $db;
    private $config;
    private $emailService;
    private $sessionTimeout;
    private $maxLoginAttempts;
    private $lockoutDuration;
    
    public function __construct() {
        $this->db = new DatabaseManager();
        $this->emailService = new EmailService();
        $this->loadConfig();
        $this->sessionTimeout = 3600; // 1 hour
        $this->maxLoginAttempts = 5;
        $this->lockoutDuration = 900; // 15 minutes

        $this->initializeDatabase();
        $this->startSecureSession();
    }
    
    /**
     * Register new user with email verification
     */
    public function registerUser($email, $password, $firstName, $lastName, $referralCode = null) {
        try {
            // Validate input
            $validation = $this->validateRegistrationData($email, $password, $firstName, $lastName);
            if (!$validation['valid']) {
                return ['success' => false, 'error' => $validation['error']];
            }
            
            // Check if user already exists
            if ($this->userExists($email)) {
                return ['success' => false, 'error' => 'User with this email already exists'];
            }
            
            // Hash password
            $passwordHash = password_hash($password, PASSWORD_ARGON2ID, [
                'memory_cost' => 65536,
                'time_cost' => 4,
                'threads' => 3
            ]);
            
            // Generate verification token
            $verificationToken = bin2hex(random_bytes(32));
            $userId = uniqid('user_');
            
            // Create user record
            $userData = [
                'user_id' => $userId,
                'email' => strtolower(trim($email)),
                'password_hash' => $passwordHash,
                'first_name' => trim($firstName),
                'last_name' => trim($lastName),
                'verification_token' => $verificationToken,
                'email_verified' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'pending_verification',
                'tier' => 'free',
                'credits' => 50, // Free tier starting credits
                'referral_code' => $referralCode,
                'last_login' => null,
                'login_attempts' => 0,
                'locked_until' => null
            ];
            
            $result = $this->db->insert('users', $userData);
            
            if ($result) {
                // Send verification email
                $emailSent = $this->sendVerificationEmail($email, $verificationToken, $firstName);
                
                // Create user profile
                $this->createUserProfile($userId);
                
                // Process referral if provided
                if ($referralCode) {
                    $this->processReferral($userId, $referralCode);
                }
                
                return [
                    'success' => true,
                    'user_id' => $userId,
                    'message' => 'Registration successful. Please check your email for verification.',
                    'email_sent' => $emailSent
                ];
            }
            
            return ['success' => false, 'error' => 'Failed to create user account'];
            
        } catch (Exception $e) {
            error_log("Registration error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Registration failed. Please try again.'];
        }
    }
    
    /**
     * Authenticate user login
     */
    public function loginUser($email, $password, $rememberMe = false) {
        try {
            $email = strtolower(trim($email));
            
            // Check if account is locked
            $lockCheck = $this->checkAccountLock($email);
            if (!$lockCheck['allowed']) {
                return ['success' => false, 'error' => $lockCheck['message']];
            }
            
            // Get user data
            $user = $this->db->selectOne('users', ['email' => $email]);
            
            if (!$user) {
                $this->recordFailedLogin($email);
                return ['success' => false, 'error' => 'Invalid email or password'];
            }
            
            // Verify password
            if (!password_verify($password, $user['password_hash'])) {
                $this->recordFailedLogin($email);
                return ['success' => false, 'error' => 'Invalid email or password'];
            }
            
            // Check if email is verified
            if (!$user['email_verified']) {
                return [
                    'success' => false, 
                    'error' => 'Please verify your email before logging in',
                    'requires_verification' => true
                ];
            }
            
            // Check account status
            if ($user['status'] !== 'active' && $user['status'] !== 'pending_verification') {
                return ['success' => false, 'error' => 'Account is suspended or inactive'];
            }
            
            // Successful login
            $this->resetLoginAttempts($email);
            $sessionData = $this->createUserSession($user, $rememberMe);
            $this->updateLastLogin($user['user_id']);
            
            return [
                'success' => true,
                'user' => $this->sanitizeUserData($user),
                'session' => $sessionData,
                'message' => 'Login successful'
            ];
            
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Login failed. Please try again.'];
        }
    }
    
    /**
     * Verify email with token
     */
    public function verifyEmail($token) {
        try {
            $user = $this->db->selectOne('users', ['verification_token' => $token]);
            
            if (!$user) {
                return ['success' => false, 'error' => 'Invalid verification token'];
            }
            
            if ($user['email_verified']) {
                return ['success' => false, 'error' => 'Email already verified'];
            }
            
            // Update user status
            $updateData = [
                'email_verified' => true,
                'status' => 'active',
                'verification_token' => null,
                'verified_at' => date('Y-m-d H:i:s')
            ];
            
            $result = $this->db->update('users', $updateData, ['user_id' => $user['user_id']]);
            
            if ($result) {
                // Send welcome email
                $this->sendWelcomeEmail($user['email'], $user['first_name']);
                
                return [
                    'success' => true,
                    'message' => 'Email verified successfully. You can now log in.',
                    'user_id' => $user['user_id']
                ];
            }
            
            return ['success' => false, 'error' => 'Failed to verify email'];
            
        } catch (Exception $e) {
            error_log("Email verification error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Verification failed. Please try again.'];
        }
    }
    
    /**
     * Initiate password reset
     */
    public function initiatePasswordReset($email) {
        try {
            $email = strtolower(trim($email));
            $user = $this->db->selectOne('users', ['email' => $email]);
            
            if (!$user) {
                // Don't reveal if email exists
                return [
                    'success' => true,
                    'message' => 'If an account with this email exists, you will receive a password reset link.'
                ];
            }
            
            // Generate reset token
            $resetToken = bin2hex(random_bytes(32));
            $expiresAt = date('Y-m-d H:i:s', strtotime('+1 hour'));
            
            // Store reset token
            $resetData = [
                'user_id' => $user['user_id'],
                'reset_token' => $resetToken,
                'expires_at' => $expiresAt,
                'created_at' => date('Y-m-d H:i:s'),
                'used' => false
            ];
            
            $this->db->insert('password_resets', $resetData);
            
            // Send reset email
            $emailSent = $this->sendPasswordResetEmail($email, $resetToken, $user['first_name']);
            
            return [
                'success' => true,
                'message' => 'If an account with this email exists, you will receive a password reset link.',
                'email_sent' => $emailSent
            ];
            
        } catch (Exception $e) {
            error_log("Password reset initiation error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to initiate password reset'];
        }
    }
    
    /**
     * Reset password with token
     */
    public function resetPassword($token, $newPassword) {
        try {
            // Validate new password
            $passwordValidation = $this->validatePassword($newPassword);
            if (!$passwordValidation['valid']) {
                return ['success' => false, 'error' => $passwordValidation['error']];
            }
            
            // Find valid reset token
            $reset = $this->db->selectOne('password_resets', [
                'reset_token' => $token,
                'used' => false,
                'expires_at >' => date('Y-m-d H:i:s')
            ]);
            
            if (!$reset) {
                return ['success' => false, 'error' => 'Invalid or expired reset token'];
            }
            
            // Hash new password
            $passwordHash = password_hash($newPassword, PASSWORD_ARGON2ID, [
                'memory_cost' => 65536,
                'time_cost' => 4,
                'threads' => 3
            ]);
            
            // Update user password
            $updateResult = $this->db->update('users', 
                ['password_hash' => $passwordHash, 'password_updated_at' => date('Y-m-d H:i:s')],
                ['user_id' => $reset['user_id']]
            );
            
            if ($updateResult) {
                // Mark reset token as used
                $this->db->update('password_resets', 
                    ['used' => true, 'used_at' => date('Y-m-d H:i:s')],
                    ['reset_token' => $token]
                );
                
                // Invalidate all user sessions
                $this->invalidateUserSessions($reset['user_id']);
                
                return [
                    'success' => true,
                    'message' => 'Password reset successfully. Please log in with your new password.'
                ];
            }
            
            return ['success' => false, 'error' => 'Failed to reset password'];
            
        } catch (Exception $e) {
            error_log("Password reset error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Password reset failed. Please try again.'];
        }
    }
    
    /**
     * OAuth login/registration
     */
    public function oauthLogin($provider, $oauthData) {
        try {
            $email = strtolower(trim($oauthData['email']));
            $providerId = $oauthData['id'];
            
            // Check if OAuth account exists
            $oauthAccount = $this->db->selectOne('oauth_accounts', [
                'provider' => $provider,
                'provider_id' => $providerId
            ]);
            
            if ($oauthAccount) {
                // Existing OAuth account - log in
                $user = $this->db->selectOne('users', ['user_id' => $oauthAccount['user_id']]);
                
                if ($user && $user['status'] === 'active') {
                    $sessionData = $this->createUserSession($user, false);
                    $this->updateLastLogin($user['user_id']);
                    
                    return [
                        'success' => true,
                        'user' => $this->sanitizeUserData($user),
                        'session' => $sessionData,
                        'message' => 'OAuth login successful'
                    ];
                }
            }
            
            // Check if user exists with this email
            $existingUser = $this->db->selectOne('users', ['email' => $email]);
            
            if ($existingUser) {
                // Link OAuth account to existing user
                $this->linkOAuthAccount($existingUser['user_id'], $provider, $oauthData);
                
                $sessionData = $this->createUserSession($existingUser, false);
                $this->updateLastLogin($existingUser['user_id']);
                
                return [
                    'success' => true,
                    'user' => $this->sanitizeUserData($existingUser),
                    'session' => $sessionData,
                    'message' => 'OAuth account linked and login successful'
                ];
            }
            
            // Create new user from OAuth data
            return $this->createUserFromOAuth($provider, $oauthData);
            
        } catch (Exception $e) {
            error_log("OAuth login error: " . $e->getMessage());
            return ['success' => false, 'error' => 'OAuth login failed. Please try again.'];
        }
    }
    
    /**
     * Logout user
     */
    public function logoutUser($sessionId = null) {
        try {
            $sessionId = $sessionId ?? ($_SESSION['session_id'] ?? null);
            
            if ($sessionId) {
                // Remove session from database
                $this->db->delete('user_sessions', ['session_id' => $sessionId]);
            }
            
            // Clear PHP session
            session_unset();
            session_destroy();
            
            // Clear remember me cookie if exists
            if (isset($_COOKIE['remember_token'])) {
                setcookie('remember_token', '', time() - 3600, '/', '', true, true);
            }
            
            return ['success' => true, 'message' => 'Logged out successfully'];
            
        } catch (Exception $e) {
            error_log("Logout error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Logout failed'];
        }
    }
    
    /**
     * Get current authenticated user
     */
    public function getCurrentUser() {
        if (!isset($_SESSION['user_id'])) {
            return null;
        }
        
        $user = $this->db->selectOne('users', ['user_id' => $_SESSION['user_id']]);
        
        if ($user && $user['status'] === 'active') {
            return $this->sanitizeUserData($user);
        }
        
        return null;
    }
    
    /**
     * Check if user is authenticated
     */
    public function isAuthenticated() {
        return $this->getCurrentUser() !== null;
    }
    
    /**
     * Require authentication (redirect if not authenticated)
     */
    public function requireAuth($redirectUrl = '/login') {
        if (!$this->isAuthenticated()) {
            header("Location: $redirectUrl");
            exit;
        }
        
        return $this->getCurrentUser();
    }

    /**
     * Initialize database tables
     */
    private function initializeDatabase() {
        // Users table
        $this->db->createTable('users', [
            'user_id VARCHAR(50) PRIMARY KEY',
            'email VARCHAR(255) UNIQUE NOT NULL',
            'password_hash VARCHAR(255)',
            'first_name VARCHAR(100) NOT NULL',
            'last_name VARCHAR(100) NOT NULL',
            'verification_token VARCHAR(64)',
            'email_verified BOOLEAN DEFAULT FALSE',
            'status ENUM("pending_verification", "active", "suspended", "deleted") DEFAULT "pending_verification"',
            'tier ENUM("free", "pro", "business", "enterprise") DEFAULT "free"',
            'credits INT DEFAULT 50',
            'referral_code VARCHAR(20)',
            'created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'verified_at TIMESTAMP NULL',
            'last_login TIMESTAMP NULL',
            'password_updated_at TIMESTAMP NULL',
            'login_attempts INT DEFAULT 0',
            'locked_until TIMESTAMP NULL'
        ]);

        // User sessions table
        $this->db->createTable('user_sessions', [
            'session_id VARCHAR(128) PRIMARY KEY',
            'user_id VARCHAR(50) NOT NULL',
            'ip_address VARCHAR(45)',
            'user_agent TEXT',
            'created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'expires_at TIMESTAMP NOT NULL',
            'remember_token VARCHAR(64)',
            'is_active BOOLEAN DEFAULT TRUE',
            'FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE'
        ]);

        // Password resets table
        $this->db->createTable('password_resets', [
            'id INT AUTO_INCREMENT PRIMARY KEY',
            'user_id VARCHAR(50) NOT NULL',
            'reset_token VARCHAR(64) NOT NULL',
            'expires_at TIMESTAMP NOT NULL',
            'created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'used BOOLEAN DEFAULT FALSE',
            'used_at TIMESTAMP NULL',
            'FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE'
        ]);

        // OAuth accounts table
        $this->db->createTable('oauth_accounts', [
            'id INT AUTO_INCREMENT PRIMARY KEY',
            'user_id VARCHAR(50) NOT NULL',
            'provider ENUM("google", "facebook", "github") NOT NULL',
            'provider_id VARCHAR(255) NOT NULL',
            'provider_email VARCHAR(255)',
            'provider_data JSON',
            'created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'UNIQUE KEY unique_provider_account (provider, provider_id)',
            'FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE'
        ]);

        // User profiles table
        $this->db->createTable('user_profiles', [
            'user_id VARCHAR(50) PRIMARY KEY',
            'avatar_url VARCHAR(500)',
            'bio TEXT',
            'company VARCHAR(255)',
            'website VARCHAR(255)',
            'location VARCHAR(255)',
            'timezone VARCHAR(50)',
            'language VARCHAR(10) DEFAULT "en"',
            'notifications_email BOOLEAN DEFAULT TRUE',
            'notifications_marketing BOOLEAN DEFAULT TRUE',
            'created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            'FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE'
        ]);
    }

    /**
     * Validate registration data
     */
    private function validateRegistrationData($email, $password, $firstName, $lastName) {
        // Email validation
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return ['valid' => false, 'error' => 'Invalid email address'];
        }

        // Password validation
        $passwordValidation = $this->validatePassword($password);
        if (!$passwordValidation['valid']) {
            return $passwordValidation;
        }

        // Name validation
        if (strlen(trim($firstName)) < 2 || strlen(trim($lastName)) < 2) {
            return ['valid' => false, 'error' => 'First and last name must be at least 2 characters'];
        }

        if (!preg_match('/^[a-zA-Z\s\-\'\.]+$/', $firstName) || !preg_match('/^[a-zA-Z\s\-\'\.]+$/', $lastName)) {
            return ['valid' => false, 'error' => 'Names can only contain letters, spaces, hyphens, apostrophes, and periods'];
        }

        return ['valid' => true];
    }

    /**
     * Validate password strength
     */
    private function validatePassword($password) {
        if (strlen($password) < 8) {
            return ['valid' => false, 'error' => 'Password must be at least 8 characters long'];
        }

        if (!preg_match('/[A-Z]/', $password)) {
            return ['valid' => false, 'error' => 'Password must contain at least one uppercase letter'];
        }

        if (!preg_match('/[a-z]/', $password)) {
            return ['valid' => false, 'error' => 'Password must contain at least one lowercase letter'];
        }

        if (!preg_match('/[0-9]/', $password)) {
            return ['valid' => false, 'error' => 'Password must contain at least one number'];
        }

        if (!preg_match('/[^a-zA-Z0-9]/', $password)) {
            return ['valid' => false, 'error' => 'Password must contain at least one special character'];
        }

        return ['valid' => true];
    }

    /**
     * Check if user exists
     */
    private function userExists($email) {
        $user = $this->db->selectOne('users', ['email' => strtolower(trim($email))]);
        return $user !== null;
    }

    /**
     * Check account lock status
     */
    private function checkAccountLock($email) {
        $user = $this->db->selectOne('users', ['email' => $email]);

        if (!$user) {
            return ['allowed' => true];
        }

        if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
            $unlockTime = date('H:i:s', strtotime($user['locked_until']));
            return [
                'allowed' => false,
                'message' => "Account is locked due to too many failed login attempts. Try again after $unlockTime."
            ];
        }

        return ['allowed' => true];
    }

    /**
     * Record failed login attempt
     */
    private function recordFailedLogin($email) {
        $user = $this->db->selectOne('users', ['email' => $email]);

        if ($user) {
            $attempts = $user['login_attempts'] + 1;
            $updateData = ['login_attempts' => $attempts];

            if ($attempts >= $this->maxLoginAttempts) {
                $updateData['locked_until'] = date('Y-m-d H:i:s', time() + $this->lockoutDuration);
            }

            $this->db->update('users', $updateData, ['user_id' => $user['user_id']]);
        }
    }

    /**
     * Reset login attempts
     */
    private function resetLoginAttempts($email) {
        $this->db->update('users',
            ['login_attempts' => 0, 'locked_until' => null],
            ['email' => $email]
        );
    }

    /**
     * Create user session
     */
    private function createUserSession($user, $rememberMe = false) {
        $sessionId = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', time() + $this->sessionTimeout);
        $rememberToken = $rememberMe ? bin2hex(random_bytes(32)) : null;

        // Store session in database
        $sessionData = [
            'session_id' => $sessionId,
            'user_id' => $user['user_id'],
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            'expires_at' => $expiresAt,
            'remember_token' => $rememberToken
        ];

        $this->db->insert('user_sessions', $sessionData);

        // Set PHP session
        $_SESSION['user_id'] = $user['user_id'];
        $_SESSION['session_id'] = $sessionId;
        $_SESSION['email'] = $user['email'];
        $_SESSION['first_name'] = $user['first_name'];
        $_SESSION['tier'] = $user['tier'];

        // Set remember me cookie if requested
        if ($rememberMe && $rememberToken) {
            setcookie('remember_token', $rememberToken, time() + (30 * 24 * 3600), '/', '', true, true);
        }

        return [
            'session_id' => $sessionId,
            'expires_at' => $expiresAt,
            'remember_me' => $rememberMe
        ];
    }

    /**
     * Update last login timestamp
     */
    private function updateLastLogin($userId) {
        $this->db->update('users',
            ['last_login' => date('Y-m-d H:i:s')],
            ['user_id' => $userId]
        );
    }

    /**
     * Sanitize user data for output
     */
    private function sanitizeUserData($user) {
        unset($user['password_hash']);
        unset($user['verification_token']);
        unset($user['login_attempts']);
        unset($user['locked_until']);

        return $user;
    }

    /**
     * Start secure session
     */
    private function startSecureSession() {
        if (session_status() === PHP_SESSION_NONE) {
            ini_set('session.cookie_httponly', 1);
            ini_set('session.cookie_secure', 1);
            ini_set('session.use_strict_mode', 1);
            session_start();
        }
    }

    /**
     * Load configuration
     */
    private function loadConfig() {
        $configFile = __DIR__ . '/../config/auth_config.json';

        if (file_exists($configFile)) {
            $this->config = json_decode(file_get_contents($configFile), true);
        } else {
            $this->config = [
                'smtp' => [
                    'host' => 'localhost',
                    'port' => 587,
                    'username' => '',
                    'password' => '',
                    'from_email' => '<EMAIL>',
                    'from_name' => 'Sutradhar 2070'
                ],
                'oauth' => [
                    'google' => ['client_id' => '', 'client_secret' => ''],
                    'facebook' => ['app_id' => '', 'app_secret' => ''],
                    'github' => ['client_id' => '', 'client_secret' => '']
                ]
            ];
        }
    }

    /**
     * Send verification email
     */
    private function sendVerificationEmail($email, $token, $firstName) {
        return $this->emailService->sendVerificationEmail($email, $token, $firstName);
    }

    /**
     * Send welcome email
     */
    private function sendWelcomeEmail($email, $firstName) {
        return $this->emailService->sendWelcomeEmail($email, $firstName);
    }

    /**
     * Send password reset email
     */
    private function sendPasswordResetEmail($email, $token, $firstName) {
        return $this->emailService->sendPasswordResetEmail($email, $token, $firstName);
    }

    /**
     * Create user profile
     */
    private function createUserProfile($userId) {
        $profileData = [
            'user_id' => $userId,
            'avatar_url' => null,
            'bio' => null,
            'company' => null,
            'website' => null,
            'location' => null,
            'timezone' => 'UTC',
            'language' => 'en',
            'notifications_email' => true,
            'notifications_marketing' => true
        ];

        return $this->db->insert('user_profiles', $profileData);
    }

    /**
     * Process referral
     */
    private function processReferral($userId, $referralCode) {
        // Find referring user
        $referrer = $this->db->selectOne('users', ['user_id' => $referralCode]);

        if ($referrer) {
            // Give bonus credits to both users
            $this->db->update('users',
                ['credits' => $referrer['credits'] + 25],
                ['user_id' => $referrer['user_id']]
            );

            $this->db->update('users',
                ['credits' => 75], // 50 + 25 bonus
                ['user_id' => $userId]
            );

            // Log referral
            $referralData = [
                'referrer_id' => $referrer['user_id'],
                'referred_id' => $userId,
                'bonus_credits' => 25,
                'created_at' => date('Y-m-d H:i:s')
            ];

            $this->db->insert('referrals', $referralData);
        }
    }

    /**
     * Create user from OAuth data
     */
    private function createUserFromOAuth($provider, $oauthData) {
        $email = strtolower(trim($oauthData['email']));
        $firstName = $oauthData['first_name'] ?? $oauthData['given_name'] ?? 'User';
        $lastName = $oauthData['last_name'] ?? $oauthData['family_name'] ?? '';
        $userId = uniqid('user_');

        // Create user without password (OAuth only)
        $userData = [
            'user_id' => $userId,
            'email' => $email,
            'password_hash' => null,
            'first_name' => $firstName,
            'last_name' => $lastName,
            'verification_token' => null,
            'email_verified' => true, // OAuth emails are pre-verified
            'created_at' => date('Y-m-d H:i:s'),
            'verified_at' => date('Y-m-d H:i:s'),
            'status' => 'active',
            'tier' => 'free',
            'credits' => 50,
            'last_login' => null,
            'login_attempts' => 0,
            'locked_until' => null
        ];

        $result = $this->db->insert('users', $userData);

        if ($result) {
            // Link OAuth account
            $this->linkOAuthAccount($userId, $provider, $oauthData);

            // Create user profile
            $this->createUserProfile($userId);

            // Send welcome email
            $this->sendWelcomeEmail($email, $firstName);

            // Create session
            $user = $this->db->selectOne('users', ['user_id' => $userId]);
            $sessionData = $this->createUserSession($user, false);
            $this->updateLastLogin($userId);

            return [
                'success' => true,
                'user' => $this->sanitizeUserData($user),
                'session' => $sessionData,
                'message' => 'Account created and logged in successfully'
            ];
        }

        return ['success' => false, 'error' => 'Failed to create OAuth account'];
    }

    /**
     * Link OAuth account to existing user
     */
    private function linkOAuthAccount($userId, $provider, $oauthData) {
        $oauthAccountData = [
            'user_id' => $userId,
            'provider' => $provider,
            'provider_id' => $oauthData['id'],
            'provider_email' => $oauthData['email'],
            'provider_data' => json_encode($oauthData)
        ];

        return $this->db->insert('oauth_accounts', $oauthAccountData);
    }

    /**
     * Invalidate all user sessions
     */
    private function invalidateUserSessions($userId) {
        return $this->db->update('user_sessions',
            ['is_active' => false],
            ['user_id' => $userId]
        );
    }
}
