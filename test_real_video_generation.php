<?php
/**
 * Test Real Video Generation for Sutradhar 2070
 * Comprehensive testing of the enhanced video generation system
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html>
<head>
    <title>🎬 Real Video Generation Test</title>
    <style>
        body { font-family: Arial, sans-serif; background: #1a1a2e; color: white; padding: 20px; }
        .success { color: #10B981; }
        .error { color: #EF4444; }
        .warning { color: #F59E0B; }
        .info { color: #3B82F6; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #333; border-radius: 8px; }
        pre { background: #0f0f23; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .video-container { margin: 10px 0; }
        .video-container video { max-width: 400px; height: auto; }
        .thumbnail { max-width: 200px; height: auto; margin: 5px; }
        .test-button { background: #10B981; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .test-button:hover { background: #059669; }
    </style>
</head>
<body>
<h1>🎬 Sutradhar 2070 - Real Video Generation Test</h1>
";

$testResults = [];

// Test 1: Check Dependencies
echo "<div class='section'>";
echo "<h2>🔍 Dependency Check</h2>";

// Check Python
$pythonVersion = shell_exec('python --version 2>&1');
if ($pythonVersion && strpos($pythonVersion, 'Python') !== false) {
    echo "<span class='success'>✅ Python: $pythonVersion</span><br>";
    $testResults['python'] = true;
} else {
    echo "<span class='error'>❌ Python not found</span><br>";
    $testResults['python'] = false;
}

// Check Python packages
$packages = ['PIL', 'numpy', 'moviepy'];
foreach ($packages as $package) {
    $result = shell_exec("python -c \"import $package; print('$package: OK')\" 2>&1");
    if (strpos($result, 'OK') !== false) {
        echo "<span class='success'>✅ $package available</span><br>";
        $testResults[$package] = true;
    } else {
        echo "<span class='error'>❌ $package missing</span><br>";
        echo "<span class='info'>Install with: pip install $package</span><br>";
        $testResults[$package] = false;
    }
}

// Check FFmpeg
$ffmpegVersion = shell_exec('ffmpeg -version 2>&1');
if ($ffmpegVersion && strpos($ffmpegVersion, 'ffmpeg version') !== false) {
    echo "<span class='success'>✅ FFmpeg available</span><br>";
    $testResults['ffmpeg'] = true;
} else {
    echo "<span class='warning'>⚠️ FFmpeg not found (fallback available)</span><br>";
    $testResults['ffmpeg'] = false;
}

// Check GD
if (extension_loaded('gd')) {
    echo "<span class='success'>✅ GD extension available</span><br>";
    $testResults['gd'] = true;
} else {
    echo "<span class='error'>❌ GD extension missing</span><br>";
    $testResults['gd'] = false;
}

echo "</div>";

// Test 2: Test Enhanced Video Engine
echo "<div class='section'>";
echo "<h2>🎨 Enhanced Video Engine Test</h2>";

try {
    require_once 'core/enhanced_video_engine.php';
    echo "<span class='success'>✅ Enhanced Video Engine loaded</span><br>";
    
    $engine = new EnhancedVideoEngine();
    echo "<span class='success'>✅ Engine instance created</span><br>";
    
    $testResults['enhanced_engine'] = true;
} catch (Exception $e) {
    echo "<span class='error'>❌ Enhanced Video Engine failed: " . $e->getMessage() . "</span><br>";
    $testResults['enhanced_engine'] = false;
}

echo "</div>";

// Test 3: Generate Test Videos
echo "<div class='section'>";
echo "<h2>🎬 Video Generation Test</h2>";

if ($testResults['enhanced_engine']) {
    $testCases = [
        [
            'mood' => 'euphoric',
            'topic' => 'nature_wildlife',
            'prompt' => 'A beautiful euphoric scene with wildlife in nature'
        ],
        [
            'mood' => 'serene',
            'topic' => 'urban_city',
            'prompt' => 'A peaceful serene cityscape at sunset'
        ],
        [
            'mood' => 'dramatic',
            'topic' => 'abstract_art',
            'prompt' => 'Dramatic abstract art with bold colors and movement'
        ]
    ];
    
    foreach ($testCases as $i => $testCase) {
        echo "<h3>Test Case " . ($i + 1) . ": {$testCase['mood']} - {$testCase['topic']}</h3>";
        
        $jobId = 'test_' . time() . '_' . $i;
        
        try {
            echo "<span class='info'>🎬 Generating video...</span><br>";
            
            $segments = [
                ['text' => $testCase['prompt'], 'content' => $testCase['prompt']]
            ];
            
            $videoPath = $engine->generateEnhancedVideo(
                $segments,
                $testCase['topic'],
                $testCase['mood'],
                $jobId
            );
            
            if ($videoPath && file_exists($videoPath)) {
                echo "<span class='success'>✅ Video generated successfully!</span><br>";
                echo "<span class='info'>📁 Path: $videoPath</span><br>";
                echo "<span class='info'>📏 Size: " . formatBytes(filesize($videoPath)) . "</span><br>";
                
                // Convert to URL
                $videoUrl = str_replace(__DIR__ . '/public/', '/', $videoPath);
                $videoUrl = str_replace('\\', '/', $videoUrl);
                
                echo "<div class='video-container'>";
                echo "<video controls width='400'>";
                echo "<source src='$videoUrl' type='video/mp4'>";
                echo "Your browser does not support the video tag.";
                echo "</video>";
                echo "<br><a href='$videoUrl' target='_blank'>🔗 Open Video</a>";
                echo "</div>";
                
                $testResults['video_' . $i] = true;
            } else {
                echo "<span class='error'>❌ Video generation failed</span><br>";
                $testResults['video_' . $i] = false;
            }
            
        } catch (Exception $e) {
            echo "<span class='error'>❌ Error: " . $e->getMessage() . "</span><br>";
            $testResults['video_' . $i] = false;
        }
        
        echo "<hr>";
    }
} else {
    echo "<span class='warning'>⚠️ Skipping video generation tests (engine not available)</span><br>";
}

echo "</div>";

// Test 4: Test Background Job Processor
echo "<div class='section'>";
echo "<h2>⚙️ Background Job Processor Test</h2>";

try {
    require_once 'core/background_job_processor.php';
    echo "<span class='success'>✅ Background Job Processor loaded</span><br>";
    
    // Test job processing
    echo "<span class='info'>🔄 Testing job processing...</span><br>";
    
    $processor = new BackgroundJobProcessor();
    echo "<span class='success'>✅ Processor instance created</span><br>";
    
    $testResults['background_processor'] = true;
} catch (Exception $e) {
    echo "<span class='error'>❌ Background Job Processor failed: " . $e->getMessage() . "</span><br>";
    $testResults['background_processor'] = false;
}

echo "</div>";

// Test 5: API Integration Test
echo "<div class='section'>";
echo "<h2>🔌 API Integration Test</h2>";

echo "<span class='info'>🧪 Testing API video generation...</span><br>";

$testData = [
    'mood' => 'futuristic',
    'topic' => 'technology',
    'custom_prompt' => 'Futuristic technology with glowing circuits and data streams'
];

$apiUrl = 'http://localhost:8000/test_api_simple.php?endpoint=generate';
$postData = json_encode($testData);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => $postData,
        'timeout' => 30
    ]
]);

$response = @file_get_contents($apiUrl, false, $context);

if ($response) {
    $data = json_decode($response, true);
    if ($data && $data['success']) {
        echo "<span class='success'>✅ API video generation request successful</span><br>";
        echo "<span class='info'>📋 Job ID: {$data['data']['job_id']}</span><br>";
        $testResults['api_integration'] = true;
    } else {
        echo "<span class='error'>❌ API returned error: " . ($data['error'] ?? 'Unknown error') . "</span><br>";
        $testResults['api_integration'] = false;
    }
} else {
    echo "<span class='error'>❌ API request failed</span><br>";
    $testResults['api_integration'] = false;
}

echo "</div>";

// Test Summary
echo "<div class='section'>";
echo "<h2>📊 Test Summary</h2>";

$totalTests = count($testResults);
$passedTests = count(array_filter($testResults));
$failedTests = $totalTests - $passedTests;

echo "<h3>Overall Results:</h3>";
echo "<span class='info'>Total Tests: $totalTests</span><br>";
echo "<span class='success'>Passed: $passedTests</span><br>";
echo "<span class='error'>Failed: $failedTests</span><br>";
echo "<span class='info'>Success Rate: " . round(($passedTests / $totalTests) * 100, 1) . "%</span><br>";

echo "<h3>Detailed Results:</h3>";
foreach ($testResults as $test => $result) {
    $icon = $result ? '✅' : '❌';
    $status = $result ? 'PASS' : 'FAIL';
    $class = $result ? 'success' : 'error';
    echo "<span class='$class'>$icon $test: $status</span><br>";
}

if ($passedTests === $totalTests) {
    echo "<div style='background: #10B981; color: white; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>🎉 ALL TESTS PASSED!</h3>";
    echo "<p>Your real video generation system is fully operational!</p>";
    echo "<p><strong>Features Available:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Python-based real video generation</li>";
    echo "<li>✅ GD-based fallback generation</li>";
    echo "<li>✅ FFmpeg video processing</li>";
    echo "<li>✅ Automatic thumbnail generation</li>";
    echo "<li>✅ API integration</li>";
    echo "</ul>";
    echo "<p><a href='mood_video_generator.html' style='color: white; text-decoration: underline;'>🎬 Try the Mood Video Generator</a></p>";
    echo "</div>";
} else {
    echo "<div style='background: #F59E0B; color: white; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>⚠️ SOME TESTS FAILED</h3>";
    echo "<p>Your system has some issues that need attention:</p>";
    echo "<ul>";
    
    if (!$testResults['python']) {
        echo "<li>❌ Install Python 3.7+ from https://python.org</li>";
    }
    
    foreach (['PIL', 'numpy', 'moviepy'] as $package) {
        if (!($testResults[$package] ?? true)) {
            echo "<li>❌ Install $package: pip install $package</li>";
        }
    }
    
    if (!$testResults['gd']) {
        echo "<li>❌ Enable GD extension in php.ini</li>";
    }
    
    if (!$testResults['ffmpeg']) {
        echo "<li>⚠️ Install FFmpeg for better video quality</li>";
    }
    
    echo "</ul>";
    echo "<p><strong>Quick Fix:</strong> Run setup_video_generation.bat</p>";
    echo "</div>";
}

echo "</div>";

// Installation Guide
echo "<div class='section'>";
echo "<h2>🛠️ Installation Guide</h2>";

echo "<h3>Automatic Setup:</h3>";
echo "<pre>setup_video_generation.bat</pre>";

echo "<h3>Manual Setup:</h3>";
echo "<pre>";
echo "1. Install Python 3.7+: https://python.org\n";
echo "2. Install packages: pip install -r requirements.txt\n";
echo "3. Install FFmpeg: https://ffmpeg.org\n";
echo "4. Enable GD extension in php.ini\n";
echo "5. Test: python core/real_video_generator.py --help\n";
echo "</pre>";

echo "<h3>Package Installation:</h3>";
echo "<pre>";
echo "pip install Pillow numpy moviepy imageio imageio-ffmpeg\n";
echo "</pre>";

echo "</div>";

echo "</body></html>";

function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}
?>
