<?php
/**
 * Enhanced TTS Manager - Integration Layer
 * Seamlessly integrates Advanced TTS with existing Sutradhar Engine pipeline
 */

class EnhancedTTSManager {
    private $advancedTTS;
    private $fallbackTTS;
    private $config;
    private $logFile;
    
    public function __construct() {
        $this->logFile = __DIR__ . '/../logs/enhanced_tts_manager.log';
        $this->loadConfig();
        $this->initializeEngines();
    }
    
    private function loadConfig() {
        $this->config = [
            'use_advanced_tts' => true,
            'fallback_enabled' => true,
            'quality_targets' => [
                'min_file_size' => 200000, // 200KB minimum
                'max_file_size' => 3000000, // 3MB maximum (increased for enhanced quality)
                'target_duration_ratio' => 0.08 // 8 seconds per 100 characters
            ],
            'voice_mapping' => [
                'default' => 'female_natural',
                'female' => 'female_natural',
                'energetic' => 'female_energetic',
                'calm' => 'female_calm',
                'male' => 'male_narrator'
            ]
        ];
    }
    
    private function initializeEngines() {
        try {
            require_once __DIR__ . '/advanced_tts_engine.php';
            $this->advancedTTS = new AdvancedTTSEngine();
            $this->log("✅ Advanced TTS Engine initialized");
        } catch (Exception $e) {
            $this->log("❌ Advanced TTS Engine failed to initialize: " . $e->getMessage());
            $this->config['use_advanced_tts'] = false;
        }
        
        if ($this->config['fallback_enabled']) {
            try {
                require_once __DIR__ . '/windows_tts_engine.php';
                $this->fallbackTTS = new WindowsTTSEngine();
                $this->log("✅ Fallback TTS Engine initialized");
            } catch (Exception $e) {
                $this->log("❌ Fallback TTS Engine failed to initialize: " . $e->getMessage());
            }
        }
    }
    
    /**
     * Main TTS generation method - compatible with existing system
     */
    public function generateSpeech($text, $voiceType = 'default', $outputFile = null) {
        $this->log("🎤 Enhanced TTS generation requested");
        $this->log("📝 Text length: " . strlen($text) . " characters");
        $this->log("🎭 Voice type: $voiceType");
        
        // Map voice type to advanced profile
        $voiceProfile = $this->config['voice_mapping'][$voiceType] ?? 'female_natural';
        
        // Try advanced TTS first
        if ($this->config['use_advanced_tts'] && $this->advancedTTS) {
            try {
                $result = $this->advancedTTS->generateSpeech($text, $voiceProfile, $outputFile);
                
                if ($this->validateOutput($result, $text)) {
                    $this->log("✅ Advanced TTS successful: " . basename($result));
                    return $result;
                } else {
                    $this->log("⚠️ Advanced TTS output validation failed");
                }
            } catch (Exception $e) {
                $this->log("❌ Advanced TTS failed: " . $e->getMessage());
            }
        }
        
        // Fallback to Windows SAPI
        if ($this->fallbackTTS) {
            $this->log("🔄 Using fallback TTS engine");
            try {
                $result = $this->fallbackTTS->generateWindowsSpeech($text, $voiceType, $outputFile);
                
                if ($result && file_exists($result)) {
                    $this->log("✅ Fallback TTS successful: " . basename($result));
                    return $result;
                }
            } catch (Exception $e) {
                $this->log("❌ Fallback TTS failed: " . $e->getMessage());
            }
        }
        
        throw new Exception("All TTS engines failed to generate speech");
    }
    
    /**
     * Generate voice segments - compatible with existing pipeline
     */
    public function generateVoiceSegments($segments, $voiceType = 'default', $jobId = null) {
        $this->log("🎤 Generating " . count($segments) . " voice segments");
        
        $voiceFiles = [];
        $tempDir = __DIR__ . '/../temp/';
        
        foreach ($segments as $index => $segment) {
            $text = $segment['text'] ?? $segment;
            if (empty($text)) continue;
            
            $this->log("  Segment $index: " . substr($text, 0, 50) . "...");
            
            $outputFile = $tempDir . ($jobId ?? 'segment') . "_enhanced_voice_segment_$index.mp3";
            
            try {
                $result = $this->generateSpeech($text, $voiceType, $outputFile);
                
                if ($result && file_exists($result)) {
                    $voiceFiles[] = [
                        'file' => $result,
                        'duration' => $this->getAudioDuration($result),
                        'segment' => $index,
                        'text' => $text,
                        'engine' => $this->getLastUsedEngine()
                    ];
                    $this->log("    ✅ Generated: " . round(filesize($result)/1024, 1) . " KB");
                } else {
                    $this->log("    ❌ Failed to generate segment $index");
                }
            } catch (Exception $e) {
                $this->log("    ❌ Segment $index error: " . $e->getMessage());
            }
        }
        
        $this->log("✅ Generated " . count($voiceFiles) . " enhanced voice segments");
        return $voiceFiles;
    }
    
    /**
     * Validate TTS output quality
     */
    private function validateOutput($audioFile, $originalText) {
        if (!file_exists($audioFile)) {
            return false;
        }
        
        $fileSize = filesize($audioFile);
        $textLength = strlen($originalText);
        
        // Check minimum file size
        if ($fileSize < $this->config['quality_targets']['min_file_size']) {
            $this->log("⚠️ File too small: " . round($fileSize/1024, 1) . " KB");
            return false;
        }
        
        // Check maximum file size
        if ($fileSize > $this->config['quality_targets']['max_file_size']) {
            $this->log("⚠️ File too large: " . round($fileSize/1024, 1) . " KB");
            return false;
        }
        
        // Check duration ratio (rough estimate)
        $expectedDuration = $textLength * $this->config['quality_targets']['target_duration_ratio'];
        $actualDuration = $this->getAudioDuration($audioFile);
        
        if ($actualDuration > 0 && abs($actualDuration - $expectedDuration) > $expectedDuration * 0.5) {
            $this->log("⚠️ Duration mismatch: expected ~{$expectedDuration}s, got {$actualDuration}s");
            // Don't fail on duration mismatch, just log it
        }
        
        return true;
    }
    
    /**
     * Get audio duration
     */
    private function getAudioDuration($audioFile) {
        if (!file_exists($audioFile)) {
            return 0;
        }
        
        $cmd = "ffprobe -v quiet -show_entries format=duration -of csv=p=0 \"$audioFile\" 2>&1";
        $duration = trim(shell_exec($cmd));
        
        if (is_numeric($duration) && $duration > 0) {
            return floatval($duration);
        }
        
        // Fallback: estimate based on file size
        $fileSize = filesize($audioFile);
        $estimatedDuration = $fileSize / 44100 / 2; // Rough estimate for 44.1kHz 16-bit
        
        return max(0, $estimatedDuration);
    }
    
    /**
     * Get last used engine for reporting
     */
    private function getLastUsedEngine() {
        if ($this->config['use_advanced_tts'] && $this->advancedTTS) {
            $engines = $this->advancedTTS->getAvailableEngines();
            return $engines[0] ?? 'advanced';
        }
        
        return 'windows_sapi';
    }
    
    /**
     * Get system status
     */
    public function getSystemStatus() {
        $status = [
            'advanced_tts_available' => $this->config['use_advanced_tts'] && $this->advancedTTS !== null,
            'fallback_tts_available' => $this->fallbackTTS !== null,
            'engines' => [],
            'voice_profiles' => []
        ];
        
        if ($this->advancedTTS) {
            $status['engines'] = $this->advancedTTS->getAvailableEngines();
            $status['voice_profiles'] = array_keys($this->advancedTTS->getVoiceProfiles());
        }
        
        return $status;
    }
    
    /**
     * Test enhanced TTS system
     */
    public function testSystem() {
        $this->log("🧪 Testing Enhanced TTS System");
        
        $testResults = [
            'advanced_tts' => null,
            'fallback_tts' => null,
            'overall_status' => 'unknown'
        ];
        
        // Test advanced TTS
        if ($this->advancedTTS) {
            try {
                $testResults['advanced_tts'] = $this->advancedTTS->testTTSSystem();
            } catch (Exception $e) {
                $testResults['advanced_tts'] = [
                    'success' => false,
                    'message' => 'Advanced TTS test failed: ' . $e->getMessage()
                ];
            }
        }
        
        // Test fallback TTS
        if ($this->fallbackTTS) {
            try {
                $testText = "Testing fallback TTS system.";
                $testFile = __DIR__ . '/../temp/fallback_test.wav';
                $result = $this->fallbackTTS->generateWindowsSpeech($testText, 'default', $testFile);
                
                $testResults['fallback_tts'] = [
                    'success' => $result && file_exists($testFile),
                    'message' => $result ? 'Fallback TTS working' : 'Fallback TTS failed',
                    'file_size' => $result ? filesize($testFile) : 0
                ];
                
                if (file_exists($testFile)) {
                    unlink($testFile);
                }
            } catch (Exception $e) {
                $testResults['fallback_tts'] = [
                    'success' => false,
                    'message' => 'Fallback TTS test failed: ' . $e->getMessage()
                ];
            }
        }
        
        // Determine overall status
        if ($testResults['advanced_tts']['success'] ?? false) {
            $testResults['overall_status'] = 'excellent';
        } elseif ($testResults['fallback_tts']['success'] ?? false) {
            $testResults['overall_status'] = 'good';
        } else {
            $testResults['overall_status'] = 'failed';
        }
        
        return $testResults;
    }
    
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] $message\n";
        file_put_contents($this->logFile, $logMessage, FILE_APPEND | LOCK_EX);
        echo $logMessage;
    }
}
