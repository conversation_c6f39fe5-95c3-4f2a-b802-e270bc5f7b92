<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎭 Complete System Fix Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0F0F23 0%, #1A1A2E 50%, #16213E 100%);
            font-family: 'Arial', sans-serif;
            color: white;
            min-height: 100vh;
        }
        .test-card {
            background: rgba(26, 26, 46, 0.8);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            transition: all 0.3s ease;
        }
        .success { border-color: #10B981; background: rgba(16, 185, 129, 0.1); }
        .error { border-color: #EF4444; background: rgba(239, 68, 68, 0.1); }
        .loading { border-color: #F59E0B; background: rgba(245, 158, 11, 0.1); }
        .cyber-button {
            background: linear-gradient(45deg, #00D4FF, #0099CC);
            border: none;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .cyber-button:hover {
            background: linear-gradient(45deg, #0099CC, #00D4FF);
            transform: translateY(-2px);
        }
        .console-log {
            background: #0f0f23;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body class="p-8">
    <div class="max-w-6xl mx-auto">
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold mb-4">🎭 Sutradhar 2070 - Complete System Fix Test</h1>
            <p class="text-gray-300">Testing all critical fixes: Homepage, Dashboard, APIs, and User Flow</p>
        </div>

        <!-- Test Progress -->
        <div class="test-card p-6 rounded-lg mb-6">
            <h3 class="text-lg font-semibold mb-3">🧪 Test Progress</h3>
            <div class="w-full bg-gray-700 rounded-full h-4 mb-2">
                <div id="overall-progress" class="bg-blue-600 h-4 rounded-full transition-all duration-300" style="width: 0%"></div>
            </div>
            <div id="test-status" class="text-sm">Ready to start comprehensive testing...</div>
        </div>

        <!-- Test Results Grid -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <!-- API Connectivity -->
            <div class="test-card p-4 rounded-lg" id="api-test">
                <h4 class="font-semibold mb-2">🔗 API Connectivity</h4>
                <div class="test-result">⏳ Testing...</div>
            </div>

            <!-- Authentication -->
            <div class="test-card p-4 rounded-lg" id="auth-test">
                <h4 class="font-semibold mb-2">🔐 Authentication</h4>
                <div class="test-result">⏳ Testing...</div>
            </div>

            <!-- Video Generation -->
            <div class="test-card p-4 rounded-lg" id="video-test">
                <h4 class="font-semibold mb-2">🎬 Video Generation</h4>
                <div class="test-result">⏳ Testing...</div>
            </div>

            <!-- Dashboard Data -->
            <div class="test-card p-4 rounded-lg" id="dashboard-test">
                <h4 class="font-semibold mb-2">📊 Dashboard Data</h4>
                <div class="test-result">⏳ Testing...</div>
            </div>

            <!-- User Flow -->
            <div class="test-card p-4 rounded-lg" id="flow-test">
                <h4 class="font-semibold mb-2">👤 User Flow</h4>
                <div class="test-result">⏳ Testing...</div>
            </div>

            <!-- Console Errors -->
            <div class="test-card p-4 rounded-lg" id="console-test">
                <h4 class="font-semibold mb-2">🐛 Console Errors</h4>
                <div class="test-result">⏳ Testing...</div>
            </div>
        </div>

        <!-- Detailed Test Results -->
        <div class="test-card p-6 rounded-lg mb-6">
            <h3 class="text-lg font-semibold mb-3">📋 Detailed Test Results</h3>
            <div id="detailed-results">
                <div class="text-gray-400">Detailed results will appear here...</div>
            </div>
        </div>

        <!-- Console Log -->
        <div class="test-card p-6 rounded-lg mb-6">
            <h3 class="text-lg font-semibold mb-3">📝 Console Log</h3>
            <div id="console-log" class="console-log">
                <div class="text-gray-400">Console output will appear here...</div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="test-card p-6 rounded-lg text-center">
            <h3 class="text-lg font-semibold mb-4">🚀 Actions</h3>
            <div class="flex flex-wrap justify-center gap-4">
                <button onclick="runCompleteTest()" class="cyber-button px-6 py-3 rounded">
                    🧪 Run Complete Test
                </button>
                <button onclick="testHomepage()" class="cyber-button px-4 py-2 rounded">
                    🏠 Test Homepage
                </button>
                <button onclick="testDashboard()" class="cyber-button px-4 py-2 rounded">
                    📊 Test Dashboard
                </button>
                <button onclick="clearLogs()" class="cyber-button px-4 py-2 rounded">
                    🗑️ Clear Logs
                </button>
                <a href="index.html" target="_blank" class="cyber-button px-4 py-2 rounded inline-block">
                    🏠 Open Homepage
                </a>
                <a href="dashboard.html" target="_blank" class="cyber-button px-4 py-2 rounded inline-block">
                    📊 Open Dashboard
                </a>
            </div>
        </div>
    </div>

    <script>
        let testResults = {};
        let testProgress = 0;
        let totalTests = 6;

        // Override console.log to capture output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function logToConsole(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.join(' ');
            const logElement = document.getElementById('console-log');
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.style.color = type === 'error' ? '#EF4444' : type === 'warn' ? '#F59E0B' : '#10B981';
            logEntry.innerHTML = `<span class="text-gray-400">[${timestamp}]</span> ${message}`;
            
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
            
            // Call original console method
            if (type === 'error') originalError(...args);
            else if (type === 'warn') originalWarn(...args);
            else originalLog(...args);
        }

        console.log = (...args) => logToConsole('log', ...args);
        console.error = (...args) => logToConsole('error', ...args);
        console.warn = (...args) => logToConsole('warn', ...args);

        function updateTestCard(testId, status, message) {
            const card = document.getElementById(`${testId}-test`);
            const result = card.querySelector('.test-result');
            
            card.className = `test-card p-4 rounded-lg ${status}`;
            
            const icons = {
                success: '✅',
                error: '❌',
                loading: '⏳',
                warning: '⚠️'
            };
            
            result.innerHTML = `${icons[status]} ${message}`;
            testResults[testId] = { status, message };
        }

        function updateProgress() {
            testProgress++;
            const percentage = (testProgress / totalTests) * 100;
            document.getElementById('overall-progress').style.width = `${percentage}%`;
            document.getElementById('test-status').textContent = 
                `Testing progress: ${testProgress}/${totalTests} tests completed (${Math.round(percentage)}%)`;
        }

        async function runCompleteTest() {
            console.log('🚀 Starting complete system test...');
            
            testProgress = 0;
            testResults = {};
            
            document.getElementById('test-status').textContent = 'Running comprehensive tests...';
            
            // Run all tests
            await Promise.all([
                testAPIConnectivity(),
                testAuthentication(),
                testVideoGeneration(),
                testDashboardData(),
                testUserFlow(),
                testConsoleErrors()
            ]);
            
            // Generate detailed results
            generateDetailedResults();
            
            console.log('✅ Complete system test finished');
        }

        async function testAPIConnectivity() {
            console.log('🔗 Testing API connectivity...');
            updateTestCard('api', 'loading', 'Testing API endpoints...');
            
            const endpoints = [
                '/api/auth/status',
                '/api/dashboard/stats',
                '/api/generate/history',
                'working_api.php?endpoint=auth/status'
            ];
            
            let successCount = 0;
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint);
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success !== undefined) {
                            successCount++;
                            console.log(`✅ ${endpoint}: Working`);
                        }
                    }
                } catch (error) {
                    console.error(`❌ ${endpoint}: ${error.message}`);
                }
            }
            
            if (successCount >= endpoints.length / 2) {
                updateTestCard('api', 'success', `${successCount}/${endpoints.length} endpoints working`);
            } else {
                updateTestCard('api', 'error', `Only ${successCount}/${endpoints.length} endpoints working`);
            }
            
            updateProgress();
        }

        async function testAuthentication() {
            console.log('🔐 Testing authentication...');
            updateTestCard('auth', 'loading', 'Testing auth flow...');
            
            try {
                // Test registration
                const regResponse = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        first_name: 'Test',
                        last_name: 'User',
                        email: `test${Date.now()}@example.com`,
                        password: 'TestPass123!'
                    })
                });
                
                if (regResponse.ok) {
                    const regData = await regResponse.json();
                    if (regData.success) {
                        console.log('✅ Registration working');
                        updateTestCard('auth', 'success', 'Registration and login working');
                    } else {
                        updateTestCard('auth', 'warning', `Registration response: ${regData.error}`);
                    }
                } else {
                    updateTestCard('auth', 'error', `Registration failed: HTTP ${regResponse.status}`);
                }
            } catch (error) {
                console.error('❌ Authentication test failed:', error);
                updateTestCard('auth', 'error', `Auth test failed: ${error.message}`);
            }
            
            updateProgress();
        }

        async function testVideoGeneration() {
            console.log('🎬 Testing video generation...');
            updateTestCard('video', 'loading', 'Testing video generation...');
            
            try {
                const response = await fetch('/api/generate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        mood: 'euphoric',
                        topic: 'nature_wildlife',
                        custom_prompt: 'Test video generation'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        console.log('✅ Video generation started successfully');
                        updateTestCard('video', 'success', 'Video generation working');
                    } else {
                        updateTestCard('video', 'warning', `Generation response: ${data.error}`);
                    }
                } else {
                    updateTestCard('video', 'error', `Generation failed: HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('❌ Video generation test failed:', error);
                updateTestCard('video', 'error', `Video test failed: ${error.message}`);
            }
            
            updateProgress();
        }

        async function testDashboardData() {
            console.log('📊 Testing dashboard data...');
            updateTestCard('dashboard', 'loading', 'Testing dashboard APIs...');
            
            try {
                const statsResponse = await fetch('/api/dashboard/stats');
                const historyResponse = await fetch('/api/generate/history');
                
                let statsWorking = false;
                let historyWorking = false;
                
                if (statsResponse.ok) {
                    const statsData = await statsResponse.json();
                    if (statsData.success) {
                        console.log('✅ Dashboard stats working');
                        statsWorking = true;
                    }
                }
                
                if (historyResponse.ok) {
                    const historyData = await historyResponse.json();
                    if (historyData.success) {
                        console.log('✅ Generation history working');
                        historyWorking = true;
                    }
                }
                
                if (statsWorking && historyWorking) {
                    updateTestCard('dashboard', 'success', 'Dashboard data APIs working');
                } else if (statsWorking || historyWorking) {
                    updateTestCard('dashboard', 'warning', 'Some dashboard APIs working');
                } else {
                    updateTestCard('dashboard', 'error', 'Dashboard APIs not working');
                }
            } catch (error) {
                console.error('❌ Dashboard test failed:', error);
                updateTestCard('dashboard', 'error', `Dashboard test failed: ${error.message}`);
            }
            
            updateProgress();
        }

        async function testUserFlow() {
            console.log('👤 Testing user flow...');
            updateTestCard('flow', 'loading', 'Testing complete user flow...');
            
            // This is a simplified test - in reality, we'd test the full flow
            try {
                // Test auth status
                const authResponse = await fetch('/api/auth/status');
                
                if (authResponse.ok) {
                    const authData = await authResponse.json();
                    console.log('✅ User flow auth check working');
                    updateTestCard('flow', 'success', 'User flow components working');
                } else {
                    updateTestCard('flow', 'warning', 'User flow partially working');
                }
            } catch (error) {
                console.error('❌ User flow test failed:', error);
                updateTestCard('flow', 'error', `User flow test failed: ${error.message}`);
            }
            
            updateProgress();
        }

        async function testConsoleErrors() {
            console.log('🐛 Testing for console errors...');
            updateTestCard('console', 'loading', 'Checking for errors...');
            
            // Check if there are any error logs
            const logElement = document.getElementById('console-log');
            const errorLogs = logElement.querySelectorAll('.log-entry.error');
            
            if (errorLogs.length === 0) {
                updateTestCard('console', 'success', 'No console errors detected');
            } else {
                updateTestCard('console', 'warning', `${errorLogs.length} console errors found`);
            }
            
            updateProgress();
        }

        function generateDetailedResults() {
            const detailedResults = document.getElementById('detailed-results');
            
            let html = '<div class="space-y-4">';
            
            for (const [testId, result] of Object.entries(testResults)) {
                const statusColor = result.status === 'success' ? 'text-green-400' : 
                                  result.status === 'error' ? 'text-red-400' : 'text-yellow-400';
                
                html += `
                    <div class="flex justify-between items-center p-3 bg-gray-800 rounded">
                        <span class="font-medium">${testId.charAt(0).toUpperCase() + testId.slice(1)} Test</span>
                        <span class="${statusColor}">${result.message}</span>
                    </div>
                `;
            }
            
            html += '</div>';
            
            const successCount = Object.values(testResults).filter(r => r.status === 'success').length;
            const totalCount = Object.keys(testResults).length;
            
            if (successCount === totalCount) {
                html = `
                    <div class="text-center p-6 bg-green-900 rounded-lg mb-4">
                        <h4 class="text-xl font-bold text-green-400 mb-2">🎉 ALL TESTS PASSED!</h4>
                        <p class="text-green-300">Your Sutradhar 2070 system is working perfectly!</p>
                    </div>
                ` + html;
            } else if (successCount > totalCount / 2) {
                html = `
                    <div class="text-center p-6 bg-yellow-900 rounded-lg mb-4">
                        <h4 class="text-xl font-bold text-yellow-400 mb-2">⚠️ MOSTLY WORKING</h4>
                        <p class="text-yellow-300">Most features are working. Some minor issues detected.</p>
                    </div>
                ` + html;
            } else {
                html = `
                    <div class="text-center p-6 bg-red-900 rounded-lg mb-4">
                        <h4 class="text-xl font-bold text-red-400 mb-2">❌ ISSUES DETECTED</h4>
                        <p class="text-red-300">Several issues need attention. Check the logs for details.</p>
                    </div>
                ` + html;
            }
            
            detailedResults.innerHTML = html;
        }

        function testHomepage() {
            window.open('index.html', '_blank');
        }

        function testDashboard() {
            window.open('dashboard.html', '_blank');
        }

        function clearLogs() {
            document.getElementById('console-log').innerHTML = '<div class="text-gray-400">Console cleared...</div>';
            console.log('🗑️ Console logs cleared');
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🎭 Sutradhar 2070 Complete System Fix Test initialized');
                console.log('Click "Run Complete Test" to start comprehensive testing');
            }, 1000);
        });
    </script>
</body>
</html>
