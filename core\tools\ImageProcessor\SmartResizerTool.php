<?php
/**
 * Smart Resizer Tool
 * Resize images with aspect ratio preservation and smart crop detection
 */

require_once __DIR__ . '/../BaseTool.php';

class SmartResizerTool extends BaseTool {
    protected $toolId = 'smart_resizer';
    
    public function process($files, $options = [], $userId = null, $operationId = null) {
        $this->validateFiles($files);
        
        $targetWidth = (int)($options['width'] ?? 800);
        $targetHeight = (int)($options['height'] ?? 600);
        $maintainAspect = $options['maintain_aspect'] ?? true;
        $cropMode = $options['crop_mode'] ?? 'center'; // center, smart, top, bottom
        $resizeMode = $options['resize_mode'] ?? 'fit'; // fit, fill, stretch
        $quality = (int)($options['quality'] ?? 85);
        $upscaleAllowed = $options['allow_upscale'] ?? false;
        
        $processedFiles = [];
        $tempFiles = [];
        
        try {
            foreach ($files as $file) {
                // Store uploaded file
                $inputPath = $this->storeUploadedFile($file, $operationId, $userId);
                $tempFiles[] = $inputPath;
                
                // Load image
                $sourceImage = $this->loadImage($inputPath);
                $sourceWidth = imagesx($sourceImage);
                $sourceHeight = imagesy($sourceImage);
                
                // Calculate dimensions
                $dimensions = $this->calculateDimensions(
                    $sourceWidth, $sourceHeight,
                    $targetWidth, $targetHeight,
                    $maintainAspect, $resizeMode, $upscaleAllowed
                );
                
                // Create output image
                $outputImage = imagecreatetruecolor($dimensions['output_width'], $dimensions['output_height']);
                
                // Preserve transparency for PNG
                if (pathinfo($file['name'], PATHINFO_EXTENSION) === 'png') {
                    imagealphablending($outputImage, false);
                    imagesavealpha($outputImage, true);
                    $transparent = imagecolorallocatealpha($outputImage, 0, 0, 0, 127);
                    imagefill($outputImage, 0, 0, $transparent);
                }
                
                // Apply resize based on mode
                switch ($resizeMode) {
                    case 'fit':
                        $this->resizeFit($sourceImage, $outputImage, $dimensions);
                        break;
                    case 'fill':
                        $this->resizeFill($sourceImage, $outputImage, $dimensions, $cropMode);
                        break;
                    case 'stretch':
                        $this->resizeStretch($sourceImage, $outputImage, $dimensions);
                        break;
                }
                
                // Generate output filename
                $baseName = pathinfo($file['name'], PATHINFO_FILENAME);
                $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                $outputPath = $this->tempDir . $operationId . '_' . $baseName . '_resized.' . $extension;
                
                // Save resized image
                $this->saveImage($outputImage, $outputPath, $extension, $quality);
                
                // Store processed file
                $storedFile = $this->storeProcessedFile($outputPath, $file['name'], $operationId, $userId);
                
                $processedFiles[] = [
                    'original_name' => $file['name'],
                    'resized_name' => basename($storedFile['stored_path']),
                    'download_url' => $storedFile['download_url'],
                    'file_size' => $storedFile['file_size'],
                    'original_dimensions' => [
                        'width' => $sourceWidth,
                        'height' => $sourceHeight
                    ],
                    'new_dimensions' => [
                        'width' => $dimensions['output_width'],
                        'height' => $dimensions['output_height']
                    ],
                    'resize_mode' => $resizeMode,
                    'crop_mode' => $cropMode,
                    'aspect_ratio_maintained' => $maintainAspect,
                    'upscaled' => $dimensions['upscaled']
                ];
                
                // Clean up
                imagedestroy($sourceImage);
                imagedestroy($outputImage);
                $tempFiles[] = $outputPath;
            }
            
            return [
                'success' => true,
                'processed_files' => $processedFiles,
                'total_files' => count($processedFiles),
                'settings_used' => [
                    'target_width' => $targetWidth,
                    'target_height' => $targetHeight,
                    'resize_mode' => $resizeMode,
                    'crop_mode' => $cropMode,
                    'maintain_aspect' => $maintainAspect
                ]
            ];
            
        } catch (Exception $e) {
            $this->cleanup($tempFiles);
            throw new ToolException("Image resizing failed: " . $e->getMessage());
        } finally {
            // Clean up temporary input files
            foreach ($tempFiles as $tempFile) {
                if (strpos($tempFile, '_input_') !== false && file_exists($tempFile)) {
                    unlink($tempFile);
                }
            }
        }
    }
    
    /**
     * Calculate output dimensions based on resize mode
     */
    private function calculateDimensions($sourceWidth, $sourceHeight, $targetWidth, $targetHeight, 
                                       $maintainAspect, $resizeMode, $upscaleAllowed) {
        $sourceRatio = $sourceWidth / $sourceHeight;
        $targetRatio = $targetWidth / $targetHeight;
        $upscaled = false;
        
        if (!$upscaleAllowed && ($targetWidth > $sourceWidth || $targetHeight > $sourceHeight)) {
            // Don't upscale - use source dimensions as maximum
            $targetWidth = min($targetWidth, $sourceWidth);
            $targetHeight = min($targetHeight, $sourceHeight);
        }
        
        switch ($resizeMode) {
            case 'fit':
                if ($maintainAspect) {
                    if ($sourceRatio > $targetRatio) {
                        // Source is wider
                        $outputWidth = $targetWidth;
                        $outputHeight = (int)($targetWidth / $sourceRatio);
                    } else {
                        // Source is taller
                        $outputHeight = $targetHeight;
                        $outputWidth = (int)($targetHeight * $sourceRatio);
                    }
                } else {
                    $outputWidth = $targetWidth;
                    $outputHeight = $targetHeight;
                }
                break;
                
            case 'fill':
                $outputWidth = $targetWidth;
                $outputHeight = $targetHeight;
                break;
                
            case 'stretch':
                $outputWidth = $targetWidth;
                $outputHeight = $targetHeight;
                break;
                
            default:
                $outputWidth = $targetWidth;
                $outputHeight = $targetHeight;
        }
        
        if (($outputWidth > $sourceWidth || $outputHeight > $sourceHeight) && 
            ($sourceWidth < $targetWidth || $sourceHeight < $targetHeight)) {
            $upscaled = true;
        }
        
        return [
            'output_width' => $outputWidth,
            'output_height' => $outputHeight,
            'source_width' => $sourceWidth,
            'source_height' => $sourceHeight,
            'upscaled' => $upscaled
        ];
    }
    
    /**
     * Resize to fit within dimensions (letterbox/pillarbox)
     */
    private function resizeFit($sourceImage, $outputImage, $dimensions) {
        $outputWidth = $dimensions['output_width'];
        $outputHeight = $dimensions['output_height'];
        $sourceWidth = $dimensions['source_width'];
        $sourceHeight = $dimensions['source_height'];
        
        // Center the image
        $destX = 0;
        $destY = 0;
        
        imagecopyresampled(
            $outputImage, $sourceImage,
            $destX, $destY, 0, 0,
            $outputWidth, $outputHeight,
            $sourceWidth, $sourceHeight
        );
    }
    
    /**
     * Resize to fill dimensions (crop if necessary)
     */
    private function resizeFill($sourceImage, $outputImage, $dimensions, $cropMode) {
        $outputWidth = $dimensions['output_width'];
        $outputHeight = $dimensions['output_height'];
        $sourceWidth = $dimensions['source_width'];
        $sourceHeight = $dimensions['source_height'];
        
        $sourceRatio = $sourceWidth / $sourceHeight;
        $targetRatio = $outputWidth / $outputHeight;
        
        if ($sourceRatio > $targetRatio) {
            // Source is wider - crop width
            $newSourceWidth = (int)($sourceHeight * $targetRatio);
            $newSourceHeight = $sourceHeight;
            
            switch ($cropMode) {
                case 'smart':
                    $sourceX = $this->findBestCropX($sourceImage, $sourceWidth, $newSourceWidth);
                    break;
                case 'left':
                    $sourceX = 0;
                    break;
                case 'right':
                    $sourceX = $sourceWidth - $newSourceWidth;
                    break;
                case 'center':
                default:
                    $sourceX = (int)(($sourceWidth - $newSourceWidth) / 2);
            }
            $sourceY = 0;
        } else {
            // Source is taller - crop height
            $newSourceWidth = $sourceWidth;
            $newSourceHeight = (int)($sourceWidth / $targetRatio);
            
            switch ($cropMode) {
                case 'smart':
                    $sourceY = $this->findBestCropY($sourceImage, $sourceHeight, $newSourceHeight);
                    break;
                case 'top':
                    $sourceY = 0;
                    break;
                case 'bottom':
                    $sourceY = $sourceHeight - $newSourceHeight;
                    break;
                case 'center':
                default:
                    $sourceY = (int)(($sourceHeight - $newSourceHeight) / 2);
            }
            $sourceX = 0;
        }
        
        imagecopyresampled(
            $outputImage, $sourceImage,
            0, 0, $sourceX, $sourceY,
            $outputWidth, $outputHeight,
            $newSourceWidth, $newSourceHeight
        );
    }
    
    /**
     * Resize by stretching (may distort aspect ratio)
     */
    private function resizeStretch($sourceImage, $outputImage, $dimensions) {
        imagecopyresampled(
            $outputImage, $sourceImage,
            0, 0, 0, 0,
            $dimensions['output_width'], $dimensions['output_height'],
            $dimensions['source_width'], $dimensions['source_height']
        );
    }
    
    /**
     * Find best horizontal crop position using edge detection
     */
    private function findBestCropX($sourceImage, $sourceWidth, $cropWidth) {
        $sourceHeight = imagesy($sourceImage);
        $bestScore = -1;
        $bestX = (int)(($sourceWidth - $cropWidth) / 2); // Default to center
        
        // Sample positions
        $positions = [
            0, // Left
            (int)(($sourceWidth - $cropWidth) / 4), // Left-center
            (int)(($sourceWidth - $cropWidth) / 2), // Center
            (int)(3 * ($sourceWidth - $cropWidth) / 4), // Right-center
            $sourceWidth - $cropWidth // Right
        ];
        
        foreach ($positions as $x) {
            if ($x < 0 || $x + $cropWidth > $sourceWidth) continue;
            
            $score = $this->calculateCropScore($sourceImage, $x, 0, $cropWidth, $sourceHeight);
            if ($score > $bestScore) {
                $bestScore = $score;
                $bestX = $x;
            }
        }
        
        return $bestX;
    }
    
    /**
     * Find best vertical crop position using edge detection
     */
    private function findBestCropY($sourceImage, $sourceHeight, $cropHeight) {
        $sourceWidth = imagesx($sourceImage);
        $bestScore = -1;
        $bestY = (int)(($sourceHeight - $cropHeight) / 2); // Default to center
        
        // Sample positions
        $positions = [
            0, // Top
            (int)(($sourceHeight - $cropHeight) / 4), // Top-center
            (int)(($sourceHeight - $cropHeight) / 2), // Center
            (int)(3 * ($sourceHeight - $cropHeight) / 4), // Bottom-center
            $sourceHeight - $cropHeight // Bottom
        ];
        
        foreach ($positions as $y) {
            if ($y < 0 || $y + $cropHeight > $sourceHeight) continue;
            
            $score = $this->calculateCropScore($sourceImage, 0, $y, $sourceWidth, $cropHeight);
            if ($score > $bestScore) {
                $bestScore = $score;
                $bestY = $y;
            }
        }
        
        return $bestY;
    }
    
    /**
     * Calculate crop score based on edge density and variance
     */
    private function calculateCropScore($image, $x, $y, $width, $height) {
        $edgeCount = 0;
        $totalPixels = 0;
        $colorSum = 0;
        
        // Sample every 10th pixel for performance
        for ($py = $y; $py < $y + $height; $py += 10) {
            for ($px = $x; $px < $x + $width; $px += 10) {
                if ($px >= imagesx($image) || $py >= imagesy($image)) continue;
                
                $color = imagecolorat($image, $px, $py);
                $r = ($color >> 16) & 0xFF;
                $g = ($color >> 8) & 0xFF;
                $b = $color & 0xFF;
                $gray = (int)(($r + $g + $b) / 3);
                
                $colorSum += $gray;
                $totalPixels++;
                
                // Check for edges
                if ($px > $x && $py > $y) {
                    $prevColor = imagecolorat($image, $px - 10, $py);
                    $prevGray = (int)((($prevColor >> 16) & 0xFF) + (($prevColor >> 8) & 0xFF) + ($prevColor & 0xFF)) / 3;
                    
                    if (abs($gray - $prevGray) > 30) {
                        $edgeCount++;
                    }
                }
            }
        }
        
        if ($totalPixels === 0) return 0;
        
        // Score based on edge density and color variance
        $edgeDensity = $edgeCount / $totalPixels;
        $avgColor = $colorSum / $totalPixels;
        
        // Prefer areas with more edges (more interesting content)
        // and avoid very dark or very bright areas
        $colorScore = 1 - abs($avgColor - 128) / 128;
        
        return $edgeDensity * 0.7 + $colorScore * 0.3;
    }
}
?>
