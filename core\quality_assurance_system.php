<?php
/**
 * Quality Assurance System - Automated quality checks for video generation
 * Implements comprehensive validation for videos, audio sync, and content quality
 */

require_once 'advanced_ffmpeg_processor.php';

class QualityAssuranceSystem {
    private $ffmpegProcessor;
    private $qualityThresholds;
    private $validationRules;
    private $reportDir;
    
    public function __construct($config = []) {
        $this->ffmpegProcessor = new AdvancedFFmpegProcessor($config);
        $this->reportDir = $config['report_dir'] ?? __DIR__ . '/../data/qa_reports/';
        
        $this->initializeQualityThresholds();
        $this->initializeValidationRules();
        $this->ensureReportDirectory();
    }
    
    /**
     * Perform comprehensive quality assurance on generated video
     */
    public function performQualityAssurance($videoFile, $audioFile = null, $jobId = null) {
        $startTime = microtime(true);
        $reportId = $jobId ?? uniqid('qa_');
        
        $qaReport = [
            'report_id' => $reportId,
            'timestamp' => date('Y-m-d H:i:s'),
            'video_file' => $videoFile,
            'audio_file' => $audioFile,
            'checks_performed' => [],
            'overall_score' => 0,
            'passed' => false,
            'issues' => [],
            'recommendations' => [],
            'processing_time' => 0
        ];
        
        try {
            // 1. File Existence and Accessibility Check
            $qaReport['checks_performed'][] = $this->checkFileExistence($videoFile, $audioFile);
            
            // 2. Video Technical Quality Check
            $qaReport['checks_performed'][] = $this->checkVideoTechnicalQuality($videoFile);
            
            // 3. Audio Quality Check (if audio file provided)
            if ($audioFile) {
                $qaReport['checks_performed'][] = $this->checkAudioQuality($audioFile);
                
                // 4. Audio-Video Synchronization Check
                $qaReport['checks_performed'][] = $this->checkAudioVideoSync($videoFile, $audioFile);
            }
            
            // 5. Content Validation Check
            $qaReport['checks_performed'][] = $this->checkContentValidation($videoFile);
            
            // 6. Platform Compliance Check
            $qaReport['checks_performed'][] = $this->checkPlatformCompliance($videoFile);
            
            // 7. Performance Metrics Check
            $qaReport['checks_performed'][] = $this->checkPerformanceMetrics($videoFile);
            
            // Calculate overall score and determine pass/fail
            $this->calculateOverallScore($qaReport);
            
            // Generate recommendations
            $this->generateRecommendations($qaReport);
            
        } catch (Exception $e) {
            $qaReport['error'] = $e->getMessage();
            $qaReport['passed'] = false;
        }
        
        $qaReport['processing_time'] = microtime(true) - $startTime;
        
        // Save report
        $this->saveQAReport($qaReport);
        
        return $qaReport;
    }
    
    /**
     * Check file existence and accessibility
     */
    private function checkFileExistence($videoFile, $audioFile = null) {
        $check = [
            'name' => 'File Existence Check',
            'category' => 'basic',
            'passed' => true,
            'score' => 100,
            'details' => [],
            'issues' => []
        ];
        
        // Check video file
        if (!file_exists($videoFile)) {
            $check['passed'] = false;
            $check['score'] = 0;
            $check['issues'][] = 'Video file does not exist';
        } else {
            $fileSize = filesize($videoFile);
            $check['details']['video_file_size'] = $fileSize;
            
            if ($fileSize < 1024) { // Less than 1KB
                $check['passed'] = false;
                $check['score'] = 20;
                $check['issues'][] = 'Video file is too small (likely corrupted)';
            } elseif ($fileSize < 10240) { // Less than 10KB
                $check['score'] = 60;
                $check['issues'][] = 'Video file is very small (may be incomplete)';
            }
        }
        
        // Check audio file if provided
        if ($audioFile) {
            if (!file_exists($audioFile)) {
                $check['passed'] = false;
                $check['score'] = min($check['score'], 50);
                $check['issues'][] = 'Audio file does not exist';
            } else {
                $audioSize = filesize($audioFile);
                $check['details']['audio_file_size'] = $audioSize;
                
                if ($audioSize < 1024) {
                    $check['passed'] = false;
                    $check['score'] = min($check['score'], 20);
                    $check['issues'][] = 'Audio file is too small (likely corrupted)';
                }
            }
        }
        
        return $check;
    }
    
    /**
     * Check video technical quality
     */
    private function checkVideoTechnicalQuality($videoFile) {
        $check = [
            'name' => 'Video Technical Quality',
            'category' => 'technical',
            'passed' => true,
            'score' => 100,
            'details' => [],
            'issues' => []
        ];
        
        try {
            $videoInfo = $this->ffmpegProcessor->getVideoInfo($videoFile);
            
            if (!$videoInfo || !$videoInfo['video']) {
                $check['passed'] = false;
                $check['score'] = 0;
                $check['issues'][] = 'Unable to read video information';
                return $check;
            }
            
            $video = $videoInfo['video'];
            $check['details'] = $video;
            
            // Check resolution
            $width = intval($video['width'] ?? 0);
            $height = intval($video['height'] ?? 0);
            
            if ($width < 720 || $height < 1280) {
                $check['score'] -= 20;
                $check['issues'][] = 'Resolution below recommended minimum (720x1280)';
            }
            
            // Check frame rate
            $fps = $this->parseFPS($video['r_frame_rate'] ?? '30/1');
            if ($fps < 24) {
                $check['score'] -= 15;
                $check['issues'][] = 'Frame rate below recommended minimum (24 FPS)';
            } elseif ($fps > 60) {
                $check['score'] -= 5;
                $check['issues'][] = 'Frame rate higher than necessary (may increase file size)';
            }
            
            // Check duration
            $duration = floatval($videoInfo['duration'] ?? 0);
            if ($duration < 25 || $duration > 35) {
                $check['score'] -= 10;
                $check['issues'][] = 'Duration outside target range (25-35 seconds)';
            }
            
            // Check codec
            $codec = $video['codec_name'] ?? '';
            if (!in_array($codec, ['h264', 'h265', 'hevc'])) {
                $check['score'] -= 15;
                $check['issues'][] = 'Video codec not optimized for web delivery';
            }
            
            // Check bitrate
            $bitRate = intval($video['bit_rate'] ?? 0);
            if ($bitRate > 0) {
                if ($bitRate < 1000000) { // Less than 1 Mbps
                    $check['score'] -= 10;
                    $check['issues'][] = 'Video bitrate may be too low for quality';
                } elseif ($bitRate > 10000000) { // More than 10 Mbps
                    $check['score'] -= 5;
                    $check['issues'][] = 'Video bitrate higher than necessary';
                }
            }
            
        } catch (Exception $e) {
            $check['passed'] = false;
            $check['score'] = 0;
            $check['issues'][] = 'Error analyzing video: ' . $e->getMessage();
        }
        
        if ($check['score'] < $this->qualityThresholds['video_technical']) {
            $check['passed'] = false;
        }
        
        return $check;
    }
    
    /**
     * Check audio quality
     */
    private function checkAudioQuality($audioFile) {
        $check = [
            'name' => 'Audio Quality Check',
            'category' => 'audio',
            'passed' => true,
            'score' => 100,
            'details' => [],
            'issues' => []
        ];
        
        try {
            $audioInfo = $this->ffmpegProcessor->getVideoInfo($audioFile);
            
            if (!$audioInfo || !$audioInfo['audio']) {
                $check['passed'] = false;
                $check['score'] = 0;
                $check['issues'][] = 'Unable to read audio information';
                return $check;
            }
            
            $audio = $audioInfo['audio'];
            $check['details'] = $audio;
            
            // Check sample rate
            $sampleRate = intval($audio['sample_rate'] ?? 0);
            if ($sampleRate < 44100) {
                $check['score'] -= 15;
                $check['issues'][] = 'Audio sample rate below recommended (44.1kHz)';
            }
            
            // Check channels
            $channels = intval($audio['channels'] ?? 0);
            if ($channels < 1) {
                $check['passed'] = false;
                $check['score'] = 0;
                $check['issues'][] = 'No audio channels detected';
            } elseif ($channels > 2) {
                $check['score'] -= 5;
                $check['issues'][] = 'More than stereo channels (may increase file size)';
            }
            
            // Check codec
            $codec = $audio['codec_name'] ?? '';
            if (!in_array($codec, ['aac', 'mp3', 'opus'])) {
                $check['score'] -= 10;
                $check['issues'][] = 'Audio codec not optimized for web delivery';
            }
            
            // Check bitrate
            $bitRate = intval($audio['bit_rate'] ?? 0);
            if ($bitRate > 0) {
                if ($bitRate < 128000) { // Less than 128 kbps
                    $check['score'] -= 10;
                    $check['issues'][] = 'Audio bitrate may be too low for quality';
                } elseif ($bitRate > 320000) { // More than 320 kbps
                    $check['score'] -= 5;
                    $check['issues'][] = 'Audio bitrate higher than necessary';
                }
            }
            
        } catch (Exception $e) {
            $check['passed'] = false;
            $check['score'] = 0;
            $check['issues'][] = 'Error analyzing audio: ' . $e->getMessage();
        }
        
        if ($check['score'] < $this->qualityThresholds['audio_quality']) {
            $check['passed'] = false;
        }
        
        return $check;
    }
    
    /**
     * Check audio-video synchronization
     */
    private function checkAudioVideoSync($videoFile, $audioFile) {
        $check = [
            'name' => 'Audio-Video Synchronization',
            'category' => 'sync',
            'passed' => true,
            'score' => 100,
            'details' => [],
            'issues' => []
        ];
        
        try {
            $videoInfo = $this->ffmpegProcessor->getVideoInfo($videoFile);
            $audioInfo = $this->ffmpegProcessor->getVideoInfo($audioFile);
            
            $videoDuration = floatval($videoInfo['duration'] ?? 0);
            $audioDuration = floatval($audioInfo['duration'] ?? 0);
            
            $check['details']['video_duration'] = $videoDuration;
            $check['details']['audio_duration'] = $audioDuration;
            
            $durationDiff = abs($videoDuration - $audioDuration);
            $check['details']['duration_difference'] = $durationDiff;
            
            if ($durationDiff > 1.0) { // More than 1 second difference
                $check['passed'] = false;
                $check['score'] = 30;
                $check['issues'][] = 'Significant duration mismatch between audio and video';
            } elseif ($durationDiff > 0.5) { // More than 0.5 second difference
                $check['score'] = 70;
                $check['issues'][] = 'Noticeable duration difference between audio and video';
            } elseif ($durationDiff > 0.1) { // More than 0.1 second difference
                $check['score'] = 90;
                $check['issues'][] = 'Minor duration difference between audio and video';
            }
            
        } catch (Exception $e) {
            $check['passed'] = false;
            $check['score'] = 0;
            $check['issues'][] = 'Error checking synchronization: ' . $e->getMessage();
        }
        
        if ($check['score'] < $this->qualityThresholds['audio_sync']) {
            $check['passed'] = false;
        }
        
        return $check;
    }
    
    /**
     * Check content validation
     */
    private function checkContentValidation($videoFile) {
        $check = [
            'name' => 'Content Validation',
            'category' => 'content',
            'passed' => true,
            'score' => 100,
            'details' => [],
            'issues' => []
        ];
        
        try {
            // Check if video has actual content (not just black frames)
            $thumbnailFile = $this->reportDir . 'thumb_' . uniqid() . '.jpg';
            $result = $this->ffmpegProcessor->createThumbnail($videoFile, $thumbnailFile, 5);
            
            if ($result['success'] && file_exists($thumbnailFile)) {
                $imageSize = getimagesize($thumbnailFile);
                if ($imageSize) {
                    $check['details']['thumbnail_created'] = true;
                    $check['details']['thumbnail_dimensions'] = $imageSize[0] . 'x' . $imageSize[1];
                    
                    // Basic content analysis (could be expanded with AI)
                    $fileSize = filesize($thumbnailFile);
                    if ($fileSize < 5000) { // Very small thumbnail might indicate black/empty frames
                        $check['score'] -= 20;
                        $check['issues'][] = 'Video may contain mostly empty or black frames';
                    }
                } else {
                    $check['score'] -= 15;
                    $check['issues'][] = 'Unable to analyze video content';
                }
                
                // Clean up thumbnail
                unlink($thumbnailFile);
            } else {
                $check['score'] -= 25;
                $check['issues'][] = 'Unable to extract thumbnail for content analysis';
            }
            
        } catch (Exception $e) {
            $check['score'] -= 20;
            $check['issues'][] = 'Error during content validation: ' . $e->getMessage();
        }
        
        if ($check['score'] < $this->qualityThresholds['content_validation']) {
            $check['passed'] = false;
        }
        
        return $check;
    }
    
    /**
     * Check platform compliance
     */
    private function checkPlatformCompliance($videoFile) {
        $check = [
            'name' => 'Platform Compliance',
            'category' => 'compliance',
            'passed' => true,
            'score' => 100,
            'details' => [],
            'issues' => []
        ];
        
        try {
            $videoInfo = $this->ffmpegProcessor->getVideoInfo($videoFile);
            
            if (!$videoInfo) {
                $check['passed'] = false;
                $check['score'] = 0;
                $check['issues'][] = 'Unable to analyze video for platform compliance';
                return $check;
            }
            
            $video = $videoInfo['video'];
            $fileSize = $videoInfo['format']['size'] ?? 0;
            $duration = floatval($videoInfo['duration'] ?? 0);
            
            // Check file size limits (Instagram: 100MB, TikTok: 287MB, YouTube Shorts: 256MB)
            $maxFileSize = 100 * 1024 * 1024; // 100MB for most restrictive platform
            if ($fileSize > $maxFileSize) {
                $check['score'] -= 20;
                $check['issues'][] = 'File size exceeds platform limits for some social media platforms';
            }
            
            // Check aspect ratio (should be 9:16 for vertical videos)
            $width = intval($video['width'] ?? 0);
            $height = intval($video['height'] ?? 0);
            
            if ($width > 0 && $height > 0) {
                $aspectRatio = $width / $height;
                $targetRatio = 9 / 16;
                $ratioDiff = abs($aspectRatio - $targetRatio);
                
                if ($ratioDiff > 0.1) {
                    $check['score'] -= 15;
                    $check['issues'][] = 'Aspect ratio not optimized for vertical video platforms';
                }
            }
            
            // Check duration limits (most platforms prefer 15-60 seconds)
            if ($duration > 60) {
                $check['score'] -= 10;
                $check['issues'][] = 'Duration exceeds optimal length for some platforms';
            } elseif ($duration < 15) {
                $check['score'] -= 5;
                $check['issues'][] = 'Duration below optimal length for engagement';
            }
            
            $check['details']['file_size_mb'] = round($fileSize / 1024 / 1024, 2);
            $check['details']['aspect_ratio'] = round($aspectRatio, 3);
            $check['details']['duration'] = $duration;
            
        } catch (Exception $e) {
            $check['score'] -= 20;
            $check['issues'][] = 'Error checking platform compliance: ' . $e->getMessage();
        }
        
        if ($check['score'] < $this->qualityThresholds['platform_compliance']) {
            $check['passed'] = false;
        }
        
        return $check;
    }

    /**
     * Check performance metrics
     */
    private function checkPerformanceMetrics($videoFile) {
        $check = [
            'name' => 'Performance Metrics',
            'category' => 'performance',
            'passed' => true,
            'score' => 100,
            'details' => [],
            'issues' => []
        ];

        try {
            $videoInfo = $this->ffmpegProcessor->getVideoInfo($videoFile);
            $fileSize = $videoInfo['format']['size'] ?? 0;
            $duration = floatval($videoInfo['duration'] ?? 0);

            // Calculate bitrate efficiency
            if ($duration > 0) {
                $avgBitrate = ($fileSize * 8) / $duration; // bits per second
                $check['details']['average_bitrate'] = round($avgBitrate);

                // Check if bitrate is efficient for the content
                $targetBitrate = 2000000; // 2 Mbps target
                $bitrateEfficiency = min(100, ($targetBitrate / max($avgBitrate, 1)) * 100);

                if ($bitrateEfficiency < 50) {
                    $check['score'] -= 15;
                    $check['issues'][] = 'Video bitrate not optimized for file size';
                }
            }

            // Check encoding efficiency
            $video = $videoInfo['video'];
            $codec = $video['codec_name'] ?? '';

            if ($codec === 'h264') {
                $check['details']['encoding_efficiency'] = 'Good';
            } elseif ($codec === 'h265' || $codec === 'hevc') {
                $check['details']['encoding_efficiency'] = 'Excellent';
            } else {
                $check['score'] -= 10;
                $check['details']['encoding_efficiency'] = 'Poor';
                $check['issues'][] = 'Video codec not optimized for efficiency';
            }

        } catch (Exception $e) {
            $check['score'] -= 10;
            $check['issues'][] = 'Error checking performance metrics: ' . $e->getMessage();
        }

        if ($check['score'] < $this->qualityThresholds['performance']) {
            $check['passed'] = false;
        }

        return $check;
    }

    /**
     * Calculate overall quality score
     */
    private function calculateOverallScore(&$qaReport) {
        $totalScore = 0;
        $totalWeight = 0;
        $allPassed = true;

        $weights = [
            'basic' => 25,
            'technical' => 20,
            'audio' => 15,
            'sync' => 15,
            'content' => 15,
            'compliance' => 10,
            'performance' => 10
        ];

        foreach ($qaReport['checks_performed'] as $check) {
            $category = $check['category'];
            $weight = $weights[$category] ?? 10;

            $totalScore += $check['score'] * $weight;
            $totalWeight += $weight * 100; // 100 is max score

            if (!$check['passed']) {
                $allPassed = false;
                $qaReport['issues'] = array_merge($qaReport['issues'], $check['issues']);
            }
        }

        $qaReport['overall_score'] = $totalWeight > 0 ? round($totalScore / $totalWeight * 100, 1) : 0;
        $qaReport['passed'] = $allPassed && $qaReport['overall_score'] >= $this->qualityThresholds['overall'];
    }

    /**
     * Generate recommendations based on QA results
     */
    private function generateRecommendations(&$qaReport) {
        $recommendations = [];

        foreach ($qaReport['checks_performed'] as $check) {
            if (!$check['passed'] || $check['score'] < 90) {
                switch ($check['category']) {
                    case 'technical':
                        if (in_array('Resolution below recommended minimum', $check['issues'])) {
                            $recommendations[] = 'Increase video resolution to at least 720x1280 for better quality';
                        }
                        if (in_array('Frame rate below recommended minimum', $check['issues'])) {
                            $recommendations[] = 'Increase frame rate to at least 24 FPS for smoother playback';
                        }
                        break;

                    case 'audio':
                        if (in_array('Audio sample rate below recommended', $check['issues'])) {
                            $recommendations[] = 'Use 44.1kHz or higher sample rate for better audio quality';
                        }
                        break;

                    case 'sync':
                        if (in_array('Significant duration mismatch', $check['issues'])) {
                            $recommendations[] = 'Re-sync audio and video to match durations exactly';
                        }
                        break;

                    case 'compliance':
                        if (in_array('File size exceeds platform limits', $check['issues'])) {
                            $recommendations[] = 'Reduce file size by optimizing encoding settings or reducing bitrate';
                        }
                        break;
                }
            }
        }

        // Add general recommendations based on overall score
        if ($qaReport['overall_score'] < 70) {
            $recommendations[] = 'Consider regenerating the video with improved settings';
        } elseif ($qaReport['overall_score'] < 85) {
            $recommendations[] = 'Minor optimizations could improve video quality';
        }

        $qaReport['recommendations'] = array_unique($recommendations);
    }

    /**
     * Initialize quality thresholds
     */
    private function initializeQualityThresholds() {
        $this->qualityThresholds = [
            'overall' => 75,
            'video_technical' => 70,
            'audio_quality' => 70,
            'audio_sync' => 80,
            'content_validation' => 60,
            'platform_compliance' => 70,
            'performance' => 60
        ];
    }

    /**
     * Initialize validation rules
     */
    private function initializeValidationRules() {
        $this->validationRules = [
            'min_duration' => 15,
            'max_duration' => 60,
            'min_resolution' => ['width' => 720, 'height' => 1280],
            'max_file_size' => 100 * 1024 * 1024, // 100MB
            'supported_video_codecs' => ['h264', 'h265', 'hevc'],
            'supported_audio_codecs' => ['aac', 'mp3', 'opus'],
            'min_fps' => 24,
            'max_fps' => 60,
            'min_audio_bitrate' => 128000,
            'max_audio_bitrate' => 320000
        ];
    }

    /**
     * Parse frame rate from FFmpeg format
     */
    private function parseFPS($frameRate) {
        if (strpos($frameRate, '/') !== false) {
            $parts = explode('/', $frameRate);
            return count($parts) === 2 ? floatval($parts[0]) / floatval($parts[1]) : 30;
        }
        return floatval($frameRate);
    }

    /**
     * Save QA report to file
     */
    private function saveQAReport($report) {
        $reportFile = $this->reportDir . 'qa_report_' . $report['report_id'] . '.json';
        file_put_contents($reportFile, json_encode($report, JSON_PRETTY_PRINT), LOCK_EX);
    }

    /**
     * Ensure report directory exists
     */
    private function ensureReportDirectory() {
        if (!is_dir($this->reportDir)) {
            mkdir($this->reportDir, 0755, true);
        }
    }

    /**
     * Get QA report by ID
     */
    public function getQAReport($reportId) {
        $reportFile = $this->reportDir . 'qa_report_' . $reportId . '.json';

        if (file_exists($reportFile)) {
            return json_decode(file_get_contents($reportFile), true);
        }

        return null;
    }

    /**
     * Get quality thresholds
     */
    public function getQualityThresholds() {
        return $this->qualityThresholds;
    }

    /**
     * Update quality thresholds
     */
    public function updateQualityThresholds($thresholds) {
        $this->qualityThresholds = array_merge($this->qualityThresholds, $thresholds);
    }

    /**
     * Quick quality check (basic validation only)
     */
    public function quickQualityCheck($videoFile) {
        $checks = [];

        // File existence
        $checks[] = $this->checkFileExistence($videoFile);

        // Basic technical check
        $checks[] = $this->checkVideoTechnicalQuality($videoFile);

        $passedCount = count(array_filter($checks, function($check) {
            return $check['passed'];
        }));

        return [
            'passed' => $passedCount === count($checks),
            'score' => $passedCount > 0 ? array_sum(array_column($checks, 'score')) / count($checks) : 0,
            'checks' => $checks
        ];
    }
}
