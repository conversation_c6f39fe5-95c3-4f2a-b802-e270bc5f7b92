# Developer Guide

## 🚀 Overview

This guide provides comprehensive information for developers working on the Sutradhar Video Generation Platform. It covers code structure, development workflows, extension guidelines, and best practices.

## 📁 Project Structure

```
sutradhar/
├── assets/                 # Static assets (CSS, JS, images)
│   ├── css/               # Stylesheets
│   ├── js/                # JavaScript files
│   ├── images/            # Static images
│   └── fonts/             # Custom fonts
├── config/                # Configuration files
│   ├── settings.json      # Main configuration
│   └── database.sql       # Database schema
├── core/                  # Core application logic
│   ├── database_manager.php
│   ├── user_manager.php
│   ├── credit_manager.php
│   ├── huggingface_video_engine.php
│   ├── real_video_engine.php
│   ├── optimized_video_engine.php
│   ├── background_job_processor.php
│   └── tools/             # Tools engine classes
│       ├── ImageProcessor.php
│       ├── GeneratorTools.php
│       ├── UtilityTools.php
│       ├── AdvancedTools.php
│       └── ToolsManager.php
├── docs/                  # Documentation
├── public/                # Web-accessible files
│   ├── index.php          # Main entry point
│   ├── api_unified.php    # API gateway
│   ├── tools/             # Tools interface
│   ├── videos/            # Generated videos
│   ├── thumbnails/        # Video thumbnails
│   └── uploads/           # User uploads
├── temp/                  # Temporary files
├── logs/                  # Application logs
└── tests/                 # Test files
```

## 🏗️ Core Architecture

### Database Manager
```php
class DatabaseManager {
    private $pdo;
    private $config;
    
    public function __construct() {
        $this->loadConfig();
        $this->connect();
    }
    
    public function select($table, $conditions = [], $options = []) {
        $sql = "SELECT * FROM {$table}";
        $params = [];
        
        if (!empty($conditions)) {
            $sql .= " WHERE " . $this->buildWhereClause($conditions, $params);
        }
        
        if (isset($options['order'])) {
            $sql .= " ORDER BY {$options['order']}";
        }
        
        if (isset($options['limit'])) {
            $sql .= " LIMIT {$options['limit']}";
        }
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function insert($table, $data) {
        $columns = implode(', ', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $stmt = $this->pdo->prepare($sql);
        
        return $stmt->execute($data);
    }
    
    public function update($table, $data, $conditions) {
        $setClause = [];
        foreach ($data as $key => $value) {
            $setClause[] = "{$key} = :{$key}";
        }
        
        $params = [];
        $whereClause = $this->buildWhereClause($conditions, $params);
        
        $sql = "UPDATE {$table} SET " . implode(', ', $setClause) . " WHERE {$whereClause}";
        $stmt = $this->pdo->prepare($sql);
        
        return $stmt->execute(array_merge($data, $params));
    }
}
```

### API Gateway Pattern
```php
class APIGateway {
    private $routes = [];
    private $middleware = [];
    
    public function addRoute($method, $endpoint, $handler) {
        $this->routes[$method][$endpoint] = $handler;
    }
    
    public function addMiddleware($middleware) {
        $this->middleware[] = $middleware;
    }
    
    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $endpoint = $_GET['endpoint'] ?? '';
        
        // Apply middleware
        foreach ($this->middleware as $middleware) {
            $result = $middleware->handle();
            if ($result !== true) {
                return $this->sendResponse($result);
            }
        }
        
        // Route request
        if (isset($this->routes[$method][$endpoint])) {
            $handler = $this->routes[$method][$endpoint];
            $result = $handler->handle();
            return $this->sendResponse($result);
        }
        
        return $this->sendResponse([
            'success' => false,
            'error' => 'Endpoint not found'
        ], 404);
    }
    
    private function sendResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
    }
}
```

## 🛠️ Tools Development

### Creating a New Tool
```php
abstract class BaseTool {
    protected $name;
    protected $description;
    protected $creditCost;
    protected $maxFileSize;
    protected $allowedFormats;
    
    abstract public function process($files, $options = []);
    
    public function validateFiles($files) {
        foreach ($files as $file) {
            if ($file['size'] > $this->maxFileSize) {
                throw new Exception("File too large: {$file['name']}");
            }
            
            $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            if (!in_array($extension, $this->allowedFormats)) {
                throw new Exception("Unsupported format: {$extension}");
            }
        }
    }
    
    public function getInfo() {
        return [
            'name' => $this->name,
            'description' => $this->description,
            'credit_cost' => $this->creditCost,
            'max_file_size' => $this->maxFileSize,
            'allowed_formats' => $this->allowedFormats
        ];
    }
}

// Example: Format Converter Tool
class FormatConverterTool extends BaseTool {
    protected $name = 'Multi-Format Converter';
    protected $description = 'Convert images between different formats';
    protected $creditCost = 1;
    protected $maxFileSize = 10485760; // 10MB
    protected $allowedFormats = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];
    
    public function process($files, $options = []) {
        $this->validateFiles($files);
        
        $outputFormat = $options['output_format'] ?? 'png';
        $quality = $options['quality'] ?? 85;
        $results = [];
        
        foreach ($files as $file) {
            $result = $this->convertImage($file, $outputFormat, $quality);
            $results[] = $result;
        }
        
        return $results;
    }
    
    private function convertImage($file, $format, $quality) {
        $image = $this->loadImage($file['tmp_name']);
        $outputPath = $this->generateOutputPath($file['name'], $format);
        
        switch ($format) {
            case 'jpg':
            case 'jpeg':
                imagejpeg($image, $outputPath, $quality);
                break;
            case 'png':
                imagepng($image, $outputPath, 9 - ($quality / 10));
                break;
            case 'webp':
                imagewebp($image, $outputPath, $quality);
                break;
            default:
                throw new Exception("Unsupported output format: {$format}");
        }
        
        imagedestroy($image);
        
        return [
            'original_name' => $file['name'],
            'output_path' => $outputPath,
            'file_size' => filesize($outputPath)
        ];
    }
}
```

### Tool Registration
```php
class ToolsManager {
    private $tools = [];
    
    public function registerTool($toolClass) {
        $tool = new $toolClass();
        $this->tools[$tool->getName()] = $tool;
    }
    
    public function executeTool($toolName, $files, $options = []) {
        if (!isset($this->tools[$toolName])) {
            throw new Exception("Tool not found: {$toolName}");
        }
        
        $tool = $this->tools[$toolName];
        
        // Check user credits
        $this->checkCredits($tool->getCreditCost());
        
        // Process files
        $result = $tool->process($files, $options);
        
        // Deduct credits
        $this->deductCredits($tool->getCreditCost());
        
        // Log usage
        $this->logToolUsage($toolName, $tool->getCreditCost());
        
        return $result;
    }
    
    public function getAvailableTools() {
        $tools = [];
        foreach ($this->tools as $name => $tool) {
            $tools[$name] = $tool->getInfo();
        }
        return $tools;
    }
}
```

## 🎨 Frontend Development

### JavaScript Architecture
```javascript
// Main application class
class SutradharApp {
    constructor() {
        this.api = new APIClient();
        this.ui = new UIManager();
        this.tools = new ToolsManager();
        this.videoGenerator = new VideoGenerator();
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadUserData();
        this.initializeComponents();
    }
    
    setupEventListeners() {
        document.addEventListener('DOMContentLoaded', () => {
            this.ui.showLoadingComplete();
        });
        
        // Global error handler
        window.addEventListener('error', (event) => {
            this.handleError(event.error);
        });
    }
}

// API Client
class APIClient {
    constructor(baseUrl = '/api_unified.php') {
        this.baseUrl = baseUrl;
        this.csrfToken = this.getCSRFToken();
    }
    
    async request(endpoint, method = 'GET', data = null) {
        const config = {
            method,
            credentials: 'include',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        };
        
        if (method === 'POST') {
            if (data instanceof FormData) {
                config.headers['X-CSRF-Token'] = this.csrfToken;
                config.body = data;
            } else {
                config.headers['Content-Type'] = 'application/json';
                config.headers['X-CSRF-Token'] = this.csrfToken;
                config.body = JSON.stringify(data);
            }
        }
        
        try {
            const response = await fetch(`${this.baseUrl}?endpoint=${endpoint}`, config);
            const result = await response.json();
            
            if (!result.success) {
                throw new APIError(result.error, result.error_code);
            }
            
            return result.data;
        } catch (error) {
            if (error instanceof APIError) {
                throw error;
            }
            throw new APIError('Network error occurred');
        }
    }
}

// Custom error class
class APIError extends Error {
    constructor(message, code = null) {
        super(message);
        this.name = 'APIError';
        this.code = code;
    }
}
```

### CSS Architecture (SCSS)
```scss
// Variables
:root {
    --primary-color: #00ff88;
    --secondary-color: #0088ff;
    --tertiary-color: #ff0088;
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a1a1a;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

// Mixins
@mixin glassmorphism {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
}

@mixin neon-glow($color: var(--primary-color)) {
    box-shadow: 
        0 0 5px $color,
        0 0 10px $color,
        0 0 15px $color,
        0 0 20px $color;
}

@mixin hover-lift {
    transition: var(--transition);
    
    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }
}

// Component styles
.tool-card {
    @include glassmorphism;
    @include hover-lift;
    
    padding: 1.5rem;
    margin: 1rem;
    
    &:hover {
        border-color: var(--primary-color);
        @include neon-glow(var(--primary-color));
    }
    
    .tool-icon {
        width: 48px;
        height: 48px;
        margin-bottom: 1rem;
        filter: drop-shadow(0 0 10px var(--primary-color));
    }
    
    .tool-title {
        color: var(--text-primary);
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .tool-description {
        color: var(--text-secondary);
        font-size: 0.9rem;
        line-height: 1.4;
    }
}
```

## 🧪 Testing

### Unit Testing
```php
use PHPUnit\Framework\TestCase;

class FormatConverterToolTest extends TestCase {
    private $tool;
    
    protected function setUp(): void {
        $this->tool = new FormatConverterTool();
    }
    
    public function testConvertPngToJpg() {
        $testFile = [
            'name' => 'test.png',
            'tmp_name' => __DIR__ . '/fixtures/test.png',
            'size' => 1024
        ];
        
        $result = $this->tool->process([$testFile], [
            'output_format' => 'jpg',
            'quality' => 85
        ]);
        
        $this->assertCount(1, $result);
        $this->assertStringEndsWith('.jpg', $result[0]['output_path']);
        $this->assertFileExists($result[0]['output_path']);
    }
    
    public function testInvalidFormat() {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Unsupported output format');
        
        $testFile = [
            'name' => 'test.png',
            'tmp_name' => __DIR__ . '/fixtures/test.png',
            'size' => 1024
        ];
        
        $this->tool->process([$testFile], [
            'output_format' => 'invalid'
        ]);
    }
}
```

### Integration Testing
```php
class VideoGenerationIntegrationTest extends TestCase {
    public function testCompleteVideoGeneration() {
        $videoEngine = new HuggingFaceVideoEngine();
        
        $segments = [
            [
                'text' => 'A beautiful sunset',
                'duration' => 10,
                'index' => 0
            ]
        ];
        
        $jobId = 'test_' . time();
        $result = $videoEngine->generateAdvancedVideo($segments, 'nature', 'cinematic', $jobId);
        
        $this->assertNotFalse($result);
        $this->assertFileExists($result);
        $this->assertGreaterThan(1000, filesize($result));
    }
}
```

## 🚀 Deployment

### Production Checklist
- [ ] Environment variables configured
- [ ] Database optimized with indexes
- [ ] SSL certificate installed
- [ ] Error logging configured
- [ ] Backup system in place
- [ ] Monitoring tools setup
- [ ] CDN configured for static assets
- [ ] Rate limiting enabled
- [ ] Security headers configured

### Docker Configuration
```dockerfile
FROM php:8.1-apache

# Install extensions
RUN apt-get update && apt-get install -y \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    ffmpeg \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install gd pdo pdo_mysql

# Enable Apache modules
RUN a2enmod rewrite

# Copy application
COPY . /var/www/html/

# Set permissions
RUN chown -R www-data:www-data /var/www/html/temp \
    && chown -R www-data:www-data /var/www/html/public/uploads

EXPOSE 80
```

## 📝 Code Standards

### PHP Standards
- Follow PSR-12 coding standards
- Use type hints for all parameters and return values
- Document all public methods with PHPDoc
- Use meaningful variable and method names
- Keep methods under 50 lines when possible

### JavaScript Standards
- Use ES6+ features
- Follow Airbnb JavaScript style guide
- Use async/await for asynchronous operations
- Implement proper error handling
- Use JSDoc for documentation

### Git Workflow
1. Create feature branch from `develop`
2. Make changes with descriptive commits
3. Write/update tests
4. Create pull request
5. Code review and approval
6. Merge to `develop`
7. Deploy to staging for testing
8. Merge to `main` for production

This developer guide provides the foundation for contributing to and extending the Sutradhar platform.
