<?php
/**
 * Comprehensive System Diagnostics for Sutradhar Engine
 * Tests all components and identifies issues for Ubuntu deployment
 */

class SystemDiagnostics {
    private $results = [];
    private $errors = [];
    private $warnings = [];
    
    public function __construct() {
        $this->createDirectories();
    }
    
    private function createDirectories() {
        $dirs = ['../temp', '../data/output_history', '../data/jobs', '../logs'];
        foreach ($dirs as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }
    
    public function runFullDiagnostics() {
        echo "🔍 SUTRADHAR ENGINE SYSTEM DIAGNOSTICS\n";
        echo "=====================================\n\n";
        
        $this->testSystemRequirements();
        $this->testPHPExtensions();
        $this->testExternalDependencies();
        $this->testFilePermissions();
        $this->testAudioGeneration();
        $this->testVideoGeneration();
        $this->testContentGeneration();
        $this->testDatabaseConnectivity();
        
        $this->generateReport();
        return $this->results;
    }
    
    private function testSystemRequirements() {
        echo "1. Testing System Requirements:\n";
        
        // PHP Version
        $phpVersion = PHP_VERSION;
        $this->addResult('PHP Version', $phpVersion, version_compare($phpVersion, '7.4.0', '>='));
        
        // Operating System
        $os = PHP_OS;
        $isLinux = stripos($os, 'linux') !== false;
        $this->addResult('Operating System', $os, true);
        $this->addResult('Linux Compatible', $isLinux ? 'Yes' : 'No', $isLinux);
        
        // Memory Limit
        $memoryLimit = ini_get('memory_limit');
        $this->addResult('Memory Limit', $memoryLimit, true);
        
        // Max Execution Time
        $maxExecTime = ini_get('max_execution_time');
        $this->addResult('Max Execution Time', $maxExecTime . 's', intval($maxExecTime) >= 300);
        
        echo "\n";
    }
    
    private function testPHPExtensions() {
        echo "2. Testing PHP Extensions:\n";
        
        $requiredExtensions = [
            'gd' => 'Image processing',
            'json' => 'JSON handling',
            'curl' => 'HTTP requests',
            'mbstring' => 'String handling',
            'fileinfo' => 'File type detection',
            'zip' => 'Archive handling'
        ];
        
        foreach ($requiredExtensions as $ext => $description) {
            $loaded = extension_loaded($ext);
            $this->addResult("Extension: $ext", $description, $loaded);
        }
        
        echo "\n";
    }
    
    private function testExternalDependencies() {
        echo "3. Testing External Dependencies:\n";
        
        // FFmpeg
        exec('ffmpeg -version 2>&1', $ffmpegOutput, $ffmpegReturn);
        $ffmpegAvailable = $ffmpegReturn === 0;
        $this->addResult('FFmpeg', $ffmpegAvailable ? 'Available' : 'Not Available', $ffmpegAvailable);
        
        // ImageMagick
        exec('convert -version 2>&1', $imagickOutput, $imagickReturn);
        $imagickAvailable = $imagickReturn === 0;
        $this->addResult('ImageMagick', $imagickAvailable ? 'Available' : 'Not Available', $imagickAvailable);
        
        // espeak (Linux TTS)
        exec('espeak --version 2>&1', $espeakOutput, $espeakReturn);
        $espeakAvailable = $espeakReturn === 0;
        $this->addResult('espeak TTS', $espeakAvailable ? 'Available' : 'Not Available', $espeakAvailable);
        
        // festival (Alternative TTS)
        exec('festival --version 2>&1', $festivalOutput, $festivalReturn);
        $festivalAvailable = $festivalReturn === 0;
        $this->addResult('Festival TTS', $festivalAvailable ? 'Available' : 'Not Available', $festivalAvailable);
        
        echo "\n";
    }
    
    private function testFilePermissions() {
        echo "4. Testing File Permissions:\n";
        
        $directories = [
            '../temp' => 'Temporary files',
            '../data/output_history' => 'Output storage',
            '../data/jobs' => 'Job tracking',
            '../logs' => 'Log files'
        ];
        
        foreach ($directories as $dir => $description) {
            $exists = is_dir($dir);
            $writable = $exists ? is_writable($dir) : false;
            $this->addResult("Directory: $dir", $description, $exists && $writable);
        }
        
        echo "\n";
    }
    
    private function testAudioGeneration() {
        echo "5. Testing Audio Generation:\n";
        
        try {
            // Test basic audio file creation
            $testFile = '../temp/test_audio.wav';
            $success = $this->createTestAudio($testFile);
            $this->addResult('Basic Audio Creation', $success ? 'Success' : 'Failed', $success);
            
            if ($success && file_exists($testFile)) {
                $fileSize = filesize($testFile);
                $this->addResult('Audio File Size', $fileSize . ' bytes', $fileSize > 1000);
                unlink($testFile);
            }
            
            // Test TTS engines
            $this->testLinuxTTS();
            
        } catch (Exception $e) {
            $this->addError('Audio Generation', $e->getMessage());
        }
        
        echo "\n";
    }
    
    private function testVideoGeneration() {
        echo "6. Testing Video Generation:\n";
        
        try {
            // Test basic video file creation
            $testFile = '../temp/test_video.mp4';
            $success = $this->createTestVideo($testFile);
            $this->addResult('Basic Video Creation', $success ? 'Success' : 'Failed', $success);
            
            if ($success && file_exists($testFile)) {
                $fileSize = filesize($testFile);
                $this->addResult('Video File Size', $fileSize . ' bytes', $fileSize > 10000);
                unlink($testFile);
            }
            
        } catch (Exception $e) {
            $this->addError('Video Generation', $e->getMessage());
        }
        
        echo "\n";
    }
    
    private function testContentGeneration() {
        echo "7. Testing Content Generation:\n";
        
        try {
            // Test content parsing
            $testContent = "This is a test content for the Sutradhar Engine system.";
            $segments = $this->parseTestContent($testContent);
            $this->addResult('Content Parsing', count($segments) . ' segments', count($segments) > 0);
            
        } catch (Exception $e) {
            $this->addError('Content Generation', $e->getMessage());
        }
        
        echo "\n";
    }
    
    private function testDatabaseConnectivity() {
        echo "8. Testing Database/Storage:\n";
        
        try {
            // Test JSON file operations
            $testData = ['test' => 'data', 'timestamp' => time()];
            $testFile = '../temp/test_storage.json';
            
            $writeSuccess = file_put_contents($testFile, json_encode($testData)) !== false;
            $this->addResult('JSON Write', $writeSuccess ? 'Success' : 'Failed', $writeSuccess);
            
            if ($writeSuccess) {
                $readData = json_decode(file_get_contents($testFile), true);
                $readSuccess = $readData && $readData['test'] === 'data';
                $this->addResult('JSON Read', $readSuccess ? 'Success' : 'Failed', $readSuccess);
                unlink($testFile);
            }
            
        } catch (Exception $e) {
            $this->addError('Database/Storage', $e->getMessage());
        }
        
        echo "\n";
    }
    
    private function createTestAudio($outputFile) {
        // Create a simple WAV file with a tone
        $sampleRate = 44100;
        $duration = 1; // 1 second
        $frequency = 440; // A4 note
        
        $samples = [];
        for ($i = 0; $i < $sampleRate * $duration; $i++) {
            $samples[] = sin(2 * pi() * $frequency * $i / $sampleRate) * 32767;
        }
        
        // Create WAV header
        $header = pack('V', 0x46464952); // "RIFF"
        $header .= pack('V', 36 + count($samples) * 2); // File size
        $header .= pack('V', 0x45564157); // "WAVE"
        $header .= pack('V', 0x20746d66); // "fmt "
        $header .= pack('V', 16); // Subchunk1Size
        $header .= pack('v', 1); // AudioFormat (PCM)
        $header .= pack('v', 1); // NumChannels (mono)
        $header .= pack('V', $sampleRate); // SampleRate
        $header .= pack('V', $sampleRate * 2); // ByteRate
        $header .= pack('v', 2); // BlockAlign
        $header .= pack('v', 16); // BitsPerSample
        $header .= pack('V', 0x61746164); // "data"
        $header .= pack('V', count($samples) * 2); // Subchunk2Size
        
        // Create audio data
        $audioData = '';
        foreach ($samples as $sample) {
            $audioData .= pack('v', intval($sample));
        }
        
        return file_put_contents($outputFile, $header . $audioData) !== false;
    }
    
    private function createTestVideo($outputFile) {
        if (!$this->results['FFmpeg']['status']) {
            return false;
        }
        
        // Create a simple test video using FFmpeg
        $cmd = "ffmpeg -f lavfi -i testsrc=duration=1:size=320x240:rate=1 -c:v libx264 -t 1 \"$outputFile\" -y 2>&1";
        exec($cmd, $output, $returnCode);
        
        return $returnCode === 0 && file_exists($outputFile);
    }
    
    private function testLinuxTTS() {
        // Test espeak
        if ($this->results['espeak TTS']['status']) {
            $testFile = '../temp/test_espeak.wav';
            $cmd = "espeak \"Hello, this is a test\" -w \"$testFile\" 2>&1";
            exec($cmd, $output, $returnCode);
            
            $success = $returnCode === 0 && file_exists($testFile);
            $this->addResult('espeak TTS Test', $success ? 'Success' : 'Failed', $success);
            
            if ($success) {
                unlink($testFile);
            }
        }
        
        // Test festival
        if ($this->results['Festival TTS']['status']) {
            $testFile = '../temp/test_festival.wav';
            $cmd = "echo \"Hello, this is a test\" | festival --tts --otype wav > \"$testFile\" 2>&1";
            exec($cmd, $output, $returnCode);
            
            $success = file_exists($testFile) && filesize($testFile) > 1000;
            $this->addResult('Festival TTS Test', $success ? 'Success' : 'Failed', $success);
            
            if (file_exists($testFile)) {
                unlink($testFile);
            }
        }
    }
    
    private function parseTestContent($content) {
        // Simple content parsing test
        $sentences = preg_split('/[.!?]+/', $content, -1, PREG_SPLIT_NO_EMPTY);
        return array_map('trim', $sentences);
    }
    
    private function addResult($test, $value, $status) {
        $this->results[$test] = [
            'value' => $value,
            'status' => $status
        ];
        
        $statusIcon = $status ? '✅' : '❌';
        echo "   $statusIcon $test: $value\n";
        
        if (!$status) {
            $this->errors[] = "$test: $value";
        }
    }
    
    private function addError($category, $message) {
        $this->errors[] = "$category: $message";
        echo "   ❌ $category Error: $message\n";
    }
    
    private function generateReport() {
        echo "\n📊 DIAGNOSTIC SUMMARY:\n";
        echo "=====================\n";
        
        $totalTests = count($this->results);
        $passedTests = count(array_filter($this->results, function($r) { return $r['status']; }));
        $failedTests = $totalTests - $passedTests;
        
        echo "Total Tests: $totalTests\n";
        echo "Passed: $passedTests\n";
        echo "Failed: $failedTests\n";
        echo "Success Rate: " . round(($passedTests / $totalTests) * 100, 1) . "%\n\n";
        
        if (!empty($this->errors)) {
            echo "❌ CRITICAL ISSUES TO FIX:\n";
            foreach ($this->errors as $error) {
                echo "   - $error\n";
            }
            echo "\n";
        }
        
        echo "🔧 RECOMMENDATIONS:\n";
        $this->generateRecommendations();
        
        // Save report to file
        $reportFile = '../logs/diagnostic_report_' . date('Y-m-d_H-i-s') . '.json';
        file_put_contents($reportFile, json_encode([
            'timestamp' => date('Y-m-d H:i:s'),
            'results' => $this->results,
            'errors' => $this->errors,
            'summary' => [
                'total' => $totalTests,
                'passed' => $passedTests,
                'failed' => $failedTests,
                'success_rate' => round(($passedTests / $totalTests) * 100, 1)
            ]
        ], JSON_PRETTY_PRINT));
        
        echo "📄 Report saved to: $reportFile\n";
    }
    
    private function generateRecommendations() {
        if (!$this->results['FFmpeg']['status']) {
            echo "   - Install FFmpeg: sudo apt-get install ffmpeg\n";
        }
        
        if (!$this->results['espeak TTS']['status']) {
            echo "   - Install espeak: sudo apt-get install espeak espeak-data\n";
        }
        
        if (!$this->results['Festival TTS']['status']) {
            echo "   - Install Festival: sudo apt-get install festival festvox-kallpc16k\n";
        }
        
        if (!$this->results['ImageMagick']['status']) {
            echo "   - Install ImageMagick: sudo apt-get install imagemagick\n";
        }
        
        foreach ($this->results as $test => $result) {
            if (!$result['status'] && strpos($test, 'Extension:') === 0) {
                $ext = str_replace('Extension: ', '', $test);
                echo "   - Install PHP extension: sudo apt-get install php-$ext\n";
            }
        }
        
        echo "   - Ensure proper file permissions: chmod 755 directories, chmod 644 files\n";
        echo "   - Configure PHP settings: memory_limit=512M, max_execution_time=300\n";
    }
}

// Run diagnostics if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $diagnostics = new SystemDiagnostics();
    $diagnostics->runFullDiagnostics();
}
?>
