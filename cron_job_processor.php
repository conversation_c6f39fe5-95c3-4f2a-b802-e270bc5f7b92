<?php
/**
 * Cron Job Processor for Sutradhar 2070
 * Run this script every minute to process video generation jobs
 * 
 * Add to crontab:
 * * * * * * php /path/to/sutradhar2070/cron_job_processor.php >> /path/to/logs/cron.log 2>&1
 */

// Prevent running from web browser
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line');
}

// Set time limit and memory limit
set_time_limit(300); // 5 minutes max
ini_set('memory_limit', '512M');

echo "[" . date('Y-m-d H:i:s') . "] Starting cron job processor\n";

try {
    require_once 'core/background_job_processor.php';
    
    $processor = new BackgroundJobProcessor();
    
    // Get current stats
    $stats = $processor->getJobStats();
    echo "[" . date('Y-m-d H:i:s') . "] Current stats: {$stats['pending']} pending, {$stats['completed']} completed, {$stats['failed']} failed\n";
    
    if ($stats['pending'] > 0) {
        echo "[" . date('Y-m-d H:i:s') . "] Processing {$stats['pending']} pending jobs...\n";
        $processor->processPendingJobs();
        echo "[" . date('Y-m-d H:i:s') . "] Job processing complete\n";
    } else {
        echo "[" . date('Y-m-d H:i:s') . "] No pending jobs to process\n";
    }
    
    // Clean up old jobs once per hour (when minutes = 0)
    if (date('i') === '00') {
        echo "[" . date('Y-m-d H:i:s') . "] Running cleanup of old jobs...\n";
        $processor->cleanupOldJobs();
        echo "[" . date('Y-m-d H:i:s') . "] Cleanup complete\n";
    }
    
    echo "[" . date('Y-m-d H:i:s') . "] Cron job processor finished successfully\n";
    
} catch (Exception $e) {
    echo "[" . date('Y-m-d H:i:s') . "] ERROR: " . $e->getMessage() . "\n";
    error_log("Cron job processor error: " . $e->getMessage());
    exit(1);
}
?>
