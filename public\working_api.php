<?php
/**
 * Working API for Sutradhar 2070
 * Simplified API that works without database dependency
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors to user
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/api_error.log');

// Global error handler
function handleAPIError($errno, $errstr, $errfile, $errline) {
    error_log("API Error [$errno]: $errstr in $errfile on line $errline");
    return false;
}

function handleAPIException($exception) {
    error_log("API Exception: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine());

    if (!headers_sent()) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Internal server error',
            'message' => 'An unexpected error occurred'
        ]);
    }
    exit();
}

set_error_handler('handleAPIError');
set_exception_handler('handleAPIException');

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Get request data
$endpoint = $_GET['endpoint'] ?? '';
$method = $_SERVER['REQUEST_METHOD'];
$input = file_get_contents('php://input');
$data = json_decode($input, true) ?: $_POST;

// Log request for debugging
error_log("Working API Request: $method $endpoint - " . json_encode($data));

try {
    // Route requests
    switch ($endpoint) {
        case 'generate':
            if ($method === 'POST') {
                handleVideoGeneration($data);
            } else {
                sendError('Method not allowed', 405);
            }
            break;

        case (preg_match('/^generate\/status\/(.+)$/', $endpoint, $matches) ? true : false):
            handleGenerationStatus($matches[1]);
            break;

        case 'generate/history':
            handleGenerationHistory();
            break;

        case 'auth/status':
            handleAuthStatus();
            break;

        case 'auth/register':
            if ($method === 'POST') {
                handleUserRegistration($data);
            } else {
                sendError('Method not allowed', 405);
            }
            break;

        case 'auth/login':
            if ($method === 'POST') {
                handleUserLogin($data);
            } else {
                sendError('Method not allowed', 405);
            }
            break;

        case 'auth/logout':
            if ($method === 'POST') {
                handleUserLogout();
            } else {
                sendError('Method not allowed', 405);
            }
            break;

        case 'payments/packages':
            handlePaymentPackages();
            break;

        case 'dashboard/stats':
            handleDashboardStats();
            break;

        case 'user/credits':
            handleUserCredits();
            break;

        case 'user/update':
            if ($method === 'GET') {
                handleUserUpdate();
            } else {
                sendError('Method not allowed', 405);
            }
            break;

        default:
            sendError('Endpoint not found: ' . $endpoint, 404);
    }
    
} catch (Exception $e) {
    error_log("Working API Error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

/**
 * Handle video generation
 */
function handleVideoGeneration($data) {
    // Check if user is authenticated (for credit deduction)
    $currentUser = getCurrentUser();
    $isDemo = !$currentUser;

    $jobId = ($isDemo ? 'demo_' : 'auth_') . uniqid() . '_' . time();

    // Extract parameters
    $mood = $data['mood'] ?? 'euphoric';
    $topic = $data['topic'] ?? 'nature_wildlife';
    $prompt = $data['custom_prompt'] ?? "Create a {$mood} video about {$topic}";
    $inspiration = $data['inspiration'] ?? ['cinematic', 'colorful'];

    // Check credits for authenticated users
    if ($currentUser) {
        $creditsRequired = 10; // Cost per video
        if (($currentUser['credits'] ?? 0) < $creditsRequired) {
            sendError('Insufficient credits. You need ' . $creditsRequired . ' credits to generate a video.', 402);
            return;
        }

        // Deduct credits
        $_SESSION['users'][$currentUser['user_id']]['credits'] -= $creditsRequired;
    }
    
    // Try enhanced video generation
    $useRealGeneration = true;
    $videoPath = null;
    
    try {
        if (file_exists('../core/enhanced_video_engine.php')) {
            require_once '../core/enhanced_video_engine.php';
            
            $engine = new EnhancedVideoEngine();
            $segments = [
                ['text' => $prompt, 'content' => $prompt]
            ];
            
            // Generate video in background
            $videoPath = $engine->generateEnhancedVideo($segments, $topic, $mood, $jobId);
            
            if ($videoPath && file_exists($videoPath)) {
                error_log("Real video generated: $videoPath");
            } else {
                $useRealGeneration = false;
            }
        } else {
            $useRealGeneration = false;
        }
    } catch (Exception $e) {
        error_log("Enhanced video generation failed: " . $e->getMessage());
        $useRealGeneration = false;
    }
    
    // Store job in session
    $_SESSION['working_jobs'][$jobId] = [
        'job_id' => $jobId,
        'user_id' => $currentUser['user_id'] ?? null,
        'mood' => $mood,
        'topic' => $topic,
        'prompt' => $prompt,
        'inspiration' => $inspiration,
        'status' => 'pending',
        'progress' => 0,
        'created_at' => time(),
        'complete_time' => time() + ($useRealGeneration ? 20 : 10),
        'real_generation' => $useRealGeneration,
        'video_path' => $videoPath,
        'is_demo' => $isDemo,
        'credits_used' => $currentUser ? 10 : 0
    ];
    
    sendSuccess([
        'job_id' => $jobId,
        'estimated_time' => $useRealGeneration ? 20 : 10,
        'status' => 'pending',
        'generation_type' => $useRealGeneration ? 'enhanced' : 'demo',
        'message' => $useRealGeneration ? 'Enhanced video generation started' : 'Demo video generation started'
    ], 'Video generation request received', 201);
}

/**
 * Handle generation status
 */
function handleGenerationStatus($jobId) {
    if (!isset($_SESSION['working_jobs'][$jobId])) {
        sendError('Job not found', 404);
        return;
    }
    
    $job = $_SESSION['working_jobs'][$jobId];
    $currentTime = time();
    $elapsed = $currentTime - $job['created_at'];
    $totalTime = $job['complete_time'] - $job['created_at'];
    $progress = min(100, ($elapsed / $totalTime) * 100);
    
    if ($progress >= 100) {
        // Job completed
        $videoUrl = 'final_test_video.mp4';
        $thumbnailUrl = 'final_test_video_thumbnail.jpg';
        
        // Check if real video was generated
        if ($job['real_generation'] && !empty($job['video_path']) && file_exists($job['video_path'])) {
            // Convert path to URL
            $videoUrl = str_replace(__DIR__ . '/../public/', '', $job['video_path']);
            $videoUrl = str_replace('\\', '/', $videoUrl);
            $videoUrl = '/' . ltrim($videoUrl, '/');
            
            // Look for thumbnail
            $thumbnailPath = str_replace('.mp4', '.jpg', $job['video_path']);
            if (file_exists($thumbnailPath)) {
                $thumbnailUrl = str_replace(__DIR__ . '/../public/', '', $thumbnailPath);
                $thumbnailUrl = str_replace('\\', '/', $thumbnailUrl);
                $thumbnailUrl = '/' . ltrim($thumbnailUrl, '/');
            }
        }
        
        sendSuccess([
            'job_id' => $jobId,
            'status' => 'completed',
            'progress' => 100,
            'message' => $job['real_generation'] ? 'Enhanced video generation completed!' : 'Demo video generation completed!',
            'video_url' => $videoUrl,
            'thumbnail_url' => $thumbnailUrl,
            'processing_time' => $totalTime,
            'generation_type' => $job['real_generation'] ? 'enhanced' : 'demo',
            'mood' => $job['mood'],
            'topic' => $job['topic'],
            'completed_at' => date('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s', $job['created_at']),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    } else {
        // Job in progress
        $stages = [
            [0, 20, 'Analyzing mood and topic...'],
            [20, 40, 'Generating AI prompts...'],
            [40, 70, 'Creating video content...'],
            [70, 90, 'Adding visual effects...'],
            [90, 100, 'Finalizing video...']
        ];
        
        $message = 'Processing...';
        foreach ($stages as $stage) {
            if ($progress >= $stage[0] && $progress < $stage[1]) {
                $message = $stage[2];
                break;
            }
        }
        
        sendSuccess([
            'job_id' => $jobId,
            'status' => 'generating',
            'progress' => round($progress),
            'message' => $message,
            'generation_type' => $job['real_generation'] ? 'enhanced' : 'demo',
            'created_at' => date('Y-m-d H:i:s', $job['created_at']),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }
}

/**
 * Handle generation history
 */
function handleGenerationHistory() {
    $jobs = $_SESSION['working_jobs'] ?? [];
    
    $history = array_map(function($job) {
        return [
            'job_id' => $job['job_id'],
            'mood' => $job['mood'],
            'topic' => $job['topic'],
            'status' => $job['progress'] >= 100 ? 'completed' : 'generating',
            'progress' => min(100, ((time() - $job['created_at']) / ($job['complete_time'] - $job['created_at'])) * 100),
            'generation_type' => $job['real_generation'] ? 'enhanced' : 'demo',
            'created_at' => date('Y-m-d H:i:s', $job['created_at'])
        ];
    }, $jobs);
    
    sendSuccess([
        'jobs' => array_values($history),
        'total' => count($history)
    ]);
}

/**
 * Handle user registration
 */
function handleUserRegistration($data) {
    // Validate required fields
    $requiredFields = ['email', 'password', 'first_name', 'last_name'];
    foreach ($requiredFields as $field) {
        if (empty($data[$field])) {
            sendError("Missing required field: $field", 400);
            return;
        }
    }

    // Validate email format
    if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        sendError('Invalid email format', 400);
        return;
    }

    // Validate password strength
    if (strlen($data['password']) < 8) {
        sendError('Password must be at least 8 characters long', 400);
        return;
    }

    // Check if user already exists (session-based)
    if (!isset($_SESSION['users'])) {
        $_SESSION['users'] = [];
    }

    $email = strtolower(trim($data['email']));

    // Check for existing user
    foreach ($_SESSION['users'] as $user) {
        if ($user['email'] === $email) {
            sendError('Email already registered', 409);
            return;
        }
    }

    // Create new user
    $userId = 'user_' . uniqid() . '_' . time();
    $newUser = [
        'user_id' => $userId,
        'email' => $email,
        'first_name' => $data['first_name'],
        'last_name' => $data['last_name'],
        'password_hash' => password_hash($data['password'], PASSWORD_DEFAULT),
        'credits' => 100, // Starting credits
        'subscription' => 'free',
        'status' => 'active',
        'created_at' => date('Y-m-d H:i:s'),
        'email_verified' => true // Auto-verify for demo
    ];

    // Store user
    $_SESSION['users'][$userId] = $newUser;

    // Auto-login the user
    $_SESSION['current_user'] = $userId;

    // Return user data (without password)
    $userData = $newUser;
    unset($userData['password_hash']);

    sendSuccess([
        'user' => $userData,
        'message' => 'Account created successfully'
    ], 'Registration successful', 201);
}

/**
 * Handle user login
 */
function handleUserLogin($data) {
    // Validate required fields
    if (empty($data['email']) || empty($data['password'])) {
        sendError('Email and password are required', 400);
        return;
    }

    $email = strtolower(trim($data['email']));
    $password = $data['password'];

    // Check if users exist
    if (!isset($_SESSION['users'])) {
        $_SESSION['users'] = [];
    }

    // Find user by email
    $foundUser = null;
    foreach ($_SESSION['users'] as $user) {
        if ($user['email'] === $email) {
            $foundUser = $user;
            break;
        }
    }

    if (!$foundUser) {
        sendError('Invalid email or password', 401);
        return;
    }

    // Verify password
    if (!password_verify($password, $foundUser['password_hash'])) {
        sendError('Invalid email or password', 401);
        return;
    }

    // Check if account is active
    if ($foundUser['status'] !== 'active') {
        sendError('Account is not active', 403);
        return;
    }

    // Set current user session
    $_SESSION['current_user'] = $foundUser['user_id'];

    // Update last login
    $_SESSION['users'][$foundUser['user_id']]['last_login'] = date('Y-m-d H:i:s');

    // Return user data (without password)
    $userData = $foundUser;
    unset($userData['password_hash']);

    sendSuccess([
        'user' => $userData,
        'message' => 'Login successful'
    ], 'Welcome back!');
}

/**
 * Handle user logout
 */
function handleUserLogout() {
    // Clear current user session
    unset($_SESSION['current_user']);

    sendSuccess([
        'message' => 'Logged out successfully'
    ], 'Goodbye!');
}

/**
 * Handle auth status
 */
function handleAuthStatus() {
    $currentUser = getCurrentUser();

    if ($currentUser) {
        sendSuccess([
            'user' => $currentUser,
            'authenticated' => true,
            'demo_mode' => false
        ], 'User authenticated');
    } else {
        sendSuccess([
            'user' => null,
            'authenticated' => false,
            'demo_mode' => true
        ], 'Demo mode - no authentication required');
    }
}

/**
 * Get current authenticated user
 */
function getCurrentUser() {
    if (!isset($_SESSION['current_user']) || !isset($_SESSION['users'])) {
        return null;
    }

    $userId = $_SESSION['current_user'];
    $user = $_SESSION['users'][$userId] ?? null;

    if ($user) {
        // Remove password hash from returned data
        unset($user['password_hash']);
        return $user;
    }

    return null;
}

/**
 * Handle payment packages
 */
function handlePaymentPackages() {
    $packages = [
        [
            'package_id' => 'starter',
            'name' => 'Starter Pack',
            'credits' => 100,
            'price' => 9.99,
            'description' => 'Perfect for getting started'
        ],
        [
            'package_id' => 'pro',
            'name' => 'Pro Pack',
            'credits' => 500,
            'price' => 39.99,
            'description' => 'Great for regular users'
        ],
        [
            'package_id' => 'enterprise',
            'name' => 'Enterprise Pack',
            'credits' => 2000,
            'price' => 149.99,
            'description' => 'For heavy usage'
        ]
    ];
    
    sendSuccess(['packages' => $packages]);
}

/**
 * Handle user credits
 */
function handleUserCredits() {
    $currentUser = getCurrentUser();

    if (!$currentUser) {
        sendError('Authentication required', 401);
        return;
    }

    // Get credit transactions
    $transactions = $_SESSION['credit_transactions'] ?? [];
    $userTransactions = array_filter($transactions, function($txn) use ($currentUser) {
        return $txn['user_id'] === $currentUser['user_id'];
    });

    sendSuccess([
        'current_balance' => $currentUser['credits'],
        'transactions' => array_values($userTransactions),
        'total_spent' => array_sum(array_map(function($txn) {
            return $txn['type'] === 'deduction' ? $txn['amount'] : 0;
        }, $userTransactions))
    ]);
}

/**
 * Handle user update (get fresh user data)
 */
function handleUserUpdate() {
    $currentUser = getCurrentUser();

    if (!$currentUser) {
        sendError('Authentication required', 401);
        return;
    }

    sendSuccess([
        'user' => $currentUser,
        'updated_at' => date('Y-m-d H:i:s')
    ], 'User data updated');
}

/**
 * Handle dashboard stats
 */
function handleDashboardStats() {
    $currentUser = getCurrentUser();
    $jobs = $_SESSION['working_jobs'] ?? [];

    // Filter jobs for current user if authenticated
    if ($currentUser) {
        $userJobs = array_filter($jobs, function($job) use ($currentUser) {
            return $job['user_id'] === $currentUser['user_id'];
        });
    } else {
        $userJobs = $jobs; // Show all jobs in demo mode
    }

    $totalJobs = count($userJobs);
    $completedJobs = 0;
    $enhancedJobs = 0;
    $totalCreditsUsed = 0;

    foreach ($userJobs as $job) {
        $progress = ((time() - $job['created_at']) / ($job['complete_time'] - $job['created_at'])) * 100;
        if ($progress >= 100) {
            $completedJobs++;
        }
        if ($job['real_generation']) {
            $enhancedJobs++;
        }
        $totalCreditsUsed += $job['credits_used'] ?? 0;
    }

    // Get user credit info
    $userCredits = $currentUser['credits'] ?? 1000;
    $totalCredits = $userCredits + $totalCreditsUsed;

    sendSuccess([
        'generation_stats' => [
            'total' => $totalJobs,
            'completed' => $completedJobs,
            'pending' => $totalJobs - $completedJobs,
            'enhanced' => $enhancedJobs,
            'demo' => $totalJobs - $enhancedJobs,
            'success_rate' => $totalJobs > 0 ? round(($completedJobs / $totalJobs) * 100, 1) : 0
        ],
        'credit_stats' => [
            'total' => $totalCredits,
            'used' => $totalCreditsUsed,
            'available' => $userCredits
        ],
        'user_stats' => [
            'authenticated' => $currentUser !== null,
            'subscription' => $currentUser['subscription'] ?? 'free',
            'member_since' => $currentUser['created_at'] ?? null
        ]
    ]);
}

/**
 * Send success response
 */
function sendSuccess($data, $message = 'Success', $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode([
        'success' => true,
        'data' => $data,
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * Send error response
 */
function sendError($message, $statusCode = 400) {
    http_response_code($statusCode);
    echo json_encode([
        'success' => false,
        'error' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}
?>
