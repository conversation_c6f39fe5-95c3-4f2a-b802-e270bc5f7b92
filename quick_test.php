<?php
/**
 * Quick Test - Verify engines work
 */

echo "🔥 QUICK TEST - REAL ENGINES\n";
echo "============================\n\n";

// Test 1: Check GD
echo "1. Testing GD Extension:\n";
echo "   GD Loaded: " . (extension_loaded('gd') ? 'YES' : 'NO') . "\n";
echo "   imagecreatetruecolor: " . (function_exists('imagecreatetruecolor') ? 'YES' : 'NO') . "\n\n";

// Test 2: Test Windows TTS
echo "2. Testing Windows TTS:\n";
try {
    require_once 'core/windows_tts_engine.php';
    $windowsTTS = new WindowsTTSEngine();
    
    $testFile = __DIR__ . "/temp/quick_test.wav";
    $success = $windowsTTS->generateWindowsSpeech("Hello, this is a quick test!", 'babu_rao', $testFile);
    
    if ($success && file_exists($testFile)) {
        $size = filesize($testFile);
        echo "   ✅ SUCCESS! Generated " . round($size/1024, 1) . " KB audio\n";
        unlink($testFile);
    } else {
        echo "   ❌ FAILED\n";
    }
} catch (Exception $e) {
    echo "   ❌ ERROR: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Test Video Generation
echo "3. Testing Video Generation:\n";
try {
    require_once 'core/working_video_engine.php';
    $workingVideo = new WorkingVideoEngine();
    
    // Simple capability check
    $capabilities = [
        'GD Extension' => extension_loaded('gd'),
        'GD Functions' => function_exists('imagecreatetruecolor'),
        'Temp Directory' => is_writable(__DIR__ . '/temp/')
    ];
    
    foreach ($capabilities as $cap => $available) {
        echo "   $cap: " . ($available ? 'YES' : 'NO') . "\n";
    }
    
    // Try to create a simple frame
    if (function_exists('imagecreatetruecolor')) {
        $image = imagecreatetruecolor(100, 100);
        if ($image) {
            $white = imagecolorallocate($image, 255, 255, 255);
            imagefill($image, 0, 0, $white);
            
            $testImageFile = __DIR__ . "/temp/test_frame.png";
            $result = imagepng($image, $testImageFile);
            imagedestroy($image);
            
            if ($result && file_exists($testImageFile)) {
                echo "   ✅ Frame generation: SUCCESS\n";
                unlink($testImageFile);
            } else {
                echo "   ❌ Frame generation: FAILED\n";
            }
        } else {
            echo "   ❌ Image creation: FAILED\n";
        }
    } else {
        echo "   ❌ GD functions not available\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ ERROR: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Test Full Generation
echo "4. Testing Full Generation:\n";
try {
    // Simulate a generation request
    $_POST = [
        'flow_type' => 'reel',
        'style' => 'funny',
        'voice_pack' => 'babu_rao',
        'background' => 'home',
        'content_source' => 'text',
        'content_text' => 'Quick test of the real engines!'
    ];
    
    $_SERVER['REQUEST_METHOD'] = 'POST';
    
    require_once 'generate.php';
    $generator = new SutradharGenerator();
    
    $result = $generator->handleRequest();
    $resultData = json_decode($result, true);
    
    if ($resultData && $resultData['success']) {
        echo "   ✅ Generation started: " . $resultData['job_id'] . "\n";
        
        // Check status once
        sleep(2);
        $_GET['action'] = 'status';
        $_GET['job_id'] = $resultData['job_id'];
        $_SERVER['REQUEST_METHOD'] = 'GET';
        
        $statusResult = $generator->handleRequest();
        $statusData = json_decode($statusResult, true);
        
        if ($statusData) {
            echo "   Status: " . ($statusData['status'] ?? 'Unknown') . "\n";
            echo "   Progress: " . ($statusData['progress'] ?? 0) . "%\n";
        }
    } else {
        echo "   ❌ Generation failed to start\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ ERROR: " . $e->getMessage() . "\n";
}

echo "\n🎉 Quick test completed!\n";
?>
