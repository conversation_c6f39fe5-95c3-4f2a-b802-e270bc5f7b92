<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sutradhar Documentation Hub</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;600;700&family=Exo+2:wght@300;400;500;600&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-neon: #00ff88;
            --secondary-neon: #0088ff;
            --tertiary-neon: #ff0088;
            --bg-primary: #0a0a0a;
            --bg-secondary: #1a1a1a;
            --bg-glass: rgba(255, 255, 255, 0.05);
            --text-primary: #ffffff;
            --text-secondary: #cccccc;
            --border-glass: rgba(255, 255, 255, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Exo 2', sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-family: 'Orbitron', monospace;
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .header p {
            font-size: 1.2rem;
            color: var(--text-secondary);
        }

        .docs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .doc-card {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-glass);
            border-radius: 16px;
            padding: 2rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .doc-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-neon), var(--secondary-neon));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .doc-card:hover {
            transform: translateY(-5px);
            border-color: var(--primary-neon);
            box-shadow: 0 10px 30px rgba(0, 255, 136, 0.2);
        }

        .doc-card:hover::before {
            transform: scaleX(1);
        }

        .doc-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .doc-card h3 {
            font-family: 'Orbitron', monospace;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .doc-card p {
            color: var(--text-secondary);
            margin-bottom: 1.5rem;
        }

        .doc-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: linear-gradient(135deg, var(--primary-neon), var(--secondary-neon));
            color: var(--bg-primary);
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .doc-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 136, 0.4);
        }

        .quick-links {
            background: var(--bg-secondary);
            border-radius: 16px;
            padding: 2rem;
            margin-top: 3rem;
        }

        .quick-links h2 {
            font-family: 'Orbitron', monospace;
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .quick-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem;
            background: var(--bg-glass);
            border: 1px solid var(--border-glass);
            border-radius: 8px;
            text-decoration: none;
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .quick-link:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--primary-neon);
        }

        .footer {
            text-align: center;
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid var(--border-glass);
            color: var(--text-secondary);
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .docs-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📚 Sutradhar Documentation</h1>
            <p>Comprehensive guides and references for the Sutradhar Video Generation Platform</p>
        </div>

        <div class="docs-grid">
            <div class="doc-card">
                <div class="doc-icon">🏗️</div>
                <h3>System Architecture</h3>
                <p>Complete technical overview of the Sutradhar platform architecture, components, and data flow.</p>
                <a href="system-architecture.html" class="doc-link">
                    View Guide →
                </a>
            </div>

            <div class="doc-card">
                <div class="doc-icon">🔌</div>
                <h3>API Reference</h3>
                <p>Detailed API documentation with endpoints, parameters, examples, and response formats.</p>
                <a href="api-reference.html" class="doc-link">
                    View Reference →
                </a>
            </div>

            <div class="doc-card">
                <div class="doc-icon">⚙️</div>
                <h3>Installation Guide</h3>
                <p>Step-by-step instructions for setting up Sutradhar on various environments and platforms.</p>
                <a href="installation-guide.html" class="doc-link">
                    View Guide →
                </a>
            </div>

            <div class="doc-card">
                <div class="doc-icon">🔧</div>
                <h3>Configuration</h3>
                <p>Environment setup, configuration options, and system optimization guidelines.</p>
                <a href="configuration-guide.html" class="doc-link">
                    View Guide →
                </a>
            </div>

            <div class="doc-card">
                <div class="doc-icon">👥</div>
                <h3>User Management</h3>
                <p>Authentication, authorization, user roles, and account management documentation.</p>
                <a href="user-management.html" class="doc-link">
                    View Guide →
                </a>
            </div>

            <div class="doc-card">
                <div class="doc-icon">💻</div>
                <h3>Developer Guide</h3>
                <p>Code standards, development workflows, extension guidelines, and best practices.</p>
                <a href="developer-guide.html" class="doc-link">
                    View Guide →
                </a>
            </div>

            <div class="doc-card">
                <div class="doc-icon">🔍</div>
                <h3>Troubleshooting</h3>
                <p>Common issues, solutions, debugging tips, and performance optimization guides.</p>
                <a href="troubleshooting.html" class="doc-link">
                    View Guide →
                </a>
            </div>

            <div class="doc-card">
                <div class="doc-icon">🛠️</div>
                <h3>Tools Documentation</h3>
                <p>Complete guide to the 25+ tools system, usage instructions, and integration details.</p>
                <a href="tools-documentation.html" class="doc-link">
                    View Guide →
                </a>
            </div>
        </div>

        <div class="quick-links">
            <h2>Quick Links</h2>
            <div class="links-grid">
                <a href="../public/tools/" class="quick-link">
                    🛠️ Tools Interface
                </a>
                <a href="../public/dashboard.html" class="quick-link">
                    📊 Dashboard
                </a>
                <a href="../public/index.html" class="quick-link">
                    🏠 Home
                </a>
                <a href="../public/pricing.html" class="quick-link">
                    💎 Pricing
                </a>
                <a href="../database/tools_schema.sql" class="quick-link">
                    🗄️ Database Schema
                </a>
                <a href="../core/tools/" class="quick-link">
                    ⚡ Core Tools
                </a>
            </div>
        </div>

        <div class="footer">
            <p>&copy; 2024 Sutradhar Platform. Built for the future of content creation.</p>
        </div>
    </div>
</body>
</html>
