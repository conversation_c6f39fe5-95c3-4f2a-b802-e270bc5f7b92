<?php
/**
 * Comprehensive API System Test for Sutradhar 2070
 * Tests all API endpoints and functionality
 */

echo "🚀 Sutradhar 2070 - API System Test\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// Test configuration
$baseUrl = 'http://localhost/public/api_unified.php';
$testResults = [];
$testUser = [
    'email' => 'apitest_' . time() . '@example.com',
    'password' => 'TestPass123!',
    'first_name' => 'API',
    'last_name' => 'Test'
];

/**
 * Make API request
 */
function makeAPIRequest($endpoint, $method = 'GET', $data = null, $headers = []) {
    global $baseUrl;
    
    $url = $baseUrl . '?endpoint=' . urlencode($endpoint);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    if ($data && ($method === 'POST' || $method === 'PUT')) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        $headers[] = 'Content-Type: application/json';
    }
    
    if (!empty($headers)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        return ['error' => $error, 'http_code' => 0];
    }
    
    $decoded = json_decode($response, true);
    return [
        'response' => $decoded,
        'http_code' => $httpCode,
        'raw' => $response
    ];
}

/**
 * Test API endpoint
 */
function testEndpoint($name, $endpoint, $method = 'GET', $data = null, $expectedCode = 200) {
    global $testResults;
    
    echo "🧪 Testing: $name\n";
    
    $result = makeAPIRequest($endpoint, $method, $data);
    
    $success = ($result['http_code'] === $expectedCode);
    
    if ($success && isset($result['response']['success'])) {
        $success = $result['response']['success'];
    }
    
    if ($success) {
        echo "✅ PASS: $name\n";
        $testResults[$name] = 'PASS';
    } else {
        echo "❌ FAIL: $name\n";
        echo "   Expected HTTP: $expectedCode, Got: {$result['http_code']}\n";
        if (isset($result['response']['error'])) {
            echo "   Error: {$result['response']['error']}\n";
        }
        if (isset($result['error'])) {
            echo "   cURL Error: {$result['error']}\n";
        }
        $testResults[$name] = 'FAIL';
    }
    
    echo "\n";
    return $result;
}

// Test 1: Database Schema Setup
echo "📊 Setting up database schema...\n";
try {
    $output = shell_exec('mysql -u root -p < database_schema.sql 2>&1');
    echo "✅ Database schema loaded\n\n";
} catch (Exception $e) {
    echo "❌ Database schema failed: " . $e->getMessage() . "\n\n";
}

// Test 2: Authentication Endpoints
echo "🔐 Testing Authentication APIs...\n";

// Test user registration
$registerResult = testEndpoint(
    'User Registration',
    'auth/register',
    'POST',
    $testUser,
    201
);

$userId = null;
if ($registerResult['response']['success'] ?? false) {
    $userId = $registerResult['response']['data']['user_id'] ?? null;
}

// Test user login
$loginResult = testEndpoint(
    'User Login',
    'auth/login',
    'POST',
    [
        'email' => $testUser['email'],
        'password' => $testUser['password']
    ]
);

$sessionId = null;
if ($loginResult['response']['success'] ?? false) {
    $sessionId = $loginResult['response']['data']['session_id'] ?? null;
}

// Test auth status
testEndpoint('Auth Status Check', 'auth/status');

// Test logout
testEndpoint('User Logout', 'auth/logout', 'POST');

// Test 3: User Management APIs
echo "👤 Testing User Management APIs...\n";

// Login again for authenticated tests
if ($sessionId) {
    makeAPIRequest('auth/login', 'POST', [
        'email' => $testUser['email'],
        'password' => $testUser['password']
    ]);
}

testEndpoint('Get User Profile', 'user/profile');
testEndpoint('Get User Credits', 'user/credits');
testEndpoint('Get User Subscription', 'user/subscription');
testEndpoint('Get User Stats', 'user/stats');
testEndpoint('Get User Settings', 'user/settings');

// Test profile update
testEndpoint(
    'Update User Profile',
    'user/profile',
    'PUT',
    [
        'first_name' => 'Updated',
        'bio' => 'Test bio for API testing'
    ]
);

// Test 4: Payment APIs
echo "💳 Testing Payment APIs...\n";

testEndpoint('Get Credit Packages', 'payments/packages');
testEndpoint('Get Subscription Plans', 'payments/plans');
testEndpoint('Get Payment History', 'payments/history');

// Test credit purchase (demo mode)
testEndpoint(
    'Purchase Credits (Demo)',
    'payments/purchase',
    'POST',
    [
        'package_id' => 'starter',
        'payment_method' => 'demo',
        'payment_data' => ['demo' => true]
    ]
);

// Test 5: Video Generation APIs
echo "🎬 Testing Video Generation APIs...\n";

// Test generation start
$generationResult = testEndpoint(
    'Start Video Generation',
    'generate',
    'POST',
    [
        'mood' => 'euphoric',
        'topic' => 'nature_wildlife',
        'inspiration' => ['cinematic', 'colorful']
    ],
    201
);

$jobId = null;
if ($generationResult['response']['success'] ?? false) {
    $jobId = $generationResult['response']['data']['job_id'] ?? null;
}

// Test generation status
if ($jobId) {
    testEndpoint('Get Generation Status', "generate/status/$jobId");
}

testEndpoint('Get Generation History', 'generate/history');

// Test 6: Dashboard APIs
echo "📊 Testing Dashboard APIs...\n";

testEndpoint('Get Dashboard Stats', 'dashboard/stats');
testEndpoint('Get Dashboard History', 'dashboard/history');
testEndpoint('Get Dashboard Analytics', 'dashboard/analytics');

// Test 7: Error Handling
echo "⚠️ Testing Error Handling...\n";

testEndpoint('Invalid Endpoint', 'invalid/endpoint', 'GET', null, 404);
testEndpoint('Unauthorized Access', 'user/profile', 'GET', null, 401);
testEndpoint('Invalid Login', 'auth/login', 'POST', [
    'email' => '<EMAIL>',
    'password' => 'wrongpassword'
], 401);

// Test 8: Input Validation
echo "✅ Testing Input Validation...\n";

testEndpoint('Missing Required Fields', 'auth/register', 'POST', [
    'email' => '<EMAIL>'
    // Missing password, first_name, last_name
], 400);

testEndpoint('Invalid Email Format', 'auth/register', 'POST', [
    'email' => 'invalid-email',
    'password' => 'ValidPass123!',
    'first_name' => 'Test',
    'last_name' => 'User'
], 400);

testEndpoint('Weak Password', 'auth/register', 'POST', [
    'email' => '<EMAIL>',
    'password' => '123',
    'first_name' => 'Test',
    'last_name' => 'User'
], 400);

// Test 9: Rate Limiting
echo "🚦 Testing Rate Limiting...\n";

// Make multiple rapid requests
$rateLimitPassed = true;
for ($i = 0; $i < 12; $i++) {
    $result = makeAPIRequest('auth/login', 'POST', [
        'email' => '<EMAIL>',
        'password' => 'wrongpassword'
    ]);
    
    if ($result['http_code'] === 429) {
        echo "✅ Rate limiting triggered after $i attempts\n";
        $testResults['Rate Limiting'] = 'PASS';
        $rateLimitPassed = true;
        break;
    }
}

if (!$rateLimitPassed) {
    echo "❌ Rate limiting not working\n";
    $testResults['Rate Limiting'] = 'FAIL';
}

// Test 10: Security Headers
echo "🔒 Testing Security Headers...\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '?endpoint=auth/status');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_NOBODY, true);

$headers = curl_exec($ch);
curl_close($ch);

$securityHeaders = [
    'X-Content-Type-Options: nosniff',
    'X-Frame-Options: DENY',
    'X-XSS-Protection: 1; mode=block'
];

$securityPassed = true;
foreach ($securityHeaders as $header) {
    if (strpos($headers, $header) === false) {
        echo "❌ Missing security header: $header\n";
        $securityPassed = false;
    }
}

if ($securityPassed) {
    echo "✅ All security headers present\n";
    $testResults['Security Headers'] = 'PASS';
} else {
    $testResults['Security Headers'] = 'FAIL';
}

// Cleanup test data
echo "\n🧹 Cleaning up test data...\n";
if ($userId) {
    try {
        require_once 'core/database_manager.php';
        $db = new DatabaseManager();
        
        // Delete test user and related data
        $db->delete('users', ['user_id' => $userId]);
        $db->delete('user_profiles', ['user_id' => $userId]);
        $db->delete('user_credit_balances', ['user_id' => $userId]);
        $db->delete('credit_transactions', ['user_id' => $userId]);
        $db->delete('user_subscriptions', ['user_id' => $userId]);
        $db->delete('generation_jobs', ['user_id' => $userId]);
        $db->delete('payment_transactions', ['user_id' => $userId]);
        
        echo "✅ Test data cleaned up\n";
    } catch (Exception $e) {
        echo "⚠️ Cleanup warning: " . $e->getMessage() . "\n";
    }
}

// Generate final report
echo "\n" . str_repeat("=", 60) . "\n";
echo "📋 API SYSTEM TEST REPORT\n";
echo str_repeat("=", 60) . "\n\n";

$totalTests = count($testResults);
$passedTests = count(array_filter($testResults, function($r) { return $r === 'PASS'; }));
$failedTests = $totalTests - $passedTests;

echo "Total Tests: $totalTests\n";
echo "Passed: $passedTests\n";
echo "Failed: $failedTests\n";
echo "Success Rate: " . round(($passedTests / $totalTests) * 100, 1) . "%\n\n";

echo "📊 DETAILED RESULTS:\n";
foreach ($testResults as $test => $result) {
    $icon = $result === 'PASS' ? '✅' : '❌';
    echo "$icon $test: $result\n";
}

if ($failedTests === 0) {
    echo "\n🎉 ALL API TESTS PASSED! 🎉\n";
    echo "\n✨ Your Sutradhar 2070 API system is fully functional!\n\n";
    echo "🚀 READY FOR PRODUCTION:\n";
    echo "   • Authentication system ✅\n";
    echo "   • User management ✅\n";
    echo "   • Video generation ✅\n";
    echo "   • Payment processing ✅\n";
    echo "   • Dashboard APIs ✅\n";
    echo "   • Error handling ✅\n";
    echo "   • Security measures ✅\n\n";
} else {
    echo "\n⚠️ SOME TESTS FAILED\n";
    echo "\n🔧 Please review the failed tests above and:\n";
    echo "1. Check database configuration\n";
    echo "2. Verify API endpoint implementations\n";
    echo "3. Ensure all required files are present\n";
    echo "4. Check server configuration\n";
}

// Save test report
$reportData = [
    'timestamp' => date('Y-m-d H:i:s'),
    'summary' => [
        'total_tests' => $totalTests,
        'passed' => $passedTests,
        'failed' => $failedTests,
        'success_rate' => round(($passedTests / $totalTests) * 100, 1)
    ],
    'results' => $testResults
];

if (!is_dir('tests/reports')) {
    mkdir('tests/reports', 0755, true);
}

file_put_contents(
    'tests/reports/api_system_test_' . date('Y-m-d_H-i-s') . '.json',
    json_encode($reportData, JSON_PRETTY_PRINT)
);

echo "\n📄 Test report saved to: tests/reports/api_system_test_" . date('Y-m-d_H-i-s') . ".json\n";
?>
