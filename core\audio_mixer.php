<?php
/**
 * Audio Mixer - Combines voices with background music and effects
 * Part of Sutradhar Engine
 */

class AudioMixer {
    private $config;
    private $tempDir;
    private $assetsDir;

    public function __construct() {
        $this->loadConfig();
        $this->setupDirectories();
    }

    private function loadConfig() {
        $configPath = __DIR__ . '/../config/settings.json';
        $this->config = json_decode(file_get_contents($configPath), true);
        $this->tempDir = __DIR__ . '/../temp/';
        $this->assetsDir = __DIR__ . '/../assets/';
    }

    private function setupDirectories() {
        if (!is_dir($this->tempDir)) {
            mkdir($this->tempDir, 0755, true);
        }
    }

    /**
     * Mix voice segments with background music and effects
     */
    public function mixAudio($voiceFiles, $segments, $style, $background, $jobId) {
        $mixedAudioFile = $this->tempDir . $jobId . '_mixed.wav';
        
        // Create timeline for mixing
        $timeline = $this->createTimeline($voiceFiles, $segments);
        
        // Get background music
        $backgroundMusic = $this->getBackgroundMusic($style, $background);
        
        // Get sound effects
        $soundEffects = $this->getSoundEffects($style, $segments);
        
        // Mix all audio components
        $success = $this->performMixing($timeline, $backgroundMusic, $soundEffects, $mixedAudioFile);
        
        if (!$success) {
            throw new Exception("Failed to mix audio components");
        }

        // Convert to MP3 if possible, otherwise keep as WAV
        $finalAudioFile = $this->convertToMP3($mixedAudioFile, $jobId);

        return [
            'file' => $finalAudioFile,
            'duration' => $this->getAudioDuration($finalAudioFile),
            'format' => pathinfo($finalAudioFile, PATHINFO_EXTENSION),
            'sample_rate' => $this->config['audio']['sample_rate'] ?? 44100,
            'channels' => $this->config['audio']['channels'] ?? 2
        ];
    }

    /**
     * Create timeline for audio mixing
     */
    private function createTimeline($voiceFiles, $segments) {
        $timeline = [];
        $currentTime = 0;

        foreach ($voiceFiles as $index => $voiceFile) {
            $segment = $segments[$index];
            $duration = $voiceFile['duration'];
            
            $timeline[] = [
                'type' => 'voice',
                'file' => $voiceFile['file'],
                'start_time' => $currentTime,
                'duration' => $duration,
                'volume' => $this->config['audio']['voice_volume'],
                'segment_data' => $segment
            ];

            $currentTime += $duration;
            
            // Add pause if specified
            if (isset($segment['pause_after'])) {
                $currentTime += $segment['pause_after'];
            }
        }

        return $timeline;
    }

    /**
     * Get background music file based on style and background
     */
    private function getBackgroundMusic($style, $background) {
        $musicDir = $this->assetsDir . 'audio/music/';
        
        // Map style to music category
        $musicMapping = [
            'funny' => 'comedy',
            'desi' => 'traditional',
            'emotional' => 'emotional',
            'bollywood' => 'bollywood'
        ];

        $musicCategory = $musicMapping[$style] ?? 'background';
        $musicFile = $musicDir . $musicCategory . '.mp3';

        // Fallback to generic background music
        if (!file_exists($musicFile)) {
            $musicFile = $musicDir . 'background.mp3';
        }

        // If no music file exists, create silence
        if (!file_exists($musicFile)) {
            $musicFile = $this->createSilence(300); // 5 minutes of silence
        }

        return $musicFile;
    }

    /**
     * Get sound effects for segments
     */
    private function getSoundEffects($style, $segments) {
        $effectsDir = $this->assetsDir . 'audio/effects/';
        $effects = [];

        $styleEffects = $this->config['styles'][$style]['effects'] ?? [];

        foreach ($segments as $index => $segment) {
            $segmentEffects = [];

            // Add style-specific effects
            foreach ($styleEffects as $effect) {
                $effectFile = $effectsDir . $effect . '.mp3';
                if (file_exists($effectFile)) {
                    $segmentEffects[] = [
                        'file' => $effectFile,
                        'timing' => 'background', // or 'start', 'end'
                        'volume' => $this->config['audio']['effects_volume']
                    ];
                }
            }

            // Add emphasis-based effects
            if (isset($segment['emphasis'])) {
                foreach ($segment['emphasis'] as $emphasis) {
                    switch ($emphasis) {
                        case 'exclamation':
                            $effectFile = $effectsDir . 'emphasis.mp3';
                            break;
                        case 'question':
                            $effectFile = $effectsDir . 'question.mp3';
                            break;
                        case 'caps':
                            $effectFile = $effectsDir . 'loud.mp3';
                            break;
                    }
                    
                    if (isset($effectFile) && file_exists($effectFile)) {
                        $segmentEffects[] = [
                            'file' => $effectFile,
                            'timing' => 'start',
                            'volume' => $this->config['audio']['effects_volume']
                        ];
                    }
                }
            }

            $effects[$index] = $segmentEffects;
        }

        return $effects;
    }

    /**
     * Perform the actual audio mixing using FFmpeg or fallback
     */
    private function performMixing($timeline, $backgroundMusic, $soundEffects, $outputFile) {
        // Check if FFmpeg is available
        $ffmpegAvailable = $this->isFFmpegAvailable();

        if (!$ffmpegAvailable) {
            return $this->performFallbackMixing($timeline, $outputFile);
        }
        // Calculate total duration
        $totalDuration = 0;
        foreach ($timeline as $item) {
            $endTime = $item['start_time'] + $item['duration'];
            if ($endTime > $totalDuration) {
                $totalDuration = $endTime;
            }
        }

        // Create FFmpeg filter complex
        $inputs = [];
        $filters = [];
        $inputIndex = 0;

        // Add background music (loop if necessary)
        $inputs[] = "-i " . escapeshellarg($backgroundMusic);
        $filters[] = sprintf(
            "[%d:a]aloop=loop=-1:size=2e+09,atrim=duration=%f,volume=%f[bg]",
            $inputIndex++,
            $totalDuration,
            $this->config['audio']['background_volume']
        );

        // Add voice segments
        $voiceFilters = [];
        foreach ($timeline as $item) {
            if ($item['type'] === 'voice') {
                $inputs[] = "-i " . escapeshellarg($item['file']);
                $voiceFilters[] = sprintf(
                    "[%d:a]adelay=%d|%d,volume=%f[v%d]",
                    $inputIndex++,
                    intval($item['start_time'] * 1000),
                    intval($item['start_time'] * 1000),
                    $item['volume'],
                    count($voiceFilters)
                );
            }
        }

        $filters = array_merge($filters, $voiceFilters);

        // Add sound effects
        $effectFilters = [];
        foreach ($soundEffects as $segmentIndex => $effects) {
            foreach ($effects as $effectIndex => $effect) {
                $inputs[] = "-i " . escapeshellarg($effect['file']);
                $timing = $timeline[$segmentIndex]['start_time'];
                
                if ($effect['timing'] === 'end') {
                    $timing += $timeline[$segmentIndex]['duration'];
                }

                $effectFilters[] = sprintf(
                    "[%d:a]adelay=%d|%d,volume=%f[e%d_%d]",
                    $inputIndex++,
                    intval($timing * 1000),
                    intval($timing * 1000),
                    $effect['volume'],
                    $segmentIndex,
                    $effectIndex
                );
            }
        }

        $filters = array_merge($filters, $effectFilters);

        // Mix all audio streams
        $mixInputs = ['[bg]'];
        for ($i = 0; $i < count($voiceFilters); $i++) {
            $mixInputs[] = "[v{$i}]";
        }
        
        foreach ($soundEffects as $segmentIndex => $effects) {
            foreach ($effects as $effectIndex => $effect) {
                $mixInputs[] = "[e{$segmentIndex}_{$effectIndex}]";
            }
        }

        $filters[] = implode('', $mixInputs) . "amix=inputs=" . count($mixInputs) . ":duration=longest[out]";

        // Build FFmpeg command
        $command = sprintf(
            'ffmpeg %s -filter_complex "%s" -map "[out]" -c:a mp3 -b:a %s %s -y',
            implode(' ', $inputs),
            implode(';', $filters),
            $this->config['audio']['bitrate'],
            escapeshellarg($outputFile)
        );

        // Execute command
        $result = shell_exec($command . ' 2>&1');
        
        return file_exists($outputFile) && filesize($outputFile) > 0;
    }

    /**
     * Create silence audio file
     */
    private function createSilence($duration) {
        $silenceFile = $this->tempDir . 'silence_' . $duration . '.mp3';
        
        if (!file_exists($silenceFile)) {
            $command = sprintf(
                'ffmpeg -f lavfi -i "anullsrc=channel_layout=stereo:sample_rate=%d" -t %d %s -y',
                $this->config['voice']['sample_rate'],
                $duration,
                escapeshellarg($silenceFile)
            );

            shell_exec($command . ' 2>&1');
        }

        return $silenceFile;
    }

    /**
     * Get audio file duration
     */
    private function getAudioDuration($audioFile) {
        if (!file_exists($audioFile)) {
            return 0;
        }

        $command = sprintf(
            'ffprobe -v quiet -show_entries format=duration -of csv="p=0" %s',
            escapeshellarg($audioFile)
        );

        $duration = shell_exec($command);
        return floatval(trim($duration ?? '0'));
    }

    /**
     * Normalize audio levels
     */
    public function normalizeAudio($audioFile) {
        $normalizedFile = str_replace('.mp3', '_normalized.mp3', $audioFile);
        
        $command = sprintf(
            'ffmpeg -i %s -af "loudnorm=I=-16:TP=-1.5:LRA=11" %s -y',
            escapeshellarg($audioFile),
            escapeshellarg($normalizedFile)
        );

        shell_exec($command . ' 2>&1');
        
        if (file_exists($normalizedFile)) {
            return $normalizedFile;
        }

        return $audioFile;
    }

    /**
     * Apply fade in/out effects
     */
    public function applyFades($audioFile, $fadeInDuration = 1.0, $fadeOutDuration = 2.0) {
        $fadedFile = str_replace('.mp3', '_faded.mp3', $audioFile);
        $duration = $this->getAudioDuration($audioFile);
        
        $command = sprintf(
            'ffmpeg -i %s -af "afade=t=in:ss=0:d=%f,afade=t=out:st=%f:d=%f" %s -y',
            escapeshellarg($audioFile),
            $fadeInDuration,
            $duration - $fadeOutDuration,
            $fadeOutDuration,
            escapeshellarg($fadedFile)
        );

        shell_exec($command . ' 2>&1');
        
        if (file_exists($fadedFile)) {
            return $fadedFile;
        }

        return $audioFile;
    }

    /**
     * Export audio in different formats
     */
    public function exportAudio($audioFile, $format = 'mp3') {
        $pathInfo = pathinfo($audioFile);
        $exportFile = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '.' . $format;
        
        $codecMap = [
            'mp3' => 'mp3',
            'wav' => 'pcm_s16le',
            'ogg' => 'libvorbis',
            'm4a' => 'aac'
        ];

        $codec = $codecMap[$format] ?? 'mp3';
        
        $command = sprintf(
            'ffmpeg -i %s -c:a %s %s -y',
            escapeshellarg($audioFile),
            $codec,
            escapeshellarg($exportFile)
        );

        shell_exec($command . ' 2>&1');
        
        return file_exists($exportFile) ? $exportFile : $audioFile;
    }

    /**
     * Check if FFmpeg is available
     */
    private function isFFmpegAvailable() {
        $output = shell_exec('ffmpeg -version 2>&1');
        return $output && strpos($output, 'ffmpeg version') !== false;
    }

    /**
     * Fallback mixing when FFmpeg is not available
     */
    private function performFallbackMixing($timeline, $outputFile) {
        // Simple fallback: concatenate voice files or use the first one
        if (empty($timeline)) {
            return false;
        }

        // Find the first voice file
        $firstVoiceFile = null;
        foreach ($timeline as $item) {
            if ($item['type'] === 'voice' && file_exists($item['file'])) {
                $firstVoiceFile = $item['file'];
                break;
            }
        }

        if (!$firstVoiceFile) {
            // Create a simple silence file
            return $this->createSimpleSilence($outputFile, 5); // 5 seconds of silence
        }

        // Copy the first voice file as output
        return copy($firstVoiceFile, $outputFile);
    }

    /**
     * Create simple silence without FFmpeg
     */
    private function createSimpleSilence($outputFile, $duration) {
        // Create a simple WAV file with silence (similar to voice engine fallback)
        $sampleRate = 44100;
        $channels = 2;
        $bitsPerSample = 16;

        $numSamples = intval($duration * $sampleRate);
        $dataSize = $numSamples * $channels * ($bitsPerSample / 8);

        // Create WAV header
        $header = pack('V', 0x46464952); // "RIFF"
        $header .= pack('V', $dataSize + 36); // File size - 8
        $header .= pack('V', 0x45564157); // "WAVE"
        $header .= pack('V', 0x20746d66); // "fmt "
        $header .= pack('V', 16); // Subchunk1Size
        $header .= pack('v', 1); // AudioFormat (PCM)
        $header .= pack('v', $channels); // NumChannels
        $header .= pack('V', $sampleRate); // SampleRate
        $header .= pack('V', $sampleRate * $channels * ($bitsPerSample / 8)); // ByteRate
        $header .= pack('v', $channels * ($bitsPerSample / 8)); // BlockAlign
        $header .= pack('v', $bitsPerSample); // BitsPerSample
        $header .= pack('V', 0x61746164); // "data"
        $header .= pack('V', $dataSize); // Subchunk2Size

        // Create audio data (silence)
        $audioData = str_repeat(pack('v', 0), $numSamples * $channels);

        // Write the WAV file
        $wavContent = $header . $audioData;
        $result = file_put_contents($outputFile, $wavContent);

        return $result !== false;
    }

    /**
     * Convert WAV to MP3 if FFmpeg is available
     */
    private function convertToMP3($wavFile, $jobId) {
        if (!$this->isFFmpegAvailable()) {
            // If FFmpeg is not available, return the WAV file as is
            return $wavFile;
        }

        $mp3File = str_replace('.wav', '.mp3', $wavFile);

        $command = sprintf(
            'ffmpeg -i %s -codec:a libmp3lame -b:a %s %s -y',
            escapeshellarg($wavFile),
            $this->config['audio']['bitrate'] ?? '192k',
            escapeshellarg($mp3File)
        );

        shell_exec($command . ' 2>&1');

        if (file_exists($mp3File) && filesize($mp3File) > 0) {
            return $mp3File;
        }

        // If conversion failed, return original WAV file
        return $wavFile;
    }

    /**
     * Clean up temporary files
     */
    public function cleanup($jobId) {
        $pattern = $this->tempDir . $jobId . '_*';
        $files = glob($pattern);

        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
    }
}
