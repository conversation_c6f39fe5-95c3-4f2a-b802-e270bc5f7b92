<?php
/**
 * Base Tool Class
 * Abstract base class for all tools in the Sutradhar platform
 */

abstract class BaseTool {
    protected $toolId;
    protected $name;
    protected $description;
    protected $icon;
    protected $creditCost;
    protected $maxFileSize;
    protected $maxFilesPerRequest;
    protected $allowedFormats;
    protected $processingOptions;
    protected $requiresSubscription;
    
    protected $db;
    protected $creditManager;
    protected $tempDir;
    
    public function __construct() {
        // Get the project root and construct proper paths for XAMPP
        $projectRoot = dirname(__DIR__, 2); // Go up from core/tools to project root
        $this->tempDir = $projectRoot . '/temp/tools/';
        $this->ensureTempDirectory();
        
        // Initialize dependencies
        require_once __DIR__ . '/../tools_database_manager.php';
        require_once __DIR__ . '/../credit_manager.php';
        
        $this->db = new ToolsDatabaseManager();
        $this->creditManager = new CreditManager();
        
        // Load tool configuration
        $this->loadConfiguration();
    }
    
    /**
     * Abstract method that each tool must implement
     */
    abstract public function process($files, $options = [], $userId = null, $operationId = null);
    
    /**
     * Get tool information
     */
    public function getInfo() {
        return [
            'tool_id' => $this->toolId,
            'name' => $this->name,
            'description' => $this->description,
            'icon' => $this->icon,
            'credit_cost' => $this->creditCost,
            'max_file_size' => $this->maxFileSize,
            'max_files_per_request' => $this->maxFilesPerRequest,
            'allowed_formats' => $this->allowedFormats,
            'processing_options' => $this->processingOptions,
            'requires_subscription' => $this->requiresSubscription
        ];
    }
    
    /**
     * Validate uploaded files
     */
    protected function validateFiles($files) {
        if (empty($files)) {
            throw new ToolException('No files provided');
        }
        
        if (count($files) > $this->maxFilesPerRequest) {
            throw new ToolException("Too many files. Maximum allowed: {$this->maxFilesPerRequest}");
        }
        
        foreach ($files as $file) {
            $this->validateSingleFile($file);
        }
    }
    
    /**
     * Validate a single file
     */
    protected function validateSingleFile($file) {
        // Check file size
        if ($file['size'] > $this->maxFileSize) {
            $maxSizeMB = round($this->maxFileSize / 1024 / 1024, 1);
            throw new ToolException("File '{$file['name']}' is too large. Maximum size: {$maxSizeMB}MB");
        }
        
        // Check file format
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $this->allowedFormats)) {
            $allowedStr = implode(', ', $this->allowedFormats);
            throw new ToolException("File '{$file['name']}' has unsupported format. Allowed: {$allowedStr}");
        }
        
        // Check file upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new ToolException("File upload error for '{$file['name']}': " . $this->getUploadErrorMessage($file['error']));
        }
        
        // Validate file content (basic security check)
        $this->validateFileContent($file);
    }
    
    /**
     * Validate file content for security
     */
    protected function validateFileContent($file) {
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        // Define allowed MIME types for each extension
        $allowedMimeTypes = [
            'jpg' => ['image/jpeg'],
            'jpeg' => ['image/jpeg'],
            'png' => ['image/png'],
            'gif' => ['image/gif'],
            'webp' => ['image/webp'],
            'bmp' => ['image/bmp', 'image/x-ms-bmp'],
            'tiff' => ['image/tiff'],
            'svg' => ['image/svg+xml'],
            'pdf' => ['application/pdf'],
            'txt' => ['text/plain'],
            'zip' => ['application/zip']
        ];
        
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $expectedMimeTypes = $allowedMimeTypes[$extension] ?? [];
        
        if (!empty($expectedMimeTypes) && !in_array($mimeType, $expectedMimeTypes)) {
            throw new ToolException("File '{$file['name']}' appears to be corrupted or has incorrect format");
        }
    }
    
    /**
     * Generate unique operation ID
     */
    protected function generateOperationId() {
        return 'op_' . uniqid() . '_' . time();
    }
    
    /**
     * Store uploaded file securely
     */
    protected function storeUploadedFile($file, $operationId, $userId) {
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $storedName = $operationId . '_input_' . uniqid() . '.' . $extension;
        $storedPath = $this->tempDir . $storedName;
        
        if (!move_uploaded_file($file['tmp_name'], $storedPath)) {
            throw new ToolException("Failed to store uploaded file: {$file['name']}");
        }
        
        // Store file info in database
        $fileData = [
            'original_name' => $file['name'],
            'stored_name' => $storedName,
            'file_path' => $storedPath,
            'file_type' => $extension,
            'file_size' => $file['size'],
            'mime_type' => $file['type'],
            'is_input' => true,
            'is_output' => false,
            'expires_at' => date('Y-m-d H:i:s', strtotime('+24 hours'))
        ];
        
        $this->db->storeFileInfo($userId, $operationId, $fileData);
        
        return $storedPath;
    }
    
    /**
     * Store processed file
     */
    protected function storeProcessedFile($filePath, $originalName, $operationId, $userId) {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        $baseName = pathinfo($originalName, PATHINFO_FILENAME);
        $storedName = $operationId . '_output_' . $baseName . '_processed.' . $extension;
        $storedPath = $this->tempDir . $storedName;
        
        if (!copy($filePath, $storedPath)) {
            throw new ToolException("Failed to store processed file");
        }
        
        // Store file info in database
        $fileData = [
            'original_name' => $baseName . '_processed.' . $extension,
            'stored_name' => $storedName,
            'file_path' => $storedPath,
            'file_type' => $extension,
            'file_size' => filesize($storedPath),
            'mime_type' => mime_content_type($storedPath),
            'is_input' => false,
            'is_output' => true,
            'expires_at' => date('Y-m-d H:i:s', strtotime('+24 hours'))
        ];
        
        $this->db->storeFileInfo($userId, $operationId, $fileData);
        
        // Create download URL relative to public directory
        $baseUrl = $this->getBaseUrl();

        return [
            'stored_path' => $storedPath,
            'stored_name' => $storedName,
            'download_url' => $baseUrl . '../temp/tools/' . $storedName,
            'file_size' => filesize($storedPath)
        ];
    }
    
    /**
     * Load image from file
     */
    protected function loadImage($filePath) {
        $imageInfo = getimagesize($filePath);
        if (!$imageInfo) {
            throw new ToolException("Invalid image file: " . basename($filePath));
        }
        
        $mimeType = $imageInfo['mime'];
        
        switch ($mimeType) {
            case 'image/jpeg':
                return imagecreatefromjpeg($filePath);
            case 'image/png':
                return imagecreatefrompng($filePath);
            case 'image/gif':
                return imagecreatefromgif($filePath);
            case 'image/webp':
                return imagecreatefromwebp($filePath);
            case 'image/bmp':
                return imagecreatefrombmp($filePath);
            default:
                throw new ToolException("Unsupported image format: $mimeType");
        }
    }
    
    /**
     * Save image to file
     */
    protected function saveImage($image, $filePath, $format = 'png', $quality = 85) {
        $format = strtolower($format);
        
        switch ($format) {
            case 'jpg':
            case 'jpeg':
                return imagejpeg($image, $filePath, $quality);
            case 'png':
                $pngQuality = 9 - (int)($quality / 10);
                return imagepng($image, $filePath, $pngQuality);
            case 'gif':
                return imagegif($image, $filePath);
            case 'webp':
                return imagewebp($image, $filePath, $quality);
            case 'bmp':
                return imagebmp($image, $filePath);
            default:
                throw new ToolException("Unsupported output format: $format");
        }
    }
    
    /**
     * Clean up temporary files
     */
    protected function cleanup($filePaths) {
        foreach ($filePaths as $filePath) {
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }
    }
    
    /**
     * Log processing time and update database
     */
    protected function logProcessingTime($operationId, $startTime, $status = 'completed', $errorMessage = null) {
        $processingTime = microtime(true) - $startTime;
        
        $updateData = [
            'processing_time' => round($processingTime, 3),
            'status' => $status
        ];
        
        if ($errorMessage) {
            $updateData['error_message'] = $errorMessage;
        }
        
        $this->db->updateToolUsage($operationId, $status, $updateData);
    }
    
    /**
     * Load tool configuration from database
     */
    protected function loadConfiguration() {
        $config = $this->db->getToolConfig($this->toolId);
        
        if ($config) {
            $this->name = $config['name'];
            $this->description = $config['description'];
            $this->icon = $config['icon'];
            $this->creditCost = (int)$config['credit_cost'];
            $this->maxFileSize = (int)$config['max_file_size'];
            $this->maxFilesPerRequest = (int)$config['max_files_per_request'];
            $this->allowedFormats = json_decode($config['allowed_formats'], true) ?: [];
            $this->processingOptions = json_decode($config['processing_options'], true) ?: [];
            $this->requiresSubscription = $config['requires_subscription'];
        }
    }
    
    /**
     * Get base URL for the application
     */
    private function getBaseUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
        $basePath = dirname($scriptName);

        // For XAMPP, construct the base URL properly
        if (strpos($basePath, '/public') !== false) {
            $basePath = str_replace('/public', '', $basePath);
        }

        return $protocol . '://' . $host . $basePath . '/public/';
    }

    /**
     * Ensure temp directory exists
     */
    private function ensureTempDirectory() {
        if (!is_dir($this->tempDir)) {
            mkdir($this->tempDir, 0755, true);
        }
    }
    
    /**
     * Get upload error message
     */
    private function getUploadErrorMessage($errorCode) {
        $errors = [
            UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize directive',
            UPLOAD_ERR_FORM_SIZE => 'File exceeds MAX_FILE_SIZE directive',
            UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'File upload stopped by extension'
        ];
        
        return $errors[$errorCode] ?? 'Unknown upload error';
    }
}

/**
 * Custom exception for tool errors
 */
class ToolException extends Exception {
    public function __construct($message = "Tool processing error", $code = 0, Exception $previous = null) {
        parent::__construct($message, $code, $previous);
    }
}
?>
