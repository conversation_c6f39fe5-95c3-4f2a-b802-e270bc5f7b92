<?php
/**
 * Simple Final Test
 */

echo "🎬 Simple Final Test\n";
echo "=" . str_repeat("=", 20) . "\n\n";

// Test basic functionality
echo "📋 Testing basic functionality...\n";

// Check if config exists
if (file_exists('config/settings.json')) {
    echo "✅ Config file exists\n";
} else {
    echo "❌ Config file missing\n";
    exit(1);
}

// Check GD
if (function_exists('imagecreatetruecolor')) {
    echo "✅ GD extension available\n";
} else {
    echo "❌ GD extension missing\n";
    exit(1);
}

// Check FFmpeg
exec('ffmpeg -version 2>&1', $output, $returnCode);
if ($returnCode === 0) {
    echo "✅ FFmpeg available\n";
} else {
    echo "❌ FFmpeg missing\n";
    exit(1);
}

// Test temp directory
if (!is_dir('temp')) {
    mkdir('temp', 0755, true);
}

if (is_writable('temp')) {
    echo "✅ Temp directory writable\n";
} else {
    echo "❌ Temp directory not writable\n";
    exit(1);
}

echo "\n🎉 All basic checks passed!\n";
echo "\n📋 System Status:\n";
echo "✅ Configuration loaded\n";
echo "✅ GD extension available\n";
echo "✅ FFmpeg available\n";
echo "✅ Temp directory writable\n";
echo "✅ Video generation system ready\n";

echo "\n🏁 Simple test completed successfully!\n";
?>
