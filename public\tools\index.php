<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo $_SESSION['csrf_token'] ?? ''; ?>">
    <title>Sutradhar Tools - Advanced AI-Powered Utilities</title>
    
    <!-- Futuristic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;900&family=Exo+2:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Feather Icons -->
    <script src="https://unpkg.com/feather-icons"></script>
    
    <!-- Tools Styles -->
    <link rel="stylesheet" href="/assets/css/tools.css">
    <link rel="stylesheet" href="/assets/css/futuristic-ui.css">
    
    <!-- Meta Tags for SEO -->
    <meta name="description" content="Advanced AI-powered tools for image processing, content generation, and digital utilities. Experience the future of digital creativity.">
    <meta name="keywords" content="AI tools, image processing, QR generator, background remover, format converter">
    
    <!-- Open Graph -->
    <meta property="og:title" content="Sutradhar Tools - Advanced AI-Powered Utilities">
    <meta property="og:description" content="25+ professional tools for image processing, content generation, and digital utilities">
    <meta property="og:type" content="website">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "Sutradhar Tools",
        "description": "Advanced AI-powered tools for digital content creation",
        "applicationCategory": "UtilitiesApplication",
        "operatingSystem": "Web Browser"
    }
    </script>
</head>
<body class="tools-page">
    <!-- Navigation -->
    <nav class="futuristic-nav">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="/" class="brand-link">
                    <div class="brand-icon">
                        <i data-feather="zap"></i>
                    </div>
                    <span class="brand-text">Sutradhar</span>
                </a>
            </div>
            
            <div class="nav-menu">
                <a href="/" class="nav-link">
                    <i data-feather="home"></i>
                    <span>Home</span>
                </a>
                <a href="/tools" class="nav-link active">
                    <i data-feather="tool"></i>
                    <span>Tools</span>
                </a>
                <a href="/generate" class="nav-link">
                    <i data-feather="video"></i>
                    <span>Generate</span>
                </a>
                <a href="/dashboard" class="nav-link">
                    <i data-feather="bar-chart-2"></i>
                    <span>Dashboard</span>
                </a>
            </div>
            
            <div class="nav-actions">
                <div class="credits-display">
                    <i data-feather="star"></i>
                    <span id="user-credits">Loading...</span>
                </div>
                <div class="user-menu">
                    <button class="user-avatar" id="user-menu-btn">
                        <i data-feather="user"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="tools-main">
        <!-- Hero Section -->
        <section class="tools-hero">
            <div class="hero-container">
                <div class="hero-content">
                    <h1 class="hero-title">
                        <span class="title-main">Advanced AI Tools</span>
                        <span class="title-sub">Unleash Your Creative Potential</span>
                    </h1>
                    <p class="hero-description">
                        Experience the future of digital creativity with our suite of 25+ AI-powered tools. 
                        From image processing to content generation, everything you need in one place.
                    </p>
                    
                    <!-- Quick Stats -->
                    <div class="hero-stats">
                        <div class="stat-item">
                            <div class="stat-number" id="total-tools">25+</div>
                            <div class="stat-label">Tools Available</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="operations-today">0</div>
                            <div class="stat-label">Operations Today</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="time-saved">∞</div>
                            <div class="stat-label">Time Saved</div>
                        </div>
                    </div>
                </div>
                
                <div class="hero-visual">
                    <div class="floating-tools">
                        <div class="tool-icon" style="--delay: 0s">
                            <i data-feather="image"></i>
                        </div>
                        <div class="tool-icon" style="--delay: 0.5s">
                            <i data-feather="zap"></i>
                        </div>
                        <div class="tool-icon" style="--delay: 1s">
                            <i data-feather="settings"></i>
                        </div>
                        <div class="tool-icon" style="--delay: 1.5s">
                            <i data-feather="layers"></i>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Tools Interface -->
        <section class="tools-interface">
            <div class="interface-container">
                <!-- Search and Filters -->
                <div class="tools-controls">
                    <div class="search-container">
                        <div class="search-box">
                            <i data-feather="search"></i>
                            <input type="text" id="tools-search" placeholder="Search tools..." autocomplete="off">
                            <button class="search-clear" id="search-clear" style="display: none;">
                                <i data-feather="x"></i>
                            </button>
                        </div>
                        
                        <div class="search-suggestions" id="search-suggestions"></div>
                    </div>
                    
                    <div class="filter-controls">
                        <div class="category-tabs" id="category-tabs">
                            <button class="tab-btn active" data-category="all">
                                <i data-feather="grid"></i>
                                <span>All Tools</span>
                            </button>
                            <button class="tab-btn" data-category="image_processing">
                                <i data-feather="image"></i>
                                <span>Image Processing</span>
                            </button>
                            <button class="tab-btn" data-category="generators">
                                <i data-feather="zap"></i>
                                <span>Generators</span>
                            </button>
                            <button class="tab-btn" data-category="utilities">
                                <i data-feather="tool"></i>
                                <span>Utilities</span>
                            </button>
                            <button class="tab-btn" data-category="advanced">
                                <i data-feather="settings"></i>
                                <span>Advanced</span>
                            </button>
                        </div>
                        
                        <div class="view-controls">
                            <button class="view-btn active" data-view="grid" title="Grid View">
                                <i data-feather="grid"></i>
                            </button>
                            <button class="view-btn" data-view="list" title="List View">
                                <i data-feather="list"></i>
                            </button>
                            <button class="favorites-btn" id="favorites-toggle" title="Show Favorites">
                                <i data-feather="heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Tools Grid -->
                <div class="tools-grid" id="tools-grid">
                    <!-- Loading State -->
                    <div class="loading-state" id="loading-state">
                        <div class="loading-spinner">
                            <div class="spinner-ring"></div>
                            <div class="spinner-ring"></div>
                            <div class="spinner-ring"></div>
                        </div>
                        <p>Loading advanced tools...</p>
                    </div>
                    
                    <!-- Tools will be loaded here dynamically -->
                </div>
                
                <!-- Empty State -->
                <div class="empty-state" id="empty-state" style="display: none;">
                    <div class="empty-icon">
                        <i data-feather="search"></i>
                    </div>
                    <h3>No tools found</h3>
                    <p>Try adjusting your search or filter criteria</p>
                    <button class="btn-primary" onclick="clearFilters()">
                        <i data-feather="refresh-cw"></i>
                        Clear Filters
                    </button>
                </div>
            </div>
        </section>

        <!-- Quick Actions -->
        <section class="quick-actions">
            <div class="actions-container">
                <h2 class="section-title">Quick Actions</h2>
                <div class="actions-grid">
                    <div class="action-card" data-action="upload-convert">
                        <div class="action-icon">
                            <i data-feather="upload"></i>
                        </div>
                        <h3>Upload & Convert</h3>
                        <p>Drag and drop files for instant format conversion</p>
                    </div>
                    
                    <div class="action-card" data-action="qr-generator">
                        <div class="action-icon">
                            <i data-feather="square"></i>
                        </div>
                        <h3>Generate QR Code</h3>
                        <p>Create custom QR codes with logos and styling</p>
                    </div>
                    
                    <div class="action-card" data-action="background-remove">
                        <div class="action-icon">
                            <i data-feather="scissors"></i>
                        </div>
                        <h3>Remove Background</h3>
                        <p>AI-powered background removal in seconds</p>
                    </div>
                    
                    <div class="action-card" data-action="batch-resize">
                        <div class="action-icon">
                            <i data-feather="maximize"></i>
                        </div>
                        <h3>Batch Resize</h3>
                        <p>Resize multiple images with smart cropping</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Tool Modal -->
    <div class="modal-overlay" id="tool-modal">
        <div class="modal-container">
            <div class="modal-header">
                <h2 class="modal-title" id="modal-title">Tool Name</h2>
                <button class="modal-close" id="modal-close">
                    <i data-feather="x"></i>
                </button>
            </div>
            <div class="modal-content" id="modal-content">
                <!-- Tool interface will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Upload Drop Zone -->
    <div class="upload-dropzone" id="upload-dropzone" style="display: none;">
        <div class="dropzone-content">
            <div class="dropzone-icon">
                <i data-feather="upload-cloud"></i>
            </div>
            <h3>Drop files here to process</h3>
            <p>Supports images, documents, and archives</p>
        </div>
    </div>

    <!-- Notification System -->
    <div class="notification-container" id="notifications"></div>

    <!-- Scripts -->
    <script src="/assets/js/tools-app.js"></script>
    <script src="/assets/js/tools-ui.js"></script>
    <script src="/assets/js/file-upload.js"></script>
    
    <script>
        // Initialize Feather Icons
        feather.replace();
        
        // Initialize Tools App
        document.addEventListener('DOMContentLoaded', function() {
            window.toolsApp = new ToolsApp();
            window.toolsApp.init();
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+F or Cmd+F for search
            if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                e.preventDefault();
                document.getElementById('tools-search').focus();
            }
            
            // Escape to close modal
            if (e.key === 'Escape') {
                window.toolsApp?.closeModal();
            }
        });
        
        // Service Worker for offline functionality
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js').catch(console.error);
        }
    </script>
</body>
</html>
