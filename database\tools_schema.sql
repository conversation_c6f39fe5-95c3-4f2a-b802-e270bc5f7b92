-- <PERSON><PERSON><PERSON><PERSON> Tools System Database Schema
-- This file extends the existing database with tools-specific tables

-- =====================================================
-- TOOLS SYSTEM TABLES
-- =====================================================

-- Tool usage tracking table
CREATE TABLE IF NOT EXISTS tool_usage (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    tool_name VARCHAR(100) NOT NULL,
    operation_id VARCHAR(255) UNIQUE NOT NULL,
    files_processed INT DEFAULT 1,
    credits_used INT DEFAULT 1,
    processing_time DECIMAL(8,3) DEFAULT 0.000,
    file_size_input BIGINT DEFAULT 0,
    file_size_output BIGINT DEFAULT 0,
    options_used JSON,
    status ENUM('processing', 'completed', 'failed') DEFAULT 'processing',
    error_message TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    INDEX idx_user_tool (user_id, tool_name),
    INDEX idx_created_at (created_at),
    INDEX idx_status (status),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tool files management table
CREATE TABLE IF NOT EXISTS tool_files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    operation_id VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    stored_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    is_input BOOLEAN DEFAULT TRUE,
    is_output BOOLEAN DEFAULT FALSE,
    download_count INT DEFAULT 0,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_operation (user_id, operation_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_file_type (file_type),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tool categories and metadata
CREATE TABLE IF NOT EXISTS tool_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(100),
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Individual tools configuration
CREATE TABLE IF NOT EXISTS tools_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tool_id VARCHAR(100) NOT NULL UNIQUE,
    category_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(100),
    credit_cost INT DEFAULT 1,
    max_file_size BIGINT DEFAULT 10485760, -- 10MB
    max_files_per_request INT DEFAULT 10,
    allowed_formats JSON,
    processing_options JSON,
    rate_limit_free INT DEFAULT 10,
    rate_limit_pro INT DEFAULT 100,
    rate_limit_business INT DEFAULT 1000,
    is_active BOOLEAN DEFAULT TRUE,
    requires_subscription ENUM('none', 'pro', 'business', 'enterprise') DEFAULT 'none',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (category_id),
    INDEX idx_active (is_active),
    FOREIGN KEY (category_id) REFERENCES tool_categories(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User tool favorites
CREATE TABLE IF NOT EXISTS user_tool_favorites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    tool_id VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_tool (user_id, tool_id),
    INDEX idx_user_id (user_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tool usage statistics (aggregated data)
CREATE TABLE IF NOT EXISTS tool_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tool_id VARCHAR(100) NOT NULL,
    date DATE NOT NULL,
    usage_count INT DEFAULT 0,
    unique_users INT DEFAULT 0,
    total_credits_used INT DEFAULT 0,
    total_processing_time DECIMAL(10,3) DEFAULT 0.000,
    avg_processing_time DECIMAL(8,3) DEFAULT 0.000,
    total_files_processed INT DEFAULT 0,
    success_rate DECIMAL(5,2) DEFAULT 100.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_tool_date (tool_id, date),
    INDEX idx_date (date),
    INDEX idx_tool_id (tool_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Rate limiting tracking
CREATE TABLE IF NOT EXISTS rate_limits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    resource_type ENUM('tool_usage', 'api_request', 'file_upload') NOT NULL,
    resource_identifier VARCHAR(100), -- tool_id for tools, endpoint for API
    request_count INT DEFAULT 1,
    window_start TIMESTAMP NOT NULL,
    window_end TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_resource (user_id, resource_type, resource_identifier),
    INDEX idx_window (window_start, window_end),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- EXTEND EXISTING TABLES
-- =====================================================

-- Add tools-related columns to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS tools_credits_used INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS favorite_tools JSON,
ADD COLUMN IF NOT EXISTS tools_preferences JSON,
ADD COLUMN IF NOT EXISTS last_tool_used VARCHAR(100),
ADD COLUMN IF NOT EXISTS last_tool_used_at TIMESTAMP NULL;

-- Add tools usage to credit transactions
ALTER TABLE payment_transactions 
ADD COLUMN IF NOT EXISTS tool_credits_purchased INT DEFAULT 0;

-- =====================================================
-- INSERT DEFAULT DATA
-- =====================================================

-- Insert tool categories
INSERT INTO tool_categories (name, display_name, description, icon, sort_order) VALUES
('image_processing', 'Image Processing', 'Tools for editing and processing images', 'image', 1),
('generators', 'Generators', 'Tools for generating content and codes', 'zap', 2),
('utilities', 'Utilities', 'Utility tools for analysis and organization', 'tool', 3),
('advanced', 'Advanced', 'Advanced processing and editing tools', 'settings', 4);

-- Insert tools configuration
INSERT INTO tools_config (tool_id, category_id, name, description, icon, credit_cost, max_file_size, allowed_formats, processing_options) VALUES
-- Image Processing Tools
('format_converter', 1, 'Multi-Format Converter', 'Convert between PNG, JPG, WebP, GIF, BMP, TIFF with quality settings', 'refresh-cw', 1, 10485760, 
 '["jpg", "jpeg", "png", "gif", "webp", "bmp", "tiff"]', 
 '{"output_formats": ["jpg", "png", "webp", "gif", "bmp", "tiff"], "quality_range": [1, 100]}'),

('background_remover', 1, 'AI Background Remover', 'Remove backgrounds using GD edge detection algorithms', 'scissors', 2, 10485760,
 '["jpg", "jpeg", "png"]',
 '{"edge_threshold": [1, 100], "smoothing": [0, 10]}'),

('smart_resizer', 1, 'Smart Resizer/Cropper', 'Resize with aspect ratio preservation and smart crop detection', 'maximize', 1, 10485760,
 '["jpg", "jpeg", "png", "gif", "webp", "bmp"]',
 '{"max_width": 4096, "max_height": 4096, "maintain_aspect": true}'),

('image_compressor', 1, 'Intelligent Compressor', 'Reduce file sizes with quality presets', 'minimize', 1, 52428800,
 '["jpg", "jpeg", "png", "webp"]',
 '{"presets": ["web", "print", "archive"], "custom_quality": [1, 100]}'),

('filter_studio', 1, 'Advanced Filter Studio', 'Apply 15+ filters with real-time preview', 'sliders', 2, 10485760,
 '["jpg", "jpeg", "png"]',
 '{"filters": ["grayscale", "sepia", "blur", "sharpen", "vintage", "hdr"]}'),

('color_extractor', 1, 'Color Palette Extractor', 'Extract dominant colors with hex codes', 'palette', 1, 10485760,
 '["jpg", "jpeg", "png", "gif", "webp"]',
 '{"max_colors": [2, 20], "format": ["hex", "rgb", "hsl"]}'),

('watermark_tool', 1, 'Professional Watermark Tool', 'Add text/image watermarks with opacity and positioning', 'droplet', 2, 10485760,
 '["jpg", "jpeg", "png"]',
 '{"positions": ["center", "top-left", "top-right", "bottom-left", "bottom-right"], "opacity": [0, 100]}'),

('metadata_manager', 1, 'Metadata Manager', 'View/edit EXIF data and GPS removal', 'info', 1, 10485760,
 '["jpg", "jpeg", "tiff"]',
 '{"operations": ["view", "edit", "remove_gps", "remove_all"]}'),

-- Generator Tools
('qr_generator', 2, 'QR Code Studio', 'Generate QR codes with custom styling and logos', 'square', 1, 0,
 '[]',
 '{"data_types": ["text", "url", "wifi", "contact"], "sizes": [100, 500], "error_correction": ["L", "M", "Q", "H"]}'),

('barcode_generator', 2, 'Barcode Generator Pro', 'Create Code128, Code39, EAN13, UPC-A barcodes', 'hash', 1, 0,
 '[]',
 '{"formats": ["code128", "code39", "ean13", "upc-a"], "sizes": [50, 300]}'),

('text_to_image', 2, 'Text-to-Image Creator', 'Convert text to stylized images with fonts and effects', 'type', 2, 0,
 '[]',
 '{"fonts": 20, "effects": ["shadow", "glow", "outline"], "backgrounds": ["solid", "gradient", "transparent"]}'),

('thumbnail_factory', 2, 'Thumbnail Factory', 'Bulk thumbnail generation with custom sizes', 'grid', 2, 52428800,
 '["jpg", "jpeg", "png", "gif", "webp"]',
 '{"sizes": [[150, 150], [300, 300], [500, 500]], "crop_modes": ["center", "smart"]}'),

('collage_maker', 2, 'Collage Maker Pro', 'Create photo collages with templates', 'layout', 3, 52428800,
 '["jpg", "jpeg", "png"]',
 '{"templates": ["grid", "freeform", "mosaic"], "max_images": 20}'),

('icon_generator', 2, 'Icon Generator', 'Create app icons and favicons from images', 'smartphone', 2, 10485760,
 '["jpg", "jpeg", "png", "svg"]',
 '{"formats": ["ico", "png"], "sizes": [16, 32, 48, 64, 128, 256, 512]}'),

('meme_generator', 2, 'Meme Generator', 'Add text to images with meme templates', 'smile', 1, 10485760,
 '["jpg", "jpeg", "png"]',
 '{"fonts": ["impact", "arial", "comic"], "positions": ["top", "bottom", "center"]}');

-- Insert more tools (utilities and advanced)
INSERT INTO tools_config (tool_id, category_id, name, description, icon, credit_cost, max_file_size, allowed_formats, processing_options) VALUES
-- Utility Tools
('color_converter', 3, 'Color Code Converter', 'Convert between HEX, RGB, HSL, CMYK', 'eye-dropper', 1, 0,
 '[]',
 '{"input_formats": ["hex", "rgb", "hsl", "cmyk"], "output_formats": ["hex", "rgb", "hsl", "cmyk"]}'),

('image_analyzer', 3, 'Image Analyzer', 'Analyze image properties and quality metrics', 'search', 1, 10485760,
 '["jpg", "jpeg", "png", "gif", "webp", "bmp", "tiff"]',
 '{"metrics": ["dimensions", "file_size", "color_depth", "compression", "quality_score"]}'),

('duplicate_finder', 3, 'Duplicate Image Finder', 'Find and remove duplicate images', 'copy', 2, 52428800,
 '["jpg", "jpeg", "png", "gif", "webp"]',
 '{"similarity_threshold": [80, 100], "hash_algorithm": ["dhash", "phash"]}'),

('image_organizer', 3, 'Image Organizer', 'Sort images by date, size, type', 'folder', 2, 104857600,
 '["jpg", "jpeg", "png", "gif", "webp", "bmp", "tiff"]',
 '{"sort_by": ["date", "size", "type", "name"], "folder_structure": ["year", "month", "type"]}'),

('batch_renamer', 3, 'Batch Renamer', 'Rename multiple files with patterns', 'edit-3', 1, 104857600,
 '["jpg", "jpeg", "png", "gif", "webp", "bmp", "tiff", "pdf", "txt"]',
 '{"patterns": ["sequential", "date", "custom"], "case": ["lower", "upper", "title"]}'),

('image_comparison', 3, 'Image Comparison Tool', 'Side-by-side comparison with difference highlighting', 'git-compare', 1, 20971520,
 '["jpg", "jpeg", "png"]',
 '{"comparison_modes": ["side_by_side", "overlay", "difference"], "highlight_differences": true}'),

-- Advanced Tools
('image_upscaler', 4, 'Image Upscaler', 'Enhance resolution using bicubic interpolation', 'trending-up', 3, 10485760,
 '["jpg", "jpeg", "png"]',
 '{"scale_factors": [2, 3, 4], "algorithms": ["bicubic", "lanczos"], "max_output": [4096, 4096]}'),

('noise_reducer', 4, 'Noise Reducer', 'Remove image noise and artifacts', 'filter', 3, 10485760,
 '["jpg", "jpeg", "png"]',
 '{"intensity": [1, 10], "preserve_edges": true, "algorithms": ["gaussian", "median"]}'),

('perspective_corrector', 4, 'Perspective Corrector', 'Fix perspective distortion with manual adjustment', 'crop', 3, 10485760,
 '["jpg", "jpeg", "png"]',
 '{"correction_modes": ["auto", "manual"], "grid_overlay": true}'),

('image_stitcher', 4, 'Image Stitcher', 'Combine multiple images into panoramas', 'layers', 4, 104857600,
 '["jpg", "jpeg", "png"]',
 '{"stitch_modes": ["horizontal", "vertical", "360"], "blend_modes": ["linear", "multiband"], "max_images": 10}');

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Additional indexes for better performance
CREATE INDEX idx_tool_usage_user_date ON tool_usage(user_id, created_at);
CREATE INDEX idx_tool_usage_tool_date ON tool_usage(tool_name, created_at);
CREATE INDEX idx_tool_files_user_date ON tool_files(user_id, created_at);
CREATE INDEX idx_tool_files_expires ON tool_files(expires_at);

-- =====================================================
-- VIEWS FOR ANALYTICS
-- =====================================================

-- View for user tool usage summary
CREATE OR REPLACE VIEW user_tool_summary AS
SELECT 
    u.id as user_id,
    u.email,
    u.subscription_type,
    COUNT(tu.id) as total_operations,
    SUM(tu.credits_used) as total_credits_used,
    SUM(tu.processing_time) as total_processing_time,
    AVG(tu.processing_time) as avg_processing_time,
    COUNT(DISTINCT tu.tool_name) as unique_tools_used,
    MAX(tu.created_at) as last_tool_usage
FROM users u
LEFT JOIN tool_usage tu ON u.id = tu.user_id
GROUP BY u.id, u.email, u.subscription_type;

-- View for popular tools
CREATE OR REPLACE VIEW popular_tools AS
SELECT 
    tc.tool_id,
    tc.name,
    tc.category_id,
    cat.display_name as category_name,
    COUNT(tu.id) as usage_count,
    COUNT(DISTINCT tu.user_id) as unique_users,
    SUM(tu.credits_used) as total_credits,
    AVG(tu.processing_time) as avg_processing_time,
    (COUNT(CASE WHEN tu.status = 'completed' THEN 1 END) * 100.0 / COUNT(tu.id)) as success_rate
FROM tools_config tc
LEFT JOIN tool_usage tu ON tc.tool_id = tu.tool_name
LEFT JOIN tool_categories cat ON tc.category_id = cat.id
WHERE tc.is_active = 1
GROUP BY tc.tool_id, tc.name, tc.category_id, cat.display_name
ORDER BY usage_count DESC;

-- View for daily tool statistics
CREATE OR REPLACE VIEW daily_tool_stats AS
SELECT 
    DATE(tu.created_at) as usage_date,
    tu.tool_name,
    COUNT(tu.id) as operations,
    COUNT(DISTINCT tu.user_id) as unique_users,
    SUM(tu.credits_used) as credits_used,
    AVG(tu.processing_time) as avg_processing_time,
    (COUNT(CASE WHEN tu.status = 'completed' THEN 1 END) * 100.0 / COUNT(tu.id)) as success_rate
FROM tool_usage tu
WHERE tu.created_at >= DATE_SUB(CURRENT_DATE, INTERVAL 30 DAY)
GROUP BY DATE(tu.created_at), tu.tool_name
ORDER BY usage_date DESC, operations DESC;
