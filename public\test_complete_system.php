<?php
/**
 * Complete System Test for Sutradhar 2070
 * Tests all functionality including authentication and video generation
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html>
<head>
    <title>🎭 Complete System Test</title>
    <style>
        body { font-family: Arial, sans-serif; background: #1a1a2e; color: white; padding: 20px; }
        .success { color: #10B981; }
        .error { color: #EF4444; }
        .warning { color: #F59E0B; }
        .info { color: #3B82F6; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #333; border-radius: 8px; }
        pre { background: #0f0f23; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .test-button { background: #10B981; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .test-button:hover { background: #059669; }
    </style>
</head>
<body>
<h1>🎭 Sutradhar 2070 - Complete System Test</h1>
";

$testResults = [];

// Test 1: API Routing
echo "<div class='section'>";
echo "<h2>🔗 API Routing Test</h2>";

$apiEndpoints = [
    '/api/auth/status',
    '/api/auth/register',
    '/api/auth/login',
    '/api/generate',
    '/api/dashboard/stats'
];

foreach ($apiEndpoints as $endpoint) {
    $testUrl = "http://localhost:8000$endpoint";
    
    // Test GET requests for status endpoints
    if (strpos($endpoint, 'status') !== false || strpos($endpoint, 'stats') !== false) {
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 5
            ]
        ]);
        
        $response = @file_get_contents($testUrl, false, $context);
        
        if ($response) {
            $data = json_decode($response, true);
            if ($data && isset($data['success'])) {
                echo "<span class='success'>✅ $endpoint: Working</span><br>";
                $testResults['routing'][$endpoint] = true;
            } else {
                echo "<span class='error'>❌ $endpoint: Invalid response</span><br>";
                $testResults['routing'][$endpoint] = false;
            }
        } else {
            echo "<span class='error'>❌ $endpoint: No response</span><br>";
            $testResults['routing'][$endpoint] = false;
        }
    } else {
        echo "<span class='info'>ℹ️ $endpoint: POST endpoint (skipping GET test)</span><br>";
        $testResults['routing'][$endpoint] = 'skipped';
    }
}

echo "</div>";

// Test 2: Working API Direct Test
echo "<div class='section'>";
echo "<h2>⚙️ Working API Direct Test</h2>";

try {
    // Test auth status
    $authResponse = file_get_contents('http://localhost:8000/working_api.php?endpoint=auth/status');
    $authData = json_decode($authResponse, true);
    
    if ($authData && $authData['success']) {
        echo "<span class='success'>✅ Auth status endpoint working</span><br>";
        echo "<span class='info'>Demo mode: " . ($authData['data']['demo_mode'] ? 'Yes' : 'No') . "</span><br>";
        $testResults['working_api']['auth_status'] = true;
    } else {
        echo "<span class='error'>❌ Auth status endpoint failed</span><br>";
        $testResults['working_api']['auth_status'] = false;
    }
    
    // Test registration
    $regData = json_encode([
        'first_name' => 'Test',
        'last_name' => 'User',
        'email' => 'test' . time() . '@example.com',
        'password' => 'TestPass123!'
    ]);
    
    $regContext = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $regData,
            'timeout' => 10
        ]
    ]);
    
    $regResponse = @file_get_contents('http://localhost:8000/working_api.php?endpoint=auth/register', false, $regContext);
    
    if ($regResponse) {
        $regResult = json_decode($regResponse, true);
        if ($regResult && $regResult['success']) {
            echo "<span class='success'>✅ Registration endpoint working</span><br>";
            echo "<span class='info'>User created: " . $regResult['data']['user']['first_name'] . " " . $regResult['data']['user']['last_name'] . "</span><br>";
            echo "<span class='info'>Starting credits: " . $regResult['data']['user']['credits'] . "</span><br>";
            $testResults['working_api']['registration'] = true;
        } else {
            echo "<span class='error'>❌ Registration failed: " . ($regResult['error'] ?? 'Unknown error') . "</span><br>";
            $testResults['working_api']['registration'] = false;
        }
    } else {
        echo "<span class='error'>❌ Registration request failed</span><br>";
        $testResults['working_api']['registration'] = false;
    }
    
} catch (Exception $e) {
    echo "<span class='error'>❌ Working API test failed: " . $e->getMessage() . "</span><br>";
    $testResults['working_api']['error'] = $e->getMessage();
}

echo "</div>";

// Test 3: Video Generation System
echo "<div class='section'>";
echo "<h2>🎬 Video Generation System Test</h2>";

try {
    // Test video generation
    $videoData = json_encode([
        'mood' => 'euphoric',
        'topic' => 'nature_wildlife',
        'custom_prompt' => 'Test video generation for system verification'
    ]);
    
    $videoContext = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $videoData,
            'timeout' => 15
        ]
    ]);
    
    echo "<span class='info'>🎬 Starting video generation test...</span><br>";
    
    $videoResponse = @file_get_contents('http://localhost:8000/working_api.php?endpoint=generate', false, $videoContext);
    
    if ($videoResponse) {
        $videoResult = json_decode($videoResponse, true);
        if ($videoResult && $videoResult['success']) {
            echo "<span class='success'>✅ Video generation started successfully</span><br>";
            echo "<span class='info'>Job ID: " . $videoResult['data']['job_id'] . "</span><br>";
            echo "<span class='info'>Generation type: " . $videoResult['data']['generation_type'] . "</span><br>";
            echo "<span class='info'>Estimated time: " . $videoResult['data']['estimated_time'] . " seconds</span><br>";
            
            $jobId = $videoResult['data']['job_id'];
            $testResults['video_generation']['start'] = true;
            
            // Test progress tracking
            echo "<span class='info'>📊 Testing progress tracking...</span><br>";
            
            $progressResponse = @file_get_contents("http://localhost:8000/working_api.php?endpoint=generate/status/$jobId");
            
            if ($progressResponse) {
                $progressResult = json_decode($progressResponse, true);
                if ($progressResult && $progressResult['success']) {
                    echo "<span class='success'>✅ Progress tracking working</span><br>";
                    echo "<span class='info'>Current progress: " . ($progressResult['data']['progress'] ?? 0) . "%</span><br>";
                    echo "<span class='info'>Status: " . ($progressResult['data']['status'] ?? 'unknown') . "</span><br>";
                    $testResults['video_generation']['progress'] = true;
                } else {
                    echo "<span class='error'>❌ Progress tracking failed</span><br>";
                    $testResults['video_generation']['progress'] = false;
                }
            } else {
                echo "<span class='error'>❌ Progress request failed</span><br>";
                $testResults['video_generation']['progress'] = false;
            }
            
        } else {
            echo "<span class='error'>❌ Video generation failed: " . ($videoResult['error'] ?? 'Unknown error') . "</span><br>";
            $testResults['video_generation']['start'] = false;
        }
    } else {
        echo "<span class='error'>❌ Video generation request failed</span><br>";
        $testResults['video_generation']['start'] = false;
    }
    
} catch (Exception $e) {
    echo "<span class='error'>❌ Video generation test failed: " . $e->getMessage() . "</span><br>";
    $testResults['video_generation']['error'] = $e->getMessage();
}

echo "</div>";

// Test 4: Enhanced Video Engine
echo "<div class='section'>";
echo "<h2>🎨 Enhanced Video Engine Test</h2>";

if (file_exists('../core/enhanced_video_engine.php')) {
    echo "<span class='success'>✅ Enhanced video engine file exists</span><br>";
    
    try {
        require_once '../core/enhanced_video_engine.php';
        echo "<span class='success'>✅ Enhanced video engine loaded</span><br>";
        
        $engine = new EnhancedVideoEngine();
        echo "<span class='success'>✅ Enhanced video engine instantiated</span><br>";
        
        $testResults['enhanced_engine']['available'] = true;
    } catch (Exception $e) {
        echo "<span class='error'>❌ Enhanced video engine error: " . $e->getMessage() . "</span><br>";
        $testResults['enhanced_engine']['available'] = false;
    }
} else {
    echo "<span class='error'>❌ Enhanced video engine file not found</span><br>";
    $testResults['enhanced_engine']['available'] = false;
}

echo "</div>";

// Test 5: File System Check
echo "<div class='section'>";
echo "<h2>📁 File System Check</h2>";

$requiredDirs = [
    '../public/generated_videos',
    '../temp'
];

foreach ($requiredDirs as $dir) {
    if (is_dir($dir)) {
        echo "<span class='success'>✅ Directory exists: $dir</span><br>";
        if (is_writable($dir)) {
            echo "<span class='success'>✅ Directory writable: $dir</span><br>";
            $testResults['filesystem'][$dir] = true;
        } else {
            echo "<span class='error'>❌ Directory not writable: $dir</span><br>";
            $testResults['filesystem'][$dir] = false;
        }
    } else {
        echo "<span class='warning'>⚠️ Creating directory: $dir</span><br>";
        if (mkdir($dir, 0755, true)) {
            echo "<span class='success'>✅ Directory created: $dir</span><br>";
            $testResults['filesystem'][$dir] = true;
        } else {
            echo "<span class='error'>❌ Failed to create directory: $dir</span><br>";
            $testResults['filesystem'][$dir] = false;
        }
    }
}

echo "</div>";

// Test Summary
echo "<div class='section'>";
echo "<h2>📊 Test Summary</h2>";

$totalTests = 0;
$passedTests = 0;

function countResults($results, &$total, &$passed) {
    foreach ($results as $key => $value) {
        if (is_array($value)) {
            countResults($value, $total, $passed);
        } elseif ($value === true) {
            $total++;
            $passed++;
        } elseif ($value === false) {
            $total++;
        }
        // Skip 'skipped' and string values
    }
}

countResults($testResults, $totalTests, $passedTests);

$failedTests = $totalTests - $passedTests;
$successRate = $totalTests > 0 ? round(($passedTests / $totalTests) * 100, 1) : 0;

echo "<h3>Overall Results:</h3>";
echo "<span class='info'>Total Tests: $totalTests</span><br>";
echo "<span class='success'>Passed: $passedTests</span><br>";
echo "<span class='error'>Failed: $failedTests</span><br>";
echo "<span class='info'>Success Rate: $successRate%</span><br>";

if ($successRate >= 80) {
    echo "<div style='background: #10B981; color: white; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>🎉 SYSTEM READY!</h3>";
    echo "<p>Your Sutradhar 2070 system is working properly!</p>";
    echo "<p><strong>What's Working:</strong></p>";
    echo "<ul>";
    echo "<li>✅ API routing and endpoints</li>";
    echo "<li>✅ User authentication system</li>";
    echo "<li>✅ Video generation system</li>";
    echo "<li>✅ Session-based user management</li>";
    echo "<li>✅ Enhanced video engine integration</li>";
    echo "</ul>";
    echo "<p><strong>Ready to Use:</strong></p>";
    echo "<ul>";
    echo "<li><a href='index.html' style='color: white;'>🏠 Homepage</a> - Signup/Login now works!</li>";
    echo "<li><a href='mood_video_generator.html' style='color: white;'>🎬 Video Generator</a> - Create videos!</li>";
    echo "<li><a href='test_auth_system.html' style='color: white;'>🔐 Auth Test</a> - Test authentication!</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #F59E0B; color: white; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>⚠️ SYSTEM NEEDS ATTENTION</h3>";
    echo "<p>Some components need fixing. Check the test results above.</p>";
    echo "<p><strong>Common Issues:</strong></p>";
    echo "<ul>";
    echo "<li>❌ Check if web server is running</li>";
    echo "<li>❌ Verify file permissions</li>";
    echo "<li>❌ Ensure PHP extensions are enabled</li>";
    echo "<li>❌ Check if ports are available</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div>";

// Quick Links
echo "<div class='section'>";
echo "<h2>🔗 Quick Links</h2>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap;'>";
echo "<a href='index.html' class='test-button'>🏠 Homepage</a>";
echo "<a href='mood_video_generator.html' class='test-button'>🎬 Video Generator</a>";
echo "<a href='test_auth_system.html' class='test-button'>🔐 Auth Test</a>";
echo "<a href='test_working_api.html' class='test-button'>⚙️ API Test</a>";
echo "<a href='test_real_video_generation.php' class='test-button'>🎨 Video Engine Test</a>";
echo "<a href='debug_api.php?endpoint=generate' class='test-button'>🐛 Debug API</a>";
echo "</div>";
echo "</div>";

echo "</body></html>";
?>
