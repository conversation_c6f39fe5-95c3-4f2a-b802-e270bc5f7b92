# 🤖 Open Source AI Setup Guide for Sutradhar Engine

This guide will help you set up open-source AI models and APIs to generate truly unique content with the Sutradhar Engine.

## 🎯 Overview

The Sutradhar Engine now supports multiple open-source AI providers for generating unique content:

- **Ollama** (Local LLM server) - 100% Free, runs locally
- **Hugging Face Inference API** - Free tier available
- **OpenAI-Compatible APIs** (Together AI, Groq, etc.) - Free tiers available
- **Local Content Generator** - Always available as fallback

## 🚀 Quick Start

### Option 1: Ollama (Recommended - 100% Free & Local)

1. **Install Ollama**
   ```bash
   # Windows
   Download from: https://ollama.ai/download/windows
   
   # macOS
   brew install ollama
   
   # Linux
   curl -fsSL https://ollama.ai/install.sh | sh
   ```

2. **Start Ollama Service**
   ```bash
   ollama serve
   ```

3. **Download Models**
   ```bash
   # Download Llama 2 (7B model - good balance of speed/quality)
   ollama pull llama2
   
   # Or download Mistral (faster, smaller)
   ollama pull mistral
   
   # Or download CodeLlama (better for technical content)
   ollama pull codellama
   ```

4. **Test Installation**
   ```bash
   ollama run llama2 "Tell me a funny Indian story"
   ```

### Option 2: Hugging Face (Free Tier)

1. **Create Account**
   - Go to https://huggingface.co/
   - Sign up for free account

2. **Get API Token**
   - Go to https://huggingface.co/settings/tokens
   - Create new token with "Inference API" permissions

3. **Configure Sutradhar**
   - Edit `config/settings.json`
   - Add your token:
   ```json
   "api_keys": {
     "huggingface": "hf_your_token_here"
   }
   ```

### Option 3: Together AI (Free Credits)

1. **Create Account**
   - Go to https://api.together.xyz/
   - Sign up for free account (gets $25 free credits)

2. **Get API Key**
   - Go to API Keys section
   - Create new API key

3. **Configure Sutradhar**
   - Edit `config/settings.json`
   - Add your key:
   ```json
   "api_keys": {
     "together": "your_api_key_here"
   }
   ```

## ⚙️ Configuration

### Edit config/settings.json

```json
{
  "api_keys": {
    "huggingface": "hf_your_token_here",
    "together": "your_together_api_key",
    "openai": "your_openai_key_if_you_have_one",
    "groq": "your_groq_key_if_you_have_one"
  },
  "api_endpoints": {
    "ollama": "http://localhost:11434/api/",
    "huggingface": "https://api-inference.huggingface.co/models/",
    "openai_compatible": "https://api.together.xyz/v1/",
    "groq": "https://api.groq.com/openai/v1/"
  },
  "content_generation": {
    "use_external_apis": true,
    "fallback_to_local": true,
    "max_tokens": 500,
    "temperature": 0.8,
    "preferred_providers": ["ollama", "huggingface", "openai_compatible"]
  }
}
```

## 🧪 Testing Your Setup

Run the test script to verify everything is working:

```bash
php test_unique_generation.php
```

This will test:
- ✅ Local Content Generator
- 🤖 AI Integration connectivity
- 🎤 Advanced Voice Engine
- 🔄 Full pipeline integration

## 📊 Provider Comparison

| Provider | Cost | Quality | Speed | Setup Difficulty |
|----------|------|---------|-------|------------------|
| **Ollama** | 100% Free | High | Fast (local) | Easy |
| **Hugging Face** | Free tier | Good | Medium | Very Easy |
| **Together AI** | $25 free credits | High | Fast | Easy |
| **Local Generator** | 100% Free | Basic | Very Fast | None |

## 🎭 Content Generation Features

### What's New:

1. **Unique Content Every Time**
   - AI models generate fresh variations
   - No more repetitive patterns
   - Culturally authentic Indian content

2. **Style-Aware Generation**
   - Funny: Sarcastic, humorous with Indian references
   - Desi: Traditional values, family situations
   - Emotional: Heartfelt, touching content
   - Bollywood: Dramatic, filmy expressions

3. **Flow-Type Optimization**
   - Reels: Hook + Content + CTA structure
   - Audio Stories: Complete narrative arc
   - Meme Rants: Cultural observations + comedy

4. **Advanced Voice Synthesis**
   - Realistic speech patterns
   - Emotion-based modulation
   - Voice pack characteristics
   - Natural pauses and emphasis

## 🔧 Troubleshooting

### Ollama Issues

**Problem**: "Ollama server not available"
```bash
# Check if Ollama is running
curl http://localhost:11434/api/tags

# Start Ollama if not running
ollama serve

# Check available models
ollama list
```

**Problem**: "Model not found"
```bash
# Download required model
ollama pull llama2
```

### Hugging Face Issues

**Problem**: "API key not configured"
- Verify your token in `config/settings.json`
- Check token permissions at https://huggingface.co/settings/tokens

**Problem**: "Rate limit exceeded"
- Hugging Face free tier has limits
- Wait a few minutes or upgrade to Pro

### Together AI Issues

**Problem**: "API key not configured"
- Verify your API key in `config/settings.json`
- Check your account at https://api.together.xyz/

**Problem**: "Credits exhausted"
- Check your usage at https://api.together.xyz/
- Add more credits or switch to Ollama

## 🎯 Best Practices

### For Best Content Quality:

1. **Use Ollama with Llama2** for best free results
2. **Combine multiple providers** for redundancy
3. **Customize prompts** in `core/openai_integration.php`
4. **Adjust temperature** (0.7-0.9) for creativity vs consistency

### For Performance:

1. **Ollama** is fastest (local processing)
2. **Cache frequently used content** (built-in)
3. **Use smaller models** (mistral) for speed
4. **Enable fallback** to local generator

## 🚀 Advanced Usage

### Custom Model Configuration

Edit `core/openai_integration.php` to add new models:

```php
'ollama' => [
    'models' => ['llama2', 'mistral', 'codellama', 'your-custom-model']
],
```

### Custom Prompts

Modify `buildSystemPrompt()` function to customize AI behavior:

```php
private function buildSystemPrompt($style, $flowType) {
    // Add your custom prompt engineering here
    return "Your custom system prompt...";
}
```

## 📈 Monitoring Usage

### Check API Usage:

```bash
# Test connectivity
php -r "
require_once 'core/openai_integration.php';
\$ai = new OpenAIIntegration();
var_dump(\$ai->testConnectivity());
"
```

### Monitor Generated Content:

- Check `data/output_history/` for generated files
- Review subtitles for content quality
- Listen to audio files for voice quality

## 🎉 Success Indicators

You'll know it's working when:

✅ **Different content every time** - No more repetitive patterns
✅ **Culturally authentic** - Proper Hinglish and Indian references  
✅ **Style-appropriate** - Content matches selected style
✅ **Natural speech** - Realistic voice patterns and emotions
✅ **Fast generation** - Quick turnaround with AI models

## 🆘 Support

If you encounter issues:

1. **Check the test script output** for specific errors
2. **Verify API keys** and connectivity
3. **Check server logs** for detailed error messages
4. **Use local fallback** if external APIs fail

The system is designed to always work - if all external APIs fail, it falls back to the local content generator, ensuring you always get unique content!

---

**🎭 Happy Content Creating with Sutradhar Engine!**
