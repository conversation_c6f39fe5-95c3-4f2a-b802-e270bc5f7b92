RewriteEngine On

# API Routes - Use unified API
RewriteRule ^api/(.*)$ api_unified.php?endpoint=$1 [QSA,L]

# Tools file download handler
Rewrite<PERSON><PERSON> %{REQUEST_URI} ^/temp/tools/
RewriteRule ^temp/tools/(.+)$ download.php?file=$1 [QSA,L]

# Default to index.html for root
DirectoryIndex index.html

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# CORS headers for API
<FilesMatch "\.(php)$">
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
</FilesMatch>

# Cache static assets
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 month"
</FilesMatch>

# Compress text files
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Prevent access to sensitive files
<FilesMatch "\.(json|log|md)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevent access to core directory from web
RedirectMatch 404 ^/core/.*$
RedirectMatch 404 ^/config/.*$
RedirectMatch 404 ^/tests/.*$
