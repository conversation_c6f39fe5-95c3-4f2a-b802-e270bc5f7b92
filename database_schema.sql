-- Sutradhar 2070 - Complete Database Schema
-- AI-Powered Video Generation Platform
-- Version: 1.0.0

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS `sutradhar2070` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `sutradhar2070`;

-- =====================================================
-- USERS AND AUTHENTICATION TABLES
-- =====================================================

-- Users table - Core user information
CREATE TABLE `users` (
  `user_id` varchar(50) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `avatar_url` varchar(500) DEFAULT NULL,
  `email_verified` tinyint(1) DEFAULT 0,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `email_verification_token` varchar(100) DEFAULT NULL,
  `password_reset_token` varchar(100) DEFAULT NULL,
  `password_reset_expires` timestamp NULL DEFAULT NULL,
  `last_login_at` timestamp NULL DEFAULT NULL,
  `login_attempts` int(11) DEFAULT 0,
  `locked_until` timestamp NULL DEFAULT NULL,
  `status` enum('active','inactive','suspended','deleted') DEFAULT 'active',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_email_verified` (`email_verified`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User sessions table - Session management
CREATE TABLE `user_sessions` (
  `session_id` varchar(128) NOT NULL,
  `user_id` varchar(50) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text,
  `last_activity` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `expires_at` timestamp NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_last_activity` (`last_activity`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User profiles table - Extended user information
CREATE TABLE `user_profiles` (
  `user_id` varchar(50) NOT NULL,
  `company_name` varchar(200) DEFAULT NULL,
  `industry` varchar(100) DEFAULT NULL,
  `website` varchar(500) DEFAULT NULL,
  `bio` text,
  `timezone` varchar(50) DEFAULT 'UTC',
  `language` varchar(10) DEFAULT 'en',
  `notification_preferences` json DEFAULT NULL,
  `marketing_consent` tinyint(1) DEFAULT 0,
  `terms_accepted_at` timestamp NULL DEFAULT NULL,
  `privacy_accepted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- SUBSCRIPTION AND BILLING TABLES
-- =====================================================

-- Subscription plans table
CREATE TABLE `subscription_plans` (
  `plan_id` varchar(50) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text,
  `price_monthly` decimal(10,2) NOT NULL,
  `price_yearly` decimal(10,2) DEFAULT NULL,
  `credits_monthly` int(11) NOT NULL,
  `features` json NOT NULL,
  `max_video_quality` enum('standard','hd','4k') DEFAULT 'standard',
  `api_access` tinyint(1) DEFAULT 0,
  `priority_support` tinyint(1) DEFAULT 0,
  `custom_branding` tinyint(1) DEFAULT 0,
  `status` enum('active','inactive','deprecated') DEFAULT 'active',
  `sort_order` int(11) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`plan_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User subscriptions table
CREATE TABLE `user_subscriptions` (
  `subscription_id` varchar(50) NOT NULL,
  `user_id` varchar(50) NOT NULL,
  `plan_id` varchar(50) NOT NULL,
  `status` enum('active','cancelled','expired','past_due','trialing') NOT NULL,
  `billing_cycle` enum('monthly','yearly') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) DEFAULT 'USD',
  `trial_ends_at` timestamp NULL DEFAULT NULL,
  `current_period_start` timestamp NOT NULL,
  `current_period_end` timestamp NOT NULL,
  `cancelled_at` timestamp NULL DEFAULT NULL,
  `cancellation_reason` text,
  `stripe_subscription_id` varchar(100) DEFAULT NULL,
  `paypal_subscription_id` varchar(100) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`subscription_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_status` (`status`),
  KEY `idx_current_period_end` (`current_period_end`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  FOREIGN KEY (`plan_id`) REFERENCES `subscription_plans` (`plan_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- CREDIT SYSTEM TABLES
-- =====================================================

-- Credit packages table
CREATE TABLE `credit_packages` (
  `package_id` varchar(50) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text,
  `credits` int(11) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `currency` varchar(3) DEFAULT 'USD',
  `bonus_credits` int(11) DEFAULT 0,
  `popular` tinyint(1) DEFAULT 0,
  `status` enum('active','inactive') DEFAULT 'active',
  `sort_order` int(11) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`package_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User credit balances table
CREATE TABLE `user_credit_balances` (
  `user_id` varchar(50) NOT NULL,
  `total_credits` int(11) DEFAULT 0,
  `used_credits` int(11) DEFAULT 0,
  `available_credits` int(11) GENERATED ALWAYS AS ((`total_credits` - `used_credits`)) STORED,
  `last_updated` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Credit transactions table
CREATE TABLE `credit_transactions` (
  `transaction_id` varchar(50) NOT NULL,
  `user_id` varchar(50) NOT NULL,
  `type` enum('purchase','consumption','refund','bonus','subscription') NOT NULL,
  `credits_amount` int(11) NOT NULL,
  `description` varchar(500) NOT NULL,
  `reference_id` varchar(100) DEFAULT NULL,
  `reference_type` enum('payment','generation','subscription','manual','bonus') DEFAULT NULL,
  `metadata` json DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`transaction_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_reference` (`reference_id`, `reference_type`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- VIDEO GENERATION TABLES
-- =====================================================

-- Generation jobs table
CREATE TABLE `generation_jobs` (
  `job_id` varchar(100) NOT NULL,
  `user_id` varchar(50) NOT NULL,
  `mood` varchar(50) NOT NULL,
  `topic` varchar(50) NOT NULL,
  `inspiration` json DEFAULT NULL,
  `custom_prompt` text,
  `video_quality` enum('standard','hd','4k') DEFAULT 'standard',
  `video_length` int(11) DEFAULT 30,
  `status` enum('pending','analyzing','generating','processing','finalizing','completed','failed','cancelled') DEFAULT 'pending',
  `progress` int(11) DEFAULT 0,
  `message` text,
  `credits_consumed` int(11) DEFAULT 0,
  `processing_time` int(11) DEFAULT NULL,
  `video_url` varchar(500) DEFAULT NULL,
  `thumbnail_url` varchar(500) DEFAULT NULL,
  `video_file_path` varchar(500) DEFAULT NULL,
  `video_file_size` bigint(20) DEFAULT NULL,
  `output_data` json DEFAULT NULL,
  `error_message` text,
  `engine_used` varchar(50) DEFAULT NULL,
  `retry_count` int(11) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `started_at` timestamp NULL DEFAULT NULL,
  `completed_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`job_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_mood_topic` (`mood`, `topic`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User video library table
CREATE TABLE `user_video_library` (
  `library_id` varchar(50) NOT NULL,
  `user_id` varchar(50) NOT NULL,
  `job_id` varchar(100) NOT NULL,
  `title` varchar(200) DEFAULT NULL,
  `description` text,
  `tags` json DEFAULT NULL,
  `is_favorite` tinyint(1) DEFAULT 0,
  `is_public` tinyint(1) DEFAULT 0,
  `download_count` int(11) DEFAULT 0,
  `last_accessed` timestamp NULL DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`library_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_job_id` (`job_id`),
  KEY `idx_is_favorite` (`is_favorite`),
  KEY `idx_is_public` (`is_public`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  FOREIGN KEY (`job_id`) REFERENCES `generation_jobs` (`job_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- PAYMENT AND BILLING TABLES
-- =====================================================

-- Payment transactions table
CREATE TABLE `payment_transactions` (
  `transaction_id` varchar(50) NOT NULL,
  `user_id` varchar(50) NOT NULL,
  `type` enum('credit_purchase','subscription','refund','chargeback') NOT NULL,
  `status` enum('pending','processing','completed','failed','cancelled','refunded') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) DEFAULT 'USD',
  `payment_method` enum('stripe','paypal','bank_transfer','crypto') NOT NULL,
  `payment_provider_id` varchar(200) DEFAULT NULL,
  `payment_intent_id` varchar(200) DEFAULT NULL,
  `invoice_id` varchar(50) DEFAULT NULL,
  `description` varchar(500) NOT NULL,
  `metadata` json DEFAULT NULL,
  `failure_reason` text,
  `refund_amount` decimal(10,2) DEFAULT 0.00,
  `refund_reason` text,
  `processed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`transaction_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_type` (`type`),
  KEY `idx_payment_provider_id` (`payment_provider_id`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Invoices table
CREATE TABLE `invoices` (
  `invoice_id` varchar(50) NOT NULL,
  `user_id` varchar(50) NOT NULL,
  `invoice_number` varchar(50) NOT NULL,
  `status` enum('draft','sent','paid','overdue','cancelled','refunded') NOT NULL,
  `subtotal` decimal(10,2) NOT NULL,
  `tax_amount` decimal(10,2) DEFAULT 0.00,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `total_amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) DEFAULT 'USD',
  `due_date` date NOT NULL,
  `paid_at` timestamp NULL DEFAULT NULL,
  `payment_transaction_id` varchar(50) DEFAULT NULL,
  `billing_address` json DEFAULT NULL,
  `line_items` json NOT NULL,
  `notes` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`invoice_id`),
  UNIQUE KEY `invoice_number` (`invoice_number`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_due_date` (`due_date`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  FOREIGN KEY (`payment_transaction_id`) REFERENCES `payment_transactions` (`transaction_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- SYSTEM AND ANALYTICS TABLES
-- =====================================================

-- API usage tracking table
CREATE TABLE `api_usage_logs` (
  `log_id` varchar(50) NOT NULL,
  `user_id` varchar(50) DEFAULT NULL,
  `endpoint` varchar(200) NOT NULL,
  `method` varchar(10) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text,
  `request_data` json DEFAULT NULL,
  `response_status` int(11) NOT NULL,
  `response_time_ms` int(11) DEFAULT NULL,
  `error_message` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`log_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_endpoint` (`endpoint`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_response_status` (`response_status`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- System settings table
CREATE TABLE `system_settings` (
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text NOT NULL,
  `setting_type` enum('string','integer','boolean','json','encrypted') DEFAULT 'string',
  `description` text,
  `is_public` tinyint(1) DEFAULT 0,
  `updated_by` varchar(50) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`setting_key`),
  KEY `idx_is_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Email templates table
CREATE TABLE `email_templates` (
  `template_id` varchar(50) NOT NULL,
  `name` varchar(100) NOT NULL,
  `subject` varchar(200) NOT NULL,
  `html_content` text NOT NULL,
  `text_content` text,
  `variables` json DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`template_id`),
  KEY `idx_name` (`name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- INSERT DEFAULT DATA
-- =====================================================

-- Insert default subscription plans
INSERT INTO `subscription_plans` (`plan_id`, `name`, `description`, `price_monthly`, `price_yearly`, `credits_monthly`, `features`, `max_video_quality`, `api_access`, `priority_support`, `custom_branding`, `status`, `sort_order`) VALUES
('free', 'Free', 'Perfect for getting started with AI video generation', 0.00, 0.00, 50, '["50 credits per month", "Standard quality videos", "Watermarked videos", "Community support"]', 'standard', 0, 0, 0, 'active', 1),
('pro', 'Pro', 'Ideal for content creators and small businesses', 9.99, 99.99, 500, '["500 credits per month", "HD quality videos", "No watermarks", "Email support", "Priority processing"]', 'hd', 0, 0, 0, 'active', 2),
('business', 'Business', 'Perfect for growing businesses and agencies', 29.99, 299.99, 2000, '["2000 credits per month", "HD quality videos", "API access", "Priority support", "Custom branding", "Analytics dashboard"]', 'hd', 1, 1, 1, 'active', 3),
('enterprise', 'Enterprise', 'Custom solutions for large organizations', 99.99, 999.99, 10000, '["10000 credits per month", "4K quality videos", "Full API access", "Dedicated support", "Custom branding", "Advanced analytics", "SLA guarantee"]', '4k', 1, 1, 1, 'active', 4);

-- Insert default credit packages
INSERT INTO `credit_packages` (`package_id`, `name`, `description`, `credits`, `price`, `currency`, `bonus_credits`, `popular`, `status`, `sort_order`) VALUES
('starter', 'Starter Pack', 'Perfect for trying out our service', 100, 9.99, 'USD', 0, 0, 'active', 1),
('popular', 'Popular Pack', 'Most popular choice for regular users', 500, 39.99, 'USD', 50, 1, 'active', 2),
('professional', 'Professional Pack', 'Great for content creators', 1000, 69.99, 'USD', 150, 0, 'active', 3),
('enterprise', 'Enterprise Pack', 'For heavy users and businesses', 2500, 149.99, 'USD', 500, 0, 'active', 4),
('ultimate', 'Ultimate Pack', 'Maximum value for power users', 5000, 249.99, 'USD', 1000, 0, 'active', 5);

-- Insert default system settings
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `setting_type`, `description`, `is_public`) VALUES
('app_name', 'Sutradhar 2070', 'string', 'Application name', 1),
('app_version', '1.0.0', 'string', 'Application version', 1),
('maintenance_mode', 'false', 'boolean', 'Enable maintenance mode', 0),
('max_video_length', '30', 'integer', 'Maximum video length in seconds', 1),
('default_video_quality', 'standard', 'string', 'Default video quality for new users', 1),
('email_verification_required', 'true', 'boolean', 'Require email verification for new accounts', 0),
('max_login_attempts', '5', 'integer', 'Maximum login attempts before lockout', 0),
('session_timeout', '86400', 'integer', 'Session timeout in seconds (24 hours)', 0);

-- Insert default email templates
INSERT INTO `email_templates` (`template_id`, `name`, `subject`, `html_content`, `text_content`, `variables`) VALUES
('welcome', 'Welcome Email', 'Welcome to Sutradhar 2070!', '<h1>Welcome {{first_name}}!</h1><p>Thank you for joining Sutradhar 2070. Start creating amazing videos today!</p>', 'Welcome {{first_name}}! Thank you for joining Sutradhar 2070.', '["first_name", "email"]'),
('email_verification', 'Email Verification', 'Verify your email address', '<h1>Verify Your Email</h1><p>Click <a href="{{verification_link}}">here</a> to verify your email address.</p>', 'Verify your email: {{verification_link}}', '["first_name", "verification_link"]'),
('password_reset', 'Password Reset', 'Reset your password', '<h1>Reset Password</h1><p>Click <a href="{{reset_link}}">here</a> to reset your password.</p>', 'Reset your password: {{reset_link}}', '["first_name", "reset_link"]'),
('payment_success', 'Payment Confirmation', 'Payment received successfully', '<h1>Payment Confirmed</h1><p>Your payment of {{amount}} has been processed successfully.</p>', 'Payment confirmed: {{amount}}', '["first_name", "amount", "transaction_id"]');

COMMIT;
