<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎭 Sutradhar 2070 - System Status</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0F0F23 0%, #1A1A2E 50%, #16213E 100%);
            font-family: 'Arial', sans-serif;
            color: white;
            min-height: 100vh;
        }
        .status-card {
            background: rgba(26, 26, 46, 0.8);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            transition: all 0.3s ease;
        }
        .status-online { border-color: #10B981; background: rgba(16, 185, 129, 0.1); }
        .status-offline { border-color: #EF4444; background: rgba(239, 68, 68, 0.1); }
        .status-warning { border-color: #F59E0B; background: rgba(245, 158, 11, 0.1); }
        .cyber-button {
            background: linear-gradient(45deg, #00D4FF, #0099CC);
            border: none;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .cyber-button:hover {
            background: linear-gradient(45deg, #0099CC, #00D4FF);
            transform: translateY(-2px);
        }
        .pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body class="p-8">
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-5xl font-bold mb-4">🎭 Sutradhar 2070</h1>
            <p class="text-xl text-gray-300 mb-2">AI-Powered Video Generation Platform</p>
            <div id="overall-status" class="text-lg font-semibold">
                <span class="pulse">🔄 Checking system status...</span>
            </div>
        </div>

        <!-- System Overview -->
        <div class="grid md:grid-cols-3 gap-6 mb-8">
            <div class="status-card p-6 rounded-lg text-center" id="auth-status">
                <div class="text-3xl mb-2">🔐</div>
                <h3 class="text-lg font-semibold mb-2">Authentication</h3>
                <div class="status-indicator">Checking...</div>
            </div>
            
            <div class="status-card p-6 rounded-lg text-center" id="video-status">
                <div class="text-3xl mb-2">🎬</div>
                <h3 class="text-lg font-semibold mb-2">Video Generation</h3>
                <div class="status-indicator">Checking...</div>
            </div>
            
            <div class="status-card p-6 rounded-lg text-center" id="api-status">
                <div class="text-3xl mb-2">⚙️</div>
                <h3 class="text-lg font-semibold mb-2">API System</h3>
                <div class="status-indicator">Checking...</div>
            </div>
        </div>

        <!-- Feature Status -->
        <div class="status-card p-6 rounded-lg mb-8">
            <h3 class="text-xl font-semibold mb-4">🚀 Feature Status</h3>
            <div class="grid md:grid-cols-2 gap-4" id="feature-list">
                <div class="feature-item">🔄 Loading features...</div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="status-card p-6 rounded-lg mb-8">
            <h3 class="text-xl font-semibold mb-4">⚡ Quick Actions</h3>
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                <a href="index.html" class="cyber-button px-4 py-3 rounded text-center block">
                    🏠 Homepage
                </a>
                <a href="mood_video_generator.html" class="cyber-button px-4 py-3 rounded text-center block">
                    🎬 Video Generator
                </a>
                <a href="test_auth_system.html" class="cyber-button px-4 py-3 rounded text-center block">
                    🔐 Test Auth
                </a>
                <a href="test_complete_system.php" class="cyber-button px-4 py-3 rounded text-center block">
                    🧪 Full Test
                </a>
            </div>
        </div>

        <!-- System Information -->
        <div class="grid md:grid-cols-2 gap-6">
            <div class="status-card p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-3">📊 System Information</h3>
                <div id="system-info">
                    <div>🔄 Loading system information...</div>
                </div>
            </div>
            
            <div class="status-card p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-3">🛠️ Recent Fixes</h3>
                <div class="space-y-2 text-sm">
                    <div>✅ Fixed "server not connected" error on signup</div>
                    <div>✅ Implemented working authentication system</div>
                    <div>✅ Added real video generation with Python/GD/FFmpeg</div>
                    <div>✅ Created multiple API fallback layers</div>
                    <div>✅ Added session-based user management</div>
                    <div>✅ Fixed API routing with .htaccess</div>
                    <div>✅ Enhanced video engine integration</div>
                    <div>✅ Comprehensive error handling</div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-8 text-gray-400">
            <p>Sutradhar 2070 - The Future of AI Video Generation</p>
            <p class="text-sm">Last updated: <span id="last-updated"></span></p>
        </div>
    </div>

    <script>
        let systemStatus = {
            auth: false,
            video: false,
            api: false,
            overall: false
        };

        // Update timestamp
        document.getElementById('last-updated').textContent = new Date().toLocaleString();

        // Check system status
        async function checkSystemStatus() {
            await Promise.all([
                checkAuthSystem(),
                checkVideoSystem(),
                checkAPISystem()
            ]);
            
            updateOverallStatus();
            updateFeatureList();
            updateSystemInfo();
        }

        async function checkAuthSystem() {
            try {
                const response = await fetch('/api/auth/status');
                const data = await response.json();
                
                if (data.success) {
                    updateStatusCard('auth-status', 'online', 'Authentication System Online');
                    systemStatus.auth = true;
                } else {
                    updateStatusCard('auth-status', 'offline', 'Authentication System Offline');
                }
            } catch (error) {
                updateStatusCard('auth-status', 'offline', 'Authentication System Error');
            }
        }

        async function checkVideoSystem() {
            try {
                const response = await fetch('/api/generate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        mood: 'test',
                        topic: 'test',
                        custom_prompt: 'System status check'
                    })
                });
                
                const data = await response.json();
                
                if (data.success || (data.error && data.error.includes('credits'))) {
                    updateStatusCard('video-status', 'online', 'Video Generation Online');
                    systemStatus.video = true;
                } else {
                    updateStatusCard('video-status', 'warning', 'Video Generation Limited');
                }
            } catch (error) {
                updateStatusCard('video-status', 'offline', 'Video Generation Error');
            }
        }

        async function checkAPISystem() {
            try {
                const response = await fetch('/api/dashboard/stats');
                const data = await response.json();
                
                if (data.success) {
                    updateStatusCard('api-status', 'online', 'API System Online');
                    systemStatus.api = true;
                } else {
                    updateStatusCard('api-status', 'warning', 'API System Limited');
                }
            } catch (error) {
                updateStatusCard('api-status', 'offline', 'API System Error');
            }
        }

        function updateStatusCard(cardId, status, message) {
            const card = document.getElementById(cardId);
            const indicator = card.querySelector('.status-indicator');
            
            card.className = `status-card p-6 rounded-lg text-center status-${status}`;
            
            const icons = {
                online: '✅',
                offline: '❌',
                warning: '⚠️'
            };
            
            indicator.innerHTML = `${icons[status]} ${message}`;
        }

        function updateOverallStatus() {
            const overallDiv = document.getElementById('overall-status');
            const onlineCount = Object.values(systemStatus).filter(Boolean).length;
            const totalCount = Object.keys(systemStatus).length;
            
            if (onlineCount === totalCount) {
                overallDiv.innerHTML = '🟢 <span class="text-green-400">All Systems Operational</span>';
                systemStatus.overall = true;
            } else if (onlineCount > 0) {
                overallDiv.innerHTML = `🟡 <span class="text-yellow-400">Partial Systems Online (${onlineCount}/${totalCount})</span>`;
            } else {
                overallDiv.innerHTML = '🔴 <span class="text-red-400">Systems Offline</span>';
            }
        }

        function updateFeatureList() {
            const featureList = document.getElementById('feature-list');
            
            const features = [
                { name: 'User Registration', status: systemStatus.auth, desc: 'Create new accounts' },
                { name: 'User Login', status: systemStatus.auth, desc: 'Authenticate existing users' },
                { name: 'Video Generation', status: systemStatus.video, desc: 'AI-powered video creation' },
                { name: 'Real-time Progress', status: systemStatus.api, desc: 'Track generation progress' },
                { name: 'Enhanced Video Engine', status: systemStatus.video, desc: 'Python/GD/FFmpeg integration' },
                { name: 'Session Management', status: systemStatus.auth, desc: 'Persistent user sessions' },
                { name: 'Credit System', status: systemStatus.auth, desc: 'User credit management' },
                { name: 'Dashboard Stats', status: systemStatus.api, desc: 'User analytics and stats' }
            ];
            
            featureList.innerHTML = features.map(feature => {
                const icon = feature.status ? '✅' : '❌';
                const statusText = feature.status ? 'Working' : 'Offline';
                const statusClass = feature.status ? 'text-green-400' : 'text-red-400';
                
                return `
                    <div class="feature-item flex items-center justify-between p-3 bg-gray-800 rounded">
                        <div>
                            <div class="font-medium">${icon} ${feature.name}</div>
                            <div class="text-sm text-gray-400">${feature.desc}</div>
                        </div>
                        <div class="text-sm ${statusClass}">${statusText}</div>
                    </div>
                `;
            }).join('');
        }

        async function updateSystemInfo() {
            try {
                const response = await fetch('/api/dashboard/stats');
                const data = await response.json();
                
                if (data.success) {
                    const stats = data.data;
                    document.getElementById('system-info').innerHTML = `
                        <div class="space-y-2 text-sm">
                            <div>📊 Total Generations: ${stats.generation_stats?.total || 0}</div>
                            <div>✅ Completed: ${stats.generation_stats?.completed || 0}</div>
                            <div>🎨 Enhanced Videos: ${stats.generation_stats?.enhanced || 0}</div>
                            <div>📈 Success Rate: ${stats.generation_stats?.success_rate || 0}%</div>
                            <div>💰 Available Credits: ${stats.credit_stats?.available || 0}</div>
                            <div>🔐 Auth Status: ${stats.user_stats?.authenticated ? 'Authenticated' : 'Demo Mode'}</div>
                        </div>
                    `;
                } else {
                    document.getElementById('system-info').innerHTML = `
                        <div class="text-yellow-400">⚠️ Unable to load system statistics</div>
                    `;
                }
            } catch (error) {
                document.getElementById('system-info').innerHTML = `
                    <div class="text-red-400">❌ Error loading system information</div>
                `;
            }
        }

        // Auto-refresh every 30 seconds
        setInterval(checkSystemStatus, 30000);

        // Initial check
        checkSystemStatus();

        // Add click handlers for status cards
        document.getElementById('auth-status').addEventListener('click', () => {
            window.open('test_auth_system.html', '_blank');
        });

        document.getElementById('video-status').addEventListener('click', () => {
            window.open('mood_video_generator.html', '_blank');
        });

        document.getElementById('api-status').addEventListener('click', () => {
            window.open('test_working_api.html', '_blank');
        });
    </script>
</body>
</html>
