<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏠 Homepage Fix Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0F0F23 0%, #1A1A2E 50%, #16213E 100%);
            font-family: 'Arial', sans-serif;
            color: white;
            min-height: 100vh;
        }
        .test-card {
            background: rgba(26, 26, 46, 0.8);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            transition: all 0.3s ease;
        }
        .success { border-color: #10B981; background: rgba(16, 185, 129, 0.1); }
        .error { border-color: #EF4444; background: rgba(239, 68, 68, 0.1); }
        .loading { border-color: #F59E0B; background: rgba(245, 158, 11, 0.1); }
        .cyber-button {
            background: linear-gradient(45deg, #00D4FF, #0099CC);
            border: none;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .cyber-button:hover {
            background: linear-gradient(45deg, #0099CC, #00D4FF);
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="p-8">
    <div class="max-w-4xl mx-auto">
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold mb-4">🏠 Homepage Connection Fix Test</h1>
            <p class="text-gray-300">Testing the fixed authentication system</p>
        </div>

        <!-- Test Results -->
        <div class="test-card p-6 rounded-lg mb-6">
            <h3 class="text-lg font-semibold mb-3">🧪 Test Results</h3>
            <div id="test-results">
                <div class="text-yellow-400">⏳ Running tests...</div>
            </div>
        </div>

        <!-- API Tests -->
        <div class="grid md:grid-cols-2 gap-6 mb-8">
            <div class="test-card p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-3">🔗 API Connectivity</h3>
                <div id="api-test-results">Testing...</div>
            </div>
            
            <div class="test-card p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-3">🔐 Authentication</h3>
                <div id="auth-test-results">Testing...</div>
            </div>
        </div>

        <!-- Manual Test -->
        <div class="test-card p-6 rounded-lg mb-6">
            <h3 class="text-lg font-semibold mb-3">👤 Manual Authentication Test</h3>
            <div class="grid md:grid-cols-2 gap-4 mb-4">
                <div>
                    <h4 class="font-semibold mb-2">Quick Registration Test</h4>
                    <div class="space-y-2">
                        <input type="text" id="test-firstname" placeholder="First Name" value="Test" class="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white text-sm">
                        <input type="text" id="test-lastname" placeholder="Last Name" value="User" class="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white text-sm">
                        <input type="email" id="test-email" placeholder="Email" class="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white text-sm">
                        <input type="password" id="test-password" placeholder="Password" value="TestPass123!" class="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white text-sm">
                        <button onclick="testRegistration()" class="cyber-button px-4 py-2 rounded text-sm w-full">Test Registration</button>
                    </div>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-2">Quick Login Test</h4>
                    <div class="space-y-2">
                        <input type="email" id="login-test-email" placeholder="Email" class="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white text-sm">
                        <input type="password" id="login-test-password" placeholder="Password" class="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white text-sm">
                        <button onclick="testLogin()" class="cyber-button px-4 py-2 rounded text-sm w-full">Test Login</button>
                    </div>
                </div>
            </div>
            <div id="manual-test-results" class="text-sm">Ready to test manual authentication</div>
        </div>

        <!-- Homepage Link -->
        <div class="test-card p-6 rounded-lg text-center">
            <h3 class="text-lg font-semibold mb-3">🚀 Ready to Test Homepage</h3>
            <p class="text-gray-300 mb-4">If tests pass, the homepage signup/login should work without "server not connected" errors!</p>
            <a href="index.html" target="_blank" class="cyber-button px-6 py-3 rounded text-lg">
                🏠 Open Homepage
            </a>
        </div>
    </div>

    <script>
        // Auto-generate unique email for testing
        document.getElementById('test-email').value = `test${Date.now()}@example.com`;

        async function runAllTests() {
            const results = document.getElementById('test-results');
            const apiResults = document.getElementById('api-test-results');
            const authResults = document.getElementById('auth-test-results');
            
            results.innerHTML = '<div class="text-blue-400">🔄 Running comprehensive tests...</div>';
            
            // Test 1: API Connectivity
            apiResults.innerHTML = '⏳ Testing API endpoints...';
            
            const apiTests = [
                { name: 'Auth Status', url: '/api/auth/status' },
                { name: 'Working API', url: 'working_api.php?endpoint=auth/status' },
                { name: 'Simple API', url: 'test_api_simple.php?endpoint=auth/status' }
            ];
            
            let apiSuccess = 0;
            let apiResults_text = '';
            
            for (const test of apiTests) {
                try {
                    const response = await fetch(test.url);
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success !== undefined) {
                            apiResults_text += `✅ ${test.name}: Working<br>`;
                            apiSuccess++;
                        } else {
                            apiResults_text += `❌ ${test.name}: Invalid response<br>`;
                        }
                    } else {
                        apiResults_text += `❌ ${test.name}: HTTP ${response.status}<br>`;
                    }
                } catch (error) {
                    apiResults_text += `❌ ${test.name}: ${error.message}<br>`;
                }
            }
            
            apiResults.innerHTML = apiResults_text;
            
            // Test 2: Authentication Flow
            authResults.innerHTML = '⏳ Testing authentication flow...';
            
            try {
                // Test registration
                const regResponse = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        first_name: 'Test',
                        last_name: 'User',
                        email: `autotest${Date.now()}@example.com`,
                        password: 'TestPass123!'
                    })
                });
                
                let authResults_text = '';
                
                if (regResponse.ok) {
                    const regData = await regResponse.json();
                    if (regData.success) {
                        authResults_text += '✅ Registration: Working<br>';
                        
                        // Test login with same credentials
                        const loginResponse = await fetch('/api/auth/login', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                email: `autotest${Date.now()-1000}@example.com`,
                                password: 'TestPass123!'
                            })
                        });
                        
                        if (loginResponse.ok) {
                            const loginData = await loginResponse.json();
                            if (loginData.success || loginData.error) {
                                authResults_text += '✅ Login: Working<br>';
                            } else {
                                authResults_text += '❌ Login: Invalid response<br>';
                            }
                        } else {
                            authResults_text += `⚠️ Login: HTTP ${loginResponse.status}<br>`;
                        }
                    } else {
                        authResults_text += `⚠️ Registration: ${regData.error || 'Failed'}<br>`;
                    }
                } else {
                    authResults_text += `❌ Registration: HTTP ${regResponse.status}<br>`;
                }
                
                authResults.innerHTML = authResults_text;
                
            } catch (error) {
                authResults.innerHTML = `❌ Authentication test failed: ${error.message}`;
            }
            
            // Final results
            const totalTests = apiTests.length + 2; // API tests + auth tests
            const passedTests = apiSuccess + (authResults.innerHTML.includes('✅') ? 1 : 0);
            
            if (passedTests >= totalTests - 1) {
                results.innerHTML = `
                    <div class="text-green-400">
                        🎉 <strong>TESTS PASSED!</strong><br>
                        ✅ ${passedTests}/${totalTests} tests successful<br>
                        🏠 Homepage signup/login should now work without "server not connected" errors!
                    </div>
                `;
            } else {
                results.innerHTML = `
                    <div class="text-yellow-400">
                        ⚠️ <strong>PARTIAL SUCCESS</strong><br>
                        ✅ ${passedTests}/${totalTests} tests passed<br>
                        🔧 Some issues detected, but fallback systems should work
                    </div>
                `;
            }
        }

        async function testRegistration() {
            const firstName = document.getElementById('test-firstname').value;
            const lastName = document.getElementById('test-lastname').value;
            const email = document.getElementById('test-email').value;
            const password = document.getElementById('test-password').value;
            const results = document.getElementById('manual-test-results');
            
            if (!firstName || !lastName || !email || !password) {
                results.innerHTML = '<div class="text-red-400">❌ Please fill all fields</div>';
                return;
            }
            
            results.innerHTML = '<div class="text-blue-400">⏳ Testing registration...</div>';
            
            try {
                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        first_name: firstName,
                        last_name: lastName,
                        email: email,
                        password: password
                    })
                });
                
                console.log('Registration response status:', response.status);
                const responseText = await response.text();
                console.log('Registration response:', responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    throw new Error('Invalid JSON response');
                }
                
                if (data.success) {
                    results.innerHTML = `
                        <div class="text-green-400">
                            ✅ <strong>Registration Successful!</strong><br>
                            👤 User: ${data.data?.user?.first_name || 'Unknown'}<br>
                            💰 Credits: ${data.data?.user?.credits || 0}<br>
                            🎉 No "server not connected" error!
                        </div>
                    `;
                    
                    // Auto-fill login form
                    document.getElementById('login-test-email').value = email;
                    document.getElementById('login-test-password').value = password;
                } else {
                    results.innerHTML = `<div class="text-yellow-400">⚠️ Registration response: ${data.error || 'Unknown error'}</div>`;
                }
            } catch (error) {
                results.innerHTML = `<div class="text-red-400">❌ Registration failed: ${error.message}</div>`;
            }
        }

        async function testLogin() {
            const email = document.getElementById('login-test-email').value;
            const password = document.getElementById('login-test-password').value;
            const results = document.getElementById('manual-test-results');
            
            if (!email || !password) {
                results.innerHTML = '<div class="text-red-400">❌ Please enter email and password</div>';
                return;
            }
            
            results.innerHTML = '<div class="text-blue-400">⏳ Testing login...</div>';
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email, password })
                });
                
                console.log('Login response status:', response.status);
                const responseText = await response.text();
                console.log('Login response:', responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    throw new Error('Invalid JSON response');
                }
                
                if (data.success) {
                    results.innerHTML = `
                        <div class="text-green-400">
                            ✅ <strong>Login Successful!</strong><br>
                            👤 Welcome: ${data.data?.user?.first_name || 'User'}<br>
                            💰 Credits: ${data.data?.user?.credits || 0}<br>
                            🎉 No "server not connected" error!
                        </div>
                    `;
                } else {
                    results.innerHTML = `<div class="text-yellow-400">⚠️ Login response: ${data.error || 'Invalid credentials'}</div>`;
                }
            } catch (error) {
                results.innerHTML = `<div class="text-red-400">❌ Login failed: ${error.message}</div>`;
            }
        }

        // Run tests on page load
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
