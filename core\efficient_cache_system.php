<?php
/**
 * Efficient Cache System - Smart caching for video generation components
 * Reduces regeneration time and improves overall performance
 */

class EfficientCacheSystem {
    private $cacheDir;
    private $config;
    private $maxCacheSize;
    private $defaultTTL;
    private $compressionEnabled;
    
    public function __construct($config = []) {
        $this->cacheDir = $config['cache_dir'] ?? __DIR__ . '/../temp/cache/';
        $this->maxCacheSize = $config['max_cache_size'] ?? 1024 * 1024 * 1024; // 1GB default
        $this->defaultTTL = $config['default_ttl'] ?? 3600; // 1 hour default
        $this->compressionEnabled = $config['compression'] ?? true;
        
        $this->ensureCacheDirectory();
        $this->loadConfig();
    }
    
    /**
     * Store data in cache with automatic compression and metadata
     */
    public function set($key, $data, $ttl = null, $tags = []) {
        $ttl = $ttl ?? $this->defaultTTL;
        $cacheKey = $this->generateCacheKey($key);
        $cacheFile = $this->getCacheFilePath($cacheKey);
        
        // Prepare cache data
        $cacheData = [
            'key' => $key,
            'data' => $data,
            'created_at' => time(),
            'expires_at' => time() + $ttl,
            'ttl' => $ttl,
            'tags' => $tags,
            'size' => 0,
            'compressed' => false,
            'checksum' => ''
        ];
        
        // Serialize data
        $serializedData = serialize($data);
        $cacheData['size'] = strlen($serializedData);
        
        // Apply compression if enabled and beneficial
        if ($this->compressionEnabled && $cacheData['size'] > 1024) {
            $compressedData = gzcompress($serializedData, 6);
            if (strlen($compressedData) < $cacheData['size'] * 0.9) {
                $serializedData = $compressedData;
                $cacheData['compressed'] = true;
                $cacheData['size'] = strlen($serializedData);
            }
        }
        
        // Generate checksum
        $cacheData['checksum'] = md5($serializedData);
        $cacheData['data'] = $serializedData;
        
        // Check cache size limits
        $this->enforceMaxCacheSize();
        
        // Write to cache
        $success = file_put_contents($cacheFile, json_encode($cacheData), LOCK_EX);
        
        if ($success) {
            $this->updateCacheIndex($cacheKey, $cacheData);
            return true;
        }
        
        return false;
    }
    
    /**
     * Retrieve data from cache with automatic decompression
     */
    public function get($key) {
        $cacheKey = $this->generateCacheKey($key);
        $cacheFile = $this->getCacheFilePath($cacheKey);
        
        if (!file_exists($cacheFile)) {
            return null;
        }
        
        $cacheData = json_decode(file_get_contents($cacheFile), true);
        
        if (!$cacheData) {
            $this->delete($key);
            return null;
        }
        
        // Check expiration
        if (time() > $cacheData['expires_at']) {
            $this->delete($key);
            return null;
        }
        
        // Verify checksum
        if (md5($cacheData['data']) !== $cacheData['checksum']) {
            $this->delete($key);
            return null;
        }
        
        // Decompress if needed
        $serializedData = $cacheData['data'];
        if ($cacheData['compressed']) {
            $serializedData = gzuncompress($serializedData);
            if ($serializedData === false) {
                $this->delete($key);
                return null;
            }
        }
        
        // Unserialize data
        $data = unserialize($serializedData);
        
        // Update access time
        $this->updateAccessTime($cacheKey);
        
        return $data;
    }
    
    /**
     * Check if key exists in cache and is valid
     */
    public function has($key) {
        $cacheKey = $this->generateCacheKey($key);
        $cacheFile = $this->getCacheFilePath($cacheKey);
        
        if (!file_exists($cacheFile)) {
            return false;
        }
        
        $cacheData = json_decode(file_get_contents($cacheFile), true);
        
        if (!$cacheData || time() > $cacheData['expires_at']) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Delete specific cache entry
     */
    public function delete($key) {
        $cacheKey = $this->generateCacheKey($key);
        $cacheFile = $this->getCacheFilePath($cacheKey);
        
        if (file_exists($cacheFile)) {
            unlink($cacheFile);
            $this->removeFromCacheIndex($cacheKey);
            return true;
        }
        
        return false;
    }
    
    /**
     * Clear cache by tags
     */
    public function clearByTags($tags) {
        $index = $this->getCacheIndex();
        $deletedCount = 0;
        
        foreach ($index as $cacheKey => $metadata) {
            if (array_intersect($tags, $metadata['tags'])) {
                $this->deleteByKey($cacheKey);
                $deletedCount++;
            }
        }
        
        return $deletedCount;
    }
    
    /**
     * Clear expired cache entries
     */
    public function clearExpired() {
        $index = $this->getCacheIndex();
        $deletedCount = 0;
        $currentTime = time();
        
        foreach ($index as $cacheKey => $metadata) {
            if ($currentTime > $metadata['expires_at']) {
                $this->deleteByKey($cacheKey);
                $deletedCount++;
            }
        }
        
        return $deletedCount;
    }
    
    /**
     * Clear all cache
     */
    public function clearAll() {
        $files = glob($this->cacheDir . 'cache_*.json');
        $deletedCount = 0;
        
        foreach ($files as $file) {
            if (unlink($file)) {
                $deletedCount++;
            }
        }
        
        // Clear index
        $this->saveCacheIndex([]);
        
        return $deletedCount;
    }
    
    /**
     * Get cache statistics
     */
    public function getStats() {
        $index = $this->getCacheIndex();
        $totalSize = 0;
        $totalEntries = count($index);
        $expiredEntries = 0;
        $compressedEntries = 0;
        $currentTime = time();
        
        foreach ($index as $metadata) {
            $totalSize += $metadata['size'];
            
            if ($currentTime > $metadata['expires_at']) {
                $expiredEntries++;
            }
            
            if ($metadata['compressed']) {
                $compressedEntries++;
            }
        }
        
        return [
            'total_entries' => $totalEntries,
            'expired_entries' => $expiredEntries,
            'compressed_entries' => $compressedEntries,
            'total_size' => $totalSize,
            'total_size_mb' => round($totalSize / 1024 / 1024, 2),
            'cache_hit_rate' => $this->getCacheHitRate(),
            'compression_ratio' => $totalEntries > 0 ? round($compressedEntries / $totalEntries * 100, 1) : 0
        ];
    }
    
    /**
     * Optimize cache by removing expired entries and compacting
     */
    public function optimize() {
        $startTime = microtime(true);
        
        // Clear expired entries
        $expiredCleared = $this->clearExpired();
        
        // Enforce size limits
        $sizeLimitCleared = $this->enforceMaxCacheSize();
        
        // Defragment cache index
        $this->defragmentIndex();
        
        $optimizationTime = microtime(true) - $startTime;
        
        return [
            'expired_cleared' => $expiredCleared,
            'size_limit_cleared' => $sizeLimitCleared,
            'optimization_time' => round($optimizationTime, 3),
            'final_stats' => $this->getStats()
        ];
    }
    
    /**
     * Cache prompts with intelligent deduplication
     */
    public function cachePrompts($prompts, $parameters, $ttl = 7200) {
        $key = 'prompts_' . md5(json_encode($parameters));
        $tags = ['prompts', 'mood_' . $parameters['mood'], 'topic_' . $parameters['topic']];
        
        return $this->set($key, $prompts, $ttl, $tags);
    }
    
    /**
     * Get cached prompts
     */
    public function getCachedPrompts($parameters) {
        $key = 'prompts_' . md5(json_encode($parameters));
        return $this->get($key);
    }
    
    /**
     * Cache video frames
     */
    public function cacheFrames($frames, $sceneHash, $ttl = 3600) {
        $key = 'frames_' . $sceneHash;
        $tags = ['frames', 'video_generation'];
        
        return $this->set($key, $frames, $ttl, $tags);
    }
    
    /**
     * Get cached frames
     */
    public function getCachedFrames($sceneHash) {
        $key = 'frames_' . $sceneHash;
        return $this->get($key);
    }
    
    /**
     * Cache audio segments
     */
    public function cacheAudio($audioData, $textHash, $voicePack, $ttl = 3600) {
        $key = 'audio_' . $textHash . '_' . $voicePack;
        $tags = ['audio', 'voice_' . $voicePack];
        
        return $this->set($key, $audioData, $ttl, $tags);
    }
    
    /**
     * Get cached audio
     */
    public function getCachedAudio($textHash, $voicePack) {
        $key = 'audio_' . $textHash . '_' . $voicePack;
        return $this->get($key);
    }
    
    /**
     * Generate cache key
     */
    private function generateCacheKey($key) {
        return 'cache_' . md5($key);
    }
    
    /**
     * Get cache file path
     */
    private function getCacheFilePath($cacheKey) {
        return $this->cacheDir . $cacheKey . '.json';
    }
    
    /**
     * Ensure cache directory exists
     */
    private function ensureCacheDirectory() {
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }
    
    /**
     * Load configuration
     */
    private function loadConfig() {
        $configFile = __DIR__ . '/../config/cache_config.json';
        
        if (file_exists($configFile)) {
            $config = json_decode(file_get_contents($configFile), true);
            
            if ($config) {
                $this->maxCacheSize = $config['max_cache_size'] ?? $this->maxCacheSize;
                $this->defaultTTL = $config['default_ttl'] ?? $this->defaultTTL;
                $this->compressionEnabled = $config['compression'] ?? $this->compressionEnabled;
            }
        }
    }
    
    /**
     * Get cache index
     */
    private function getCacheIndex() {
        $indexFile = $this->cacheDir . 'cache_index.json';
        
        if (file_exists($indexFile)) {
            $index = json_decode(file_get_contents($indexFile), true);
            return $index ?: [];
        }
        
        return [];
    }
    
    /**
     * Save cache index
     */
    private function saveCacheIndex($index) {
        $indexFile = $this->cacheDir . 'cache_index.json';
        file_put_contents($indexFile, json_encode($index), LOCK_EX);
    }
    
    /**
     * Update cache index
     */
    private function updateCacheIndex($cacheKey, $metadata) {
        $index = $this->getCacheIndex();
        
        $index[$cacheKey] = [
            'created_at' => $metadata['created_at'],
            'expires_at' => $metadata['expires_at'],
            'size' => $metadata['size'],
            'tags' => $metadata['tags'],
            'compressed' => $metadata['compressed'],
            'last_accessed' => time()
        ];
        
        $this->saveCacheIndex($index);
    }
    
    /**
     * Remove from cache index
     */
    private function removeFromCacheIndex($cacheKey) {
        $index = $this->getCacheIndex();
        unset($index[$cacheKey]);
        $this->saveCacheIndex($index);
    }
    
    /**
     * Update access time
     */
    private function updateAccessTime($cacheKey) {
        $index = $this->getCacheIndex();
        
        if (isset($index[$cacheKey])) {
            $index[$cacheKey]['last_accessed'] = time();
            $this->saveCacheIndex($index);
        }
    }
    
    /**
     * Delete by cache key
     */
    private function deleteByKey($cacheKey) {
        $cacheFile = $this->getCacheFilePath($cacheKey);
        
        if (file_exists($cacheFile)) {
            unlink($cacheFile);
        }
        
        $this->removeFromCacheIndex($cacheKey);
    }
    
    /**
     * Enforce maximum cache size
     */
    private function enforceMaxCacheSize() {
        $index = $this->getCacheIndex();
        $totalSize = array_sum(array_column($index, 'size'));
        $deletedCount = 0;
        
        if ($totalSize > $this->maxCacheSize) {
            // Sort by last accessed time (LRU)
            uasort($index, function($a, $b) {
                return $a['last_accessed'] - $b['last_accessed'];
            });
            
            foreach ($index as $cacheKey => $metadata) {
                if ($totalSize <= $this->maxCacheSize * 0.8) {
                    break;
                }
                
                $this->deleteByKey($cacheKey);
                $totalSize -= $metadata['size'];
                $deletedCount++;
            }
        }
        
        return $deletedCount;
    }
    
    /**
     * Get cache hit rate
     */
    private function getCacheHitRate() {
        $statsFile = $this->cacheDir . 'cache_stats.json';
        
        if (file_exists($statsFile)) {
            $stats = json_decode(file_get_contents($statsFile), true);
            
            if ($stats && $stats['total_requests'] > 0) {
                return round($stats['cache_hits'] / $stats['total_requests'] * 100, 1);
            }
        }
        
        return 0;
    }
    
    /**
     * Defragment cache index
     */
    private function defragmentIndex() {
        $index = $this->getCacheIndex();
        $validIndex = [];
        
        foreach ($index as $cacheKey => $metadata) {
            $cacheFile = $this->getCacheFilePath($cacheKey);
            
            if (file_exists($cacheFile)) {
                $validIndex[$cacheKey] = $metadata;
            }
        }
        
        $this->saveCacheIndex($validIndex);
    }
}
