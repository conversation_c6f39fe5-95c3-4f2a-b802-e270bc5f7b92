<?php
/**
 * Credit Manager
 * Handles credit system for video generation and tools
 */

require_once 'database_manager.php';

class CreditManager {
    private $db;
    
    public function __construct() {
        $this->db = new DatabaseManager();
    }
    
    /**
     * Get user's current credit balance
     */
    public function getCreditBalance($userId) {
        $user = $this->db->selectOne('users', ['id' => $userId]);
        return $user ? (int)$user['credits_available'] : 0;
    }
    
    /**
     * Check if user has sufficient credits
     */
    public function hasCredits($userId, $amount) {
        $balance = $this->getCreditBalance($userId);
        return $balance >= $amount;
    }
    
    /**
     * Deduct credits from user account
     */
    public function deductCredits($userId, $amount, $operation, $operationId = null) {
        if (!$this->hasCredits($userId, $amount)) {
            throw new InsufficientCreditsException(
                "Insufficient credits. Required: $amount, Available: " . $this->getCreditBalance($userId)
            );
        }
        
        // Start transaction
        $this->db->beginTransaction();
        
        try {
            // Update user credits
            $sql = "UPDATE users SET credits_available = credits_available - ? WHERE id = ?";
            $this->db->query($sql, [$amount, $userId]);
            
            // Log credit transaction
            $this->logCreditTransaction($userId, -$amount, $operation, $operationId);
            
            // Update tools credits used if it's a tool operation
            if (strpos($operation, 'tool_') === 0) {
                $sql = "UPDATE users SET tools_credits_used = tools_credits_used + ? WHERE id = ?";
                $this->db->query($sql, [$amount, $userId]);
            }
            
            $this->db->commit();
            
            return $this->getCreditBalance($userId);
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Add credits to user account
     */
    public function addCredits($userId, $amount, $source = 'purchase', $transactionId = null) {
        // Start transaction
        $this->db->beginTransaction();
        
        try {
            // Update user credits
            $sql = "UPDATE users SET credits_available = credits_available + ? WHERE id = ?";
            $this->db->query($sql, [$amount, $userId]);
            
            // Log credit transaction
            $this->logCreditTransaction($userId, $amount, $source, $transactionId);
            
            $this->db->commit();
            
            return $this->getCreditBalance($userId);
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Get credit cost for a tool
     */
    public function getToolCreditCost($toolId) {
        $tool = $this->db->selectOne('tools_config', ['tool_id' => $toolId]);
        return $tool ? (int)$tool['credit_cost'] : 1;
    }
    
    /**
     * Get credit cost for video generation based on quality
     */
    public function getVideoCreditCost($quality = 'medium') {
        $costs = [
            'low' => 5,
            'medium' => 10,
            'high' => 15,
            'ultra' => 25
        ];
        
        return $costs[$quality] ?? $costs['medium'];
    }
    
    /**
     * Process tool credit deduction
     */
    public function processToolCredits($userId, $toolId, $operationId, $options = []) {
        $creditCost = $this->getToolCreditCost($toolId);
        
        // Apply multipliers for batch operations
        if (isset($options['file_count']) && $options['file_count'] > 1) {
            $batchMultiplier = min($options['file_count'], 10); // Cap at 10x
            $creditCost *= $batchMultiplier;
        }
        
        // Apply complexity multipliers
        if (isset($options['complexity_multiplier'])) {
            $creditCost = (int)($creditCost * $options['complexity_multiplier']);
        }
        
        return $this->deductCredits($userId, $creditCost, "tool_$toolId", $operationId);
    }
    
    /**
     * Process video generation credits
     */
    public function processVideoCredits($userId, $jobId, $quality = 'medium', $duration = 30) {
        $baseCost = $this->getVideoCreditCost($quality);
        
        // Apply duration multiplier (base is 30 seconds)
        $durationMultiplier = max(1, $duration / 30);
        $totalCost = (int)($baseCost * $durationMultiplier);
        
        return $this->deductCredits($userId, $totalCost, 'video_generation', $jobId);
    }
    
    /**
     * Get user's credit usage statistics
     */
    public function getCreditUsageStats($userId, $period = '30 days') {
        $days = $this->parsePeriodToDays($period);
        
        $sql = "
            SELECT 
                operation_type,
                SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as credits_used,
                SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as credits_added,
                COUNT(CASE WHEN amount < 0 THEN 1 END) as usage_count,
                DATE(created_at) as usage_date
            FROM credit_transactions 
            WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY operation_type, DATE(created_at)
            ORDER BY usage_date DESC
        ";
        
        return $this->db->query($sql, [$userId, $days]);
    }
    
    /**
     * Get credit transaction history
     */
    public function getCreditHistory($userId, $limit = 50, $offset = 0) {
        $sql = "
            SELECT 
                amount,
                operation_type,
                operation_id,
                description,
                created_at,
                balance_after
            FROM credit_transactions 
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        ";
        
        return $this->db->query($sql, [$userId, $limit, $offset]);
    }
    
    /**
     * Get monthly credit summary
     */
    public function getMonthlyCreditSummary($userId) {
        $sql = "
            SELECT 
                YEAR(created_at) as year,
                MONTH(created_at) as month,
                SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as credits_used,
                SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as credits_purchased,
                COUNT(CASE WHEN amount < 0 THEN 1 END) as operations_count
            FROM credit_transactions 
            WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
            GROUP BY YEAR(created_at), MONTH(created_at)
            ORDER BY year DESC, month DESC
        ";
        
        return $this->db->query($sql, [$userId]);
    }
    
    /**
     * Check if user needs credit warning
     */
    public function needsCreditWarning($userId) {
        $balance = $this->getCreditBalance($userId);
        $user = $this->db->selectOne('users', ['id' => $userId]);
        $subscriptionType = $user['subscription_type'] ?? 'free';
        
        // Warning thresholds by subscription type
        $thresholds = [
            'free' => 5,
            'pro' => 50,
            'business' => 100,
            'enterprise' => 500
        ];
        
        $threshold = $thresholds[$subscriptionType] ?? $thresholds['free'];
        
        return $balance <= $threshold;
    }
    
    /**
     * Get credit refill information
     */
    public function getCreditRefillInfo($userId) {
        $user = $this->db->selectOne('users', ['id' => $userId]);
        $subscriptionType = $user['subscription_type'] ?? 'free';
        
        // Monthly credit allocations
        $monthlyCredits = [
            'free' => 50,
            'pro' => 500,
            'business' => 2000,
            'enterprise' => -1 // Unlimited
        ];
        
        $allocation = $monthlyCredits[$subscriptionType] ?? $monthlyCredits['free'];
        
        // Calculate next refill date (first day of next month)
        $nextRefill = date('Y-m-01 00:00:00', strtotime('first day of next month'));
        
        // Calculate credits used this month
        $sql = "
            SELECT SUM(ABS(amount)) as credits_used
            FROM credit_transactions 
            WHERE user_id = ? AND amount < 0 
            AND created_at >= DATE_FORMAT(NOW(), '%Y-%m-01 00:00:00')
        ";
        
        $result = $this->db->query($sql, [$userId]);
        $creditsUsedThisMonth = $result[0]['credits_used'] ?? 0;
        
        return [
            'subscription_type' => $subscriptionType,
            'monthly_allocation' => $allocation,
            'credits_used_this_month' => (int)$creditsUsedThisMonth,
            'credits_remaining' => $allocation > 0 ? max(0, $allocation - $creditsUsedThisMonth) : -1,
            'next_refill' => $nextRefill,
            'days_until_refill' => (int)((strtotime($nextRefill) - time()) / 86400)
        ];
    }
    
    /**
     * Process monthly credit refill
     */
    public function processMonthlyRefill($userId) {
        $refillInfo = $this->getCreditRefillInfo($userId);
        
        if ($refillInfo['monthly_allocation'] > 0) {
            // Set credits to monthly allocation (not add to existing)
            $sql = "UPDATE users SET credits_available = ? WHERE id = ?";
            $this->db->query($sql, [$refillInfo['monthly_allocation'], $userId]);
            
            // Log the refill
            $this->logCreditTransaction(
                $userId, 
                $refillInfo['monthly_allocation'], 
                'monthly_refill', 
                date('Y-m')
            );
            
            return $refillInfo['monthly_allocation'];
        }
        
        return 0;
    }
    
    /**
     * Log credit transaction
     */
    private function logCreditTransaction($userId, $amount, $operationType, $operationId = null) {
        $currentBalance = $this->getCreditBalance($userId);
        $balanceAfter = $currentBalance; // Current balance already reflects the change
        
        $data = [
            'user_id' => $userId,
            'amount' => $amount,
            'operation_type' => $operationType,
            'operation_id' => $operationId,
            'description' => $this->getOperationDescription($operationType, $amount),
            'balance_after' => $balanceAfter,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        // Create table if it doesn't exist
        $this->createCreditTransactionsTable();
        
        return $this->db->insert('credit_transactions', $data);
    }
    
    /**
     * Create credit transactions table if it doesn't exist
     */
    private function createCreditTransactionsTable() {
        $sql = "
            CREATE TABLE IF NOT EXISTS credit_transactions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                amount INT NOT NULL,
                operation_type VARCHAR(100) NOT NULL,
                operation_id VARCHAR(255),
                description TEXT,
                balance_after INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_date (user_id, created_at),
                INDEX idx_operation (operation_type),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $this->db->query($sql);
    }
    
    /**
     * Get operation description for logging
     */
    private function getOperationDescription($operationType, $amount) {
        $descriptions = [
            'video_generation' => $amount < 0 ? 'Video generation' : 'Video generation refund',
            'monthly_refill' => 'Monthly credit allocation',
            'purchase' => 'Credit purchase',
            'bonus' => 'Bonus credits',
            'refund' => 'Credit refund'
        ];
        
        // Handle tool operations
        if (strpos($operationType, 'tool_') === 0) {
            $toolName = substr($operationType, 5);
            return $amount < 0 ? "Tool usage: $toolName" : "Tool refund: $toolName";
        }
        
        return $descriptions[$operationType] ?? $operationType;
    }
    
    /**
     * Parse period string to days
     */
    private function parsePeriodToDays($period) {
        if (is_numeric($period)) {
            return (int)$period;
        }
        
        $periods = [
            '7 days' => 7,
            '30 days' => 30,
            '90 days' => 90,
            '1 year' => 365
        ];
        
        return $periods[$period] ?? 30;
    }
}

/**
 * Custom exception for insufficient credits
 */
class InsufficientCreditsException extends Exception {
    public function __construct($message = "Insufficient credits", $code = 0, Exception $previous = null) {
        parent::__construct($message, $code, $previous);
    }
}
?>
