{"flow_info": {"name": "Bollywood Reel", "description": "Short video content with Bollywood flair", "type": "reel", "duration_target": "15-60 seconds", "aspect_ratio": "9:16"}, "structure": {"hook": {"duration": "3-5 seconds", "purpose": "Grab attention immediately", "style_hints": ["dramatic", "over_the_top", "filmy"], "voice_style": "energetic", "background_music": "upbeat_bollywood"}, "content": {"duration": "40-50 seconds", "purpose": "Deliver main message with entertainment", "segments": 3, "style_hints": ["movie_references", "dramatic_pauses", "emotional"], "voice_style": "storytelling", "background_music": "background_bollywood"}, "call_to_action": {"duration": "5-10 seconds", "purpose": "Engage audience for interaction", "style_hints": ["persuasive", "friendly", "encouraging"], "voice_style": "persuasive", "background_music": "outro_bollywood"}}, "voice_guidelines": {"tone": "dramatic and expressive", "pace": "varied - fast for excitement, slow for emphasis", "accent": "hindi_bollywood", "emotional_range": "high", "pronunciation": {"emphasis_words": ["hero", "villain", "drama", "emotion", "family"], "hinglish_mixing": true, "movie_dialogue_style": true}}, "visual_guidelines": {"background_types": ["movie_scenes", "dramatic_lighting", "colorful"], "text_overlay": {"font": "bold_hindi_english", "colors": ["gold", "red", "white"], "animation": "dramatic_entrance", "position": "center_bottom"}, "transitions": ["fade", "zoom", "dramatic_cuts"], "effects": ["lens_flare", "color_grading", "slow_motion"]}, "audio_guidelines": {"background_music": {"style": "bollywood_instrumental", "volume": "30%", "fade_in": true, "fade_out": true}, "sound_effects": [{"type": "dhol", "timing": "emphasis_moments", "volume": "50%"}, {"type": "<PERSON><PERSON><PERSON>", "timing": "emotional_moments", "volume": "40%"}, {"type": "tabla", "timing": "rhythm_sections", "volume": "45%"}], "voice_effects": {"reverb": "slight", "compression": "medium", "eq": "warm_vocal"}}, "content_templates": {"startup_pitch": {"hook": "<PERSON><PERSON> <PERSON><PERSON>, {bollywood_actor} ki tarah main bhi ek sapna lekar aaya hun!", "content": "Mera startup idea hai - {startup_concept}. Just like in movies, pehle sab hasenge, phir sab invest karna chahenge. {number} crore mein unicorn ban jayenge!", "cta": "Agar tumhe lagta hai ye idea hit hai, toh comment mein batao!"}, "life_advice": {"hook": "Zindagi ek Bollywood movie ki tarah hai, full of drama!", "content": "Kabhi hero ban jaa<PERSON> ho, kabhi villain. But remember, har movie mein interval hota hai - life mein bhi break lena zaroori hai. {wisdom_quote}", "cta": "Apni life story share karo comments mein!"}, "cultural_commentary": {"hook": "Indian families mein ye scene dekha hai kabhi?", "content": "{cultural_situation} - bilk<PERSON> {movie_reference} jaisa! Har ghar mein ye drama chalta rehta hai. {relatable_example}", "cta": "Tag that friend jo aise situations mein fas jaata hai!"}}, "style_variations": {"funny": {"prompt_tokens": ["sarcastic", "witty", "comedic_timing"], "voice_modifiers": ["playful", "exaggerated"], "background_music": "comedy_bollywood", "effects": ["laugh_track", "comic_timing"]}, "emotional": {"prompt_tokens": ["heartfelt", "touching", "family_values"], "voice_modifiers": ["warm", "sincere"], "background_music": "emotional_bollywood", "effects": ["soft_strings", "emotional_pause"]}, "motivational": {"prompt_tokens": ["inspiring", "powerful", "uplifting"], "voice_modifiers": ["strong", "confident"], "background_music": "motivational_bollywood", "effects": ["power_chords", "building_crescendo"]}}, "cultural_elements": {"bollywood_references": ["movie_dialogues", "actor_mannerisms", "iconic_scenes", "song_references"], "indian_expressions": ["arre yaar", "kya baat hai", "bilkul sahi", "ekdum mast"], "family_dynamics": ["typical_indian_family", "generation_gap", "cultural_traditions", "modern_vs_traditional"]}, "engagement_hooks": {"questions": ["<PERSON><PERSON><PERSON> saath bhi aisa hua hai?", "Kya lagta hai, sahi kaha na?", "Agar tum hote toh kya karte?"], "challenges": ["Try this at home", "Tag someone who does this", "Share your story"], "relatability": ["Every Indian can relate", "Typical desi situation", "We've all been there"]}, "technical_specs": {"video": {"resolution": "720x1280", "fps": 30, "codec": "h264", "bitrate": "2000k"}, "audio": {"sample_rate": 44100, "channels": 2, "bitrate": "192k", "format": "aac"}, "subtitles": {"format": "srt", "font": "Arial Bold", "size": 24, "color": "white", "outline": "black", "position": "bottom_center"}}}