<?php
/**
 * Quick Fix Test for Sutradhar 2070
 * Tests all components and provides fixes
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html>
<head>
    <title>Sutradhar 2070 - Quick Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; background: #1a1a2e; color: white; padding: 20px; }
        .success { color: #10B981; }
        .error { color: #EF4444; }
        .warning { color: #F59E0B; }
        .info { color: #3B82F6; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #333; border-radius: 8px; }
        pre { background: #0f0f23; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
<h1>🎭 Sutradhar 2070 - Quick Fix Test</h1>
";

$issues = [];
$fixes = [];

// Test 1: Check if core files exist
echo "<div class='section'>";
echo "<h2>📁 File System Check</h2>";

$requiredFiles = [
    'api_unified.php' => 'Main API router',
    'mood_api.php' => 'Mood API endpoint',
    'generator.php' => 'Legacy generator endpoint',
    'mood_video_generator.html' => 'Mood generator page',
    'mood_video_generator.js' => 'Mood generator JavaScript'
];

foreach ($requiredFiles as $file => $description) {
    if (file_exists($file)) {
        echo "<span class='success'>✅ $file</span> - $description<br>";
    } else {
        echo "<span class='error'>❌ $file</span> - $description<br>";
        $issues[] = "Missing file: $file";
    }
}

// Test 2: Check database connection
echo "</div><div class='section'>";
echo "<h2>🗄️ Database Connection</h2>";

try {
    require_once '../core/database_manager.php';
    $db = new DatabaseManager();
    echo "<span class='success'>✅ Database connection successful</span><br>";
    
    // Test if tables exist
    try {
        $tables = $db->query('SHOW TABLES');
        if (count($tables) > 0) {
            echo "<span class='success'>✅ Found " . count($tables) . " database tables</span><br>";
        } else {
            echo "<span class='warning'>⚠️ No tables found - need to import schema</span><br>";
            $fixes[] = "Import database schema: mysql -u root -p < database_schema.sql";
        }
    } catch (Exception $e) {
        echo "<span class='warning'>⚠️ Tables check failed: " . $e->getMessage() . "</span><br>";
        $fixes[] = "Import database schema: mysql -u root -p < database_schema.sql";
    }
    
} catch (Exception $e) {
    echo "<span class='error'>❌ Database connection failed: " . $e->getMessage() . "</span><br>";
    $issues[] = "Database connection failed";
    $fixes[] = "Start XAMPP MySQL service";
    $fixes[] = "Check database credentials in core/database_manager.php";
}

// Test 3: Test API endpoints
echo "</div><div class='section'>";
echo "<h2>🔌 API Endpoints Test</h2>";

$endpoints = [
    'mood_api.php?action=get_moods' => 'Mood API',
    'api_unified.php?endpoint=auth/status' => 'Auth Status API',
    'api_unified.php?endpoint=payments/packages' => 'Payments API'
];

foreach ($endpoints as $endpoint => $name) {
    $url = 'http://localhost:8000/' . $endpoint;
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'ignore_errors' => true
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if ($data && isset($data['success'])) {
            echo "<span class='success'>✅ $name</span> - Responding correctly<br>";
        } else {
            echo "<span class='warning'>⚠️ $name</span> - Responding but format may be incorrect<br>";
        }
    } else {
        echo "<span class='error'>❌ $name</span> - Not responding<br>";
        $issues[] = "$name not responding";
    }
}

// Test 4: Check mood_video_generator.html
echo "</div><div class='section'>";
echo "<h2>🎬 Mood Video Generator Test</h2>";

if (file_exists('mood_video_generator.html')) {
    echo "<span class='success'>✅ mood_video_generator.html exists</span><br>";
    
    // Check if it can load
    $url = 'http://localhost:8000/mood_video_generator.html';
    $response = @file_get_contents($url, false, stream_context_create([
        'http' => ['timeout' => 5, 'ignore_errors' => true]
    ]));
    
    if ($response !== false) {
        echo "<span class='success'>✅ Page loads successfully</span><br>";
        echo "<span class='info'>🔗 <a href='mood_video_generator.html' style='color: #3B82F6;'>Test the Mood Video Generator</a></span><br>";
    } else {
        echo "<span class='error'>❌ Page failed to load</span><br>";
        $issues[] = "mood_video_generator.html not accessible";
    }
} else {
    echo "<span class='error'>❌ mood_video_generator.html missing</span><br>";
    $issues[] = "mood_video_generator.html missing";
}

// Test 5: JavaScript functionality
echo "</div><div class='section'>";
echo "<h2>📜 JavaScript Test</h2>";

if (file_exists('mood_video_generator.js')) {
    echo "<span class='success'>✅ mood_video_generator.js exists</span><br>";
    
    $jsContent = file_get_contents('mood_video_generator.js');
    if (strpos($jsContent, 'api_unified.php') !== false) {
        echo "<span class='success'>✅ JavaScript updated to use new API</span><br>";
    } else {
        echo "<span class='warning'>⚠️ JavaScript may still use old API endpoints</span><br>";
        $fixes[] = "Update JavaScript to use api_unified.php endpoints";
    }
} else {
    echo "<span class='error'>❌ mood_video_generator.js missing</span><br>";
    $issues[] = "mood_video_generator.js missing";
}

// Summary and fixes
echo "</div><div class='section'>";
echo "<h2>📋 Summary & Quick Fixes</h2>";

if (empty($issues)) {
    echo "<div style='background: #10B981; color: white; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>🎉 ALL SYSTEMS WORKING!</h3>";
    echo "<p>Your Sutradhar 2070 system appears to be functioning correctly.</p>";
    echo "<p><strong>Ready to use:</strong></p>";
    echo "<ul>";
    echo "<li><a href='mood_video_generator.html' style='color: white;'>🎬 Mood Video Generator</a></li>";
    echo "<li><a href='index.html' style='color: white;'>🏠 Main Application</a></li>";
    echo "<li><a href='dashboard.html' style='color: white;'>📊 Dashboard</a></li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #EF4444; color: white; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>⚠️ ISSUES FOUND</h3>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li>$issue</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($fixes)) {
    echo "<div style='background: #F59E0B; color: white; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>🔧 QUICK FIXES</h3>";
    echo "<ol>";
    foreach ($fixes as $fix) {
        echo "<li>$fix</li>";
    }
    echo "</ol>";
    echo "</div>";
}

// Quick action buttons
echo "<div style='margin: 20px 0;'>";
echo "<h3>🚀 Quick Actions</h3>";
echo "<a href='mood_video_generator.html' style='background: #10B981; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🎬 Try Video Generator</a>";
echo "<a href='test_fixed_system.html' style='background: #3B82F6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🧪 Run Detailed Tests</a>";
echo "<a href='index.html' style='background: #8B5CF6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🏠 Main App</a>";
echo "</div>";

echo "</div>";

// Auto-refresh option
echo "<div class='section'>";
echo "<h3>🔄 Auto-Refresh</h3>";
echo "<p>This page will auto-refresh in <span id='countdown'>30</span> seconds to check for updates.</p>";
echo "<button onclick='location.reload()' style='background: #10B981; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;'>Refresh Now</button>";
echo "<button onclick='clearInterval(refreshTimer)' style='background: #EF4444; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;'>Stop Auto-Refresh</button>";
echo "</div>";

echo "
<script>
let countdown = 30;
const refreshTimer = setInterval(() => {
    countdown--;
    document.getElementById('countdown').textContent = countdown;
    if (countdown <= 0) {
        location.reload();
    }
}, 1000);
</script>
</body>
</html>";
?>
