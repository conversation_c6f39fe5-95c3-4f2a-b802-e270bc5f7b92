<?php
/**
 * Test Video Generation System - Direct Testing
 * Tests the Hugging Face video generation functionality
 */

echo "🎬 Testing Hugging Face Video Generation\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// Test configuration
$testData = [
    'mood' => 'euphoric',
    'topic' => 'nature_wildlife',
    'inspiration' => ['cinematic', 'colorful']
];

try {
    // Step 1: Test Hugging Face Video Engine directly
    echo "🤗 Testing Hugging Face Video Engine...\n";

    require_once 'core/huggingface_video_engine.php';
    $hfEngine = new HuggingFaceVideoEngine();

    // Test connection first
    echo "🔗 Testing Hugging Face API connection...\n";
    $connectionTest = $hfEngine->testHuggingFaceConnection();

    if ($connectionTest['status'] === 'success') {
        echo "✅ Hugging Face API connection successful\n";

        // Test video generation
        echo "\n🎥 Testing video generation...\n";

        // Create test segments
        $testSegments = [
            [
                'text' => 'A majestic elephant walks through the African savanna at sunset',
                'duration' => 10,
                'index' => 0
            ],
            [
                'text' => 'The golden light illuminates the vast grasslands and acacia trees',
                'duration' => 10,
                'index' => 1
            ],
            [
                'text' => 'Birds fly overhead as the elephant reaches a watering hole',
                'duration' => 10,
                'index' => 2
            ]
        ];

        $jobId = 'test_' . time();
        $background = 'nature';
        $style = 'cinematic';

        echo "📋 Test parameters:\n";
        echo "   Job ID: $jobId\n";
        echo "   Background: $background\n";
        echo "   Style: $style\n";
        echo "   Segments: " . count($testSegments) . "\n\n";

        // Generate video
        echo "⚡ Starting video generation...\n";
        $videoFile = $hfEngine->generateAdvancedVideo($testSegments, $background, $style, $jobId);

        if ($videoFile && file_exists($videoFile)) {
            $fileSize = filesize($videoFile);
            echo "✅ Video generation successful!\n";
            echo "📁 Video file: $videoFile\n";
            echo "📊 File size: " . round($fileSize / 1024 / 1024, 2) . " MB\n";

            // Check video properties
            if ($fileSize > 100000) { // At least 100KB
                echo "✅ Video file size looks good\n";
            } else {
                echo "⚠️ Video file seems small, might be incomplete\n";
            }

        } else {
            echo "❌ Video generation failed\n";
            echo "   Expected file: $videoFile\n";
        }

    } else {
        echo "❌ Hugging Face API connection failed: " . $connectionTest['message'] . "\n";
        echo "🔧 Checking configuration...\n";

        // Check config
        $configPath = 'config/settings.json';
        if (file_exists($configPath)) {
            $config = json_decode(file_get_contents($configPath), true);
            $apiKey = $config['api_keys']['huggingface'] ?? '';

            if (empty($apiKey)) {
                echo "❌ Hugging Face API key not configured\n";
            } else {
                echo "✅ API key configured: " . substr($apiKey, 0, 10) . "...\n";
            }
        } else {
            echo "❌ Configuration file not found\n";
        }
    }

} catch (Exception $e) {
    echo "❌ Test failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

// Step 3: Test individual components
echo "\n🧪 Testing individual components...\n";

try {
    // Test mood database
    echo "🎭 Testing mood database...\n";
    require_once 'core/mood_database.php';
    $moodDb = new MoodDatabase();
    $characteristics = $moodDb->getMoodCharacteristics('euphoric');
    
    if ($characteristics) {
        echo "✅ Mood database working\n";
        echo "   Euphoric characteristics: " . json_encode($characteristics) . "\n";
    } else {
        echo "❌ Mood database failed\n";
    }
    
} catch (Exception $e) {
    echo "❌ Mood database error: " . $e->getMessage() . "\n";
}

try {
    // Test prompt generation
    echo "\n📝 Testing prompt generation...\n";
    require_once 'core/prompt_generation_engine.php';
    $promptEngine = new PromptGenerationEngine();
    $prompts = $promptEngine->generatePrompts('euphoric', 'nature_wildlife', 'cinematic', ['colorful'], 5);
    
    if ($prompts && count($prompts) > 0) {
        echo "✅ Prompt generation working\n";
        echo "   Generated " . count($prompts) . " prompts\n";
        echo "   Sample prompt: " . $prompts[0] . "\n";
    } else {
        echo "❌ Prompt generation failed\n";
    }
    
} catch (Exception $e) {
    echo "❌ Prompt generation error: " . $e->getMessage() . "\n";
}

try {
    // Test content generator
    echo "\n📄 Testing content generator...\n";
    require_once 'core/content_generator.php';
    $contentGen = new ContentGenerator();
    $content = $contentGen->generateUniqueContent(
        "Create a 30-second video about nature with euphoric mood",
        'reel',
        'euphoric',
        'default'
    );
    
    if ($content && isset($content['total_text'])) {
        echo "✅ Content generator working\n";
        echo "   Generated content length: " . strlen($content['total_text']) . " characters\n";
        echo "   Sample: " . substr($content['total_text'], 0, 100) . "...\n";
    } else {
        echo "❌ Content generator failed\n";
    }
    
} catch (Exception $e) {
    echo "❌ Content generator error: " . $e->getMessage() . "\n";
}

// Step 4: System health check
echo "\n🏥 System health check...\n";

// Check required directories
$requiredDirs = ['public/videos', 'logs', 'uploads'];
foreach ($requiredDirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "📁 Created directory: $dir\n";
    } else {
        echo "✅ Directory exists: $dir\n";
    }
}

// Check file permissions
$criticalFiles = ['public/api_unified.php', 'core/background_job_processor.php'];
foreach ($criticalFiles as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        echo "✅ File permissions OK: $file (" . decoct($perms & 0777) . ")\n";
    } else {
        echo "❌ File missing: $file\n";
    }
}

// Check database connection
try {
    require_once 'core/database_manager.php';
    $db = new DatabaseManager();
    $result = $db->query("SELECT 1 as test");
    if ($result && $result[0]['test'] == 1) {
        echo "✅ Database connection OK\n";
    } else {
        echo "❌ Database query failed\n";
    }
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
}

echo "\n🎉 Video generation test complete!\n";
echo "\n📋 Next steps:\n";
echo "1. If tests passed, your video generation system is working\n";
echo "2. Set up cron job: * * * * * php " . __DIR__ . "/cron_job_processor.php\n";
echo "3. Access the application at: http://localhost/public/\n";
echo "4. Try generating a video through the web interface\n";
?>
