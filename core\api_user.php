<?php
/**
 * User Management API for Sutradhar 2070
 * Handles user profile, credits, and account management
 */

require_once 'api_base.php';

class UserAPI extends APIBase {
    
    public function handleRequest() {
        $path = $this->requestPath;
        $method = $this->requestMethod;
        
        switch ($path) {
            case 'user/profile':
                if ($method === 'GET') $this->getProfile();
                if ($method === 'PUT') $this->updateProfile();
                break;
            case 'user/credits':
                if ($method === 'GET') $this->getCredits();
                break;
            case 'user/credits/history':
                if ($method === 'GET') $this->getCreditHistory();
                break;
            case 'user/subscription':
                if ($method === 'GET') $this->getSubscription();
                break;
            case 'user/library':
                if ($method === 'GET') $this->getVideoLibrary();
                break;
            case 'user/library/favorite':
                if ($method === 'POST') $this->toggleFavorite();
                break;
            case 'user/stats':
                if ($method === 'GET') $this->getUserStats();
                break;
            case 'user/settings':
                if ($method === 'GET') $this->getSettings();
                if ($method === 'PUT') $this->updateSettings();
                break;
            case 'user/avatar':
                if ($method === 'POST') $this->uploadAvatar();
                break;
            case 'user/delete':
                if ($method === 'DELETE') $this->deleteAccount();
                break;
            default:
                $this->sendError('Endpoint not found', 404);
        }
    }
    
    /**
     * Get user profile
     */
    private function getProfile() {
        $this->requireAuth();
        
        $user = $this->db->selectOne('users', ['user_id' => $this->currentUser['user_id']]);
        $profile = $this->db->selectOne('user_profiles', ['user_id' => $this->currentUser['user_id']]);
        $credits = $this->db->selectOne('user_credit_balances', ['user_id' => $this->currentUser['user_id']]);
        $subscription = $this->db->selectOne('user_subscriptions', [
            'user_id' => $this->currentUser['user_id'],
            'status' => 'active'
        ]);
        
        // Get subscription plan details
        $plan = null;
        if ($subscription) {
            $plan = $this->db->selectOne('subscription_plans', ['plan_id' => $subscription['plan_id']]);
        }
        
        // Remove sensitive data
        unset($user['password_hash'], $user['email_verification_token'], 
              $user['password_reset_token'], $user['password_reset_expires']);
        
        $profileData = [
            'user' => $user,
            'profile' => $profile,
            'credits' => [
                'total' => $credits['total_credits'] ?? 0,
                'used' => $credits['used_credits'] ?? 0,
                'available' => $credits['available_credits'] ?? 0
            ],
            'subscription' => $subscription ? [
                'plan_id' => $subscription['plan_id'],
                'plan_name' => $plan['name'] ?? 'Unknown',
                'status' => $subscription['status'],
                'billing_cycle' => $subscription['billing_cycle'],
                'current_period_start' => $subscription['current_period_start'],
                'current_period_end' => $subscription['current_period_end'],
                'features' => json_decode($plan['features'] ?? '[]', true)
            ] : null
        ];
        
        $this->sendSuccess($profileData);
    }
    
    /**
     * Update user profile
     */
    private function updateProfile() {
        $this->requireAuth();
        
        $allowedFields = ['first_name', 'last_name', 'phone'];
        $allowedProfileFields = ['company_name', 'industry', 'website', 'bio', 'timezone', 'language'];
        
        $userUpdates = [];
        $profileUpdates = [];
        
        // Validate and prepare user updates
        foreach ($allowedFields as $field) {
            if (isset($this->requestData[$field])) {
                $value = trim($this->requestData[$field]);
                
                if ($field === 'first_name' || $field === 'last_name') {
                    if (strlen($value) < 2) {
                        $this->sendError("$field must be at least 2 characters long", 400);
                    }
                }
                
                if ($field === 'phone' && !empty($value)) {
                    if (!preg_match('/^[\+]?[1-9][\d]{0,15}$/', $value)) {
                        $this->sendError('Invalid phone number format', 400);
                    }
                }
                
                $userUpdates[$field] = $value;
            }
        }
        
        // Validate and prepare profile updates
        foreach ($allowedProfileFields as $field) {
            if (isset($this->requestData[$field])) {
                $value = trim($this->requestData[$field]);
                
                if ($field === 'website' && !empty($value)) {
                    if (!filter_var($value, FILTER_VALIDATE_URL)) {
                        $this->sendError('Invalid website URL format', 400);
                    }
                }
                
                if ($field === 'bio' && strlen($value) > 500) {
                    $this->sendError('Bio must be less than 500 characters', 400);
                }
                
                $profileUpdates[$field] = $value;
            }
        }
        
        try {
            $this->db->beginTransaction();
            
            // Update user table
            if (!empty($userUpdates)) {
                $userUpdates['updated_at'] = date('Y-m-d H:i:s');
                $this->db->update('users', $userUpdates, ['user_id' => $this->currentUser['user_id']]);
            }
            
            // Update profile table
            if (!empty($profileUpdates)) {
                $profileUpdates['updated_at'] = date('Y-m-d H:i:s');
                $this->db->update('user_profiles', $profileUpdates, ['user_id' => $this->currentUser['user_id']]);
            }
            
            $this->db->commit();
            
            $this->sendSuccess(null, 'Profile updated successfully');
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Profile update failed: " . $e->getMessage());
            $this->sendError('Failed to update profile', 500);
        }
    }
    
    /**
     * Get user credits
     */
    private function getCredits() {
        $this->requireAuth();
        
        $credits = $this->db->selectOne('user_credit_balances', ['user_id' => $this->currentUser['user_id']]);
        
        // Get recent transactions
        $recentTransactions = $this->db->select('credit_transactions', 
            ['user_id' => $this->currentUser['user_id']], 
            'ORDER BY created_at DESC LIMIT 10'
        );
        
        $this->sendSuccess([
            'balance' => [
                'total' => $credits['total_credits'] ?? 0,
                'used' => $credits['used_credits'] ?? 0,
                'available' => $credits['available_credits'] ?? 0
            ],
            'recent_transactions' => array_map(function($txn) {
                return [
                    'transaction_id' => $txn['transaction_id'],
                    'type' => $txn['type'],
                    'amount' => intval($txn['credits_amount']),
                    'description' => $txn['description'],
                    'created_at' => $txn['created_at']
                ];
            }, $recentTransactions)
        ]);
    }
    
    /**
     * Get credit history
     */
    private function getCreditHistory() {
        $this->requireAuth();
        
        $page = intval($this->requestData['page'] ?? 1);
        $limit = min(intval($this->requestData['limit'] ?? 20), 100);
        $type = $this->requestData['type'] ?? null;
        
        $offset = ($page - 1) * $limit;
        
        $conditions = ['user_id' => $this->currentUser['user_id']];
        if ($type) {
            $conditions['type'] = $type;
        }
        
        $transactions = $this->db->select('credit_transactions', $conditions, 
            "ORDER BY created_at DESC LIMIT $limit OFFSET $offset");
        
        $total = $this->db->count('credit_transactions', $conditions);
        
        $formattedTransactions = array_map(function($txn) {
            return [
                'transaction_id' => $txn['transaction_id'],
                'type' => $txn['type'],
                'amount' => intval($txn['credits_amount']),
                'description' => $txn['description'],
                'reference_id' => $txn['reference_id'],
                'reference_type' => $txn['reference_type'],
                'created_at' => $txn['created_at']
            ];
        }, $transactions);
        
        $this->sendSuccess([
            'transactions' => $formattedTransactions,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]);
    }
    
    /**
     * Get user subscription
     */
    private function getSubscription() {
        $this->requireAuth();
        
        $subscription = $this->db->selectOne('user_subscriptions', [
            'user_id' => $this->currentUser['user_id'],
            'status' => 'active'
        ]);
        
        if (!$subscription) {
            $this->sendSuccess(['subscription' => null]);
            return;
        }
        
        $plan = $this->db->selectOne('subscription_plans', ['plan_id' => $subscription['plan_id']]);
        
        // Get billing history
        $billingHistory = $this->db->select('payment_transactions', [
            'user_id' => $this->currentUser['user_id'],
            'type' => 'subscription'
        ], 'ORDER BY created_at DESC LIMIT 5');
        
        $subscriptionData = [
            'subscription_id' => $subscription['subscription_id'],
            'plan' => [
                'plan_id' => $plan['plan_id'],
                'name' => $plan['name'],
                'description' => $plan['description'],
                'price_monthly' => floatval($plan['price_monthly']),
                'price_yearly' => floatval($plan['price_yearly']),
                'credits_monthly' => intval($plan['credits_monthly']),
                'features' => json_decode($plan['features'], true),
                'max_video_quality' => $plan['max_video_quality']
            ],
            'status' => $subscription['status'],
            'billing_cycle' => $subscription['billing_cycle'],
            'amount' => floatval($subscription['amount']),
            'current_period_start' => $subscription['current_period_start'],
            'current_period_end' => $subscription['current_period_end'],
            'trial_ends_at' => $subscription['trial_ends_at'],
            'cancelled_at' => $subscription['cancelled_at'],
            'billing_history' => array_map(function($payment) {
                return [
                    'transaction_id' => $payment['transaction_id'],
                    'amount' => floatval($payment['amount']),
                    'status' => $payment['status'],
                    'created_at' => $payment['created_at']
                ];
            }, $billingHistory)
        ];
        
        $this->sendSuccess(['subscription' => $subscriptionData]);
    }
    
    /**
     * Get video library
     */
    private function getVideoLibrary() {
        $this->requireAuth();
        
        $page = intval($this->requestData['page'] ?? 1);
        $limit = min(intval($this->requestData['limit'] ?? 20), 100);
        $favorites_only = $this->requestData['favorites_only'] === 'true';
        
        $offset = ($page - 1) * $limit;
        
        $query = "
            SELECT l.*, j.mood, j.topic, j.video_quality, j.video_length, 
                   j.created_at as generated_at, j.credits_consumed
            FROM user_video_library l
            JOIN generation_jobs j ON l.job_id = j.job_id
            WHERE l.user_id = ? AND j.status = 'completed'
        ";
        
        $params = [$this->currentUser['user_id']];
        
        if ($favorites_only) {
            $query .= " AND l.is_favorite = 1";
        }
        
        $query .= " ORDER BY l.created_at DESC LIMIT $limit OFFSET $offset";
        
        $videos = $this->db->query($query, $params);
        
        // Get total count
        $countQuery = "
            SELECT COUNT(*) as total
            FROM user_video_library l
            JOIN generation_jobs j ON l.job_id = j.job_id
            WHERE l.user_id = ? AND j.status = 'completed'
        ";
        
        if ($favorites_only) {
            $countQuery .= " AND l.is_favorite = 1";
        }
        
        $totalResult = $this->db->query($countQuery, [$this->currentUser['user_id']]);
        $total = $totalResult[0]['total'];
        
        $formattedVideos = array_map(function($video) {
            return [
                'library_id' => $video['library_id'],
                'job_id' => $video['job_id'],
                'title' => $video['title'],
                'description' => $video['description'],
                'mood' => $video['mood'],
                'topic' => $video['topic'],
                'video_quality' => $video['video_quality'],
                'video_length' => intval($video['video_length']),
                'credits_consumed' => intval($video['credits_consumed']),
                'is_favorite' => (bool)$video['is_favorite'],
                'is_public' => (bool)$video['is_public'],
                'download_count' => intval($video['download_count']),
                'tags' => json_decode($video['tags'] ?? '[]', true),
                'generated_at' => $video['generated_at'],
                'created_at' => $video['created_at']
            ];
        }, $videos);
        
        $this->sendSuccess([
            'videos' => $formattedVideos,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]);
    }
    
    /**
     * Toggle video favorite status
     */
    private function toggleFavorite() {
        $this->requireAuth();
        
        $this->validateRequired(['library_id']);
        
        $libraryId = $this->requestData['library_id'];
        
        $video = $this->db->selectOne('user_video_library', [
            'library_id' => $libraryId,
            'user_id' => $this->currentUser['user_id']
        ]);
        
        if (!$video) {
            $this->sendError('Video not found in your library', 404);
        }
        
        $newFavoriteStatus = !$video['is_favorite'];
        
        $this->db->update('user_video_library', [
            'is_favorite' => $newFavoriteStatus ? 1 : 0,
            'updated_at' => date('Y-m-d H:i:s')
        ], ['library_id' => $libraryId]);
        
        $this->sendSuccess([
            'library_id' => $libraryId,
            'is_favorite' => $newFavoriteStatus
        ], $newFavoriteStatus ? 'Added to favorites' : 'Removed from favorites');
    }
    
    /**
     * Get user statistics
     */
    private function getUserStats() {
        $this->requireAuth();
        
        $userId = $this->currentUser['user_id'];
        
        // Get generation stats
        $totalGenerations = $this->db->count('generation_jobs', ['user_id' => $userId]);
        $completedGenerations = $this->db->count('generation_jobs', [
            'user_id' => $userId,
            'status' => 'completed'
        ]);
        $failedGenerations = $this->db->count('generation_jobs', [
            'user_id' => $userId,
            'status' => 'failed'
        ]);
        
        // Get credit stats
        $credits = $this->db->selectOne('user_credit_balances', ['user_id' => $userId]);
        $totalSpent = $this->db->query(
            "SELECT SUM(ABS(credits_amount)) as total FROM credit_transactions 
             WHERE user_id = ? AND type = 'consumption'",
            [$userId]
        )[0]['total'] ?? 0;
        
        // Get recent activity
        $recentJobs = $this->db->select('generation_jobs', 
            ['user_id' => $userId], 
            'ORDER BY created_at DESC LIMIT 5'
        );
        
        // Get monthly stats
        $monthlyStats = $this->db->query(
            "SELECT DATE_FORMAT(created_at, '%Y-%m') as month, 
                    COUNT(*) as generations,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed
             FROM generation_jobs 
             WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
             GROUP BY DATE_FORMAT(created_at, '%Y-%m')
             ORDER BY month DESC",
            [$userId]
        );
        
        $this->sendSuccess([
            'generation_stats' => [
                'total' => intval($totalGenerations),
                'completed' => intval($completedGenerations),
                'failed' => intval($failedGenerations),
                'success_rate' => $totalGenerations > 0 ? round(($completedGenerations / $totalGenerations) * 100, 1) : 0
            ],
            'credit_stats' => [
                'total_earned' => intval($credits['total_credits'] ?? 0),
                'total_spent' => intval($totalSpent),
                'available' => intval($credits['available_credits'] ?? 0)
            ],
            'recent_activity' => array_map(function($job) {
                return [
                    'job_id' => $job['job_id'],
                    'mood' => $job['mood'],
                    'topic' => $job['topic'],
                    'status' => $job['status'],
                    'created_at' => $job['created_at']
                ];
            }, $recentJobs),
            'monthly_stats' => $monthlyStats
        ]);
    }
    
    /**
     * Get user settings
     */
    private function getSettings() {
        $this->requireAuth();
        
        $profile = $this->db->selectOne('user_profiles', ['user_id' => $this->currentUser['user_id']]);
        
        $settings = [
            'timezone' => $profile['timezone'] ?? 'UTC',
            'language' => $profile['language'] ?? 'en',
            'notification_preferences' => json_decode($profile['notification_preferences'] ?? '{}', true),
            'marketing_consent' => (bool)($profile['marketing_consent'] ?? false)
        ];
        
        $this->sendSuccess(['settings' => $settings]);
    }
    
    /**
     * Update user settings
     */
    private function updateSettings() {
        $this->requireAuth();
        
        $allowedSettings = ['timezone', 'language', 'notification_preferences', 'marketing_consent'];
        $updates = [];
        
        foreach ($allowedSettings as $setting) {
            if (isset($this->requestData[$setting])) {
                $value = $this->requestData[$setting];
                
                if ($setting === 'notification_preferences') {
                    $value = json_encode($value);
                } elseif ($setting === 'marketing_consent') {
                    $value = $value ? 1 : 0;
                }
                
                $updates[$setting] = $value;
            }
        }
        
        if (!empty($updates)) {
            $updates['updated_at'] = date('Y-m-d H:i:s');
            $this->db->update('user_profiles', $updates, ['user_id' => $this->currentUser['user_id']]);
        }
        
        $this->sendSuccess(null, 'Settings updated successfully');
    }
    
    /**
     * Upload avatar
     */
    private function uploadAvatar() {
        $this->requireAuth();
        
        if (!isset($_FILES['avatar'])) {
            $this->sendError('No avatar file provided', 400);
        }
        
        $file = $_FILES['avatar'];
        
        // Validate file
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $this->sendError('File upload failed', 400);
        }
        
        if ($file['size'] > 5 * 1024 * 1024) { // 5MB limit
            $this->sendError('File size must be less than 5MB', 400);
        }
        
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!in_array($file['type'], $allowedTypes)) {
            $this->sendError('Only JPEG, PNG, and GIF images are allowed', 400);
        }
        
        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'avatar_' . $this->currentUser['user_id'] . '_' . time() . '.' . $extension;
        $uploadPath = '../public/uploads/avatars/';
        
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }
        
        $fullPath = $uploadPath . $filename;
        
        if (move_uploaded_file($file['tmp_name'], $fullPath)) {
            $avatarUrl = '/uploads/avatars/' . $filename;
            
            // Update user avatar URL
            $this->db->update('users', [
                'avatar_url' => $avatarUrl,
                'updated_at' => date('Y-m-d H:i:s')
            ], ['user_id' => $this->currentUser['user_id']]);
            
            $this->sendSuccess(['avatar_url' => $avatarUrl], 'Avatar uploaded successfully');
        } else {
            $this->sendError('Failed to save avatar file', 500);
        }
    }
    
    /**
     * Delete user account
     */
    private function deleteAccount() {
        $this->requireAuth();
        
        $this->validateRequired(['password']);
        
        $password = $this->requestData['password'];
        
        // Verify password
        if (!$this->verifyPassword($password, $this->currentUser['password_hash'])) {
            $this->sendError('Incorrect password', 400);
        }
        
        try {
            $this->db->beginTransaction();
            
            // Mark user as deleted instead of actually deleting
            $this->db->update('users', [
                'status' => 'deleted',
                'email' => 'deleted_' . time() . '@deleted.com',
                'updated_at' => date('Y-m-d H:i:s')
            ], ['user_id' => $this->currentUser['user_id']]);
            
            // Clear session
            $this->clearSession();
            
            $this->db->commit();
            
            $this->sendSuccess(null, 'Account deleted successfully');
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Account deletion failed: " . $e->getMessage());
            $this->sendError('Failed to delete account', 500);
        }
    }
}
?>
