<?php
/**
 * Test the improved system with real content generation
 */

echo "🎬 TESTING IMPROVED SUTRADHAR ENGINE\n";
echo "===================================\n\n";

// Test the improved video engine
require_once 'core/working_video_engine.php';
require_once 'core/windows_tts_engine.php';

echo "1. Testing improved video generation...\n";

$videoEngine = new WorkingVideoEngine();
$ttsEngine = new WindowsTTSEngine();

// Test segments with elephant jungle theme
$testSegments = [
    ['text' => 'A magnificent elephant walks slowly through the lush green jungle'],
    ['text' => 'Birds chirp in the trees above as the gentle giant moves gracefully'],
    ['text' => 'The elephant stops to drink from a crystal clear stream'],
    ['text' => 'Sunlight filters through the dense canopy as the journey continues']
];

echo "Generating 30-second elephant jungle video...\n";

$videoFile = $videoEngine->generateWorkingVideo($testSegments, 'nature', 'cinematic', 'improved_test');

if ($videoFile && file_exists($videoFile)) {
    $videoSize = filesize($videoFile);
    echo "✅ Video generated: " . round($videoSize/1024, 1) . " KB\n";
    
    // Copy to public directory
    $publicVideo = 'public/improved_elephant_video.mp4';
    copy($videoFile, $publicVideo);
    echo "📁 Copied to: $publicVideo\n";
    
    // Get video info
    exec("ffprobe -v quiet -print_format json -show_format \"$publicVideo\" 2>&1", $probeOutput, $probeReturn);
    if ($probeReturn === 0) {
        $videoInfo = json_decode(implode('', $probeOutput), true);
        if ($videoInfo && isset($videoInfo['format']['duration'])) {
            echo "⏱️  Duration: " . round($videoInfo['format']['duration'], 1) . " seconds\n";
        }
    }
} else {
    echo "❌ Video generation failed\n";
}

echo "\n2. Testing improved audio generation...\n";

$fullText = implode(' ', array_column($testSegments, 'text'));
echo "Generating TTS for: " . substr($fullText, 0, 100) . "...\n";

$audioFile = 'public/improved_elephant_audio.wav';
$audioSuccess = $ttsEngine->generateWindowsSpeech($fullText, 'default', $audioFile);

if ($audioSuccess && file_exists($audioFile)) {
    $audioSize = filesize($audioFile);
    echo "✅ Audio generated: " . round($audioSize/1024, 1) . " KB\n";
    
    // Get audio info
    exec("ffprobe -v quiet -print_format json -show_format \"$audioFile\" 2>&1", $probeOutput, $probeReturn);
    if ($probeReturn === 0) {
        $audioInfo = json_decode(implode('', $probeOutput), true);
        if ($audioInfo && isset($audioInfo['format']['duration'])) {
            echo "⏱️  Duration: " . round($audioInfo['format']['duration'], 1) . " seconds\n";
        }
    }
} else {
    echo "❌ Audio generation failed\n";
}

echo "\n3. Testing full system integration...\n";

// Simulate a full generation request
$_POST = [
    'action' => 'generate',
    'flow_type' => 'reel',
    'style' => 'cinematic',
    'voice_pack' => 'default',
    'background' => 'nature',
    'content_source' => 'text',
    'content_text' => 'Create a stunning 30-second video of a majestic elephant walking through a beautiful jungle. Show the elephant moving gracefully between ancient trees, with birds flying overhead and sunlight filtering through the canopy. The elephant stops to drink from a crystal clear stream before continuing its peaceful journey through the wilderness.'
];

$_SERVER['REQUEST_METHOD'] = 'POST';

try {
    require_once 'generate.php';
    $generator = new SutradharGenerator();
    
    echo "Starting full system test...\n";
    $result = $generator->handleRequest();
    $resultData = json_decode($result, true);
    
    if ($resultData && $resultData['success']) {
        $jobId = $resultData['job_id'];
        echo "✅ Generation started: $jobId\n";
        
        // Monitor progress
        for ($i = 0; $i < 15; $i++) {
            sleep(2);
            $_GET['action'] = 'status';
            $_GET['job_id'] = $jobId;
            $_SERVER['REQUEST_METHOD'] = 'GET';
            
            $statusResult = $generator->handleRequest();
            $statusData = json_decode($statusResult, true);
            
            if ($statusData) {
                $progress = $statusData['progress'] ?? 0;
                $message = $statusData['message'] ?? 'Processing';
                echo "  Progress: {$progress}% - $message\n";
                
                if ($statusData['status'] === 'complete') {
                    echo "🎉 FULL SYSTEM TEST COMPLETED!\n";
                    
                    if (isset($statusData['output'])) {
                        echo "\nGenerated files:\n";
                        foreach ($statusData['output'] as $type => $fileInfo) {
                            if (isset($fileInfo['file']) && file_exists($fileInfo['file'])) {
                                $size = filesize($fileInfo['file']);
                                echo "  $type: " . basename($fileInfo['file']) . " (" . round($size/1024, 1) . " KB)\n";
                                
                                // Copy to easy access location
                                $easyPath = "public/final_test_$type." . pathinfo($fileInfo['file'], PATHINFO_EXTENSION);
                                copy($fileInfo['file'], $easyPath);
                                echo "    → Copied to: $easyPath\n";
                            }
                        }
                    }
                    break;
                } elseif ($statusData['status'] === 'error') {
                    echo "❌ Generation failed: " . ($statusData['error'] ?? 'Unknown error') . "\n";
                    break;
                }
            }
        }
    } else {
        echo "❌ Failed to start generation\n";
        if ($resultData && isset($resultData['error'])) {
            echo "Error: " . $resultData['error'] . "\n";
        }
    }
} catch (Exception $e) {
    echo "❌ System test error: " . $e->getMessage() . "\n";
}

echo "\n🎯 IMPROVED SYSTEM TEST SUMMARY\n";
echo "==============================\n";

$testFiles = [
    'public/improved_elephant_video.mp4' => 'Improved Video',
    'public/improved_elephant_audio.wav' => 'Improved Audio',
    'public/final_test_video.mp4' => 'Full System Video',
    'public/final_test_audio.mp3' => 'Full System Audio'
];

foreach ($testFiles as $file => $description) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo "✅ $description: " . round($size/1024, 1) . " KB\n";
    } else {
        echo "❌ $description: Not found\n";
    }
}

echo "\n🌐 Access the improved demos at:\n";
echo "- http://localhost:8000/real_content_demo.html\n";
echo "- http://localhost:8000/improved_demo.html (if created)\n";
echo "\n🎉 IMPROVED SYSTEM READY FOR USE!\n";
?>
