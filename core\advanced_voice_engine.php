<?php
/**
 * Advanced Voice Engine - Creates realistic speech patterns and unique audio
 * Part of Sutradhar Engine
 */

class AdvancedVoiceEngine {
    private $config;
    private $voicePacks;
    private $cacheDir;
    private $tempDir;

    public function __construct() {
        $this->loadConfig();
        $this->setupDirectories();
    }

    private function loadConfig() {
        $configPath = __DIR__ . '/../config/settings.json';
        $this->config = json_decode(file_get_contents($configPath), true);
        $this->voicePacks = $this->config['voice_packs'];
        $this->cacheDir = __DIR__ . '/../data/voice_cache/';
        $this->tempDir = __DIR__ . '/../temp/';
    }

    private function setupDirectories() {
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
        if (!is_dir($this->tempDir)) {
            mkdir($this->tempDir, 0755, true);
        }
    }

    /**
     * Generate advanced voice audio with realistic speech patterns
     */
    public function generateAdvancedVoice($segments, $voicePack, $jobId) {
        $audioFiles = [];
        $voiceConfig = $this->voicePacks[$voicePack] ?? $this->voicePacks['babu_rao'];

        foreach ($segments as $index => $segment) {
            $audioFile = $this->generateAdvancedSegmentAudio($segment, $voiceConfig, $jobId, $index);
            $audioFiles[] = $audioFile;
        }

        return $audioFiles;
    }

    /**
     * Generate advanced audio for a single segment with realistic speech patterns
     */
    private function generateAdvancedSegmentAudio($segment, $voiceConfig, $jobId, $index) {
        $text = $segment['text'];
        $voiceStyle = $segment['voice_style'] ?? 'normal';
        
        // Create cache key
        $cacheKey = md5($text . $voiceConfig['name'] . $voiceStyle . time()); // Add time for uniqueness
        $cacheFile = $this->cacheDir . $cacheKey . '.wav';

        // Generate new audio (always unique)
        $outputFile = $this->tempDir . $jobId . '_segment_' . $index . '.wav';
        
        // Analyze text for speech patterns
        $speechAnalysis = $this->analyzeTextForSpeech($text, $voiceStyle, $voiceConfig);
        
        // Generate realistic audio
        $success = $this->generateRealisticSpeech($speechAnalysis, $outputFile);
        
        if (!$success) {
            throw new Exception("Failed to generate audio for segment {$index}");
        }

        return [
            'file' => $outputFile,
            'duration' => $this->getAudioDuration($outputFile),
            'cached' => false,
            'analysis' => $speechAnalysis
        ];
    }

    /**
     * Analyze text for realistic speech generation
     */
    private function analyzeTextForSpeech($text, $voiceStyle, $voiceConfig) {
        $words = explode(' ', $text);
        $syllables = $this->countSyllables($text);
        $punctuation = $this->analyzePunctuation($text);
        $emotions = $this->detectEmotions($text);
        
        return [
            'text' => $text,
            'words' => $words,
            'word_count' => count($words),
            'syllable_count' => $syllables,
            'punctuation' => $punctuation,
            'emotions' => $emotions,
            'voice_style' => $voiceStyle,
            'voice_config' => $voiceConfig,
            'speech_rate' => $this->calculateSpeechRate($voiceStyle, $emotions),
            'pitch_variation' => $this->calculatePitchVariation($voiceConfig, $emotions),
            'pauses' => $this->calculatePauses($punctuation, $words),
            'emphasis' => $this->findEmphasisPoints($text, $emotions)
        ];
    }

    /**
     * Generate realistic speech audio
     */
    private function generateRealisticSpeech($analysis, $outputFile) {
        $sampleRate = $this->config['voice']['sample_rate'];
        $channels = 1;
        $bitsPerSample = 16;
        
        // Calculate total duration based on speech analysis
        $baseDuration = $analysis['word_count'] * 0.6; // 600ms per word base
        $speechRate = $analysis['speech_rate'];
        $totalDuration = $baseDuration / $speechRate;
        
        $numSamples = intval($totalDuration * $sampleRate);
        $dataSize = $numSamples * $channels * ($bitsPerSample / 8);
        
        // Create WAV header
        $header = $this->createWAVHeader($dataSize, $sampleRate, $channels, $bitsPerSample);
        
        // Generate speech audio data
        $audioData = $this->generateSpeechAudioData($analysis, $numSamples, $sampleRate);
        
        // Write the WAV file
        $wavContent = $header . $audioData;
        $result = file_put_contents($outputFile, $wavContent);
        
        return $result !== false && file_exists($outputFile);
    }

    /**
     * Generate speech audio data with realistic patterns
     */
    private function generateSpeechAudioData($analysis, $numSamples, $sampleRate) {
        $audioData = '';
        $words = $analysis['words'];
        $samplesPerWord = intval($numSamples / max(count($words), 1));
        
        $currentSample = 0;
        
        foreach ($words as $wordIndex => $word) {
            $wordLength = strlen($word);
            $syllableCount = $this->countWordSyllables($word);
            
            // Determine if this word should be emphasized
            $isEmphasized = in_array($wordIndex, $analysis['emphasis']);
            
            // Calculate word duration based on syllables and emphasis
            $wordDuration = $samplesPerWord;
            if ($isEmphasized) {
                $wordDuration *= 1.3; // Emphasized words are longer
            }
            
            // Generate audio for this word
            $wordAudio = $this->generateWordAudio(
                $word, 
                $wordDuration, 
                $sampleRate, 
                $analysis, 
                $isEmphasized,
                $wordIndex
            );
            
            $audioData .= $wordAudio;
            $currentSample += strlen($wordAudio) / 2;
            
            // Add pause between words
            if ($wordIndex < count($words) - 1) {
                $pauseDuration = $this->calculateWordPause($word, $words[$wordIndex + 1], $analysis);
                $pauseSamples = intval($pauseDuration * $sampleRate);
                
                // Generate silence or breathing sound
                $pauseAudio = $this->generatePause($pauseSamples, $sampleRate, $analysis);
                $audioData .= $pauseAudio;
                $currentSample += $pauseSamples;
            }
        }
        
        // Pad or trim to exact duration
        $targetBytes = $numSamples * 2;
        $currentBytes = strlen($audioData);
        
        if ($currentBytes < $targetBytes) {
            // Pad with silence
            $audioData .= str_repeat(pack('v', 0), ($targetBytes - $currentBytes) / 2);
        } else {
            // Trim to exact length
            $audioData = substr($audioData, 0, $targetBytes);
        }
        
        return $audioData;
    }

    /**
     * Generate audio for a single word
     */
    private function generateWordAudio($word, $duration, $sampleRate, $analysis, $isEmphasized, $wordIndex) {
        $audioData = '';
        $samples = intval($duration);
        
        // Base frequency for this voice
        $baseFreq = $this->getVoiceBaseFrequency($analysis['voice_config']);
        
        // Modify frequency based on word characteristics
        $wordFreq = $baseFreq + ($wordIndex % 3) * 20; // Slight variation per word
        
        // Add emotional modulation
        $emotionalMod = $this->getEmotionalFrequencyMod($analysis['emotions']);
        $wordFreq += $emotionalMod;
        
        // Generate formants (multiple frequencies for realistic speech)
        $formants = $this->calculateFormants($word, $wordFreq, $analysis);
        
        for ($i = 0; $i < $samples; $i++) {
            $time = $i / $sampleRate;
            $progress = $i / $samples;
            
            // Create complex waveform with multiple formants
            $sample = 0;
            foreach ($formants as $formant) {
                $freq = $formant['frequency'];
                $amplitude = $formant['amplitude'];
                
                // Add frequency modulation for natural speech
                $modulation = sin($time * 2 * pi() * 5) * 0.1; // 5Hz modulation
                $actualFreq = $freq * (1 + $modulation);
                
                $sample += sin($time * 2 * pi() * $actualFreq) * $amplitude;
            }
            
            // Apply envelope for natural word boundaries
            $envelope = $this->calculateWordEnvelope($progress, $isEmphasized);
            $sample *= $envelope;
            
            // Add some noise for realism
            $noise = (rand(-1000, 1000) / 10000) * 0.1;
            $sample += $noise;
            
            // Apply voice characteristics
            $sample = $this->applyVoiceCharacteristics($sample, $analysis['voice_config'], $time);
            
            // Convert to 16-bit integer
            $intSample = intval($sample * 16000); // Reduced amplitude for mixing
            $intSample = max(-32767, min(32767, $intSample));
            
            $audioData .= pack('v', $intSample);
        }
        
        return $audioData;
    }

    /**
     * Calculate formants for realistic speech
     */
    private function calculateFormants($word, $baseFreq, $analysis) {
        // Simplified formant calculation based on vowels
        $vowels = ['a', 'e', 'i', 'o', 'u'];
        $vowelCount = 0;
        
        foreach ($vowels as $vowel) {
            $vowelCount += substr_count(strtolower($word), $vowel);
        }
        
        $formants = [
            ['frequency' => $baseFreq, 'amplitude' => 0.6], // F0 (fundamental)
            ['frequency' => $baseFreq * 2.5, 'amplitude' => 0.3], // F1
            ['frequency' => $baseFreq * 4.2, 'amplitude' => 0.2], // F2
        ];
        
        // Modify based on vowel content
        if ($vowelCount > 0) {
            $formants[1]['frequency'] += $vowelCount * 50;
            $formants[2]['amplitude'] += $vowelCount * 0.1;
        }
        
        return $formants;
    }

    /**
     * Get voice base frequency
     */
    private function getVoiceBaseFrequency($voiceConfig) {
        $frequencies = [
            'babu_rao' => 180,
            'villain' => 120,
            'dadi' => 220,
            'gym_bro' => 160,
            'news_anchor' => 200
        ];
        
        return $frequencies[$voiceConfig['name']] ?? 180;
    }

    /**
     * Get emotional frequency modulation
     */
    private function getEmotionalFrequencyMod($emotions) {
        $modulation = 0;
        
        foreach ($emotions as $emotion) {
            switch ($emotion) {
                case 'excited':
                    $modulation += 30;
                    break;
                case 'sad':
                    $modulation -= 20;
                    break;
                case 'angry':
                    $modulation += 40;
                    break;
                case 'calm':
                    $modulation -= 10;
                    break;
            }
        }
        
        return $modulation;
    }

    /**
     * Calculate word envelope for natural boundaries
     */
    private function calculateWordEnvelope($progress, $isEmphasized) {
        $baseEnvelope = sin($progress * pi()); // Natural word shape
        
        if ($isEmphasized) {
            // Emphasized words have stronger attack and longer sustain
            if ($progress < 0.3) {
                return $progress / 0.3; // Quick attack
            } elseif ($progress < 0.7) {
                return 1.0; // Sustain
            } else {
                return (1.0 - $progress) / 0.3; // Decay
            }
        }
        
        return $baseEnvelope * 0.8; // Normal words are quieter
    }

    /**
     * Apply voice characteristics
     */
    private function applyVoiceCharacteristics($sample, $voiceConfig, $time) {
        switch ($voiceConfig['name']) {
            case 'villain':
                // Add some distortion for villain voice
                $sample *= (1 + sin($time * 100) * 0.1);
                break;
            case 'dadi':
                // Add slight tremolo for elderly voice
                $sample *= (1 + sin($time * 8) * 0.05);
                break;
            case 'gym_bro':
                // Add some roughness
                $sample *= (1 + sin($time * 50) * 0.08);
                break;
        }
        
        return $sample;
    }

    /**
     * Generate pause between words
     */
    private function generatePause($samples, $sampleRate, $analysis) {
        $audioData = '';
        
        for ($i = 0; $i < $samples; $i++) {
            // Generate very quiet breathing or silence
            $breathingNoise = (rand(-50, 50) / 10000) * 0.02;
            $sample = intval($breathingNoise * 1000);
            $audioData .= pack('v', $sample);
        }
        
        return $audioData;
    }

    /**
     * Count syllables in text
     */
    private function countSyllables($text) {
        $vowels = 'aeiouy';
        $syllableCount = 0;
        $words = explode(' ', strtolower($text));
        
        foreach ($words as $word) {
            $wordSyllables = $this->countWordSyllables($word);
            $syllableCount += $wordSyllables;
        }
        
        return max($syllableCount, 1);
    }

    /**
     * Count syllables in a word
     */
    private function countWordSyllables($word) {
        $word = strtolower(preg_replace('/[^a-z]/', '', $word));
        $vowels = 'aeiouy';
        $syllables = 0;
        $prevWasVowel = false;
        
        for ($i = 0; $i < strlen($word); $i++) {
            $isVowel = strpos($vowels, $word[$i]) !== false;
            if ($isVowel && !$prevWasVowel) {
                $syllables++;
            }
            $prevWasVowel = $isVowel;
        }
        
        // Handle silent e
        if (substr($word, -1) === 'e' && $syllables > 1) {
            $syllables--;
        }
        
        return max($syllables, 1);
    }

    /**
     * Analyze punctuation for pauses
     */
    private function analyzePunctuation($text) {
        return [
            'periods' => substr_count($text, '.'),
            'commas' => substr_count($text, ','),
            'exclamations' => substr_count($text, '!'),
            'questions' => substr_count($text, '?'),
            'semicolons' => substr_count($text, ';')
        ];
    }

    /**
     * Detect emotions in text
     */
    private function detectEmotions($text) {
        $emotions = [];
        $emotionKeywords = [
            'excited' => ['amazing', 'awesome', 'fantastic', 'incredible', 'wow'],
            'sad' => ['sad', 'sorry', 'unfortunate', 'tragic', 'disappointed'],
            'angry' => ['angry', 'furious', 'mad', 'annoyed', 'frustrated'],
            'calm' => ['peaceful', 'calm', 'serene', 'quiet', 'gentle'],
            'happy' => ['happy', 'joyful', 'cheerful', 'delighted', 'pleased']
        ];
        
        $lowerText = strtolower($text);
        
        foreach ($emotionKeywords as $emotion => $keywords) {
            foreach ($keywords as $keyword) {
                if (strpos($lowerText, $keyword) !== false) {
                    $emotions[] = $emotion;
                    break;
                }
            }
        }
        
        return array_unique($emotions);
    }

    /**
     * Calculate speech rate based on style and emotions
     */
    private function calculateSpeechRate($voiceStyle, $emotions) {
        $baseRate = 1.0;
        
        switch ($voiceStyle) {
            case 'energetic':
                $baseRate = 1.3;
                break;
            case 'slow':
                $baseRate = 0.7;
                break;
            case 'aggressive':
                $baseRate = 1.4;
                break;
            case 'storytelling':
                $baseRate = 0.9;
                break;
        }
        
        // Modify based on emotions
        foreach ($emotions as $emotion) {
            switch ($emotion) {
                case 'excited':
                    $baseRate *= 1.2;
                    break;
                case 'sad':
                    $baseRate *= 0.8;
                    break;
                case 'angry':
                    $baseRate *= 1.3;
                    break;
                case 'calm':
                    $baseRate *= 0.9;
                    break;
            }
        }
        
        return max(0.5, min(2.0, $baseRate)); // Clamp between 0.5x and 2.0x
    }

    /**
     * Calculate pitch variation
     */
    private function calculatePitchVariation($voiceConfig, $emotions) {
        $baseVariation = 0.2;
        
        // Voice-specific variations
        switch ($voiceConfig['name']) {
            case 'villain':
                $baseVariation = 0.4;
                break;
            case 'dadi':
                $baseVariation = 0.3;
                break;
            case 'gym_bro':
                $baseVariation = 0.5;
                break;
        }
        
        // Emotion-based modifications
        foreach ($emotions as $emotion) {
            switch ($emotion) {
                case 'excited':
                    $baseVariation += 0.2;
                    break;
                case 'angry':
                    $baseVariation += 0.3;
                    break;
                case 'calm':
                    $baseVariation -= 0.1;
                    break;
            }
        }
        
        return max(0.1, min(0.8, $baseVariation));
    }

    /**
     * Calculate pauses based on punctuation and words
     */
    private function calculatePauses($punctuation, $words) {
        $pauses = [];
        
        // Add pauses for punctuation
        $pauses['period'] = $punctuation['periods'] * 0.8;
        $pauses['comma'] = $punctuation['commas'] * 0.3;
        $pauses['exclamation'] = $punctuation['exclamations'] * 0.6;
        $pauses['question'] = $punctuation['questions'] * 0.7;
        
        return $pauses;
    }

    /**
     * Find emphasis points in text
     */
    private function findEmphasisPoints($text, $emotions) {
        $words = explode(' ', $text);
        $emphasisPoints = [];
        
        // Emphasize words with exclamation marks
        foreach ($words as $index => $word) {
            if (strpos($word, '!') !== false) {
                $emphasisPoints[] = $index;
            }
        }
        
        // Emphasize emotional words
        $emotionalWords = ['amazing', 'incredible', 'terrible', 'fantastic', 'awful'];
        foreach ($words as $index => $word) {
            $cleanWord = strtolower(preg_replace('/[^a-z]/', '', $word));
            if (in_array($cleanWord, $emotionalWords)) {
                $emphasisPoints[] = $index;
            }
        }
        
        return array_unique($emphasisPoints);
    }

    /**
     * Calculate pause duration between words
     */
    private function calculateWordPause($currentWord, $nextWord, $analysis) {
        $basePause = 0.1; // 100ms base pause
        
        // Longer pause after punctuation
        if (strpos($currentWord, '.') !== false) {
            $basePause = 0.8;
        } elseif (strpos($currentWord, ',') !== false) {
            $basePause = 0.3;
        } elseif (strpos($currentWord, '!') !== false || strpos($currentWord, '?') !== false) {
            $basePause = 0.6;
        }
        
        return $basePause;
    }

    /**
     * Create WAV header
     */
    private function createWAVHeader($dataSize, $sampleRate, $channels, $bitsPerSample) {
        $header = pack('V', 0x46464952); // "RIFF"
        $header .= pack('V', $dataSize + 36); // File size - 8
        $header .= pack('V', 0x45564157); // "WAVE"
        $header .= pack('V', 0x20746d66); // "fmt "
        $header .= pack('V', 16); // Subchunk1Size
        $header .= pack('v', 1); // AudioFormat (PCM)
        $header .= pack('v', $channels); // NumChannels
        $header .= pack('V', $sampleRate); // SampleRate
        $header .= pack('V', $sampleRate * $channels * ($bitsPerSample / 8)); // ByteRate
        $header .= pack('v', $channels * ($bitsPerSample / 8)); // BlockAlign
        $header .= pack('v', $bitsPerSample); // BitsPerSample
        $header .= pack('V', 0x61746164); // "data"
        $header .= pack('V', $dataSize); // Subchunk2Size
        
        return $header;
    }

    /**
     * Get audio file duration
     */
    private function getAudioDuration($audioFile) {
        if (!file_exists($audioFile)) {
            return 0;
        }

        // Simple duration calculation based on file size and format
        $fileSize = filesize($audioFile);
        $sampleRate = $this->config['voice']['sample_rate'];
        $channels = 1;
        $bitsPerSample = 16;
        
        $dataSize = $fileSize - 44; // Subtract WAV header size
        $bytesPerSecond = $sampleRate * $channels * ($bitsPerSample / 8);
        
        return $dataSize / $bytesPerSecond;
    }

    /**
     * Clean up temporary files
     */
    public function cleanup($jobId) {
        $pattern = $this->tempDir . $jobId . '_*';
        $files = glob($pattern);
        
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
    }
}
