<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sutradhar Engine - API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🎭 Sutradhar Engine - API Test</h1>
    
    <div id="results"></div>
    
    <button onclick="runTests()">Run API Tests</button>
    
    <script>
        async function runTests() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="info">Running tests...</div>';
            
            const tests = [
                {
                    name: 'Load Templates',
                    url: 'api.php?action=list_templates',
                    method: 'GET'
                },
                {
                    name: 'Get Specific Template',
                    url: 'api.php?action=get_template&id=panchtantra_wisdom',
                    method: 'GET'
                }
            ];
            
            let results = [];
            
            for (const test of tests) {
                try {
                    console.log(`Running test: ${test.name}`);
                    const response = await fetch(test.url, { method: test.method });
                    
                    const responseText = await response.text();
                    console.log(`Response for ${test.name}:`, responseText);
                    
                    let data;
                    try {
                        data = JSON.parse(responseText);
                    } catch (parseError) {
                        throw new Error(`Invalid JSON response: ${responseText.substring(0, 200)}...`);
                    }
                    
                    results.push({
                        name: test.name,
                        status: 'success',
                        data: data,
                        response: response
                    });
                } catch (error) {
                    console.error(`Test failed: ${test.name}`, error);
                    results.push({
                        name: test.name,
                        status: 'error',
                        error: error.message
                    });
                }
            }
            
            // Display results
            let html = '';
            results.forEach(result => {
                if (result.status === 'success') {
                    html += `
                        <div class="test-result success">
                            <h3>✅ ${result.name}</h3>
                            <p>Status: ${result.response.status} ${result.response.statusText}</p>
                            <pre>${JSON.stringify(result.data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    html += `
                        <div class="test-result error">
                            <h3>❌ ${result.name}</h3>
                            <p>Error: ${result.error}</p>
                        </div>
                    `;
                }
            });
            
            resultsDiv.innerHTML = html;
        }
        
        // Auto-run tests on page load
        document.addEventListener('DOMContentLoaded', () => {
            runTests();
        });
    </script>
</body>
</html>
