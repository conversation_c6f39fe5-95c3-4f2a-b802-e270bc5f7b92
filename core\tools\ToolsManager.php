<?php
/**
 * Tools Manager
 * Central coordinator for all tools in the Sutradhar platform
 */

require_once 'BaseTool.php';
require_once __DIR__ . '/../tools_database_manager.php';
require_once __DIR__ . '/../credit_manager.php';

class ToolsManager {
    private $tools = [];
    private $db;
    private $creditManager;
    
    public function __construct() {
        $this->db = new ToolsDatabaseManager();
        $this->creditManager = new CreditManager();
        $this->loadTools();
    }
    
    /**
     * Execute a tool operation
     */
    public function executeTool($toolId, $files, $options = [], $userId = null) {
        if (!$userId) {
            throw new ToolException('User authentication required');
        }
        
        if (!isset($this->tools[$toolId])) {
            throw new ToolException("Tool not found: $toolId");
        }
        
        $tool = $this->tools[$toolId];
        $operationId = $this->generateOperationId();
        
        // Check rate limits
        $rateLimitCheck = $this->db->checkRateLimit($userId, 'tool_usage', $toolId);
        if (!$rateLimitCheck['allowed']) {
            throw new RateLimitException(
                "Rate limit exceeded for tool '$toolId'. " .
                "Limit: {$rateLimitCheck['limit']} per {$rateLimitCheck['window']}. " .
                "Try again after " . date('Y-m-d H:i:s', $rateLimitCheck['reset_time'])
            );
        }
        
        // Check user subscription requirements
        $this->checkSubscriptionRequirements($userId, $tool);
        
        // Calculate credit cost
        $creditCost = $this->calculateCreditCost($tool, $files, $options);
        
        // Check and deduct credits
        if (!$this->creditManager->hasCredits($userId, $creditCost)) {
            throw new InsufficientCreditsException(
                "Insufficient credits. Required: $creditCost, Available: " . 
                $this->creditManager->getCreditBalance($userId)
            );
        }
        
        // Record tool usage in database
        $this->db->recordToolUsage($userId, $toolId, $operationId, [
            'files_processed' => count($files),
            'credits_used' => $creditCost,
            'file_size_input' => $this->calculateTotalFileSize($files),
            'tool_options' => $options
        ]);
        
        // Record rate limit usage
        $this->db->recordRateLimit($userId, 'tool_usage', $toolId);
        
        $startTime = microtime(true);
        
        try {
            // Deduct credits
            $this->creditManager->processToolCredits($userId, $toolId, $operationId, [
                'file_count' => count($files),
                'complexity_multiplier' => $this->getComplexityMultiplier($options)
            ]);
            
            // Process files with the tool
            $result = $tool->process($files, $options, $userId, $operationId);
            
            // Update processing time and status
            $processingTime = microtime(true) - $startTime;
            $this->db->updateToolUsage($operationId, 'completed', [
                'processing_time' => round($processingTime, 3),
                'file_size_output' => $this->calculateOutputFileSize($result)
            ]);
            
            // Return result with operation info
            return [
                'operation_id' => $operationId,
                'tool_id' => $toolId,
                'status' => 'completed',
                'credits_used' => $creditCost,
                'processing_time' => round($processingTime, 3),
                'files_processed' => count($files),
                'result' => $result
            ];
            
        } catch (Exception $e) {
            // Update status to failed
            $this->db->updateToolUsage($operationId, 'failed', [
                'error_message' => $e->getMessage(),
                'processing_time' => round(microtime(true) - $startTime, 3)
            ]);
            
            // Refund credits on failure
            $this->creditManager->addCredits($userId, $creditCost, 'tool_refund', $operationId);
            
            throw $e;
        }
    }
    
    /**
     * Get available tools
     */
    public function getAvailableTools($userId = null) {
        $toolsConfig = $this->db->getToolsByCategory();
        
        // Add usage statistics if user is provided
        if ($userId) {
            $userStats = $this->db->getUserToolStats($userId);
            $favorites = $this->db->getUserFavoriteTools($userId);
            
            $statsMap = [];
            foreach ($userStats as $stat) {
                $statsMap[$stat['tool_name']] = $stat;
            }
            
            $favoritesMap = [];
            foreach ($favorites as $fav) {
                $favoritesMap[$fav['tool_id']] = true;
            }
            
            // Enhance tools with user data
            foreach ($toolsConfig as &$category) {
                foreach ($category['tools'] as &$tool) {
                    $tool['user_stats'] = $statsMap[$tool['tool_id']] ?? null;
                    $tool['is_favorite'] = isset($favoritesMap[$tool['tool_id']]);
                }
            }
        }
        
        return $toolsConfig;
    }
    
    /**
     * Get tool information
     */
    public function getToolInfo($toolId) {
        if (!isset($this->tools[$toolId])) {
            throw new ToolException("Tool not found: $toolId");
        }
        
        return $this->tools[$toolId]->getInfo();
    }
    
    /**
     * Get operation status
     */
    public function getOperationStatus($operationId, $userId = null) {
        $conditions = ['operation_id' => $operationId];
        if ($userId) {
            $conditions['user_id'] = $userId;
        }
        
        $operation = $this->db->selectOne('tool_usage', $conditions);
        if (!$operation) {
            throw new ToolException("Operation not found: $operationId");
        }
        
        // Get associated files
        $files = $this->db->getOperationFiles($operationId, $userId);
        
        return [
            'operation_id' => $operationId,
            'tool_name' => $operation['tool_name'],
            'status' => $operation['status'],
            'progress' => $operation['status'] === 'completed' ? 100 : 
                         ($operation['status'] === 'processing' ? 50 : 0),
            'credits_used' => (int)$operation['credits_used'],
            'processing_time' => (float)$operation['processing_time'],
            'files_processed' => (int)$operation['files_processed'],
            'created_at' => $operation['created_at'],
            'completed_at' => $operation['completed_at'],
            'error_message' => $operation['error_message'],
            'files' => $files
        ];
    }
    
    /**
     * Get user's tool usage statistics
     */
    public function getUserStats($userId, $period = '30 days') {
        return $this->db->getUserToolStats($userId, $period);
    }
    
    /**
     * Add tool to user's favorites
     */
    public function addToFavorites($userId, $toolId) {
        if (!isset($this->tools[$toolId])) {
            throw new ToolException("Tool not found: $toolId");
        }
        
        return $this->db->addToolToFavorites($userId, $toolId);
    }
    
    /**
     * Remove tool from user's favorites
     */
    public function removeFromFavorites($userId, $toolId) {
        return $this->db->removeToolFromFavorites($userId, $toolId);
    }
    
    /**
     * Get user's favorite tools
     */
    public function getUserFavorites($userId) {
        return $this->db->getUserFavoriteTools($userId);
    }
    
    /**
     * Get tool analytics
     */
    public function getToolAnalytics($period = '30 days') {
        return $this->db->getToolAnalytics($period);
    }
    
    /**
     * Clean up expired files
     */
    public function cleanupExpiredFiles() {
        return $this->db->cleanupExpiredFiles();
    }
    
    /**
     * Download processed file
     */
    public function downloadFile($fileId, $userId = null) {
        $conditions = ['id' => $fileId];
        if ($userId) {
            $conditions['user_id'] = $userId;
        }
        
        $file = $this->db->selectOne('tool_files', $conditions);
        if (!$file) {
            throw new ToolException("File not found");
        }
        
        if (!file_exists($file['file_path'])) {
            throw new ToolException("File no longer available");
        }
        
        // Update download count
        $this->db->incrementDownloadCount($fileId);
        
        return [
            'file_path' => $file['file_path'],
            'original_name' => $file['original_name'],
            'mime_type' => $file['mime_type'],
            'file_size' => $file['file_size']
        ];
    }
    
    // Private helper methods
    
    /**
     * Load all available tools
     */
    private function loadTools() {
        // Load tool classes
        $toolClasses = [
            // Image Processing Tools
            'format_converter' => 'ImageProcessor\\FormatConverterTool',
            'background_remover' => 'ImageProcessor\\BackgroundRemoverTool',
            'smart_resizer' => 'ImageProcessor\\SmartResizerTool',
            'image_compressor' => 'ImageProcessor\\ImageCompressorTool',
            'filter_studio' => 'ImageProcessor\\FilterStudioTool',
            'color_extractor' => 'ImageProcessor\\ColorExtractorTool',
            'watermark_tool' => 'ImageProcessor\\WatermarkTool',
            'metadata_manager' => 'ImageProcessor\\MetadataManagerTool',
            
            // Generator Tools
            'qr_generator' => 'GeneratorTools\\QRGeneratorTool',
            'barcode_generator' => 'GeneratorTools\\BarcodeGeneratorTool',
            'text_to_image' => 'GeneratorTools\\TextToImageTool',
            'thumbnail_factory' => 'GeneratorTools\\ThumbnailFactoryTool',
            'collage_maker' => 'GeneratorTools\\CollageMakerTool',
            'icon_generator' => 'GeneratorTools\\IconGeneratorTool',
            'meme_generator' => 'GeneratorTools\\MemeGeneratorTool',
            
            // Utility Tools
            'color_converter' => 'UtilityTools\\ColorConverterTool',
            'image_analyzer' => 'UtilityTools\\ImageAnalyzerTool',
            'duplicate_finder' => 'UtilityTools\\DuplicateFinderTool',
            'image_organizer' => 'UtilityTools\\ImageOrganizerTool',
            'batch_renamer' => 'UtilityTools\\BatchRenamerTool',
            'image_comparison' => 'UtilityTools\\ImageComparisonTool',
            
            // Advanced Tools
            'image_upscaler' => 'AdvancedTools\\ImageUpscalerTool',
            'noise_reducer' => 'AdvancedTools\\NoiseReducerTool',
            'perspective_corrector' => 'AdvancedTools\\PerspectiveCorrectorTool',
            'image_stitcher' => 'AdvancedTools\\ImageStitcherTool'
        ];
        
        foreach ($toolClasses as $toolId => $className) {
            try {
                $classFile = __DIR__ . '/' . str_replace('\\', '/', $className) . '.php';
                if (file_exists($classFile)) {
                    require_once $classFile;
                    $this->tools[$toolId] = new $className();
                }
            } catch (Exception $e) {
                // Log error but continue loading other tools
                error_log("Failed to load tool $toolId: " . $e->getMessage());
            }
        }
    }
    
    /**
     * Check subscription requirements
     */
    private function checkSubscriptionRequirements($userId, $tool) {
        $toolInfo = $tool->getInfo();
        $requiredSubscription = $toolInfo['requires_subscription'];
        
        if ($requiredSubscription === 'none') {
            return true;
        }
        
        $user = $this->db->selectOne('users', ['id' => $userId]);
        $userSubscription = $user['subscription_type'] ?? 'free';
        
        $subscriptionLevels = ['free' => 0, 'pro' => 1, 'business' => 2, 'enterprise' => 3];
        $userLevel = $subscriptionLevels[$userSubscription] ?? 0;
        $requiredLevel = $subscriptionLevels[$requiredSubscription] ?? 0;
        
        if ($userLevel < $requiredLevel) {
            throw new SubscriptionRequiredException(
                "This tool requires a $requiredSubscription subscription or higher"
            );
        }
        
        return true;
    }
    
    /**
     * Calculate credit cost for operation
     */
    private function calculateCreditCost($tool, $files, $options) {
        $toolInfo = $tool->getInfo();
        $baseCost = $toolInfo['credit_cost'];
        
        // Apply file count multiplier for batch operations
        $fileCount = count($files);
        if ($fileCount > 1) {
            $batchMultiplier = min($fileCount, 10); // Cap at 10x
            $baseCost *= $batchMultiplier;
        }
        
        // Apply complexity multiplier based on options
        $complexityMultiplier = $this->getComplexityMultiplier($options);
        $baseCost = (int)($baseCost * $complexityMultiplier);
        
        return max(1, $baseCost); // Minimum 1 credit
    }
    
    /**
     * Get complexity multiplier based on options
     */
    private function getComplexityMultiplier($options) {
        $multiplier = 1.0;
        
        // High quality operations cost more
        if (isset($options['quality']) && $options['quality'] > 90) {
            $multiplier *= 1.5;
        }
        
        // Large output sizes cost more
        if (isset($options['output_width']) && $options['output_width'] > 2048) {
            $multiplier *= 1.3;
        }
        
        // Complex filters cost more
        if (isset($options['filters']) && count($options['filters']) > 3) {
            $multiplier *= 1.2;
        }
        
        return $multiplier;
    }
    
    /**
     * Calculate total file size
     */
    private function calculateTotalFileSize($files) {
        $totalSize = 0;
        foreach ($files as $file) {
            $totalSize += $file['size'];
        }
        return $totalSize;
    }
    
    /**
     * Calculate output file size from result
     */
    private function calculateOutputFileSize($result) {
        $totalSize = 0;
        
        if (isset($result['processed_files'])) {
            foreach ($result['processed_files'] as $file) {
                if (isset($file['file_size'])) {
                    $totalSize += $file['file_size'];
                }
            }
        }
        
        return $totalSize;
    }
    
    /**
     * Generate unique operation ID
     */
    private function generateOperationId() {
        return 'op_' . uniqid() . '_' . time();
    }
}

/**
 * Custom exceptions
 */
class RateLimitException extends Exception {}
class SubscriptionRequiredException extends Exception {}
?>
