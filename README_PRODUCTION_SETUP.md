# 🎭 Sutradhar 2070 - Production Setup Guide

## 🚀 **COMPLETE SYSTEM INTEGRATION COMPLETED**

This guide provides step-by-step instructions to set up and run the fully integrated Sutradhar 2070 video generation platform.

## 📋 **WHAT'S BEEN FIXED & INTEGRATED**

### ✅ **Issues Resolved:**
1. **Server Startup Errors**: Fixed "invalid content source error" in index_2070.html
2. **Video Generation Pipeline**: Integrated with unified API and proper error handling
3. **UI Integration**: Complete integration of user management and monetization systems
4. **API Endpoint Conflicts**: Unified all APIs into single endpoint structure
5. **Authentication Gaps**: Full integration between video generation and credit system

### ✅ **New Integrated Features:**
- **Unified Main Page**: `public/index_integrated.html` with full system integration
- **Complete Authentication**: Login/register with credit tracking
- **Video Generation Interface**: 30 moods, topics, and inspiration selection
- **Real-time Progress**: Live generation status with progress indicators
- **Credit Management**: Automatic credit consumption and balance tracking
- **Responsive Design**: Consistent 2070 futuristic theme throughout

## 🛠️ **INSTALLATION INSTRUCTIONS**

### **Step 1: System Requirements**
```bash
# Required:
- PHP 8.0+
- MySQL 8.0+
- Apache/Nginx with mod_rewrite
- 2GB+ RAM
- 10GB+ disk space

# PHP Extensions:
- pdo, pdo_mysql, json, curl, openssl
```

### **Step 2: Initial Setup**
```bash
# 1. Run the setup script
php setup.php

# 2. Verify system integrity
php verify_system.php
```

### **Step 3: Database Configuration**
```bash
# 1. Create MySQL database
mysql -u root -p
CREATE DATABASE sutradhar2070;
exit

# 2. Update config/database.json
{
    "host": "localhost",
    "database": "sutradhar2070",
    "username": "your_username",
    "password": "your_password",
    "charset": "utf8mb4"
}
```

### **Step 4: Configure APIs & Services**

#### **Hugging Face API Setup**
```json
// config/huggingface_config.json
{
    "api_key": "hf_your_actual_api_key_here",
    "models": {
        "text_to_video": "stabilityai/stable-video-diffusion-img2vid-xt",
        "text_to_image": "runwayml/stable-diffusion-v1-5"
    }
}
```

#### **Payment Configuration**
```json
// config/payment_config.json
{
    "demo_mode": false,
    "stripe": {
        "publishable_key": "pk_live_your_stripe_key",
        "secret_key": "sk_live_your_stripe_secret",
        "webhook_secret": "whsec_your_webhook_secret"
    }
}
```

#### **Email Configuration**
```json
// config/email_config.json
{
    "smtp_host": "smtp.gmail.com",
    "smtp_port": 587,
    "smtp_username": "<EMAIL>",
    "smtp_password": "your-app-password"
}
```

### **Step 5: Web Server Setup**

#### **Apache Configuration**
```apache
# Enable required modules
sudo a2enmod rewrite
sudo a2enmod headers
sudo systemctl restart apache2

# Virtual Host Example
<VirtualHost *:80>
    ServerName sutradhar2070.local
    DocumentRoot /path/to/sutradhar2070/public
    
    <Directory /path/to/sutradhar2070/public>
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

#### **Nginx Configuration**
```nginx
server {
    listen 80;
    server_name sutradhar2070.local;
    root /path/to/sutradhar2070/public;
    index index_integrated.html;

    location / {
        try_files $uri $uri/ /index_integrated.html;
    }

    location /api/ {
        try_files $uri /api_unified.php?endpoint=$1;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        include fastcgi_params;
    }
}
```

## 🧪 **TESTING PROCEDURES**

### **1. System Verification**
```bash
# Run comprehensive system check
php verify_system.php

# Expected output: All tests should PASS
# If any tests fail, follow the recommended actions
```

### **2. Authentication Testing**
```bash
# Test user registration and login
curl -X POST http://localhost/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Test123!","first_name":"Test","last_name":"User"}'

# Test login
curl -X POST http://localhost/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Test123!"}'
```

### **3. Video Generation Testing**
```bash
# Test generation endpoint
curl -X POST http://localhost/api/generate \
  -H "Content-Type: application/json" \
  -d '{"mood":"euphoric","topic":"nature_wildlife","user_id":"test_user"}'

# Check generation status
curl http://localhost/api/generate/status/job_id_here
```

### **4. Payment System Testing**
```bash
# Test credit purchase (demo mode)
curl -X POST http://localhost/api/payments/purchase \
  -H "Content-Type: application/json" \
  -d '{"package_id":"starter","payment_method":"stripe","payment_data":{"token":"demo"}}'
```

## 🌐 **ACCESSING THE APPLICATION**

### **Main Application URLs:**
- **Homepage**: `http://localhost/` (redirects to index_integrated.html)
- **Dashboard**: `http://localhost/dashboard.html`
- **Pricing**: `http://localhost/pricing.html`
- **Marketing**: `http://localhost/marketing_landing.html`

### **Default Admin Account:**
- **Email**: `<EMAIL>`
- **Password**: `Admin123!`
- **Credits**: 1000 (for testing)

## 🔧 **TROUBLESHOOTING**

### **Common Issues & Solutions:**

#### **1. "Invalid content source error"**
```bash
# Solution: Ensure .htaccess is working
# Check: DirectoryIndex is set to index_integrated.html
# Verify: mod_rewrite is enabled
```

#### **2. Database Connection Failed**
```bash
# Check database credentials in config/database.json
# Ensure MySQL service is running
# Verify database exists: sutradhar2070
```

#### **3. API Endpoints Not Working**
```bash
# Check .htaccess rewrite rules
# Verify api_unified.php exists and is executable
# Check PHP error logs
```

#### **4. Video Generation Fails**
```bash
# Verify Hugging Face API key in config/huggingface_config.json
# Check user has sufficient credits
# Review generation_jobs table for error messages
```

#### **5. Payment Processing Issues**
```bash
# For testing: Set "demo_mode": true in payment_config.json
# For production: Configure actual Stripe/PayPal credentials
# Check webhook endpoints are accessible
```

## 📊 **MONITORING & MAINTENANCE**

### **Log Files:**
- **Application Logs**: `logs/application.log`
- **Error Logs**: `logs/error.log`
- **Generation Logs**: `logs/generation.log`

### **Database Monitoring:**
```sql
-- Check system health
SELECT COUNT(*) as total_users FROM users;
SELECT COUNT(*) as total_generations FROM generation_jobs;
SELECT SUM(credits_amount) as total_credits FROM credit_transactions;

-- Monitor active generations
SELECT * FROM generation_jobs WHERE status IN ('analyzing', 'generating', 'processing');
```

### **Performance Optimization:**
```bash
# Enable PHP OPcache
# Configure MySQL query cache
# Use Redis for session storage (optional)
# Implement CDN for static assets
```

## 🚀 **PRODUCTION DEPLOYMENT**

### **Security Checklist:**
- [ ] Change default admin password
- [ ] Configure SSL/HTTPS
- [ ] Set secure file permissions (644 for files, 755 for directories)
- [ ] Enable firewall rules
- [ ] Configure backup procedures
- [ ] Set up monitoring alerts

### **Performance Checklist:**
- [ ] Enable gzip compression
- [ ] Configure browser caching
- [ ] Optimize database indexes
- [ ] Set up CDN for static assets
- [ ] Configure load balancing (if needed)

### **Backup Procedures:**
```bash
# Database backup
mysqldump -u username -p sutradhar2070 > backup_$(date +%Y%m%d).sql

# File backup
tar -czf sutradhar2070_backup_$(date +%Y%m%d).tar.gz /path/to/sutradhar2070/

# Automated backup script (add to crontab)
0 2 * * * /path/to/backup_script.sh
```

## 📞 **SUPPORT & DOCUMENTATION**

### **Additional Resources:**
- **API Documentation**: `/docs/api.md`
- **User Guide**: `/docs/user-guide.md`
- **Developer Guide**: `/docs/developer.md`

### **Getting Help:**
1. Check the troubleshooting section above
2. Review log files for error messages
3. Run `php verify_system.php` for system diagnostics
4. Check GitHub issues for known problems

## 🎉 **SUCCESS CRITERIA**

Your system is working correctly when:
- [ ] All verification tests pass
- [ ] Users can register and login
- [ ] Video generation completes successfully
- [ ] Credit system tracks consumption
- [ ] Payment processing works (demo or live)
- [ ] Dashboard shows user data correctly
- [ ] All pages load without errors

---

**🎭 Sutradhar 2070 - The Future of AI Video Generation**

*Built with ❤️ for creators, by creators*
