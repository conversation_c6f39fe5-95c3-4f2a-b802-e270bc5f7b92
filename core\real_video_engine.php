<?php
/**
 * Real Video Engine - Actually generates video frames and animations
 * This replaces placeholder videos with real visual content
 */

class RealVideoEngine {
    private $config;
    private $tempDir;
    private $assetsDir;

    public function __construct() {
        $this->loadConfig();
        $this->tempDir = __DIR__ . '/../temp/';
        $this->assetsDir = __DIR__ . '/../assets/';
        
        if (!is_dir($this->tempDir)) {
            mkdir($this->tempDir, 0755, true);
        }
    }

    private function loadConfig() {
        $configPath = __DIR__ . '/../config/settings.json';
        $this->config = json_decode(file_get_contents($configPath), true);
    }

    /**
     * Generate real video with frames, animations, and effects
     */
    public function generateRealVideo($segments, $background, $style, $jobId) {
        echo "🎬 Generating real video with frames...\n";
        
        $outputFile = $this->tempDir . $jobId . '_video.mp4';
        
        // Try multiple video generation methods
        $methods = [
            'ffmpeg_slideshow',
            'python_moviepy',
            'imagemagick_animation',
            'manual_frame_generation'
        ];
        
        foreach ($methods as $method) {
            try {
                echo "Trying $method...\n";
                $success = $this->callVideoMethod($method, $segments, $background, $style, $outputFile);
                if ($success && file_exists($outputFile) && filesize($outputFile) > 10000) {
                    echo "✅ Success with $method! Generated " . round(filesize($outputFile)/1024, 1) . " KB\n";
                    return $outputFile;
                }
            } catch (Exception $e) {
                echo "❌ $method failed: " . $e->getMessage() . "\n";
                continue;
            }
        }
        
        echo "⚠️ All video methods failed, generating enhanced placeholder\n";
        return $this->generateEnhancedPlaceholder($segments, $background, $style, $outputFile);
    }

    /**
     * Call specific video generation method
     */
    private function callVideoMethod($method, $segments, $background, $style, $outputFile) {
        switch ($method) {
            case 'ffmpeg_slideshow':
                return $this->ffmpegSlideshow($segments, $background, $style, $outputFile);
            case 'python_moviepy':
                return $this->pythonMoviePy($segments, $background, $style, $outputFile);
            case 'imagemagick_animation':
                return $this->imageMagickAnimation($segments, $background, $style, $outputFile);
            case 'manual_frame_generation':
                return $this->manualFrameGeneration($segments, $background, $style, $outputFile);
            default:
                return false;
        }
    }

    /**
     * FFmpeg slideshow with text overlays
     */
    private function ffmpegSlideshow($segments, $background, $style, $outputFile) {
        // Check if ffmpeg is available
        exec('ffmpeg -version 2>&1', $output, $returnCode);
        if ($returnCode !== 0) {
            throw new Exception("FFmpeg not installed");
        }

        // Generate background images for each segment
        $imageFiles = [];
        foreach ($segments as $index => $segment) {
            $imageFile = $this->generateSegmentImage($segment, $background, $style, $index);
            if ($imageFile) {
                $imageFiles[] = $imageFile;
            }
        }

        if (empty($imageFiles)) {
            throw new Exception("No images generated");
        }

        // Create video from images
        $duration = count($segments) * 3; // 3 seconds per segment
        $frameRate = 30;
        
        // Create image list file
        $listFile = $this->tempDir . 'image_list.txt';
        $listContent = '';
        foreach ($imageFiles as $imageFile) {
            $listContent .= "file '" . realpath($imageFile) . "'\n";
            $listContent .= "duration 3\n";
        }
        file_put_contents($listFile, $listContent);

        // Generate video with ffmpeg
        $cmd = "ffmpeg -f concat -safe 0 -i \"$listFile\" -vf \"scale=720:1280:force_original_aspect_ratio=decrease,pad=720:1280:(ow-iw)/2:(oh-ih)/2\" -c:v libx264 -r $frameRate -pix_fmt yuv420p -t $duration \"$outputFile\" -y 2>&1";
        
        exec($cmd, $output, $returnCode);
        
        // Clean up
        unlink($listFile);
        foreach ($imageFiles as $imageFile) {
            if (file_exists($imageFile)) {
                unlink($imageFile);
            }
        }

        return $returnCode === 0 && file_exists($outputFile);
    }

    /**
     * Python MoviePy for advanced video generation
     */
    private function pythonMoviePy($segments, $background, $style, $outputFile) {
        // Check if moviepy is available
        exec('python -c "import moviepy" 2>&1', $output, $returnCode);
        if ($returnCode !== 0) {
            // Try to install moviepy
            echo "  Installing moviepy...\n";
            exec('pip install moviepy 2>&1', $installOutput, $installCode);
            if ($installCode !== 0) {
                throw new Exception("Could not install moviepy");
            }
        }

        // Create Python script for video generation
        $pythonScript = $this->tempDir . 'video_script.py';
        $scriptContent = $this->generateMoviePyScript($segments, $background, $style, $outputFile);
        file_put_contents($pythonScript, $scriptContent);

        // Run the script
        $cmd = "python \"$pythonScript\" 2>&1";
        exec($cmd, $output, $returnCode);

        // Clean up
        unlink($pythonScript);

        return $returnCode === 0 && file_exists($outputFile);
    }

    /**
     * Generate MoviePy Python script
     */
    private function generateMoviePyScript($segments, $background, $style, $outputFile) {
        $segmentsJson = json_encode($segments);
        
        return "
import json
from moviepy.editor import *
import numpy as np

# Configuration
segments = json.loads('$segmentsJson')
output_file = '$outputFile'
background = '$background'
style = '$style'

# Video settings
width, height = 720, 1280
fps = 30
segment_duration = 3

# Create clips for each segment
clips = []

for i, segment in enumerate(segments):
    # Create background
    if background == 'home':
        bg_color = (245, 245, 220)  # Beige
    elif background == 'office':
        bg_color = (230, 230, 250)  # Lavender
    elif background == 'nature':
        bg_color = (144, 238, 144)  # Light Green
    else:
        bg_color = (255, 255, 255)  # White
    
    # Create background clip
    bg_clip = ColorClip(size=(width, height), color=bg_color, duration=segment_duration)
    
    # Add text overlay
    text = segment.get('text', 'Sample text')[:100]  # Limit text length
    
    # Style-based text formatting
    if style == 'funny':
        fontsize = 40
        color = 'red'
        font = 'Arial-Bold'
    elif style == 'desi':
        fontsize = 35
        color = 'darkblue'
        font = 'Arial'
    elif style == 'emotional':
        fontsize = 32
        color = 'darkgreen'
        font = 'Arial'
    else:
        fontsize = 36
        color = 'black'
        font = 'Arial'
    
    # Create text clip
    txt_clip = TextClip(text, 
                       fontsize=fontsize, 
                       color=color, 
                       font=font,
                       size=(width-40, None),
                       method='caption').set_position('center').set_duration(segment_duration)
    
    # Add animation
    if i % 2 == 0:
        # Fade in animation
        txt_clip = txt_clip.fadein(0.5)
    else:
        # Slide in animation
        txt_clip = txt_clip.set_position(lambda t: ('center', 50 + (height/2-50)*(t/0.5) if t < 0.5 else 'center'))
    
    # Composite the clip
    final_clip = CompositeVideoClip([bg_clip, txt_clip])
    clips.append(final_clip)

# Concatenate all clips
final_video = concatenate_videoclips(clips)

# Write the video file
final_video.write_videofile(output_file, fps=fps, codec='libx264')

print('Video generation completed successfully')
";
    }

    /**
     * ImageMagick animation
     */
    private function imageMagickAnimation($segments, $background, $style, $outputFile) {
        // Check if ImageMagick is available
        exec('convert -version 2>&1', $output, $returnCode);
        if ($returnCode !== 0) {
            throw new Exception("ImageMagick not installed");
        }

        // Generate frames
        $frameFiles = [];
        $framesPerSegment = 90; // 3 seconds at 30fps
        
        foreach ($segments as $segIndex => $segment) {
            for ($frame = 0; $frame < $framesPerSegment; $frame++) {
                $frameFile = $this->generateAnimatedFrame($segment, $background, $style, $segIndex, $frame, $framesPerSegment);
                if ($frameFile) {
                    $frameFiles[] = $frameFile;
                }
            }
        }

        if (empty($frameFiles)) {
            throw new Exception("No frames generated");
        }

        // Convert frames to video using ffmpeg
        $framePattern = $this->tempDir . 'frame_%04d.png';
        
        // Rename files to sequential pattern
        foreach ($frameFiles as $index => $frameFile) {
            $newName = sprintf($this->tempDir . 'frame_%04d.png', $index + 1);
            rename($frameFile, $newName);
        }

        // Create video
        $cmd = "ffmpeg -r 30 -i \"$framePattern\" -c:v libx264 -pix_fmt yuv420p \"$outputFile\" -y 2>&1";
        exec($cmd, $output, $returnCode);

        // Clean up frames
        foreach (glob($this->tempDir . 'frame_*.png') as $frameFile) {
            unlink($frameFile);
        }

        return $returnCode === 0 && file_exists($outputFile);
    }

    /**
     * Manual frame generation using GD
     */
    private function manualFrameGeneration($segments, $background, $style, $outputFile) {
        if (!extension_loaded('gd')) {
            throw new Exception("GD extension not available");
        }

        echo "  Generating frames manually with GD...\n";
        
        $width = 720;
        $height = 1280;
        $fps = 30;
        $segmentDuration = 3;
        $framesPerSegment = $fps * $segmentDuration;
        
        $frameFiles = [];
        
        foreach ($segments as $segIndex => $segment) {
            $text = $segment['text'] ?? 'Sample text';
            
            for ($frame = 0; $frame < $framesPerSegment; $frame++) {
                $frameFile = $this->tempDir . "frame_{$segIndex}_{$frame}.png";
                
                // Create frame image
                $image = imagecreatetruecolor($width, $height);
                
                // Background color based on background setting
                $bgColors = [
                    'home' => [245, 245, 220],
                    'office' => [230, 230, 250],
                    'nature' => [144, 238, 144],
                    'city' => [112, 128, 144],
                    'traditional' => [255, 228, 181]
                ];
                
                $bgColor = $bgColors[$background] ?? [255, 255, 255];
                $backgroundColor = imagecolorallocate($image, $bgColor[0], $bgColor[1], $bgColor[2]);
                imagefill($image, 0, 0, $backgroundColor);
                
                // Text color based on style
                $textColors = [
                    'funny' => [255, 0, 0],
                    'desi' => [0, 0, 139],
                    'emotional' => [0, 100, 0],
                    'bollywood' => [255, 140, 0]
                ];
                
                $textColor = $textColors[$style] ?? [0, 0, 0];
                $textColorGD = imagecolorallocate($image, $textColor[0], $textColor[1], $textColor[2]);
                
                // Add animation effects
                $progress = $frame / $framesPerSegment;
                
                // Animated text position
                $baseY = $height / 2;
                $animatedY = $baseY + sin($progress * 2 * pi()) * 20; // Floating animation
                
                // Word-by-word reveal animation
                $words = explode(' ', $text);
                $wordsToShow = min(count($words), intval($progress * count($words)) + 1);
                $displayText = implode(' ', array_slice($words, 0, $wordsToShow));
                
                // Add text with word wrapping
                $this->addWrappedText($image, 5, $displayText, $width - 40, $animatedY, $textColorGD);
                
                // Add frame number for debugging
                $frameInfo = "Frame: $frame/" . ($framesPerSegment - 1);
                imagestring($image, 2, 10, 10, $frameInfo, $textColorGD);
                
                // Save frame
                imagepng($image, $frameFile);
                imagedestroy($image);
                
                $frameFiles[] = $frameFile;
            }
        }
        
        echo "  Generated " . count($frameFiles) . " frames\n";
        
        // Convert to video using ffmpeg
        if (count($frameFiles) > 0) {
            // Create frame list
            $listFile = $this->tempDir . 'frame_list.txt';
            $listContent = '';
            foreach ($frameFiles as $frameFile) {
                $listContent .= "file '" . realpath($frameFile) . "'\n";
                $listContent .= "duration " . (1/$fps) . "\n";
            }
            file_put_contents($listFile, $listContent);
            
            // Generate video
            $cmd = "ffmpeg -f concat -safe 0 -i \"$listFile\" -c:v libx264 -pix_fmt yuv420p -r $fps \"$outputFile\" -y 2>&1";
            exec($cmd, $output, $returnCode);
            
            // Clean up
            unlink($listFile);
            foreach ($frameFiles as $frameFile) {
                if (file_exists($frameFile)) {
                    unlink($frameFile);
                }
            }
            
            return $returnCode === 0 && file_exists($outputFile);
        }
        
        return false;
    }

    /**
     * Add wrapped text to image
     */
    private function addWrappedText($image, $fontSize, $text, $maxWidth, $y, $color) {
        $words = explode(' ', $text);
        $lines = [];
        $currentLine = '';
        
        foreach ($words as $word) {
            $testLine = $currentLine . ($currentLine ? ' ' : '') . $word;
            $bbox = imagettfbbox($fontSize, 0, __DIR__ . '/../assets/fonts/arial.ttf', $testLine);
            
            if (!$bbox || ($bbox[4] - $bbox[0]) <= $maxWidth) {
                $currentLine = $testLine;
            } else {
                if ($currentLine) {
                    $lines[] = $currentLine;
                }
                $currentLine = $word;
            }
        }
        
        if ($currentLine) {
            $lines[] = $currentLine;
        }
        
        // Draw lines
        $lineHeight = $fontSize + 5;
        $startY = $y - (count($lines) * $lineHeight / 2);
        
        foreach ($lines as $index => $line) {
            $lineY = $startY + ($index * $lineHeight);
            
            // Try to use TTF font, fallback to built-in
            if (file_exists(__DIR__ . '/../assets/fonts/arial.ttf')) {
                imagettftext($image, $fontSize, 0, 20, $lineY, $color, __DIR__ . '/../assets/fonts/arial.ttf', $line);
            } else {
                imagestring($image, 5, 20, $lineY - 10, $line, $color);
            }
        }
    }

    /**
     * Generate segment image
     */
    private function generateSegmentImage($segment, $background, $style, $index) {
        if (!extension_loaded('gd')) {
            return false;
        }

        $width = 720;
        $height = 1280;
        
        $image = imagecreatetruecolor($width, $height);
        
        // Background
        $bgColors = [
            'home' => [245, 245, 220],
            'office' => [230, 230, 250],
            'nature' => [144, 238, 144],
            'city' => [112, 128, 144],
            'traditional' => [255, 228, 181]
        ];
        
        $bgColor = $bgColors[$background] ?? [255, 255, 255];
        $backgroundColor = imagecolorallocate($image, $bgColor[0], $bgColor[1], $bgColor[2]);
        imagefill($image, 0, 0, $backgroundColor);
        
        // Text
        $text = $segment['text'] ?? 'Sample text';
        $textColors = [
            'funny' => [255, 0, 0],
            'desi' => [0, 0, 139],
            'emotional' => [0, 100, 0],
            'bollywood' => [255, 140, 0]
        ];
        
        $textColor = $textColors[$style] ?? [0, 0, 0];
        $textColorGD = imagecolorallocate($image, $textColor[0], $textColor[1], $textColor[2]);
        
        // Add text
        $this->addWrappedText($image, 5, $text, $width - 40, $height / 2, $textColorGD);
        
        // Save image
        $imageFile = $this->tempDir . "segment_$index.png";
        imagepng($image, $imageFile);
        imagedestroy($image);
        
        return $imageFile;
    }

    /**
     * Generate animated frame
     */
    private function generateAnimatedFrame($segment, $background, $style, $segIndex, $frame, $totalFrames) {
        if (!extension_loaded('gd')) {
            return false;
        }

        $width = 720;
        $height = 1280;
        
        $image = imagecreatetruecolor($width, $height);
        
        // Animated background
        $progress = $frame / $totalFrames;
        $bgColors = [
            'home' => [245, 245, 220],
            'office' => [230, 230, 250],
            'nature' => [144, 238, 144],
            'city' => [112, 128, 144],
            'traditional' => [255, 228, 181]
        ];
        
        $bgColor = $bgColors[$background] ?? [255, 255, 255];
        
        // Add slight color animation
        $animatedBg = [
            intval($bgColor[0] + sin($progress * 2 * pi()) * 10),
            intval($bgColor[1] + sin($progress * 2 * pi() + 2) * 10),
            intval($bgColor[2] + sin($progress * 2 * pi() + 4) * 10)
        ];
        
        $backgroundColor = imagecolorallocate($image, 
            max(0, min(255, $animatedBg[0])),
            max(0, min(255, $animatedBg[1])),
            max(0, min(255, $animatedBg[2]))
        );
        imagefill($image, 0, 0, $backgroundColor);
        
        // Animated text
        $text = $segment['text'] ?? 'Sample text';
        $textColors = [
            'funny' => [255, 0, 0],
            'desi' => [0, 0, 139],
            'emotional' => [0, 100, 0],
            'bollywood' => [255, 140, 0]
        ];
        
        $textColor = $textColors[$style] ?? [0, 0, 0];
        $textColorGD = imagecolorallocate($image, $textColor[0], $textColor[1], $textColor[2]);
        
        // Animated text position
        $baseY = $height / 2;
        $animatedY = $baseY + sin($progress * 4 * pi()) * 15;
        
        $this->addWrappedText($image, 5, $text, $width - 40, $animatedY, $textColorGD);
        
        // Save frame
        $frameFile = $this->tempDir . "anim_frame_{$segIndex}_{$frame}.png";
        imagepng($image, $frameFile);
        imagedestroy($image);
        
        return $frameFile;
    }

    /**
     * Generate enhanced placeholder with actual content
     */
    private function generateEnhancedPlaceholder($segments, $background, $style, $outputFile) {
        echo "🔧 Generating enhanced video placeholder...\n";
        
        // Create a simple but real video file
        if (extension_loaded('gd')) {
            return $this->manualFrameGeneration($segments, $background, $style, $outputFile);
        }
        
        // Fallback: create minimal MP4 file
        $minimalMp4 = base64_decode('AAAAIGZ0eXBpc29tAAACAGlzb21pc28yYXZjMW1wNDEAAAAIZnJlZQAAAsdtZGF0');
        file_put_contents($outputFile, $minimalMp4);
        
        return file_exists($outputFile);
    }

    /**
     * Test video generation methods
     */
    public function testMethods() {
        echo "🧪 Testing video generation methods...\n";
        
        $testSegments = [
            ['text' => 'This is a test video generation'],
            ['text' => 'Testing different methods']
        ];
        
        $methods = [
            'ffmpeg_slideshow' => 'FFmpeg Slideshow',
            'python_moviepy' => 'Python MoviePy',
            'imagemagick_animation' => 'ImageMagick Animation',
            'manual_frame_generation' => 'Manual Frame Generation'
        ];
        
        $results = [];
        
        foreach ($methods as $method => $name) {
            echo "\nTesting $name...\n";
            $testFile = $this->tempDir . "test_$method.mp4";
            
            try {
                $success = $this->callVideoMethod($method, $testSegments, 'home', 'funny', $testFile);
                if ($success && file_exists($testFile)) {
                    $size = filesize($testFile);
                    $results[$method] = "✅ Working - " . round($size/1024, 1) . " KB";
                    unlink($testFile);
                } else {
                    $results[$method] = "❌ Failed - No output";
                }
            } catch (Exception $e) {
                $results[$method] = "❌ Error - " . $e->getMessage();
            }
        }
        
        echo "\n📊 Video Generation Test Results:\n";
        foreach ($results as $method => $result) {
            echo "  $method: $result\n";
        }
        
        return $results;
    }
}
