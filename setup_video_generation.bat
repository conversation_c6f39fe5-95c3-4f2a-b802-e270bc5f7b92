@echo off
echo 🎭 Sutradhar 2070 - Video Generation Setup
echo ==========================================

echo.
echo 🔍 Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found! Please install Python 3.7+ from https://python.org
    echo    Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

python --version
echo ✅ Python found!

echo.
echo 🔍 Checking pip...
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ pip not found! Please reinstall Python with pip
    pause
    exit /b 1
)

echo ✅ pip found!

echo.
echo 📦 Installing Python packages for video generation...
echo This may take a few minutes...

pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo ❌ Package installation failed!
    echo Trying alternative installation...
    
    echo Installing Pillow...
    pip install Pillow
    
    echo Installing numpy...
    pip install numpy
    
    echo Installing moviepy...
    pip install moviepy
    
    echo Installing imageio...
    pip install imageio
    
    echo Installing imageio-ffmpeg...
    pip install imageio-ffmpeg
)

echo.
echo 🔍 Checking FFmpeg...
ffmpeg -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ FFmpeg not found in PATH
    echo.
    echo FFmpeg is required for video generation. You can:
    echo 1. Download from https://ffmpeg.org/download.html
    echo 2. Extract to a folder (e.g., C:\ffmpeg)
    echo 3. Add C:\ffmpeg\bin to your system PATH
    echo.
    echo Or use the automatic installer:
    echo winget install ffmpeg
    echo.
    echo The system will work with GD fallback, but FFmpeg provides better quality.
) else (
    echo ✅ FFmpeg found!
    ffmpeg -version | findstr "ffmpeg version"
)

echo.
echo 🔍 Checking GD extension...
php -m | findstr -i gd >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ GD extension not found!
    echo Please enable GD extension in php.ini:
    echo 1. Open php.ini file
    echo 2. Find ;extension=gd
    echo 3. Remove the semicolon: extension=gd
    echo 4. Restart Apache/web server
) else (
    echo ✅ GD extension found!
)

echo.
echo 🧪 Testing Python video generation...
python core/real_video_generator.py --job_id "test_setup" --mood "euphoric" --topic "nature_wildlife" --prompt "Test video generation setup" 2>nul
if %errorlevel% equ 0 (
    echo ✅ Python video generation test successful!
) else (
    echo ⚠️ Python video generation test failed, but fallback systems are available
)

echo.
echo 📁 Creating required directories...
if not exist "public\generated_videos" mkdir "public\generated_videos"
if not exist "temp" mkdir "temp"
echo ✅ Directories created!

echo.
echo 🎉 Setup Complete!
echo ==================
echo.
echo Your Sutradhar 2070 video generation system is ready!
echo.
echo ✅ Available features:
echo    • Python-based real video generation
echo    • GD-based fallback generation  
echo    • FFmpeg video processing
echo    • Automatic thumbnail generation
echo    • Multiple quality levels
echo.
echo 🚀 Test your system:
echo    • Open: http://localhost:8000/mood_video_generator.html
echo    • Or run: http://localhost:8000/final_test_page.html
echo.
echo 📚 Troubleshooting:
echo    • If videos don't generate: Check Python packages
echo    • If thumbnails fail: Enable GD extension
echo    • If quality is low: Install FFmpeg
echo.

pause
