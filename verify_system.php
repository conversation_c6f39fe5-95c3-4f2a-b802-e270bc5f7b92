<?php
/**
 * System Verification Script for Sutradhar 2070
 * Tests all components and provides detailed status report
 */

echo "🔍 Sutradhar 2070 System Verification\n";
echo "=" . str_repeat("=", 50) . "\n\n";

$results = [];

// Test 1: Check file structure
echo "📁 Checking file structure...\n";
$requiredFiles = [
    'public/index_integrated.html',
    'public/dashboard.html',
    'public/pricing.html',
    'public/marketing_landing.html',
    'public/api_unified.php',
    'core/user_authentication.php',
    'core/credit_system.php',
    'core/subscription_manager.php',
    'core/payment_processor.php',
    'core/database_manager.php',
    'config/database.json'
];

$missingFiles = [];
foreach ($requiredFiles as $file) {
    if (!file_exists($file)) {
        $missingFiles[] = $file;
    }
}

if (empty($missingFiles)) {
    echo "✅ All required files present\n";
    $results['file_structure'] = 'PASS';
} else {
    echo "❌ Missing files: " . implode(', ', $missingFiles) . "\n";
    $results['file_structure'] = 'FAIL';
}

// Test 2: Database connectivity
echo "\n🗄️ Testing database connectivity...\n";
try {
    require_once 'core/database_manager.php';
    $db = new DatabaseManager();
    
    // Test basic query
    $result = $db->query("SELECT 1 as test");
    if ($result && $result[0]['test'] == 1) {
        echo "✅ Database connection successful\n";
        $results['database'] = 'PASS';
    } else {
        echo "❌ Database query failed\n";
        $results['database'] = 'FAIL';
    }
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    $results['database'] = 'FAIL';
}

// Test 3: Authentication system
echo "\n🔐 Testing authentication system...\n";
try {
    require_once 'core/user_authentication.php';
    $auth = new UserAuthentication();
    
    // Test password hashing
    $testPassword = 'TestPassword123!';
    $hash = password_hash($testPassword, PASSWORD_ARGON2ID);
    
    if (password_verify($testPassword, $hash)) {
        echo "✅ Password hashing working\n";
        $results['authentication'] = 'PASS';
    } else {
        echo "❌ Password hashing failed\n";
        $results['authentication'] = 'FAIL';
    }
} catch (Exception $e) {
    echo "❌ Authentication system error: " . $e->getMessage() . "\n";
    $results['authentication'] = 'FAIL';
}

// Test 4: Credit system
echo "\n💎 Testing credit system...\n";
try {
    require_once 'core/credit_system.php';
    $creditSystem = new CreditSystem();
    
    // Test credit packages
    $packages = $creditSystem->getCreditPackages();
    
    if (!empty($packages)) {
        echo "✅ Credit packages loaded: " . count($packages) . " packages\n";
        $results['credit_system'] = 'PASS';
    } else {
        echo "❌ No credit packages found\n";
        $results['credit_system'] = 'FAIL';
    }
} catch (Exception $e) {
    echo "❌ Credit system error: " . $e->getMessage() . "\n";
    $results['credit_system'] = 'FAIL';
}

// Test 5: Subscription system
echo "\n📊 Testing subscription system...\n";
try {
    require_once 'core/subscription_manager.php';
    $subscriptionManager = new SubscriptionManager();
    
    // Test subscription plans
    $plans = $subscriptionManager->getPlans();
    
    if (!empty($plans)) {
        echo "✅ Subscription plans loaded: " . count($plans) . " plans\n";
        $results['subscription_system'] = 'PASS';
    } else {
        echo "❌ No subscription plans found\n";
        $results['subscription_system'] = 'FAIL';
    }
} catch (Exception $e) {
    echo "❌ Subscription system error: " . $e->getMessage() . "\n";
    $results['subscription_system'] = 'FAIL';
}

// Test 6: Payment system
echo "\n💳 Testing payment system...\n";
try {
    require_once 'core/payment_processor.php';
    $paymentProcessor = new PaymentProcessor();
    
    echo "✅ Payment processor initialized\n";
    $results['payment_system'] = 'PASS';
} catch (Exception $e) {
    echo "❌ Payment system error: " . $e->getMessage() . "\n";
    $results['payment_system'] = 'FAIL';
}

// Test 7: API endpoints
echo "\n🌐 Testing API endpoints...\n";
$apiTests = [
    '/api/auth/status' => 'GET',
    '/api/user/credits' => 'GET'
];

$apiResults = [];
foreach ($apiTests as $endpoint => $method) {
    try {
        // Simulate API call
        $_SERVER['REQUEST_METHOD'] = $method;
        $_GET['endpoint'] = ltrim($endpoint, '/api/');
        
        ob_start();
        include 'public/api_unified.php';
        $output = ob_get_clean();
        
        $response = json_decode($output, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "✅ $endpoint responds correctly\n";
            $apiResults[$endpoint] = 'PASS';
        } else {
            echo "❌ $endpoint invalid JSON response\n";
            $apiResults[$endpoint] = 'FAIL';
        }
    } catch (Exception $e) {
        echo "❌ $endpoint error: " . $e->getMessage() . "\n";
        $apiResults[$endpoint] = 'FAIL';
    }
}

$results['api_endpoints'] = empty(array_filter($apiResults, function($r) { return $r === 'FAIL'; })) ? 'PASS' : 'FAIL';

// Test 8: Configuration files
echo "\n⚙️ Testing configuration files...\n";
$configFiles = [
    'config/database.json',
    'config/email_config.json',
    'config/payment_config.json',
    'config/huggingface_config.json'
];

$configResults = [];
foreach ($configFiles as $configFile) {
    if (file_exists($configFile)) {
        $config = json_decode(file_get_contents($configFile), true);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "✅ $configFile valid\n";
            $configResults[$configFile] = 'PASS';
        } else {
            echo "❌ $configFile invalid JSON\n";
            $configResults[$configFile] = 'FAIL';
        }
    } else {
        echo "❌ $configFile missing\n";
        $configResults[$configFile] = 'FAIL';
    }
}

$results['configuration'] = empty(array_filter($configResults, function($r) { return $r === 'FAIL'; })) ? 'PASS' : 'FAIL';

// Test 9: Web server configuration
echo "\n🌐 Testing web server configuration...\n";
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    $requiredModules = ['mod_rewrite', 'mod_headers'];
    $missingModules = [];
    
    foreach ($requiredModules as $module) {
        if (!in_array($module, $modules)) {
            $missingModules[] = $module;
        }
    }
    
    if (empty($missingModules)) {
        echo "✅ Required Apache modules loaded\n";
        $results['web_server'] = 'PASS';
    } else {
        echo "❌ Missing Apache modules: " . implode(', ', $missingModules) . "\n";
        $results['web_server'] = 'FAIL';
    }
} else {
    echo "⚠️ Cannot detect Apache modules (non-Apache server?)\n";
    $results['web_server'] = 'UNKNOWN';
}

// Test 10: Security checks
echo "\n🔒 Running security checks...\n";
$securityIssues = [];

// Check file permissions
$sensitiveFiles = ['config/database.json', 'config/payment_config.json'];
foreach ($sensitiveFiles as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file) & 0777;
        if ($perms > 0644) {
            $securityIssues[] = "$file has overly permissive permissions";
        }
    }
}

// Check for exposed sensitive directories
$exposedDirs = [];
$sensitiveDirectories = ['config', 'core', 'tests'];
foreach ($sensitiveDirectories as $dir) {
    if (is_dir("public/$dir")) {
        $exposedDirs[] = $dir;
    }
}

if (!empty($exposedDirs)) {
    $securityIssues[] = "Sensitive directories exposed in public: " . implode(', ', $exposedDirs);
}

if (empty($securityIssues)) {
    echo "✅ No security issues detected\n";
    $results['security'] = 'PASS';
} else {
    echo "❌ Security issues found:\n";
    foreach ($securityIssues as $issue) {
        echo "   - $issue\n";
    }
    $results['security'] = 'FAIL';
}

// Generate final report
echo "\n" . str_repeat("=", 60) . "\n";
echo "📊 SYSTEM VERIFICATION REPORT\n";
echo str_repeat("=", 60) . "\n\n";

$totalTests = count($results);
$passedTests = count(array_filter($results, function($r) { return $r === 'PASS'; }));
$failedTests = count(array_filter($results, function($r) { return $r === 'FAIL'; }));
$unknownTests = count(array_filter($results, function($r) { return $r === 'UNKNOWN'; }));

echo "Total Tests: $totalTests\n";
echo "Passed: $passedTests\n";
echo "Failed: $failedTests\n";
echo "Unknown: $unknownTests\n";
echo "Success Rate: " . round(($passedTests / $totalTests) * 100, 1) . "%\n\n";

echo "📋 DETAILED RESULTS:\n";
foreach ($results as $test => $result) {
    $icon = $result === 'PASS' ? '✅' : ($result === 'FAIL' ? '❌' : '⚠️');
    echo "$icon " . ucwords(str_replace('_', ' ', $test)) . ": $result\n";
}

if ($failedTests > 0) {
    echo "\n🔧 RECOMMENDED ACTIONS:\n";
    
    if ($results['database'] === 'FAIL') {
        echo "1. Check database configuration in config/database.json\n";
        echo "2. Ensure MySQL server is running\n";
        echo "3. Create database: sutradhar2070\n";
    }
    
    if ($results['file_structure'] === 'FAIL') {
        echo "4. Run setup.php to create missing files\n";
    }
    
    if ($results['configuration'] === 'FAIL') {
        echo "5. Update configuration files with correct settings\n";
    }
    
    if ($results['security'] === 'FAIL') {
        echo "6. Fix security issues listed above\n";
    }
    
    if ($results['web_server'] === 'FAIL') {
        echo "7. Enable required Apache modules (mod_rewrite, mod_headers)\n";
    }
} else {
    echo "\n🎉 All tests passed! Your Sutradhar 2070 system is ready for production.\n";
    echo "\n🚀 NEXT STEPS:\n";
    echo "1. Configure Hugging Face API key in config/huggingface_config.json\n";
    echo "2. Set up payment provider credentials\n";
    echo "3. Configure email settings for notifications\n";
    echo "4. Test video generation functionality\n";
    echo "5. Deploy to production server\n";
}

echo "\n📄 Report saved to: tests/reports/verification_" . date('Y-m-d_H-i-s') . ".json\n";

// Save detailed report
$reportData = [
    'timestamp' => date('Y-m-d H:i:s'),
    'summary' => [
        'total_tests' => $totalTests,
        'passed' => $passedTests,
        'failed' => $failedTests,
        'unknown' => $unknownTests,
        'success_rate' => round(($passedTests / $totalTests) * 100, 1)
    ],
    'results' => $results,
    'api_tests' => $apiResults ?? [],
    'config_tests' => $configResults ?? []
];

if (!is_dir('tests/reports')) {
    mkdir('tests/reports', 0755, true);
}

file_put_contents(
    'tests/reports/verification_' . date('Y-m-d_H-i-s') . '.json',
    json_encode($reportData, JSON_PRETTY_PRINT)
);
?>
