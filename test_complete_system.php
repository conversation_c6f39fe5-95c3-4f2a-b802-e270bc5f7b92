<?php
/**
 * Complete System Test for Sutradhar 2070
 * End-to-end testing of the integrated platform
 */

echo "🎭 Sutradhar 2070 - Complete System Test\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// Test configuration
$testUser = [
    'email' => 'test_' . time() . '@example.com',
    'password' => 'TestPass123!',
    'first_name' => 'Test',
    'last_name' => 'User'
];

$testResults = [];

// Test 1: User Registration
echo "👤 Testing user registration...\n";
try {
    require_once 'core/user_authentication.php';
    $auth = new UserAuthentication();
    
    $result = $auth->registerUser(
        $testUser['email'],
        $testUser['password'],
        $testUser['first_name'],
        $testUser['last_name']
    );
    
    if ($result['success']) {
        echo "✅ User registration successful\n";
        $testUserId = $result['user_id'];
        $testResults['registration'] = 'PASS';
        
        // Auto-verify for testing
        require_once 'core/database_manager.php';
        $db = new DatabaseManager();
        $db->update('users', [
            'email_verified' => 1,
            'email_verified_at' => date('Y-m-d H:i:s')
        ], ['user_id' => $testUserId]);
        
    } else {
        echo "❌ User registration failed: " . $result['error'] . "\n";
        $testResults['registration'] = 'FAIL';
        exit(1);
    }
} catch (Exception $e) {
    echo "❌ Registration error: " . $e->getMessage() . "\n";
    $testResults['registration'] = 'FAIL';
    exit(1);
}

// Test 2: User Login
echo "\n🔐 Testing user login...\n";
try {
    $loginResult = $auth->loginUser($testUser['email'], $testUser['password']);
    
    if ($loginResult['success']) {
        echo "✅ User login successful\n";
        $testResults['login'] = 'PASS';
    } else {
        echo "❌ User login failed: " . $loginResult['error'] . "\n";
        $testResults['login'] = 'FAIL';
    }
} catch (Exception $e) {
    echo "❌ Login error: " . $e->getMessage() . "\n";
    $testResults['login'] = 'FAIL';
}

// Test 3: Credit System
echo "\n💎 Testing credit system...\n";
try {
    require_once 'core/credit_system.php';
    $creditSystem = new CreditSystem();
    
    // Add initial credits
    $creditSystem->addCredits($testUserId, 100, 'test', 'Test credits');
    $balance = $creditSystem->getUserCredits($testUserId);
    
    if ($balance >= 100) {
        echo "✅ Credit system working - Balance: $balance\n";
        $testResults['credits'] = 'PASS';
    } else {
        echo "❌ Credit system failed - Expected 100+, got: $balance\n";
        $testResults['credits'] = 'FAIL';
    }
} catch (Exception $e) {
    echo "❌ Credit system error: " . $e->getMessage() . "\n";
    $testResults['credits'] = 'FAIL';
}

// Test 4: Subscription System
echo "\n📊 Testing subscription system...\n";
try {
    require_once 'core/subscription_manager.php';
    $subscriptionManager = new SubscriptionManager();
    
    // Get user subscription (should be free tier)
    $subscription = $subscriptionManager->getUserSubscription($testUserId);
    
    if ($subscription && $subscription['plan_id'] === 'free') {
        echo "✅ Subscription system working - Plan: " . $subscription['plan_id'] . "\n";
        $testResults['subscription'] = 'PASS';
    } else {
        echo "❌ Subscription system failed\n";
        $testResults['subscription'] = 'FAIL';
    }
} catch (Exception $e) {
    echo "❌ Subscription system error: " . $e->getMessage() . "\n";
    $testResults['subscription'] = 'FAIL';
}

// Test 5: Video Generation Job Creation
echo "\n🎬 Testing video generation system...\n";
try {
    $jobId = 'test_job_' . uniqid();
    $jobData = [
        'job_id' => $jobId,
        'user_id' => $testUserId,
        'mood' => 'euphoric',
        'topic' => 'nature_wildlife',
        'inspiration' => json_encode(['cinematic', 'colorful']),
        'status' => 'analyzing',
        'progress' => 0,
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    $db->insert('generation_jobs', $jobData);
    
    // Test credit consumption
    $consumeResult = $creditSystem->consumeCredits($testUserId, 'mood_optimized', $jobId);
    
    if ($consumeResult['success']) {
        echo "✅ Video generation job created and credits consumed\n";
        $testResults['video_generation'] = 'PASS';
        
        // Check remaining balance
        $newBalance = $creditSystem->getUserCredits($testUserId);
        echo "   Credits after generation: $newBalance\n";
    } else {
        echo "❌ Video generation failed: " . $consumeResult['error'] . "\n";
        $testResults['video_generation'] = 'FAIL';
    }
} catch (Exception $e) {
    echo "❌ Video generation error: " . $e->getMessage() . "\n";
    $testResults['video_generation'] = 'FAIL';
}

// Test 6: API Endpoints
echo "\n🌐 Testing API endpoints...\n";
try {
    // Simulate API calls
    $apiTests = [
        'auth/status' => ['method' => 'GET', 'expected' => 'user data'],
        'user/credits' => ['method' => 'GET', 'expected' => 'credit balance']
    ];
    
    $apiPassed = 0;
    $apiTotal = count($apiTests);
    
    foreach ($apiTests as $endpoint => $test) {
        // Mock the API call environment
        $_SERVER['REQUEST_METHOD'] = $test['method'];
        $_GET['endpoint'] = $endpoint;
        
        ob_start();
        
        // Capture any output from the API
        try {
            include 'public/api_unified.php';
            $output = ob_get_clean();
            
            $response = json_decode($output, true);
            if (json_last_error() === JSON_ERROR_NONE && isset($response['success'])) {
                echo "✅ API $endpoint working\n";
                $apiPassed++;
            } else {
                echo "❌ API $endpoint failed - Invalid response\n";
            }
        } catch (Exception $e) {
            ob_get_clean();
            echo "❌ API $endpoint error: " . $e->getMessage() . "\n";
        }
    }
    
    $testResults['api_endpoints'] = ($apiPassed === $apiTotal) ? 'PASS' : 'FAIL';
    
} catch (Exception $e) {
    echo "❌ API testing error: " . $e->getMessage() . "\n";
    $testResults['api_endpoints'] = 'FAIL';
}

// Test 7: Payment System (Demo Mode)
echo "\n💳 Testing payment system (demo mode)...\n";
try {
    require_once 'core/payment_processor.php';
    $paymentProcessor = new PaymentProcessor();
    
    // Test demo credit purchase
    $purchaseResult = $paymentProcessor->processCreditPurchase(
        $testUserId,
        'starter',
        'stripe',
        ['payment_method_id' => 'pm_demo_success']
    );
    
    if ($purchaseResult['success']) {
        echo "✅ Payment system working (demo mode)\n";
        $testResults['payment'] = 'PASS';
    } else {
        echo "❌ Payment system failed: " . $purchaseResult['error'] . "\n";
        $testResults['payment'] = 'FAIL';
    }
} catch (Exception $e) {
    echo "❌ Payment system error: " . $e->getMessage() . "\n";
    $testResults['payment'] = 'FAIL';
}

// Test 8: File Structure and Permissions
echo "\n📁 Testing file structure and permissions...\n";
$criticalFiles = [
    'public/index.html',
    'public/dashboard.html',
    'public/pricing.html',
    'public/api_unified.php',
    'public/.htaccess'
];

$fileIssues = [];
foreach ($criticalFiles as $file) {
    if (!file_exists($file)) {
        $fileIssues[] = "Missing: $file";
    } elseif (!is_readable($file)) {
        $fileIssues[] = "Not readable: $file";
    }
}

if (empty($fileIssues)) {
    echo "✅ All critical files present and accessible\n";
    $testResults['file_structure'] = 'PASS';
} else {
    echo "❌ File structure issues:\n";
    foreach ($fileIssues as $issue) {
        echo "   - $issue\n";
    }
    $testResults['file_structure'] = 'FAIL';
}

// Cleanup test data
echo "\n🧹 Cleaning up test data...\n";
try {
    // Remove test user
    $db->delete('users', ['user_id' => $testUserId]);
    $db->delete('credit_transactions', ['user_id' => $testUserId]);
    $db->delete('generation_jobs', ['user_id' => $testUserId]);
    echo "✅ Test data cleaned up\n";
} catch (Exception $e) {
    echo "⚠️ Cleanup warning: " . $e->getMessage() . "\n";
}

// Generate final report
echo "\n" . str_repeat("=", 60) . "\n";
echo "🎯 COMPLETE SYSTEM TEST REPORT\n";
echo str_repeat("=", 60) . "\n\n";

$totalTests = count($testResults);
$passedTests = count(array_filter($testResults, function($r) { return $r === 'PASS'; }));
$failedTests = $totalTests - $passedTests;

echo "Total Tests: $totalTests\n";
echo "Passed: $passedTests\n";
echo "Failed: $failedTests\n";
echo "Success Rate: " . round(($passedTests / $totalTests) * 100, 1) . "%\n\n";

echo "📊 DETAILED RESULTS:\n";
foreach ($testResults as $test => $result) {
    $icon = $result === 'PASS' ? '✅' : '❌';
    echo "$icon " . ucwords(str_replace('_', ' ', $test)) . ": $result\n";
}

if ($failedTests === 0) {
    echo "\n🎉 ALL TESTS PASSED! 🎉\n";
    echo "\n✨ Your Sutradhar 2070 system is fully functional and ready for use!\n\n";
    echo "🚀 READY FOR PRODUCTION:\n";
    echo "   • User registration and authentication ✅\n";
    echo "   • Credit system and consumption ✅\n";
    echo "   • Video generation pipeline ✅\n";
    echo "   • Payment processing (demo) ✅\n";
    echo "   • API endpoints ✅\n";
    echo "   • File structure ✅\n\n";
    echo "🌐 Access your application at: http://localhost/public/\n";
    echo "👤 Default admin: <EMAIL> / Admin123!\n";
} else {
    echo "\n⚠️ SOME TESTS FAILED\n";
    echo "\n🔧 Please review the failed tests above and:\n";
    echo "1. Check configuration files\n";
    echo "2. Verify database connectivity\n";
    echo "3. Ensure all required files are present\n";
    echo "4. Run: php verify_system.php for detailed diagnostics\n";
}

// Save test report
$reportData = [
    'timestamp' => date('Y-m-d H:i:s'),
    'test_user' => $testUser['email'],
    'summary' => [
        'total_tests' => $totalTests,
        'passed' => $passedTests,
        'failed' => $failedTests,
        'success_rate' => round(($passedTests / $totalTests) * 100, 1)
    ],
    'results' => $testResults
];

if (!is_dir('tests/reports')) {
    mkdir('tests/reports', 0755, true);
}

file_put_contents(
    'tests/reports/complete_system_test_' . date('Y-m-d_H-i-s') . '.json',
    json_encode($reportData, JSON_PRETTY_PRINT)
);

echo "\n📄 Test report saved to: tests/reports/complete_system_test_" . date('Y-m-d_H-i-s') . ".json\n";
?>
