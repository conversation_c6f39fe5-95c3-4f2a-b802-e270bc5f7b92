<?php
/**
 * Enhanced Video Engine for Sutradhar 2070
 * Generates real videos using GD, FFmpeg, and Python integration
 */

class EnhancedVideoEngine {
    private $pythonPath;
    private $generatorScript;
    private $outputDir;
    private $tempDir;
    
    public function __construct() {
        $this->pythonPath = $this->findPython();
        $this->generatorScript = __DIR__ . '/real_video_generator.py';
        $this->outputDir = __DIR__ . '/../public/generated_videos';
        $this->tempDir = __DIR__ . '/../temp';
        
        // Create directories
        if (!is_dir($this->outputDir)) {
            mkdir($this->outputDir, 0755, true);
        }
        if (!is_dir($this->tempDir)) {
            mkdir($this->tempDir, 0755, true);
        }
    }
    
    /**
     * Generate real video based on prompt
     */
    public function generateEnhancedVideo($segments, $topic, $mood, $jobId, $moodCharacteristics = null) {
        echo "🎬 Enhanced Video Engine: Starting generation for job $jobId\n";
        echo "Topic: $topic, Mood: $mood\n";
        
        // Extract prompt from segments
        $prompt = $this->extractPromptFromSegments($segments);
        echo "Extracted prompt: $prompt\n";
        
        try {
            // Try Python-based generation first
            if ($this->checkPythonDependencies()) {
                echo "🐍 Using Python video generator...\n";
                $result = $this->generateWithPython($jobId, $mood, $topic, $prompt);
                if ($result['success']) {
                    return $result['video_file_path'];
                }
            }
            
            // Fallback to GD + FFmpeg
            echo "🎨 Using GD + FFmpeg fallback...\n";
            $result = $this->generateWithGDFFmpeg($jobId, $mood, $topic, $prompt);
            if ($result['success']) {
                return $result['video_file_path'];
            }
            
            // Final fallback
            echo "📹 Using simple fallback...\n";
            return $this->generateSimpleFallback($jobId, $mood, $topic);
            
        } catch (Exception $e) {
            echo "❌ Enhanced video generation failed: " . $e->getMessage() . "\n";
            return $this->generateSimpleFallback($jobId, $mood, $topic);
        }
    }
    
    /**
     * Extract meaningful prompt from segments
     */
    private function extractPromptFromSegments($segments) {
        if (empty($segments)) {
            return "Create a beautiful video";
        }
        
        $text = '';
        foreach ($segments as $segment) {
            if (isset($segment['text'])) {
                $text .= $segment['text'] . ' ';
            } elseif (isset($segment['content'])) {
                $text .= $segment['content'] . ' ';
            }
        }
        
        // Clean and limit text
        $text = trim($text);
        if (strlen($text) > 200) {
            $text = substr($text, 0, 200) . '...';
        }
        
        return $text ?: "Create a beautiful video";
    }
    
    /**
     * Generate video using Python
     */
    private function generateWithPython($jobId, $mood, $topic, $prompt) {
        try {
            $inspirationJson = json_encode(['cinematic', 'colorful']);
            $command = sprintf(
                '%s "%s" --job_id "%s" --mood "%s" --topic "%s" --prompt "%s" --inspiration \'%s\' 2>&1',
                $this->pythonPath,
                $this->generatorScript,
                escapeshellarg($jobId),
                escapeshellarg($mood),
                escapeshellarg($topic),
                escapeshellarg($prompt),
                $inspirationJson
            );
            
            echo "Executing: $command\n";
            $output = shell_exec($command);
            echo "Python output: $output\n";
            
            $lines = explode("\n", trim($output));
            $lastLine = end($lines);
            $result = json_decode($lastLine, true);
            
            if ($result && $result['success']) {
                return [
                    'success' => true,
                    'video_file_path' => $result['video_path'],
                    'thumbnail_path' => $result['thumbnail_path']
                ];
            }
            
            throw new Exception('Python generation failed');
            
        } catch (Exception $e) {
            echo "Python generation error: " . $e->getMessage() . "\n";
            return ['success' => false];
        }
    }
    
    /**
     * Generate video using GD and FFmpeg
     */
    private function generateWithGDFFmpeg($jobId, $mood, $topic, $prompt) {
        try {
            if (!extension_loaded('gd')) {
                throw new Exception('GD extension not available');
            }
            
            // Generate frames
            $frames = $this->generateGDFrames($mood, $topic, $prompt, $jobId);
            
            // Create video with FFmpeg
            $videoPath = $this->createVideoWithFFmpeg($frames, $jobId);
            
            // Generate thumbnail
            $thumbnailPath = $this->generateThumbnail($frames[0], $jobId);
            
            return [
                'success' => true,
                'video_file_path' => $videoPath,
                'thumbnail_path' => $thumbnailPath
            ];
            
        } catch (Exception $e) {
            echo "GD+FFmpeg generation error: " . $e->getMessage() . "\n";
            return ['success' => false];
        }
    }
    
    /**
     * Generate frames using GD
     */
    private function generateGDFrames($mood, $topic, $prompt, $jobId) {
        echo "🖼️ Generating frames with GD...\n";
        
        $frames = [];
        $totalFrames = 150; // 5 seconds at 30fps for demo
        $width = 1280;
        $height = 720;
        
        // Mood-based color schemes
        $moodColors = $this->getMoodColors($mood);
        
        for ($i = 0; $i < $totalFrames; $i++) {
            $progress = $i / $totalFrames;
            
            // Create frame
            $img = imagecreatetruecolor($width, $height);
            
            // Create animated background
            $this->createAnimatedBackground($img, $width, $height, $progress, $moodColors);
            
            // Add topic-specific elements
            $this->addTopicElements($img, $width, $height, $progress, $topic, $moodColors);
            
            // Add text overlay
            $this->addTextOverlay($img, $width, $height, $prompt, $progress);
            
            // Add mood effects
            $this->addMoodEffects($img, $width, $height, $progress, $mood);
            
            $frames[] = $img;
        }
        
        echo "✅ Generated " . count($frames) . " frames\n";
        return $frames;
    }
    
    /**
     * Get color scheme for mood
     */
    private function getMoodColors($mood) {
        $schemes = [
            'euphoric' => [[255, 215, 0], [255, 140, 0], [255, 69, 0]],
            'serene' => [[135, 206, 235], [176, 224, 230], [240, 248, 255]],
            'dramatic' => [[139, 0, 0], [220, 20, 60], [255, 0, 0]],
            'mysterious' => [[75, 0, 130], [138, 43, 226], [148, 0, 211]],
            'energetic' => [[255, 20, 147], [255, 105, 180], [255, 182, 193]],
            'contemplative' => [[119, 136, 153], [176, 196, 222], [230, 230, 250]],
            'adventurous' => [[34, 139, 34], [50, 205, 50], [144, 238, 144]],
            'romantic' => [[255, 192, 203], [255, 182, 193], [255, 228, 225]],
            'futuristic' => [[0, 255, 255], [0, 191, 255], [30, 144, 255]],
            'nostalgic' => [[210, 180, 140], [222, 184, 135], [245, 222, 179]]
        ];
        
        return $schemes[$mood] ?? [[128, 128, 128], [160, 160, 160], [192, 192, 192]];
    }
    
    /**
     * Create animated background
     */
    private function createAnimatedBackground($img, $width, $height, $progress, $colors) {
        // Gradient background with animation
        for ($y = 0; $y < $height; $y++) {
            $ratio = $y / $height;
            $waveOffset = sin($progress * 4 * M_PI + $y * 0.01) * 0.2;
            $colorRatio = ($ratio + $waveOffset) % 1;
            
            $r = intval($colors[0][0] + ($colors[1][0] - $colors[0][0]) * $colorRatio);
            $g = intval($colors[0][1] + ($colors[1][1] - $colors[0][1]) * $colorRatio);
            $b = intval($colors[0][2] + ($colors[1][2] - $colors[0][2]) * $colorRatio);
            
            $color = imagecolorallocate($img, $r, $g, $b);
            imageline($img, 0, $y, $width, $y, $color);
        }
        
        // Add animated particles
        for ($i = 0; $i < 20; $i++) {
            $x = ($width * 0.1 * $i + $progress * $width * 2) % $width;
            $y = $height * 0.3 + sin($progress * 6 * M_PI + $i) * $height * 0.3;
            $size = 5 + sin($progress * 8 * M_PI + $i) * 5;
            
            $alpha = intval(50 + 50 * sin($progress * 10 * M_PI + $i));
            $particleColor = imagecolorallocatealpha($img, 255, 255, 255, 127 - $alpha);
            
            imagefilledellipse($img, $x, $y, $size * 2, $size * 2, $particleColor);
        }
    }
    
    /**
     * Add topic-specific elements
     */
    private function addTopicElements($img, $width, $height, $progress, $topic, $colors) {
        switch ($topic) {
            case 'nature_wildlife':
                $this->addNatureElements($img, $width, $height, $progress);
                break;
            case 'urban_city':
                $this->addUrbanElements($img, $width, $height, $progress);
                break;
            case 'abstract_art':
                $this->addAbstractElements($img, $width, $height, $progress, $colors);
                break;
            case 'technology':
                $this->addTechElements($img, $width, $height, $progress);
                break;
            default:
                $this->addGenericElements($img, $width, $height, $progress, $colors);
        }
    }
    
    /**
     * Add nature elements
     */
    private function addNatureElements($img, $width, $height, $progress) {
        // Animated trees
        for ($i = 0; $i < 8; $i++) {
            $x = $width * 0.125 * $i + sin($progress * 3 * M_PI + $i) * 30;
            $y = $height * 0.8;
            
            // Tree trunk
            $brown = imagecolorallocate($img, 101, 67, 33);
            $trunkSway = sin($progress * 4 * M_PI + $i) * 5;
            imagefilledrectangle($img, $x - 8 + $trunkSway, $y - 80, $x + 8 + $trunkSway, $y, $brown);
            
            // Tree crown with animation
            $green = imagecolorallocate($img, 34, 139, 34);
            $crownSize = 50 + sin($progress * 6 * M_PI + $i) * 10;
            imagefilledellipse($img, $x + $trunkSway, $y - 60, $crownSize, $crownSize, $green);
        }
        
        // Flying birds
        for ($i = 0; $i < 5; $i++) {
            $x = ($width * $progress + $i * 200) % ($width + 100);
            $y = 100 + sin($progress * 8 * M_PI + $i) * 50;
            
            $black = imagecolorallocate($img, 0, 0, 0);
            // Simple bird shape
            imagearc($img, $x, $y, 20, 10, 0, 180, $black);
            imagearc($img, $x + 10, $y, 20, 10, 0, 180, $black);
        }
    }
    
    /**
     * Add urban elements
     */
    private function addUrbanElements($img, $width, $height, $progress) {
        // Animated city skyline
        for ($i = 0; $i < 12; $i++) {
            $x = $width * 0.083 * $i;
            $buildingHeight = 150 + ($i * 40) % 250;
            $y = $height - $buildingHeight;
            
            // Building
            $gray = imagecolorallocate($img, 60 + $i * 5, 60 + $i * 5, 60 + $i * 5);
            imagefilledrectangle($img, $x, $y, $x + 60, $height, $gray);
            
            // Animated windows
            $windowRows = intval($buildingHeight / 25);
            for ($row = 0; $row < $windowRows; $row++) {
                for ($col = 0; $col < 2; $col++) {
                    if (rand(0, 100) < 80) { // 80% chance of lit window
                        $windowX = $x + 10 + $col * 25;
                        $windowY = $y + 10 + $row * 25;
                        
                        // Animated brightness
                        $brightness = 150 + sin($progress * 6 * M_PI + $i + $row) * 100;
                        $yellow = imagecolorallocate($img, $brightness, $brightness, 100);
                        imagefilledrectangle($img, $windowX, $windowY, $windowX + 15, $windowY + 15, $yellow);
                    }
                }
            }
        }
    }
    
    /**
     * Add abstract elements
     */
    private function addAbstractElements($img, $width, $height, $progress, $colors) {
        // Rotating geometric shapes
        $centerX = $width / 2;
        $centerY = $height / 2;
        
        for ($i = 0; $i < 6; $i++) {
            $angle = $progress * 4 * M_PI + $i * M_PI / 3;
            $radius = 100 + sin($progress * 6 * M_PI + $i) * 50;
            
            $x = $centerX + $radius * cos($angle);
            $y = $centerY + $radius * sin($angle);
            
            $colorIndex = $i % count($colors);
            $shapeColor = imagecolorallocate($img, $colors[$colorIndex][0], $colors[$colorIndex][1], $colors[$colorIndex][2]);
            
            $size = 30 + sin($progress * 8 * M_PI + $i) * 15;
            imagefilledellipse($img, $x, $y, $size, $size, $shapeColor);
        }
    }
    
    /**
     * Add technology elements
     */
    private function addTechElements($img, $width, $height, $progress) {
        // Circuit-like patterns
        $cyan = imagecolorallocate($img, 0, 255, 255);
        $blue = imagecolorallocate($img, 0, 100, 255);
        
        // Grid lines
        for ($i = 0; $i < 20; $i++) {
            $x = $width * 0.05 * $i;
            $alpha = sin($progress * 4 * M_PI + $i) * 127 + 127;
            
            imageline($img, $x, 0, $x, $height, $cyan);
            
            $y = $height * 0.05 * $i;
            imageline($img, 0, $y, $width, $y, $blue);
        }
        
        // Moving data points
        for ($i = 0; $i < 15; $i++) {
            $x = ($width * $progress * 2 + $i * 100) % $width;
            $y = $height * 0.1 * $i + sin($progress * 6 * M_PI + $i) * 50;
            
            $size = 8 + sin($progress * 10 * M_PI + $i) * 4;
            imagefilledellipse($img, $x, $y, $size, $size, $cyan);
        }
    }
    
    /**
     * Add generic elements
     */
    private function addGenericElements($img, $width, $height, $progress, $colors) {
        // Simple animated shapes
        for ($i = 0; $i < 10; $i++) {
            $x = $width * 0.1 * $i + sin($progress * 4 * M_PI + $i) * 100;
            $y = $height * 0.5 + cos($progress * 6 * M_PI + $i) * 200;
            
            $colorIndex = $i % count($colors);
            $shapeColor = imagecolorallocate($img, $colors[$colorIndex][0], $colors[$colorIndex][1], $colors[$colorIndex][2]);
            
            $size = 20 + sin($progress * 8 * M_PI + $i) * 10;
            imagefilledellipse($img, $x, $y, $size, $size, $shapeColor);
        }
    }
    
    /**
     * Add text overlay
     */
    private function addTextOverlay($img, $width, $height, $prompt, $progress) {
        $white = imagecolorallocate($img, 255, 255, 255);
        $black = imagecolorallocate($img, 0, 0, 0);
        
        // Title text with animation
        $text = substr($prompt, 0, 60) . (strlen($prompt) > 60 ? '...' : '');
        $fontSize = 5;
        $textWidth = strlen($text) * imagefontwidth($fontSize);
        $x = ($width - $textWidth) / 2;
        $y = 50 + sin($progress * 4 * M_PI) * 20;
        
        // Text shadow
        imagestring($img, $fontSize, $x + 3, $y + 3, $text, $black);
        // Main text
        imagestring($img, $fontSize, $x, $y, $text, $white);
        
        // Subtitle
        $subtitle = "Sutradhar 2070 - AI Generated";
        $subWidth = strlen($subtitle) * imagefontwidth(3);
        $subX = ($width - $subWidth) / 2;
        $subY = $y + 30;
        
        imagestring($img, 3, $subX + 2, $subY + 2, $subtitle, $black);
        imagestring($img, 3, $subX, $subY, $subtitle, $white);
    }
    
    /**
     * Add mood-specific effects
     */
    private function addMoodEffects($img, $width, $height, $progress, $mood) {
        switch ($mood) {
            case 'dramatic':
                // Add lightning effect
                if (sin($progress * 20 * M_PI) > 0.8) {
                    $white = imagecolorallocate($img, 255, 255, 255);
                    $x = rand(0, $width);
                    imageline($img, $x, 0, $x + rand(-50, 50), $height, $white);
                }
                break;
                
            case 'mysterious':
                // Add fog effect
                for ($i = 0; $i < 100; $i++) {
                    $x = rand(0, $width);
                    $y = $height * 0.7 + rand(0, $height * 0.3);
                    $alpha = rand(20, 60);
                    $fog = imagecolorallocatealpha($img, 200, 200, 200, 127 - $alpha);
                    imagefilledellipse($img, $x, $y, 30, 15, $fog);
                }
                break;
                
            case 'energetic':
                // Add sparkle effects
                for ($i = 0; $i < 30; $i++) {
                    $x = rand(0, $width);
                    $y = rand(0, $height);
                    $sparkle = imagecolorallocate($img, 255, 255, 0);
                    imagesetpixel($img, $x, $y, $sparkle);
                    imagesetpixel($img, $x+1, $y, $sparkle);
                    imagesetpixel($img, $x, $y+1, $sparkle);
                    imagesetpixel($img, $x-1, $y, $sparkle);
                    imagesetpixel($img, $x, $y-1, $sparkle);
                }
                break;
        }
    }
    
    /**
     * Create video using FFmpeg
     */
    private function createVideoWithFFmpeg($frames, $jobId) {
        echo "🎬 Creating video with FFmpeg...\n";
        
        // Save frames
        $frameDir = $this->tempDir . "/frames_$jobId";
        if (!is_dir($frameDir)) {
            mkdir($frameDir, 0755, true);
        }
        
        foreach ($frames as $i => $frame) {
            $framePath = sprintf("%s/frame_%06d.png", $frameDir, $i);
            imagepng($frame, $framePath);
            imagedestroy($frame);
        }
        
        // Find FFmpeg
        $ffmpegPath = $this->findFFmpeg();
        if (!$ffmpegPath) {
            throw new Exception('FFmpeg not found');
        }
        
        // Create video
        $videoPath = $this->outputDir . "/enhanced_$jobId.mp4";
        $command = sprintf(
            '%s -framerate 30 -i "%s/frame_%%06d.png" -c:v libx264 -pix_fmt yuv420p -t 5 -y "%s" 2>&1',
            $ffmpegPath,
            $frameDir,
            $videoPath
        );
        
        echo "FFmpeg command: $command\n";
        $output = shell_exec($command);
        echo "FFmpeg output: $output\n";
        
        // Clean up frames
        $this->cleanupDirectory($frameDir);
        
        if (file_exists($videoPath) && filesize($videoPath) > 1000) {
            echo "✅ Enhanced video created successfully\n";
            return $videoPath;
        } else {
            throw new Exception('FFmpeg video creation failed');
        }
    }
    
    /**
     * Generate thumbnail
     */
    private function generateThumbnail($firstFrame, $jobId) {
        $thumbWidth = 320;
        $thumbHeight = 180;
        $thumbnail = imagecreatetruecolor($thumbWidth, $thumbHeight);
        
        imagecopyresampled(
            $thumbnail, $firstFrame,
            0, 0, 0, 0,
            $thumbWidth, $thumbHeight,
            imagesx($firstFrame), imagesy($firstFrame)
        );
        
        // Add play button
        $white = imagecolorallocate($thumbnail, 255, 255, 255);
        $black = imagecolorallocate($thumbnail, 0, 0, 0);
        
        $centerX = $thumbWidth / 2;
        $centerY = $thumbHeight / 2;
        
        imagefilledellipse($thumbnail, $centerX, $centerY, 60, 60, $black);
        imagefilledellipse($thumbnail, $centerX, $centerY, 56, 56, $white);
        
        $triangle = [
            $centerX - 10, $centerY - 15,
            $centerX - 10, $centerY + 15,
            $centerX + 15, $centerY
        ];
        imagefilledpolygon($thumbnail, $triangle, 3, $black);
        
        $thumbnailPath = $this->outputDir . "/thumb_enhanced_$jobId.jpg";
        imagejpeg($thumbnail, $thumbnailPath, 85);
        imagedestroy($thumbnail);
        
        return $thumbnailPath;
    }
    
    /**
     * Simple fallback
     */
    private function generateSimpleFallback($jobId, $mood, $topic) {
        echo "📹 Generating simple fallback...\n";
        
        // Copy existing demo video
        $demoVideos = [
            __DIR__ . '/../public/final_test_video.mp4',
            __DIR__ . '/../public/elephant_jungle_video.mp4',
            __DIR__ . '/../public/real_elephant_video.mp4'
        ];
        
        $videoPath = $this->outputDir . "/fallback_$jobId.mp4";
        
        foreach ($demoVideos as $demo) {
            if (file_exists($demo)) {
                copy($demo, $videoPath);
                echo "✅ Copied demo video: $demo\n";
                return $videoPath;
            }
        }
        
        // Create placeholder if no demo available
        file_put_contents($videoPath, "Fallback video for $jobId");
        return $videoPath;
    }
    
    /**
     * Check Python dependencies
     */
    private function checkPythonDependencies() {
        if (!$this->pythonPath) return false;
        
        $packages = ['PIL', 'numpy', 'moviepy'];
        foreach ($packages as $package) {
            $result = shell_exec(sprintf('%s -c "import %s" 2>&1', $this->pythonPath, $package));
            if ($result && strpos($result, 'Error') !== false) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Find Python executable
     */
    private function findPython() {
        $commands = ['python3', 'python', 'py'];
        foreach ($commands as $cmd) {
            $result = shell_exec("$cmd --version 2>&1");
            if ($result && strpos($result, 'Python') !== false) {
                return $cmd;
            }
        }
        return null;
    }
    
    /**
     * Find FFmpeg executable
     */
    private function findFFmpeg() {
        $commands = ['ffmpeg', 'ffmpeg.exe'];
        foreach ($commands as $cmd) {
            $result = shell_exec("$cmd -version 2>&1");
            if ($result && strpos($result, 'ffmpeg version') !== false) {
                return $cmd;
            }
        }
        return null;
    }
    
    /**
     * Clean up directory
     */
    private function cleanupDirectory($dir) {
        if (is_dir($dir)) {
            $files = glob($dir . '/*');
            foreach ($files as $file) {
                if (is_file($file)) unlink($file);
            }
            rmdir($dir);
        }
    }
}
?>
