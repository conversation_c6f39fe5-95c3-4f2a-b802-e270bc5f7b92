<?php
/**
 * Unified API Router for Sutradhar 2070
 * Routes requests to appropriate API handlers
 */

// Set error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Include API handlers
require_once '../core/api_auth.php';
require_once '../core/api_generation.php';
require_once '../core/api_user.php';
require_once '../core/api_payments.php';
require_once '../core/api_tools.php';

class APIRouter {
    private $endpoint;
    private $method;

    public function __construct() {
        $this->endpoint = $_GET['endpoint'] ?? '';
        $this->method = $_SERVER['REQUEST_METHOD'];
    }

    public function route() {
        try {
            // Route to appropriate API handler based on endpoint prefix
            if (strpos($this->endpoint, 'auth/') === 0) {
                $api = new AuthAPI();
                $api->handleRequest();
            } elseif (strpos($this->endpoint, 'generate') === 0) {
                $api = new GenerationAPI();
                $api->handleRequest();
            } elseif (strpos($this->endpoint, 'user/') === 0) {
                $api = new UserAPI();
                $api->handleRequest();
            } elseif (strpos($this->endpoint, 'payments/') === 0) {
                $api = new PaymentAPI();
                $api->handleRequest();
            } elseif (strpos($this->endpoint, 'tools/') === 0) {
                $api = new ToolsAPI();
                $api->handleRequest();
            } elseif (strpos($this->endpoint, 'dashboard/') === 0) {
                $this->handleDashboardAPI();
            } else {
                $this->sendError('API endpoint not found', 404);
            }
        } catch (Exception $e) {
            error_log("API Error: " . $e->getMessage());
            $this->sendError('Internal server error', 500);
        }
    }

    /**
     * Handle dashboard-specific API endpoints
     */
    private function handleDashboardAPI() {
        require_once '../core/api_base.php';

        $dashboardAPI = new class extends APIBase {
            public function handleRequest() {
                $path = $this->requestPath;
                $method = $this->requestMethod;

                switch ($path) {
                    case 'dashboard/stats':
                        if ($method === 'GET') $this->getDashboardStats();
                        break;
                    case 'dashboard/history':
                        if ($method === 'GET') $this->getGenerationHistory();
                        break;
                    case 'dashboard/analytics':
                        if ($method === 'GET') $this->getAnalytics();
                        break;
                    default:
                        $this->sendError('Dashboard endpoint not found', 404);
                }
            }

            private function getDashboardStats() {
                $this->requireAuth();

                $userId = $this->currentUser['user_id'];

                // Get user stats
                $totalGenerations = $this->db->count('generation_jobs', ['user_id' => $userId]);
                $completedGenerations = $this->db->count('generation_jobs', [
                    'user_id' => $userId,
                    'status' => 'completed'
                ]);
                $pendingGenerations = $this->db->count('generation_jobs', [
                    'user_id' => $userId,
                    'status' => ['pending', 'analyzing', 'generating', 'processing']
                ]);

                // Get credit stats
                $credits = $this->db->selectOne('user_credit_balances', ['user_id' => $userId]);

                // Get recent generations
                $recentGenerations = $this->db->select('generation_jobs',
                    ['user_id' => $userId],
                    'ORDER BY created_at DESC LIMIT 5'
                );

                // Get subscription info
                $subscription = $this->db->selectOne('user_subscriptions', [
                    'user_id' => $userId,
                    'status' => 'active'
                ]);

                $plan = null;
                if ($subscription) {
                    $plan = $this->db->selectOne('subscription_plans', ['plan_id' => $subscription['plan_id']]);
                }

                $this->sendSuccess([
                    'generation_stats' => [
                        'total' => intval($totalGenerations),
                        'completed' => intval($completedGenerations),
                        'pending' => intval($pendingGenerations),
                        'success_rate' => $totalGenerations > 0 ? round(($completedGenerations / $totalGenerations) * 100, 1) : 0
                    ],
                    'credit_stats' => [
                        'total' => intval($credits['total_credits'] ?? 0),
                        'used' => intval($credits['used_credits'] ?? 0),
                        'available' => intval($credits['available_credits'] ?? 0)
                    ],
                    'subscription' => $subscription ? [
                        'plan_name' => $plan['name'] ?? 'Unknown',
                        'plan_id' => $subscription['plan_id'],
                        'status' => $subscription['status'],
                        'current_period_end' => $subscription['current_period_end']
                    ] : null,
                    'recent_generations' => array_map(function($gen) {
                        return [
                            'job_id' => $gen['job_id'],
                            'mood' => $gen['mood'],
                            'topic' => $gen['topic'],
                            'status' => $gen['status'],
                            'progress' => intval($gen['progress']),
                            'created_at' => $gen['created_at']
                        ];
                    }, $recentGenerations)
                ]);
            }

            private function getGenerationHistory() {
                $this->requireAuth();

                $page = intval($this->requestData['page'] ?? 1);
                $limit = min(intval($this->requestData['limit'] ?? 10), 50);
                $status = $this->requestData['status'] ?? null;

                $offset = ($page - 1) * $limit;

                $conditions = ['user_id' => $this->currentUser['user_id']];
                if ($status) {
                    $conditions['status'] = $status;
                }

                $generations = $this->db->select('generation_jobs', $conditions,
                    "ORDER BY created_at DESC LIMIT $limit OFFSET $offset");

                $total = $this->db->count('generation_jobs', $conditions);

                $formattedGenerations = array_map(function($gen) {
                    return [
                        'job_id' => $gen['job_id'],
                        'mood' => $gen['mood'],
                        'topic' => $gen['topic'],
                        'status' => $gen['status'],
                        'progress' => intval($gen['progress']),
                        'video_quality' => $gen['video_quality'],
                        'credits_consumed' => intval($gen['credits_consumed']),
                        'video_url' => $gen['video_url'],
                        'created_at' => $gen['created_at'],
                        'completed_at' => $gen['completed_at']
                    ];
                }, $generations);

                $this->sendSuccess([
                    'generations' => $formattedGenerations,
                    'pagination' => [
                        'page' => $page,
                        'limit' => $limit,
                        'total' => $total,
                        'pages' => ceil($total / $limit)
                    ]
                ]);
            }

            private function getAnalytics() {
                $this->requireAuth();

                $userId = $this->currentUser['user_id'];

                // Get monthly generation stats
                $monthlyStats = $this->db->query(
                    "SELECT DATE_FORMAT(created_at, '%Y-%m') as month,
                            COUNT(*) as total,
                            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                            SUM(credits_consumed) as credits_used
                     FROM generation_jobs
                     WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
                     GROUP BY DATE_FORMAT(created_at, '%Y-%m')
                     ORDER BY month DESC",
                    [$userId]
                );

                // Get mood distribution
                $moodStats = $this->db->query(
                    "SELECT mood, COUNT(*) as count
                     FROM generation_jobs
                     WHERE user_id = ? AND status = 'completed'
                     GROUP BY mood
                     ORDER BY count DESC
                     LIMIT 10",
                    [$userId]
                );

                // Get topic distribution
                $topicStats = $this->db->query(
                    "SELECT topic, COUNT(*) as count
                     FROM generation_jobs
                     WHERE user_id = ? AND status = 'completed'
                     GROUP BY topic
                     ORDER BY count DESC
                     LIMIT 10",
                    [$userId]
                );

                $this->sendSuccess([
                    'monthly_stats' => $monthlyStats,
                    'mood_distribution' => $moodStats,
                    'topic_distribution' => $topicStats
                ]);
            }
        };

        $dashboardAPI->handleRequest();
    }

    /**
     * Send error response
     */
    private function sendError($message, $statusCode = 400) {
        http_response_code($statusCode);
        echo json_encode([
            'success' => false,
            'error' => $message
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// Initialize and route the request
$router = new APIRouter();
$router->route();
?>
