/* Custom styles for Sutradhar Engine */

/* Radio button styling for flow options */
.flow-option input[type="radio"]:checked + div {
    border-color: #FF9933;
    background-color: #FFF7ED;
    box-shadow: 0 0 0 3px rgba(255, 153, 51, 0.1);
}

.voice-option input[type="radio"]:checked + div {
    border-color: #FF9933;
    background-color: #FFF7ED;
    box-shadow: 0 0 0 3px rgba(255, 153, 51, 0.1);
}

/* Progress step styling */
.step-active {
    background-color: #FF9933 !important;
    color: white;
}

.step-complete {
    background-color: #138808 !important;
    color: white;
}

/* Content sections */
.content-section.hidden {
    display: none;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #FF9933;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #e6851a;
}

/* Animation for cards */
.flow-option div,
.voice-option div {
    transition: all 0.3s ease;
}

.flow-option:hover div,
.voice-option:hover div {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Loading animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading {
    animation: spin 1s linear infinite;
}

/* Output preview styling */
.output-item {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
}

.output-item h4 {
    color: #000080;
    font-weight: 600;
    margin-bottom: 8px;
}

.download-btn {
    background-color: #138808;
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    display: inline-block;
    margin-right: 8px;
    margin-top: 8px;
    transition: background-color 0.3s ease;
}

.download-btn:hover {
    background-color: #0f6b06;
}

/* Media player styling */
audio, video {
    width: 100%;
    border-radius: 8px;
    margin: 8px 0;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .grid-cols-3 {
        grid-template-columns: 1fr;
    }
    
    .lg:grid-cols-3 {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Error and success messages */
.alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 16px;
}

.alert-error {
    background-color: #fee2e2;
    border: 1px solid #fecaca;
    color: #dc2626;
}

.alert-success {
    background-color: #dcfce7;
    border: 1px solid #bbf7d0;
    color: #16a34a;
}

.alert-info {
    background-color: #dbeafe;
    border: 1px solid #bfdbfe;
    color: #2563eb;
}

/* Form validation */
.form-error {
    border-color: #dc2626 !important;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1) !important;
}

.error-message {
    color: #dc2626;
    font-size: 0.875rem;
    margin-top: 4px;
}

/* Tooltip styling */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #000080;
    color: white;
    text-align: center;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 0.875rem;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* File upload styling */
input[type="file"] {
    border: 2px dashed #d1d5db;
    background-color: #f9fafb;
    transition: all 0.3s ease;
}

input[type="file"]:hover {
    border-color: #FF9933;
    background-color: #fff7ed;
}

/* Button loading state */
.btn-loading {
    opacity: 0.7;
    cursor: not-allowed;
}

.btn-loading::after {
    content: "";
    width: 16px;
    height: 16px;
    margin-left: 8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    display: inline-block;
    animation: spin 1s linear infinite;
}

/* Custom select styling */
select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 8px center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: 40px;
    appearance: none;
}

/* Progress bar animation */
.progress-bar-animated {
    background: linear-gradient(45deg, 
        rgba(255,255,255,0.2) 25%, 
        transparent 25%, 
        transparent 50%, 
        rgba(255,255,255,0.2) 50%, 
        rgba(255,255,255,0.2) 75%, 
        transparent 75%, 
        transparent);
    background-size: 20px 20px;
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% { background-position: 0 0; }
    100% { background-position: 20px 0; }
}
