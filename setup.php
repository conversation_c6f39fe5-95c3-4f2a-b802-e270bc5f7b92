<?php
/**
 * Sutradhar 2070 Setup Script
 * Initializes database, creates configuration files, and sets up the system
 */

echo "🎭 Sutradhar 2070 Setup Script\n";
echo "=" . str_repeat("=", 40) . "\n\n";

// Check PHP version
if (version_compare(PHP_VERSION, '8.0.0') < 0) {
    die("❌ PHP 8.0 or higher is required. Current version: " . PHP_VERSION . "\n");
}

echo "✅ PHP Version: " . PHP_VERSION . "\n";

// Check required extensions
$requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'curl', 'openssl'];
$missingExtensions = [];

foreach ($requiredExtensions as $ext) {
    if (!extension_loaded($ext)) {
        $missingExtensions[] = $ext;
    }
}

if (!empty($missingExtensions)) {
    die("❌ Missing required PHP extensions: " . implode(', ', $missingExtensions) . "\n");
}

echo "✅ All required PHP extensions are loaded\n\n";

// Create directories
$directories = [
    'config',
    'logs',
    'uploads',
    'videos',
    'tests/reports'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "📁 Created directory: $dir\n";
    }
}

// Create default configuration files
createDatabaseConfig();
createEmailConfig();
createPaymentConfig();
createHuggingFaceConfig();

// Initialize database
initializeDatabase();

echo "\n🎉 Setup completed successfully!\n\n";
echo "📋 Next Steps:\n";
echo "1. Configure your database connection in config/database.json\n";
echo "2. Set up email settings in config/email_config.json\n";
echo "3. Configure payment providers in config/payment_config.json\n";
echo "4. Add your Hugging Face API key in config/huggingface_config.json\n";
echo "5. Start your web server and visit the application\n\n";
echo "🌐 Access your application at: http://localhost/public/\n";

function createDatabaseConfig() {
    $configFile = 'config/database.json';
    
    if (!file_exists($configFile)) {
        $config = [
            'host' => 'localhost',
            'database' => 'sutradhar2070',
            'username' => 'root',
            'password' => '',
            'charset' => 'utf8mb4',
            'options' => [
                'PDO::ATTR_ERRMODE' => 'PDO::ERRMODE_EXCEPTION',
                'PDO::ATTR_DEFAULT_FETCH_MODE' => 'PDO::FETCH_ASSOC',
                'PDO::ATTR_EMULATE_PREPARES' => false
            ]
        ];
        
        file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT));
        echo "📄 Created database configuration: $configFile\n";
    }
}

function createEmailConfig() {
    $configFile = 'config/email_config.json';
    
    if (!file_exists($configFile)) {
        $config = [
            'smtp_host' => 'smtp.gmail.com',
            'smtp_port' => 587,
            'smtp_username' => '<EMAIL>',
            'smtp_password' => 'your-app-password',
            'smtp_encryption' => 'tls',
            'from_email' => '<EMAIL>',
            'from_name' => 'Sutradhar 2070',
            'templates' => [
                'verification' => 'templates/email_verification.html',
                'password_reset' => 'templates/password_reset.html',
                'payment_confirmation' => 'templates/payment_confirmation.html'
            ]
        ];
        
        file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT));
        echo "📄 Created email configuration: $configFile\n";
    }
}

function createPaymentConfig() {
    $configFile = 'config/payment_config.json';
    
    if (!file_exists($configFile)) {
        $config = [
            'demo_mode' => true,
            'stripe' => [
                'publishable_key' => 'pk_test_your_stripe_publishable_key',
                'secret_key' => 'sk_test_your_stripe_secret_key',
                'webhook_secret' => 'whsec_your_webhook_secret'
            ],
            'paypal' => [
                'client_id' => 'your_paypal_client_id',
                'client_secret' => 'your_paypal_client_secret',
                'mode' => 'sandbox'
            ],
            'return_url' => 'https://sutradhar2070.com/payment/return',
            'cancel_url' => 'https://sutradhar2070.com/payment/cancel'
        ];
        
        file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT));
        echo "📄 Created payment configuration: $configFile\n";
    }
}

function createHuggingFaceConfig() {
    $configFile = 'config/huggingface_config.json';
    
    if (!file_exists($configFile)) {
        $config = [
            'api_key' => 'your_huggingface_api_key_here',
            'models' => [
                'text_to_video' => 'stabilityai/stable-video-diffusion-img2vid-xt',
                'text_to_image' => 'runwayml/stable-diffusion-v1-5',
                'image_to_video' => 'stabilityai/stable-video-diffusion-img2vid'
            ],
            'endpoints' => [
                'inference' => 'https://api-inference.huggingface.co/models/',
                'hosted_inference' => 'https://api-inference.huggingface.co/pipeline/'
            ],
            'settings' => [
                'max_retries' => 3,
                'timeout' => 300,
                'video_length' => 30,
                'fps' => 24,
                'resolution' => '1024x576'
            ]
        ];
        
        file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT));
        echo "📄 Created Hugging Face configuration: $configFile\n";
    }
}

function initializeDatabase() {
    echo "\n🗄️ Initializing database...\n";
    
    try {
        require_once 'core/database_manager.php';
        $db = new DatabaseManager();
        
        echo "✅ Database connection successful\n";
        echo "✅ All tables initialized\n";
        
        // Create default admin user
        createDefaultUser($db);
        
    } catch (Exception $e) {
        echo "❌ Database initialization failed: " . $e->getMessage() . "\n";
        echo "💡 Please check your database configuration in config/database.json\n";
    }
}

function createDefaultUser($db) {
    // Check if admin user exists
    $adminExists = $db->selectOne('users', ['email' => '<EMAIL>']);
    
    if (!$adminExists) {
        require_once 'core/user_authentication.php';
        $auth = new UserAuthentication();
        
        $result = $auth->registerUser(
            '<EMAIL>',
            'Admin123!',
            'Admin',
            'User'
        );
        
        if ($result['success']) {
            // Verify the admin user automatically
            $db->update('users', [
                'email_verified' => 1,
                'email_verified_at' => date('Y-m-d H:i:s')
            ], ['email' => '<EMAIL>']);
            
            // Give admin user some initial credits
            require_once 'core/credit_system.php';
            $creditSystem = new CreditSystem();
            $creditSystem->addCredits($result['user_id'], 1000, 'setup', 'Initial admin credits');
            
            echo "👤 Created admin user: <EMAIL> (password: Admin123!)\n";
            echo "💎 Added 1000 initial credits\n";
        }
    }
}
?>
