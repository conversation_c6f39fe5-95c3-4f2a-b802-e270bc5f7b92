<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sutradhar 2070 - Mood-Based Video Generation Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'cyber-blue': '#00D4FF',
                        'cyber-purple': '#8B5CF6',
                        'cyber-pink': '#F472B6',
                        'cyber-green': '#10B981',
                        'cyber-orange': '#F59E0B',
                        'dark-bg': '#0F0F23',
                        'dark-card': '#1A1A2E'
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #0F0F23 0%, #1A1A2E 50%, #16213E 100%);
            font-family: 'Exo 2', sans-serif;
            color: white;
        }

        .glass-morphism {
            background: rgba(26, 26, 46, 0.7);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 212, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .cyber-button {
            background: linear-gradient(45deg, #00D4FF, #8B5CF6);
            border: 1px solid #00D4FF;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .cyber-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.8);
        }

        .demo-card {
            background: rgba(26, 26, 46, 0.8);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            transition: all 0.4s ease;
            cursor: pointer;
        }

        .demo-card:hover {
            transform: translateY(-5px);
            border-color: #00D4FF;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 212, 255, 0.5);
        }

        .hologram-text {
            background: linear-gradient(45deg, #00D4FF, #8B5CF6, #F472B6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: hologram 3s ease-in-out infinite;
        }

        @keyframes hologram {
            0%, 100% { opacity: 0.8; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
        }

        .floating-element {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .stats-counter {
            font-size: 2rem;
            font-weight: bold;
            color: #00D4FF;
        }

        .progress-ring {
            transform: rotate(-90deg);
        }

        .progress-ring-circle {
            stroke-dasharray: 251.2;
            stroke-dashoffset: 251.2;
            transition: stroke-dashoffset 0.5s ease-in-out;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="glass-morphism border-b border-cyber-blue/30 sticky top-0 z-50">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-cyber-blue to-cyber-purple rounded-full flex items-center justify-center floating-element">
                        <span class="text-white font-bold text-xl">🎭</span>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold hologram-text">SUTRADHAR 2070</h1>
                        <p class="text-cyber-blue text-sm">Mood-Based Video Generation Demo</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="w-3 h-3 bg-cyber-green rounded-full animate-pulse"></div>
                    <span class="text-cyber-green text-sm font-semibold">DEMO MODE ACTIVE</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="container mx-auto px-6 py-16">
        <div class="text-center mb-16">
            <h2 class="text-6xl font-bold mb-6 hologram-text">
                NEXT-GEN VIDEO SYNTHESIS
            </h2>
            <p class="text-xl text-gray-300 mb-8 max-w-4xl mx-auto">
                Experience the future of AI-powered video generation with our revolutionary mood-based system. 
                Select from 30 distinct emotional moods, choose your topic and visual style, then watch as our 
                neural networks create stunning 30-second videos tailored to your vision.
            </p>
            <div class="flex justify-center space-x-4">
                <a href="mood_video_generator.html" class="cyber-button px-8 py-4 rounded-lg font-bold text-lg">
                    🚀 START CREATING
                </a>
                <button onclick="scrollToDemo()" class="border border-cyber-blue text-cyber-blue px-8 py-4 rounded-lg font-bold text-lg hover:bg-cyber-blue hover:text-dark-bg transition-all">
                    📺 VIEW DEMO
                </button>
            </div>
        </div>

        <!-- Stats Section -->
        <div class="grid md:grid-cols-4 gap-8 mb-16">
            <div class="text-center">
                <div class="stats-counter" data-target="30">0</div>
                <p class="text-gray-400">Emotional Moods</p>
            </div>
            <div class="text-center">
                <div class="stats-counter" data-target="25">0</div>
                <p class="text-gray-400">AI-Generated Prompts</p>
            </div>
            <div class="text-center">
                <div class="stats-counter" data-target="30">0</div>
                <p class="text-gray-400">Second Videos</p>
            </div>
            <div class="text-center">
                <div class="stats-counter" data-target="100">0</div>
                <p class="text-gray-400">% Efficiency</p>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="container mx-auto px-6 py-16">
        <h3 class="text-4xl font-bold text-center mb-12 hologram-text">REVOLUTIONARY FEATURES</h3>
        
        <div class="feature-grid">
            <!-- Mood Database -->
            <div class="demo-card p-8 rounded-lg">
                <div class="text-4xl mb-4">🧠</div>
                <h4 class="text-xl font-semibold mb-4 text-cyber-blue">30 Emotional Moods</h4>
                <p class="text-gray-300 mb-4">
                    Choose from 30 carefully crafted emotional moods across 6 categories: 
                    Positive Energy, Calm & Peaceful, Dramatic & Intense, Mysterious & Dark, 
                    Romantic & Emotional, and Futuristic & Tech.
                </p>
                <div class="flex flex-wrap gap-2">
                    <span class="text-xs bg-cyber-green/20 text-cyber-green px-2 py-1 rounded">Euphoric</span>
                    <span class="text-xs bg-cyber-blue/20 text-cyber-blue px-2 py-1 rounded">Serene</span>
                    <span class="text-xs bg-cyber-purple/20 text-cyber-purple px-2 py-1 rounded">Dramatic</span>
                    <span class="text-xs bg-cyber-pink/20 text-cyber-pink px-2 py-1 rounded">Mysterious</span>
                </div>
            </div>

            <!-- Intelligent Prompts -->
            <div class="demo-card p-8 rounded-lg">
                <div class="text-4xl mb-4">✨</div>
                <h4 class="text-xl font-semibold mb-4 text-cyber-purple">Intelligent Prompt Generation</h4>
                <p class="text-gray-300 mb-4">
                    Our AI generates 20-30 unique prompts based on your mood, topic, image type, 
                    and inspiration. Each prompt is crafted using advanced algorithms that understand 
                    emotional context and visual storytelling.
                </p>
                <div class="bg-dark-bg/50 p-3 rounded text-sm text-gray-400">
                    "Create a euphoric scene featuring nature & wildlife with cinematic style..."
                </div>
            </div>

            <!-- Optimized Pipeline -->
            <div class="demo-card p-8 rounded-lg">
                <div class="text-4xl mb-4">⚡</div>
                <h4 class="text-xl font-semibold mb-4 text-cyber-orange">Optimized Generation</h4>
                <p class="text-gray-300 mb-4">
                    Lightning-fast video generation using parallel processing, intelligent caching, 
                    and optimized FFmpeg pipelines. Generate professional-quality 30-second videos 
                    in minutes, not hours.
                </p>
                <div class="flex items-center space-x-2">
                    <div class="w-16 h-16 relative">
                        <svg class="progress-ring w-16 h-16">
                            <circle class="progress-ring-circle stroke-cyber-orange" 
                                    stroke-width="4" fill="transparent" r="40" cx="32" cy="32"/>
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center text-sm font-bold">85%</div>
                    </div>
                    <span class="text-sm text-gray-400">Faster than traditional methods</span>
                </div>
            </div>

            <!-- Smart Caching -->
            <div class="demo-card p-8 rounded-lg">
                <div class="text-4xl mb-4">🧮</div>
                <h4 class="text-xl font-semibold mb-4 text-cyber-pink">Smart Caching System</h4>
                <p class="text-gray-300 mb-4">
                    Advanced caching system that learns from your preferences and reuses 
                    generated assets intelligently. Reduces generation time by up to 70% 
                    for similar content requests.
                </p>
                <div class="grid grid-cols-2 gap-2 text-xs">
                    <div class="bg-cyber-pink/20 p-2 rounded text-center">Prompts Cached</div>
                    <div class="bg-cyber-pink/20 p-2 rounded text-center">Assets Reused</div>
                </div>
            </div>

            <!-- Real-time Progress -->
            <div class="demo-card p-8 rounded-lg">
                <div class="text-4xl mb-4">📊</div>
                <h4 class="text-xl font-semibold mb-4 text-cyber-green">Real-time Progress</h4>
                <p class="text-gray-300 mb-4">
                    Watch your video come to life with detailed progress tracking, 
                    estimated completion times, and visual indicators for each generation step.
                </p>
                <div class="space-y-2">
                    <div class="flex justify-between text-xs">
                        <span>Mood Analysis</span>
                        <span class="text-cyber-green">✓ Complete</span>
                    </div>
                    <div class="flex justify-between text-xs">
                        <span>Prompt Generation</span>
                        <span class="text-cyber-green">✓ Complete</span>
                    </div>
                    <div class="flex justify-between text-xs">
                        <span>Video Synthesis</span>
                        <span class="text-cyber-orange">⚡ Processing</span>
                    </div>
                </div>
            </div>

            <!-- Quality Assurance -->
            <div class="demo-card p-8 rounded-lg">
                <div class="text-4xl mb-4">🎯</div>
                <h4 class="text-xl font-semibold mb-4 text-cyber-blue">Quality Assurance</h4>
                <p class="text-gray-300 mb-4">
                    Automated quality checks ensure every generated video meets our high standards. 
                    Audio synchronization, visual coherence, and content validation are performed 
                    before final output.
                </p>
                <div class="flex items-center space-x-2 text-sm">
                    <div class="w-2 h-2 bg-cyber-green rounded-full"></div>
                    <span>Audio Sync: Perfect</span>
                </div>
                <div class="flex items-center space-x-2 text-sm">
                    <div class="w-2 h-2 bg-cyber-green rounded-full"></div>
                    <span>Visual Quality: HD</span>
                </div>
                <div class="flex items-center space-x-2 text-sm">
                    <div class="w-2 h-2 bg-cyber-green rounded-full"></div>
                    <span>Content Validation: Passed</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Demo Video Section -->
    <section id="demo" class="container mx-auto px-6 py-16">
        <div class="glass-morphism p-8 rounded-lg">
            <h3 class="text-3xl font-bold text-center mb-8 hologram-text">EXPERIENCE THE MAGIC</h3>
            <div class="grid md:grid-cols-2 gap-8 items-center">
                <div>
                    <h4 class="text-xl font-semibold mb-4 text-cyber-blue">See It In Action</h4>
                    <p class="text-gray-300 mb-6">
                        Watch how our mood-based video generation system transforms a simple idea 
                        into a stunning visual narrative. From mood selection to final video output, 
                        experience the seamless workflow that makes professional video creation accessible to everyone.
                    </p>
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-cyber-blue rounded-full flex items-center justify-center text-sm font-bold">1</div>
                            <span>Select your emotional mood</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-cyber-purple rounded-full flex items-center justify-center text-sm font-bold">2</div>
                            <span>Choose topic and visual style</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-cyber-pink rounded-full flex items-center justify-center text-sm font-bold">3</div>
                            <span>Add your inspiration</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-cyber-green rounded-full flex items-center justify-center text-sm font-bold">4</div>
                            <span>Generate and enjoy your video</span>
                        </div>
                    </div>
                </div>
                <div class="bg-dark-bg/50 rounded-lg p-8 text-center">
                    <div class="text-6xl mb-4">🎬</div>
                    <p class="text-gray-400 mb-4">Demo Video Coming Soon</p>
                    <p class="text-sm text-gray-500">
                        Interactive demo showcasing the complete mood-based video generation workflow
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="container mx-auto px-6 py-16 text-center">
        <div class="glass-morphism p-12 rounded-lg">
            <h3 class="text-4xl font-bold mb-6 hologram-text">READY TO CREATE?</h3>
            <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                Join the future of video creation. Start generating mood-based videos that captivate, 
                inspire, and tell your story like never before.
            </p>
            <a href="mood_video_generator.html" class="cyber-button px-12 py-4 rounded-lg font-bold text-xl">
                🚀 START YOUR JOURNEY
            </a>
        </div>
    </section>

    <script>
        // Animate stats counters
        function animateCounters() {
            const counters = document.querySelectorAll('.stats-counter');
            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-target'));
                const duration = 2000;
                const step = target / (duration / 16);
                let current = 0;
                
                const timer = setInterval(() => {
                    current += step;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    counter.textContent = Math.floor(current);
                }, 16);
            });
        }

        // Smooth scroll to demo section
        function scrollToDemo() {
            document.getElementById('demo').scrollIntoView({ behavior: 'smooth' });
        }

        // Initialize animations when page loads
        document.addEventListener('DOMContentLoaded', () => {
            // Animate counters when they come into view
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        animateCounters();
                        observer.disconnect();
                    }
                });
            });
            
            observer.observe(document.querySelector('.stats-counter'));
            
            // Animate progress ring
            const progressRing = document.querySelector('.progress-ring-circle');
            if (progressRing) {
                setTimeout(() => {
                    const circumference = 2 * Math.PI * 40;
                    const offset = circumference - (85 / 100) * circumference;
                    progressRing.style.strokeDashoffset = offset;
                }, 1000);
            }
        });
    </script>
</body>
</html>
