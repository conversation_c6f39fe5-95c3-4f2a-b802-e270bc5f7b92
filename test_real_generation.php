<?php
/**
 * Test Real Audio and Video Generation
 * Check what's actually working and fix the issues
 */

echo "🐘 TESTING REAL AUDIO & VIDEO GENERATION\n";
echo "========================================\n\n";

// Test 1: Check if directories exist
echo "1. Checking directories:\n";
$dirs = ['temp', 'data/output_history', 'data/jobs'];
foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "   Created: $dir\n";
    } else {
        echo "   ✅ Exists: $dir\n";
    }
}
echo "\n";

// Test 2: Test Windows TTS
echo "2. Testing Windows TTS:\n";
try {
    require_once 'core/windows_tts_engine.php';
    $windowsTTS = new WindowsTTSEngine();
    
    $testText = "A majestic elephant is walking slowly through the dense jungle, trumpeting loudly as birds fly overhead.";
    $testFile = "temp/elephant_audio_test.wav";
    
    echo "   Generating audio: '$testText'\n";
    $success = $windowsTTS->generateWindowsSpeech($testText, 'default', $testFile);
    
    if ($success && file_exists($testFile)) {
        $size = filesize($testFile);
        echo "   ✅ SUCCESS! Generated audio file: " . round($size/1024, 1) . " KB\n";
        echo "   File: $testFile\n";
    } else {
        echo "   ❌ FAILED to generate audio\n";
    }
} catch (Exception $e) {
    echo "   ❌ ERROR: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 3: Test Video Generation
echo "3. Testing Video Generation:\n";
try {
    require_once 'core/working_video_engine.php';
    $videoEngine = new WorkingVideoEngine();
    
    // Create test segments
    $segments = [
        ['text' => 'A majestic elephant walks through the jungle'],
        ['text' => 'The elephant trumpets loudly as birds scatter'],
        ['text' => 'Sunlight filters through the dense canopy above']
    ];
    
    echo "   Generating video with elephant jungle scenes...\n";
    $videoFile = $videoEngine->generateWorkingVideo($segments, 'nature', 'cinematic', 'elephant_test');
    
    if ($videoFile && file_exists($videoFile)) {
        $size = filesize($videoFile);
        echo "   ✅ SUCCESS! Generated video file: " . round($size/1024, 1) . " KB\n";
        echo "   File: $videoFile\n";
    } else {
        echo "   ❌ FAILED to generate video\n";
    }
} catch (Exception $e) {
    echo "   ❌ ERROR: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 4: Test Full Generation Process
echo "4. Testing Full Generation Process:\n";
try {
    // Simulate a real generation request
    $_POST = [
        'action' => 'generate',
        'flow_type' => 'reel',
        'style' => 'cinematic',
        'voice_pack' => 'default',
        'background' => 'nature',
        'content_source' => 'text',
        'content_text' => 'Create an amazing video of an elephant walking through a beautiful jungle with birds and nature sounds.'
    ];
    
    $_SERVER['REQUEST_METHOD'] = 'POST';
    
    echo "   Starting full generation process...\n";
    require_once 'generate.php';
    $generator = new SutradharGenerator();
    
    $result = $generator->handleRequest();
    $resultData = json_decode($result, true);
    
    if ($resultData && $resultData['success']) {
        echo "   ✅ Generation started: " . $resultData['job_id'] . "\n";
        
        // Monitor for a few seconds
        for ($i = 0; $i < 10; $i++) {
            sleep(1);
            $_GET['action'] = 'status';
            $_GET['job_id'] = $resultData['job_id'];
            $_SERVER['REQUEST_METHOD'] = 'GET';
            
            $statusResult = $generator->handleRequest();
            $statusData = json_decode($statusResult, true);
            
            if ($statusData) {
                echo "     Progress: " . ($statusData['progress'] ?? 0) . "% - " . ($statusData['message'] ?? 'Processing') . "\n";
                
                if ($statusData['status'] === 'complete') {
                    echo "   ✅ Generation completed!\n";
                    
                    // Check output files
                    if (isset($statusData['output'])) {
                        foreach ($statusData['output'] as $type => $fileInfo) {
                            if (isset($fileInfo['file']) && file_exists($fileInfo['file'])) {
                                echo "     $type: " . $fileInfo['file'] . " (" . round(filesize($fileInfo['file'])/1024, 1) . " KB)\n";
                            }
                        }
                    }
                    break;
                } elseif ($statusData['status'] === 'error') {
                    echo "   ❌ Generation failed: " . ($statusData['error'] ?? 'Unknown error') . "\n";
                    break;
                }
            }
        }
    } else {
        echo "   ❌ Failed to start generation\n";
        if ($resultData && isset($resultData['error'])) {
            echo "   Error: " . $resultData['error'] . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "   ❌ ERROR: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 5: Check FFmpeg
echo "5. Testing FFmpeg:\n";
exec('ffmpeg -version 2>&1', $ffmpegOutput, $ffmpegReturn);
if ($ffmpegReturn === 0) {
    echo "   ✅ FFmpeg is available\n";
    
    // Test creating a simple video
    $testVideoFile = "temp/ffmpeg_test.mp4";
    $cmd = "ffmpeg -f lavfi -i testsrc=duration=3:size=320x240:rate=1 -c:v libx264 \"$testVideoFile\" -y 2>&1";
    exec($cmd, $output, $returnCode);
    
    if ($returnCode === 0 && file_exists($testVideoFile)) {
        echo "   ✅ FFmpeg can create videos: " . round(filesize($testVideoFile)/1024, 1) . " KB\n";
        unlink($testVideoFile);
    } else {
        echo "   ❌ FFmpeg failed to create test video\n";
    }
} else {
    echo "   ❌ FFmpeg not available\n";
}
echo "\n";

// Test 6: Check GD Extension
echo "6. Testing GD Extension:\n";
if (extension_loaded('gd')) {
    echo "   ✅ GD extension loaded\n";
    
    // Test creating an image
    $testImage = imagecreatetruecolor(640, 480);
    if ($testImage) {
        // Create a jungle scene
        $green = imagecolorallocate($testImage, 34, 139, 34);
        $brown = imagecolorallocate($testImage, 139, 69, 19);
        $gray = imagecolorallocate($testImage, 105, 105, 105);
        
        // Fill background with green (jungle)
        imagefill($testImage, 0, 0, $green);
        
        // Draw elephant shape (simple rectangle for test)
        imagefilledrectangle($testImage, 200, 300, 400, 400, $gray);
        
        // Draw tree trunk
        imagefilledrectangle($testImage, 100, 200, 120, 400, $brown);
        
        $testImageFile = "temp/elephant_test.png";
        $result = imagepng($testImage, $testImageFile);
        imagedestroy($testImage);
        
        if ($result && file_exists($testImageFile)) {
            echo "   ✅ GD can create images: " . round(filesize($testImageFile)/1024, 1) . " KB\n";
            echo "   Created elephant jungle scene: $testImageFile\n";
        } else {
            echo "   ❌ GD failed to create image\n";
        }
    } else {
        echo "   ❌ GD failed to create image resource\n";
    }
} else {
    echo "   ❌ GD extension not loaded\n";
}

echo "\n🎯 SUMMARY:\n";
echo "===========\n";
echo "The goal is to generate REAL content like:\n";
echo "🐘 Audio: Actual narration about elephants in jungle\n";
echo "🎬 Video: Visual scenes of elephants moving through jungle\n";
echo "🎵 Combined: 30-second video with real audio and visuals\n\n";

echo "Next steps:\n";
echo "1. Fix any failing components above\n";
echo "2. Ensure audio files have actual speech (not silence)\n";
echo "3. Ensure video files have actual moving content\n";
echo "4. Test end-to-end generation with elephant jungle theme\n";
?>
