<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎬 Test Working API</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0F0F23 0%, #1A1A2E 50%, #16213E 100%);
            font-family: 'Arial', sans-serif;
            color: white;
            min-height: 100vh;
        }
        .test-card {
            background: rgba(26, 26, 46, 0.8);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            transition: all 0.3s ease;
        }
        .success { border-color: #10B981; background: rgba(16, 185, 129, 0.1); }
        .error { border-color: #EF4444; background: rgba(239, 68, 68, 0.1); }
        .loading { border-color: #F59E0B; background: rgba(245, 158, 11, 0.1); }
        .cyber-button {
            background: linear-gradient(45deg, #00D4FF, #0099CC);
            border: none;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .cyber-button:hover {
            background: linear-gradient(45deg, #0099CC, #00D4FF);
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="p-8">
    <div class="max-w-4xl mx-auto">
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold mb-4">🎬 Working API Test</h1>
            <p class="text-gray-300">Testing the working API system</p>
        </div>

        <div class="grid md:grid-cols-2 gap-6 mb-8">
            <!-- API Status Test -->
            <div class="test-card p-6 rounded-lg" id="test-status">
                <h3 class="text-lg font-semibold mb-3">📡 API Status</h3>
                <button onclick="testAPIStatus()" class="cyber-button px-4 py-2 rounded text-sm">Test Status</button>
                <div class="mt-3 text-sm" id="status-result">Ready to test</div>
            </div>

            <!-- Video Generation Test -->
            <div class="test-card p-6 rounded-lg" id="test-generation">
                <h3 class="text-lg font-semibold mb-3">🎬 Video Generation</h3>
                <button onclick="testVideoGeneration()" class="cyber-button px-4 py-2 rounded text-sm">Test Generation</button>
                <div class="mt-3 text-sm" id="generation-result">Ready to test</div>
            </div>

            <!-- Progress Tracking Test -->
            <div class="test-card p-6 rounded-lg" id="test-progress">
                <h3 class="text-lg font-semibold mb-3">📊 Progress Tracking</h3>
                <button onclick="testProgressTracking()" class="cyber-button px-4 py-2 rounded text-sm">Test Progress</button>
                <div class="mt-3 text-sm" id="progress-result">Ready to test</div>
            </div>

            <!-- Dashboard Test -->
            <div class="test-card p-6 rounded-lg" id="test-dashboard">
                <h3 class="text-lg font-semibold mb-3">📈 Dashboard</h3>
                <button onclick="testDashboard()" class="cyber-button px-4 py-2 rounded text-sm">Test Dashboard</button>
                <div class="mt-3 text-sm" id="dashboard-result">Ready to test</div>
            </div>
        </div>

        <!-- Full Integration Test -->
        <div class="test-card p-6 rounded-lg mb-8">
            <h3 class="text-lg font-semibold mb-3">🚀 Full Integration Test</h3>
            <button onclick="runFullTest()" class="cyber-button px-6 py-3 rounded text-lg">Run Complete Test</button>
            <div class="mt-4">
                <div id="full-test-progress" class="w-full bg-gray-700 rounded-full h-2 mb-2 hidden">
                    <div id="full-test-bar" class="bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
                </div>
                <div id="full-test-result" class="text-sm">Click to run complete integration test</div>
            </div>
        </div>

        <!-- Video Display -->
        <div class="test-card p-6 rounded-lg" id="video-display" style="display: none;">
            <h3 class="text-lg font-semibold mb-3">🎬 Generated Video</h3>
            <div id="video-container"></div>
        </div>

        <!-- API Comparison -->
        <div class="test-card p-6 rounded-lg">
            <h3 class="text-lg font-semibold mb-3">🔄 API Comparison</h3>
            <div class="grid md:grid-cols-3 gap-4">
                <button onclick="testAPI('api_unified.php')" class="cyber-button px-4 py-2 rounded text-sm">Test Unified API</button>
                <button onclick="testAPI('test_api_simple.php')" class="cyber-button px-4 py-2 rounded text-sm">Test Simple API</button>
                <button onclick="testAPI('working_api.php')" class="cyber-button px-4 py-2 rounded text-sm">Test Working API</button>
            </div>
            <div class="mt-4" id="comparison-result">Click buttons to compare APIs</div>
        </div>
    </div>

    <script>
        let currentJobId = null;

        function updateTestStatus(testId, status, message) {
            const card = document.getElementById(`test-${testId}`);
            const result = document.getElementById(`${testId}-result`);
            
            card.className = `test-card p-6 rounded-lg ${status}`;
            result.innerHTML = message;
        }

        async function testAPIStatus() {
            updateTestStatus('status', 'loading', 'Testing API status...');
            
            try {
                const response = await fetch('working_api.php?endpoint=auth/status');
                const data = await response.json();
                
                if (data.success) {
                    updateTestStatus('status', 'success', `✅ API Status: ${data.message}<br>Demo Mode: ${data.data.demo_mode}`);
                } else {
                    updateTestStatus('status', 'error', `❌ API Error: ${data.error}`);
                }
            } catch (error) {
                updateTestStatus('status', 'error', `❌ Request failed: ${error.message}`);
            }
        }

        async function testVideoGeneration() {
            updateTestStatus('generation', 'loading', 'Starting video generation...');
            
            try {
                const response = await fetch('working_api.php?endpoint=generate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        mood: 'euphoric',
                        topic: 'nature_wildlife',
                        custom_prompt: 'Beautiful euphoric nature scene with wildlife'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    currentJobId = data.data.job_id;
                    updateTestStatus('generation', 'success', 
                        `✅ Generation started<br>Job ID: ${data.data.job_id}<br>Type: ${data.data.generation_type}<br>ETA: ${data.data.estimated_time}s`);
                } else {
                    updateTestStatus('generation', 'error', `❌ Generation failed: ${data.error}`);
                }
            } catch (error) {
                updateTestStatus('generation', 'error', `❌ Request failed: ${error.message}`);
            }
        }

        async function testProgressTracking() {
            if (!currentJobId) {
                updateTestStatus('progress', 'error', '❌ No job ID available. Run video generation first.');
                return;
            }
            
            updateTestStatus('progress', 'loading', 'Tracking progress...');
            
            const trackProgress = async () => {
                try {
                    const response = await fetch(`working_api.php?endpoint=generate/status/${currentJobId}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        const progress = data.data.progress || 0;
                        const status = data.data.status;
                        const message = data.data.message;
                        
                        updateTestStatus('progress', 'loading', 
                            `📊 Progress: ${Math.round(progress)}%<br>Status: ${status}<br>Message: ${message}`);
                        
                        if (status === 'completed') {
                            updateTestStatus('progress', 'success', 
                                `✅ Completed!<br>Video: ${data.data.video_url}<br>Thumbnail: ${data.data.thumbnail_url}`);
                            
                            // Show video
                            showGeneratedVideo(data.data);
                        } else if (progress < 100) {
                            setTimeout(trackProgress, 2000);
                        }
                    } else {
                        updateTestStatus('progress', 'error', `❌ Progress check failed: ${data.error}`);
                    }
                } catch (error) {
                    updateTestStatus('progress', 'error', `❌ Progress tracking failed: ${error.message}`);
                }
            };
            
            trackProgress();
        }

        async function testDashboard() {
            updateTestStatus('dashboard', 'loading', 'Loading dashboard data...');
            
            try {
                const response = await fetch('working_api.php?endpoint=dashboard/stats');
                const data = await response.json();
                
                if (data.success) {
                    const stats = data.data.generation_stats;
                    updateTestStatus('dashboard', 'success', 
                        `✅ Dashboard loaded<br>Total: ${stats.total}<br>Completed: ${stats.completed}<br>Enhanced: ${stats.enhanced}<br>Success Rate: ${stats.success_rate}%`);
                } else {
                    updateTestStatus('dashboard', 'error', `❌ Dashboard failed: ${data.error}`);
                }
            } catch (error) {
                updateTestStatus('dashboard', 'error', `❌ Dashboard request failed: ${error.message}`);
            }
        }

        async function runFullTest() {
            const progressBar = document.getElementById('full-test-progress');
            const progressFill = document.getElementById('full-test-bar');
            const result = document.getElementById('full-test-result');
            
            progressBar.classList.remove('hidden');
            result.innerHTML = 'Running full integration test...';
            
            const tests = [
                { name: 'API Status', func: testAPIStatus, weight: 20 },
                { name: 'Video Generation', func: testVideoGeneration, weight: 30 },
                { name: 'Progress Tracking', func: testProgressTracking, weight: 30 },
                { name: 'Dashboard', func: testDashboard, weight: 20 }
            ];
            
            let totalProgress = 0;
            
            for (const test of tests) {
                result.innerHTML = `Running: ${test.name}...`;
                await test.func();
                totalProgress += test.weight;
                progressFill.style.width = `${totalProgress}%`;
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            result.innerHTML = '🎉 Full integration test completed! Check individual test results above.';
            progressFill.style.width = '100%';
        }

        async function testAPI(apiFile) {
            const result = document.getElementById('comparison-result');
            result.innerHTML = `Testing ${apiFile}...`;
            
            try {
                const response = await fetch(`${apiFile}?endpoint=auth/status`);
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML += `<br>✅ ${apiFile}: Working`;
                } else {
                    result.innerHTML += `<br>❌ ${apiFile}: Error - ${data.error}`;
                }
            } catch (error) {
                result.innerHTML += `<br>❌ ${apiFile}: Failed - ${error.message}`;
            }
        }

        function showGeneratedVideo(videoData) {
            const videoDisplay = document.getElementById('video-display');
            const videoContainer = document.getElementById('video-container');
            
            videoContainer.innerHTML = `
                <div class="mb-4">
                    <h4 class="font-semibold mb-2">Generated Video Details:</h4>
                    <p class="text-sm text-gray-300">Mood: ${videoData.mood}</p>
                    <p class="text-sm text-gray-300">Topic: ${videoData.topic}</p>
                    <p class="text-sm text-gray-300">Type: ${videoData.generation_type}</p>
                    <p class="text-sm text-gray-300">Processing Time: ${videoData.processing_time}s</p>
                </div>
                <div class="mb-4">
                    <video controls width="100%" style="max-width: 500px;">
                        <source src="${videoData.video_url}" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                </div>
                <div class="flex gap-2">
                    <a href="${videoData.video_url}" target="_blank" class="cyber-button px-4 py-2 rounded text-sm">🎬 Open Video</a>
                    <a href="${videoData.thumbnail_url}" target="_blank" class="cyber-button px-4 py-2 rounded text-sm">🖼️ View Thumbnail</a>
                </div>
            `;
            
            videoDisplay.style.display = 'block';
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testAPIStatus();
            }, 1000);
        });
    </script>
</body>
</html>
