<?php
/**
 * API Endpoint for Sutradhar Engine Frontend
 * Handles requests from the public directory
 */

// Disable error display for clean JSON output
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Start output buffering to catch any unexpected output
ob_start();

// Set content type and CORS headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Include required classes
require_once '../core/template_engine.php';

try {
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    
    switch ($action) {
        case 'list_templates':
            $templateEngine = new TemplateEngine();
            $templates = $templateEngine->getTemplateList();
            echo json_encode($templates);
            break;
            
        case 'get_template':
            $templateId = $_GET['id'] ?? $_POST['id'] ?? '';
            if (empty($templateId)) {
                throw new Exception('Template ID required');
            }
            
            $templateEngine = new TemplateEngine();
            $template = $templateEngine->getTemplate($templateId);
            echo json_encode($template);
            break;
            
        case 'generate':
            // Include and instantiate the generator
            require_once '../generate.php';
            $generator = new SutradharGenerator();
            echo $generator->handleRequest();
            break;

        case 'status':
            // Include and instantiate the generator for status check
            require_once '../generate.php';
            $generator = new SutradharGenerator();
            echo $generator->handleRequest();
            break;

        case 'download':
            // Include and instantiate the generator for downloads
            require_once '../generate.php';
            $generator = new SutradharGenerator();
            echo $generator->handleRequest();
            break;

        case 'system_status':
            // Check system status for 2070 UI
            require_once '../core/huggingface_video_engine.php';
            require_once '../core/female_voice_engine.php';

            $hfEngine = new HuggingFaceVideoEngine();
            $femaleVoice = new FemaleVoiceEngine();

            // Check FFmpeg
            exec('ffmpeg -version 2>&1', $output, $returnCode);
            $ffmpegStatus = $returnCode === 0 ? 'online' : 'offline';

            // Check Hugging Face
            $hfTest = $hfEngine->testHuggingFaceConnection();
            $hfStatus = $hfTest['status'] === 'success' ? 'online' : 'offline';

            // Check Voice Engine
            $voiceTest = $femaleVoice->testFemaleVoice();
            $voiceStatus = $voiceTest['status'] === 'success' ? 'online' : 'offline';

            echo json_encode([
                'success' => true,
                'huggingface_status' => $hfStatus,
                'ffmpeg_status' => $ffmpegStatus,
                'voice_status' => $voiceStatus,
                'gd_status' => extension_loaded('gd') ? 'online' : 'offline',
                'system_ready' => $ffmpegStatus === 'online' && extension_loaded('gd')
            ]);
            break;

        default:
            throw new Exception('Invalid action: ' . $action);
    }
    
} catch (Exception $e) {
    // Clean any output buffer
    ob_clean();

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} finally {
    // Ensure clean output
    if (ob_get_level()) {
        $output = ob_get_clean();

        // Check if output is valid JSON
        $decoded = json_decode($output);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo $output;
        } else {
            // If not valid JSON, return error
            echo json_encode([
                'success' => false,
                'error' => 'Invalid response format'
            ]);
        }
    }
}
?>
