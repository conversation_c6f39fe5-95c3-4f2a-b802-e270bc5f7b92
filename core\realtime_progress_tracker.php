<?php
/**
 * Real-time Progress Tracker - Advanced progress tracking with detailed status updates
 * Provides estimated completion times and visual progress indicators
 */

class RealtimeProgressTracker {
    private $jobsDir;
    private $progressFile;
    private $currentJob;
    private $startTime;
    private $stages;
    private $currentStage;
    private $stageProgress;
    private $estimatedTotalTime;
    
    public function __construct($jobId) {
        $this->jobsDir = __DIR__ . '/../data/jobs/';
        $this->progressFile = $this->jobsDir . $jobId . '_progress.json';
        $this->currentJob = $jobId;
        $this->startTime = microtime(true);
        $this->currentStage = 0;
        $this->stageProgress = 0;
        
        $this->initializeStages();
        $this->initializeProgressFile();
    }
    
    /**
     * Initialize processing stages with estimated durations
     */
    private function initializeStages() {
        $this->stages = [
            [
                'id' => 'initialization',
                'name' => 'System Initialization',
                'description' => 'Preparing generation environment',
                'estimated_duration' => 5,
                'weight' => 5,
                'substeps' => [
                    'Loading mood database',
                    'Initializing engines',
                    'Preparing workspace',
                    'Validating parameters'
                ]
            ],
            [
                'id' => 'mood_analysis',
                'name' => 'Mood Analysis',
                'description' => 'Analyzing emotional characteristics',
                'estimated_duration' => 10,
                'weight' => 10,
                'substeps' => [
                    'Processing mood selection',
                    'Extracting visual characteristics',
                    'Analyzing audio properties',
                    'Preparing content style'
                ]
            ],
            [
                'id' => 'prompt_generation',
                'name' => 'Prompt Generation',
                'description' => 'Creating AI-powered prompts',
                'estimated_duration' => 15,
                'weight' => 15,
                'substeps' => [
                    'Analyzing topic and inspiration',
                    'Generating mood-focused prompts',
                    'Creating topic-focused variations',
                    'Applying image style filters',
                    'Optimizing prompt quality'
                ]
            ],
            [
                'id' => 'content_parsing',
                'name' => 'Content Processing',
                'description' => 'Parsing and structuring content',
                'estimated_duration' => 20,
                'weight' => 15,
                'substeps' => [
                    'Analyzing selected prompt',
                    'Breaking into segments',
                    'Optimizing for voice synthesis',
                    'Preparing visual cues'
                ]
            ],
            [
                'id' => 'audio_generation',
                'name' => 'Audio Synthesis',
                'description' => 'Generating high-quality voice audio',
                'estimated_duration' => 45,
                'weight' => 20,
                'substeps' => [
                    'Initializing voice engine',
                    'Processing text segments',
                    'Applying mood-based prosody',
                    'Generating audio files',
                    'Optimizing audio quality'
                ]
            ],
            [
                'id' => 'scene_generation',
                'name' => 'Scene Creation',
                'description' => 'Creating visual scenes with mood characteristics',
                'estimated_duration' => 60,
                'weight' => 25,
                'substeps' => [
                    'Analyzing scene requirements',
                    'Applying mood visual style',
                    'Generating scene elements',
                    'Processing in parallel',
                    'Optimizing scene quality'
                ]
            ],
            [
                'id' => 'video_assembly',
                'name' => 'Video Assembly',
                'description' => 'Assembling final video with optimizations',
                'estimated_duration' => 30,
                'weight' => 10,
                'substeps' => [
                    'Preparing video pipeline',
                    'Merging audio and video',
                    'Applying final effects',
                    'Optimizing for platform',
                    'Quality validation'
                ]
            ]
        ];
        
        // Calculate total estimated time
        $this->estimatedTotalTime = array_sum(array_column($this->stages, 'estimated_duration'));
    }
    
    /**
     * Initialize progress file
     */
    private function initializeProgressFile() {
        $progressData = [
            'job_id' => $this->currentJob,
            'status' => 'initializing',
            'start_time' => $this->startTime,
            'current_stage' => 0,
            'stage_progress' => 0,
            'overall_progress' => 0,
            'estimated_completion' => $this->startTime + $this->estimatedTotalTime,
            'estimated_remaining' => $this->estimatedTotalTime,
            'stages' => $this->stages,
            'current_substep' => '',
            'processing_speed' => 1.0,
            'last_update' => microtime(true),
            'messages' => []
        ];
        
        $this->saveProgress($progressData);
    }
    
    /**
     * Update current stage
     */
    public function updateStage($stageIndex, $substepIndex = 0, $message = '') {
        $this->currentStage = $stageIndex;
        $this->stageProgress = 0;
        
        $progressData = $this->loadProgress();
        $progressData['current_stage'] = $stageIndex;
        $progressData['stage_progress'] = 0;
        $progressData['status'] = $this->stages[$stageIndex]['id'];
        
        if ($substepIndex < count($this->stages[$stageIndex]['substeps'])) {
            $progressData['current_substep'] = $this->stages[$stageIndex]['substeps'][$substepIndex];
        }
        
        if ($message) {
            $progressData['messages'][] = [
                'timestamp' => microtime(true),
                'stage' => $stageIndex,
                'message' => $message
            ];
        }
        
        $this->updateOverallProgress($progressData);
        $this->saveProgress($progressData);
    }
    
    /**
     * Update progress within current stage
     */
    public function updateStageProgress($progress, $substepIndex = null, $message = '') {
        $this->stageProgress = max(0, min(100, $progress));
        
        $progressData = $this->loadProgress();
        $progressData['stage_progress'] = $this->stageProgress;
        
        if ($substepIndex !== null && $substepIndex < count($this->stages[$this->currentStage]['substeps'])) {
            $progressData['current_substep'] = $this->stages[$this->currentStage]['substeps'][$substepIndex];
        }
        
        if ($message) {
            $progressData['messages'][] = [
                'timestamp' => microtime(true),
                'stage' => $this->currentStage,
                'substep' => $substepIndex,
                'message' => $message
            ];
        }
        
        $this->updateOverallProgress($progressData);
        $this->updateTimeEstimates($progressData);
        $this->saveProgress($progressData);
    }
    
    /**
     * Mark stage as complete
     */
    public function completeStage($message = '') {
        $this->updateStageProgress(100, null, $message);
        
        if ($this->currentStage < count($this->stages) - 1) {
            $this->updateStage($this->currentStage + 1, 0, 'Moving to next stage');
        }
    }
    
    /**
     * Mark entire job as complete
     */
    public function completeJob($outputData = []) {
        $progressData = $this->loadProgress();
        $progressData['status'] = 'completed';
        $progressData['overall_progress'] = 100;
        $progressData['stage_progress'] = 100;
        $progressData['completion_time'] = microtime(true);
        $progressData['total_duration'] = microtime(true) - $this->startTime;
        $progressData['output'] = $outputData;
        
        $progressData['messages'][] = [
            'timestamp' => microtime(true),
            'stage' => 'completion',
            'message' => 'Video generation completed successfully'
        ];
        
        $this->saveProgress($progressData);
    }
    
    /**
     * Mark job as failed
     */
    public function failJob($error, $stage = null) {
        $progressData = $this->loadProgress();
        $progressData['status'] = 'failed';
        $progressData['error'] = $error;
        $progressData['failure_time'] = microtime(true);
        $progressData['failure_stage'] = $stage ?? $this->currentStage;
        
        $progressData['messages'][] = [
            'timestamp' => microtime(true),
            'stage' => $stage ?? $this->currentStage,
            'message' => 'Error: ' . $error,
            'type' => 'error'
        ];
        
        $this->saveProgress($progressData);
    }
    
    /**
     * Get current progress data
     */
    public function getProgress() {
        return $this->loadProgress();
    }
    
    /**
     * Get progress for API response
     */
    public function getProgressForAPI() {
        $data = $this->loadProgress();
        
        return [
            'job_id' => $data['job_id'],
            'status' => $data['status'],
            'overall_progress' => $data['overall_progress'],
            'current_stage' => [
                'index' => $data['current_stage'],
                'name' => $this->stages[$data['current_stage']]['name'] ?? 'Unknown',
                'description' => $this->stages[$data['current_stage']]['description'] ?? '',
                'progress' => $data['stage_progress']
            ],
            'current_substep' => $data['current_substep'],
            'estimated_remaining' => $data['estimated_remaining'],
            'processing_speed' => $data['processing_speed'],
            'messages' => array_slice($data['messages'], -5), // Last 5 messages
            'start_time' => $data['start_time'],
            'last_update' => $data['last_update']
        ];
    }
    
    /**
     * Update overall progress calculation
     */
    private function updateOverallProgress(&$progressData) {
        $totalWeight = array_sum(array_column($this->stages, 'weight'));
        $completedWeight = 0;
        
        // Add weight for completed stages
        for ($i = 0; $i < $this->currentStage; $i++) {
            $completedWeight += $this->stages[$i]['weight'];
        }
        
        // Add partial weight for current stage
        if ($this->currentStage < count($this->stages)) {
            $currentStageWeight = $this->stages[$this->currentStage]['weight'];
            $completedWeight += ($currentStageWeight * $this->stageProgress / 100);
        }
        
        $progressData['overall_progress'] = min(100, ($completedWeight / $totalWeight) * 100);
    }
    
    /**
     * Update time estimates based on current progress
     */
    private function updateTimeEstimates(&$progressData) {
        $currentTime = microtime(true);
        $elapsedTime = $currentTime - $this->startTime;
        
        if ($progressData['overall_progress'] > 0) {
            // Calculate processing speed
            $expectedTimeForProgress = ($progressData['overall_progress'] / 100) * $this->estimatedTotalTime;
            $progressData['processing_speed'] = $expectedTimeForProgress > 0 ? $elapsedTime / $expectedTimeForProgress : 1.0;
            
            // Estimate remaining time
            $remainingProgress = 100 - $progressData['overall_progress'];
            $estimatedRemainingTime = ($remainingProgress / 100) * $this->estimatedTotalTime * $progressData['processing_speed'];
            $progressData['estimated_remaining'] = max(0, $estimatedRemainingTime);
            $progressData['estimated_completion'] = $currentTime + $progressData['estimated_remaining'];
        }
        
        $progressData['last_update'] = $currentTime;
    }
    
    /**
     * Load progress data from file
     */
    private function loadProgress() {
        if (file_exists($this->progressFile)) {
            $data = json_decode(file_get_contents($this->progressFile), true);
            return $data ?: [];
        }
        return [];
    }
    
    /**
     * Save progress data to file
     */
    private function saveProgress($data) {
        if (!is_dir($this->jobsDir)) {
            mkdir($this->jobsDir, 0755, true);
        }
        
        file_put_contents($this->progressFile, json_encode($data, JSON_PRETTY_PRINT), LOCK_EX);
    }
    
    /**
     * Clean up progress file
     */
    public function cleanup() {
        if (file_exists($this->progressFile)) {
            unlink($this->progressFile);
        }
    }
    
    /**
     * Get stage information
     */
    public function getStageInfo($stageIndex) {
        return $this->stages[$stageIndex] ?? null;
    }
    
    /**
     * Get all stages information
     */
    public function getAllStages() {
        return $this->stages;
    }
}
