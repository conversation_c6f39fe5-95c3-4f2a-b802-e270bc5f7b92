<?php
/**
 * Video Engine - Creates videos with audio and visual backgrounds
 * Part of Sutradhar Engine
 */

class VideoEngine {
    private $config;
    private $tempDir;
    private $assetsDir;

    public function __construct() {
        $this->loadConfig();
        $this->setupDirectories();
    }

    private function loadConfig() {
        $configPath = __DIR__ . '/../config/settings.json';
        $this->config = json_decode(file_get_contents($configPath), true);
        $this->tempDir = __DIR__ . '/../temp/';
        $this->assetsDir = __DIR__ . '/../assets/';
    }

    private function setupDirectories() {
        if (!is_dir($this->tempDir)) {
            mkdir($this->tempDir, 0755, true);
        }
    }

    /**
     * Create video with audio and visual elements
     */
    public function createVideo($audioFile, $segments, $background, $flowType, $jobId) {
        $videoFile = $this->tempDir . $jobId . '_video.mp4';
        
        // Get background video/image
        $backgroundMedia = $this->getBackgroundMedia($background, $flowType);
        
        // Generate subtitles
        $subtitleFile = $this->generateSubtitles($segments, $jobId);
        
        // Create video with all components
        $success = $this->composeVideo($audioFile, $backgroundMedia, $subtitleFile, $videoFile, $flowType);
        
        if (!$success) {
            throw new Exception("Failed to create video");
        }

        return [
            'file' => $videoFile,
            'duration' => $this->getVideoDuration($videoFile),
            'format' => 'mp4',
            'resolution' => $this->config['video']['resolution'],
            'fps' => $this->config['video']['fps'],
            'subtitles' => $subtitleFile
        ];
    }

    /**
     * Get background media (video or image)
     */
    private function getBackgroundMedia($background, $flowType) {
        $backgroundsDir = $this->assetsDir . 'backgrounds/';
        
        // Try to find video first, then image
        $videoFile = $backgroundsDir . $background . '.mp4';
        $imageFile = $backgroundsDir . $background . '.jpg';
        
        if (file_exists($videoFile)) {
            return ['type' => 'video', 'file' => $videoFile];
        } elseif (file_exists($imageFile)) {
            return ['type' => 'image', 'file' => $imageFile];
        } else {
            // Create default background
            return $this->createDefaultBackground($background, $flowType);
        }
    }

    /**
     * Create default background if none exists
     */
    private function createDefaultBackground($background, $flowType) {
        $defaultFile = $this->tempDir . 'default_bg_' . $background . '.jpg';
        
        if (!file_exists($defaultFile)) {
            // Create a simple colored background
            $colors = [
                'home' => '#F5F5DC',      // Beige
                'office' => '#E6E6FA',    // Lavender
                'nature' => '#90EE90',    // Light Green
                'city' => '#708090',      // Slate Gray
                'traditional' => '#FFE4B5' // Moccasin
            ];
            
            $color = $colors[$background] ?? '#FFFFFF';
            $resolution = explode('x', $this->config['video']['resolution']);
            
            $command = sprintf(
                'ffmpeg -f lavfi -i "color=%s:size=%dx%d:duration=1" %s -y',
                $color,
                $resolution[0],
                $resolution[1],
                escapeshellarg($defaultFile)
            );
            
            shell_exec($command . ' 2>&1');
        }
        
        return ['type' => 'image', 'file' => $defaultFile];
    }

    /**
     * Generate subtitle file (SRT format)
     */
    private function generateSubtitles($segments, $jobId) {
        $subtitleFile = $this->tempDir . $jobId . '_subtitles.srt';
        $srtContent = '';
        $currentTime = 0;
        $subtitleIndex = 1;

        foreach ($segments as $segment) {
            $text = $segment['text'];
            $duration = $this->estimateSegmentDuration($text);
            
            $startTime = $this->formatSRTTime($currentTime);
            $endTime = $this->formatSRTTime($currentTime + $duration);
            
            $srtContent .= "{$subtitleIndex}\n";
            $srtContent .= "{$startTime} --> {$endTime}\n";
            $srtContent .= $this->formatSubtitleText($text) . "\n\n";
            
            $currentTime += $duration;
            $currentTime += $segment['pause_after'] ?? 0.5;
            $subtitleIndex++;
        }

        file_put_contents($subtitleFile, $srtContent);
        return $subtitleFile;
    }

    /**
     * Format time for SRT subtitles
     */
    private function formatSRTTime($seconds) {
        $hours = intval(floor($seconds / 3600));
        $minutes = intval(floor(($seconds % 3600) / 60));
        $secs = $seconds % 60;
        $milliseconds = intval(($secs - floor($secs)) * 1000);
        
        return sprintf('%02d:%02d:%02d,%03d', $hours, $minutes, intval(floor($secs)), $milliseconds);
    }

    /**
     * Format subtitle text for display
     */
    private function formatSubtitleText($text) {
        // Break long lines
        $maxLength = 40;
        if (strlen($text) > $maxLength) {
            $words = explode(' ', $text);
            $lines = [];
            $currentLine = '';
            
            foreach ($words as $word) {
                if (strlen($currentLine . ' ' . $word) > $maxLength) {
                    if (!empty($currentLine)) {
                        $lines[] = $currentLine;
                        $currentLine = $word;
                    } else {
                        $lines[] = $word;
                    }
                } else {
                    $currentLine .= ($currentLine ? ' ' : '') . $word;
                }
            }
            
            if (!empty($currentLine)) {
                $lines[] = $currentLine;
            }
            
            return implode("\n", $lines);
        }
        
        return $text;
    }

    /**
     * Compose final video with all elements
     */
    private function composeVideo($audioFile, $backgroundMedia, $subtitleFile, $outputFile, $flowType) {
        // Check if FFmpeg is available
        $ffmpegAvailable = $this->isFFmpegAvailable();

        if (!$ffmpegAvailable) {
            // Fallback: just copy the audio file as output (for audio-only content)
            $audioOnlyFile = str_replace('.mp4', '.mp3', $outputFile);
            if (copy($audioFile, $audioOnlyFile)) {
                // Create a placeholder video file
                return $this->createPlaceholderVideo($outputFile);
            }
            return false;
        }

        $audioDuration = $this->getAudioDuration($audioFile);
        $resolution = $this->config['video']['resolution'];
        $fps = $this->config['video']['fps'];

        // Build FFmpeg command based on background type
        if ($backgroundMedia['type'] === 'video') {
            $command = $this->buildVideoCommand($audioFile, $backgroundMedia['file'], $subtitleFile, $outputFile, $audioDuration, $resolution, $fps);
        } else {
            $command = $this->buildImageCommand($audioFile, $backgroundMedia['file'], $subtitleFile, $outputFile, $audioDuration, $resolution, $fps);
        }

        // Execute command
        $result = shell_exec($command . ' 2>&1');

        return file_exists($outputFile) && filesize($outputFile) > 0;
    }

    /**
     * Build FFmpeg command for video background
     */
    private function buildVideoCommand($audioFile, $videoFile, $subtitleFile, $outputFile, $duration, $resolution, $fps) {
        return sprintf(
            'ffmpeg -i %s -i %s -i %s -filter_complex "[1:v]scale=%s,setsar=1[bg];[bg]subtitles=%s:force_style=\'FontName=%s,FontSize=%d,PrimaryColour=&Hffffff,OutlineColour=&H000000,Outline=2\'" -map 0:a -c:v %s -c:a aac -t %f -r %d %s -y',
            escapeshellarg($videoFile),
            escapeshellarg($audioFile),
            escapeshellarg($subtitleFile),
            $resolution,
            escapeshellarg($subtitleFile),
            $this->config['video']['subtitle_font'],
            $this->config['video']['subtitle_size'],
            $this->config['video']['codec'],
            $duration,
            $fps,
            escapeshellarg($outputFile)
        );
    }

    /**
     * Build FFmpeg command for image background
     */
    private function buildImageCommand($audioFile, $imageFile, $subtitleFile, $outputFile, $duration, $resolution, $fps) {
        return sprintf(
            'ffmpeg -loop 1 -i %s -i %s -filter_complex "[0:v]scale=%s,setsar=1[bg];[bg]subtitles=%s:force_style=\'FontName=%s,FontSize=%d,PrimaryColour=&Hffffff,OutlineColour=&H000000,Outline=2\'" -map 1:a -c:v %s -c:a aac -t %f -r %d -pix_fmt yuv420p %s -y',
            escapeshellarg($imageFile),
            escapeshellarg($audioFile),
            $resolution,
            escapeshellarg($subtitleFile),
            $this->config['video']['subtitle_font'],
            $this->config['video']['subtitle_size'],
            $this->config['video']['codec'],
            $duration,
            $fps,
            escapeshellarg($outputFile)
        );
    }

    /**
     * Estimate segment duration for subtitles
     */
    private function estimateSegmentDuration($text) {
        $wordCount = str_word_count($text);
        $wordsPerMinute = 150;
        return ($wordCount / $wordsPerMinute) * 60;
    }

    /**
     * Get audio file duration
     */
    private function getAudioDuration($audioFile) {
        $command = sprintf(
            'ffprobe -v quiet -show_entries format=duration -of csv="p=0" %s',
            escapeshellarg($audioFile)
        );
        
        $duration = shell_exec($command);
        return floatval(trim($duration ?? '0'));
    }

    /**
     * Get video file duration
     */
    private function getVideoDuration($videoFile) {
        return $this->getAudioDuration($videoFile); // Same command works for video
    }

    /**
     * Create thumbnail for video
     */
    public function createThumbnail($videoFile, $jobId) {
        $thumbnailFile = $this->tempDir . $jobId . '_thumbnail.jpg';
        
        $command = sprintf(
            'ffmpeg -i %s -ss 00:00:01 -vframes 1 -q:v 2 %s -y',
            escapeshellarg($videoFile),
            escapeshellarg($thumbnailFile)
        );
        
        shell_exec($command . ' 2>&1');
        
        return file_exists($thumbnailFile) ? $thumbnailFile : null;
    }

    /**
     * Check if FFmpeg is available
     */
    private function isFFmpegAvailable() {
        $output = shell_exec('ffmpeg -version 2>&1');
        return $output && strpos($output, 'ffmpeg version') !== false;
    }

    /**
     * Create a placeholder video file when FFmpeg is not available
     */
    private function createPlaceholderVideo($outputFile) {
        // Create a simple text file indicating video generation requires FFmpeg
        $placeholderContent = "Video generation requires FFmpeg. Audio file is available separately.";
        $textFile = str_replace('.mp4', '_video_placeholder.txt', $outputFile);
        file_put_contents($textFile, $placeholderContent);

        // Create a minimal "video" file (actually just a text file with .mp4 extension)
        return file_put_contents($outputFile, $placeholderContent) !== false;
    }

    /**
     * Clean up temporary files
     */
    public function cleanup($jobId) {
        $pattern = $this->tempDir . $jobId . '_*';
        $files = glob($pattern);

        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
    }
}
