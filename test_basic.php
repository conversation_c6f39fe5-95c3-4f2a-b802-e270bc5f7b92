<?php
/**
 * Basic System Test
 */

echo "🔧 Basic System Test\n";
echo "=" . str_repeat("=", 20) . "\n\n";

// Test 1: Check PHP version
echo "🐘 PHP Version: " . PHP_VERSION . "\n";

// Test 2: Check if config file exists
echo "📋 Checking config file...\n";
$configPath = 'config/settings.json';

if (file_exists($configPath)) {
    echo "✅ Config file exists\n";
    
    $configContent = file_get_contents($configPath);
    if ($configContent) {
        echo "✅ Config file readable\n";
        
        $config = json_decode($configContent, true);
        if ($config) {
            echo "✅ Config file valid JSON\n";
            
            $apiKey = $config['api_keys']['huggingface'] ?? '';
            if (!empty($apiKey)) {
                echo "✅ Hugging Face API key configured\n";
            } else {
                echo "❌ Hugging Face API key missing\n";
            }
        } else {
            echo "❌ Config file invalid JSON\n";
        }
    } else {
        echo "❌ Config file not readable\n";
    }
} else {
    echo "❌ Config file not found\n";
}

// Test 3: Check directories
echo "\n📁 Checking directories...\n";
$dirs = ['temp', 'public', 'core', 'assets'];
foreach ($dirs as $dir) {
    if (is_dir($dir)) {
        echo "✅ Directory exists: $dir\n";
    } else {
        echo "❌ Directory missing: $dir\n";
    }
}

// Test 4: Check GD extension
echo "\n🎨 Checking GD extension...\n";
if (extension_loaded('gd')) {
    echo "✅ GD extension loaded\n";
    $gdInfo = gd_info();
    echo "   Version: " . ($gdInfo['GD Version'] ?? 'Unknown') . "\n";
} else {
    echo "❌ GD extension not loaded\n";
}

// Test 5: Check FFmpeg
echo "\n🎥 Checking FFmpeg...\n";
$ffmpegOutput = [];
$ffmpegReturn = 0;
exec('ffmpeg -version 2>&1', $ffmpegOutput, $ffmpegReturn);

if ($ffmpegReturn === 0 && !empty($ffmpegOutput)) {
    echo "✅ FFmpeg available\n";
    echo "   " . (isset($ffmpegOutput[0]) ? substr($ffmpegOutput[0], 0, 50) . "..." : "Version unknown") . "\n";
} else {
    echo "❌ FFmpeg not available\n";
}

// Test 6: Test simple image creation
echo "\n🖼️ Testing image creation...\n";
if (function_exists('imagecreatetruecolor')) {
    $testImage = imagecreatetruecolor(100, 100);
    if ($testImage) {
        $testFile = 'temp/test_image.png';
        if (!is_dir('temp')) {
            mkdir('temp', 0755, true);
        }
        
        $result = imagepng($testImage, $testFile);
        imagedestroy($testImage);
        
        if ($result && file_exists($testFile)) {
            echo "✅ Image creation successful\n";
            echo "   Test file: $testFile (" . filesize($testFile) . " bytes)\n";
            unlink($testFile); // Clean up
        } else {
            echo "❌ Image creation failed\n";
        }
    } else {
        echo "❌ Could not create image resource\n";
    }
} else {
    echo "❌ imagecreatetruecolor function not available\n";
}

echo "\n🎉 Basic test completed!\n";
?>
